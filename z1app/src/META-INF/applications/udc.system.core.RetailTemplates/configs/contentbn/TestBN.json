{"contents": {"en": {"status": "published", "title": "", "content": "<div class=\"z1_b_box z1_b_box_style\" style=\"background: #0000ff; color: #222; border-bottom: 1px solid #5F9832; \"><div class=\"z1_b_c2\"><div class=\"z1_b_c2_1\" style=\"color: #ff0000;\">Test Banner Notification</div></div></div>", "draft": null, "payload": "<div class=\"z1_b_box z1_b_box_style\" style=\"background: ${backgroundcolor}; color: #222; border-bottom: 1px solid #5F9832; \"><div class=\"z1_b_c2\"><div class=\"z1_b_c2_1\" style=\"color: ${fontcolor};\">${body}</div></div></div>"}}, "title": "Test Banner Notification", "contentType": "text", "tags": ["_z1_os:android", "_z1_os:ios", "_z1_os:html5", "_z1_banner"], "custom": {"old_template_format": false, "en": {"mode": "simple", "template_name": "Text Template With Color Customizations", "template_id": "b_text", "structure": [{"name": "body", "id": "body", "displayName": "Banner Content", "type": "multiline", "value": "Test Banner Notification"}, {"name": "backgroundcolor", "id": "backgroundcolor", "displayName": "Banner Background Color", "type": "colorpicker", "value": "#0000ff"}, {"name": "fontcolor", "id": "fontcolor", "displayName": "Banner Text Color", "type": "colorpicker", "value": "#ff0000"}]}, "custom_action_type": "noaction", "custom_action_value": ""}, "status": "published"}