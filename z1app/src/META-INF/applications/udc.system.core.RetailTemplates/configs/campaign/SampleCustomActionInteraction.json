{"compositeActions": [], "states": [{"timers": [{"actions": [{"params": [{"name": "z1_ref", "value": "${udc.system.core.RetailTemplates:TestAction}"}, {"name": "z1_target", "value": "100"}, {"name": "z1_tags", "value": ""}, {"name": "z1_isAB", "value": "false"}], "selector": [], "ref": "udc.system.core.ZineOne Processing:CustomAction", "name": "Custom Action", "dimension": "custom", "nextSession": false}], "unit": "minutes", "value": "1"}], "activities": [], "ref": "${udc.system.core.RetailTemplates:FrequentUser}"}], "params": []}