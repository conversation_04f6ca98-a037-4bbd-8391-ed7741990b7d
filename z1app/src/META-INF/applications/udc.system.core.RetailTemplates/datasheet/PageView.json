{"axes": [{"id": "page", "label": "Page", "src": "page/*"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Views per page", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.RetailTemplate:PageViewCube", "type": "cube"}, "dataPoints": [{"label": "Views", "id": "views", "method": "udc.system.aggregation:sum", "src": "views"}], "description": "", "filters": []}