<?xml version="1.0" encoding="UTF-8"?>
<p:application id="udc.system.core.CommonOOTB"
        version="" listener="z1.AppInstallListener" xmlns:p="http://udichi/core/application/def"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://udichi/core/application/def ../../../../platform/schema/application.xsd ">

        <!-- <p:artifact type="config" subtype="goal"
                id="udc.system.core.CommonOOTB:TestCheckDepositFailureGoal" name="Deposit Failure"
                description="Deposit failure" />
		 -->

        <!--p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:MAU" /-->
        <!--p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:FrequentUser" /-->
        <!-- <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:HeavyUser" />
         -->
         
        <!-- <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:GalaxyS2User" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:Nexus7User" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:IPhoneUser" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:HTCUser" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:ThunderboltUser" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:KitKat" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:IOS7" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:JellyBean" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:IOS6" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:IceCream" />
                
        -->
        
        <!-- 
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:Paid" />
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.CommonOOTB:Unpaid" />
        -->

        <!--p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:AppStart" /-->
                
<!--        <p:artifact type="config" subtype="signal"-->
<!--                id="udc.system.core.CommonOOTB:SampleEvent" />-->

        <!--  <p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:AppExit" />
        <p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:AppUnresponsive" />
        <p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:UserInStore" />
        <p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:UserInProximity" />
        <p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:UserInZipcode" />
        -->
        
        
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppBanner" />                
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppAlert" />                
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppFullscreen"/>
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppAlertKB" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppSMSMessage" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppRaw" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:MobileAppInbox" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:PushNotification" />

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_aov"
                name="bp_aov"
                description="Avg. Order Value - Experience specific Business Performance Overview"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_conversionRate"
                name="bp_conversionRate"
                description="Conversion rate/action - Experience specific Business Performance Overview"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_lift"
                name="bp_lift"
                description="CVR lift, RPV lift and AOV lift - Experience specific Business Performance Overview"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_rpv"
                name="bp_rpv"
                description="Revenue Per Visitor - Experience specific Business Performance Overview"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_uniqueVisitor"
                name="bp_uniqueVisitor"
                description="Experience specific buisness performance - unique visitors by day"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:compareSameMetricPerDay"
                name="compareSameMetricPerDay"
                description="Compare the same metric per day."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:compareSameMetricPerDayWithGroup"
                name="compareSameMetricPerDayWithGroup"
                description="Compare the same metric per day with grouping."/>     
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:compareSameMetricPerDayWithFilter"
                name="compareSameMetricPerDayWithFilter"
                description="Compare the same metric per day with filter."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventsPerChannel"
                name="eventsPerChannel"
                description="Events per Channel Chart."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:sessionsPerChannel"
                name="sessionsPerChannel"
                description="Sessions per Channel Chart."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:sessionsPerChannelV2"
                name="sessionsPerChannelV2"
                description="Sessions per Channel Chart."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:operationalMetrics"
                name="operationalMetrics"
                description="Operational Metrics."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_boolean_discountPurchase_chart_totalRevenue"
                name="promotional_boolean_discountPurchase_chart_totalRevenue"
                description="Promotional Boolean Discount Purchase Chart Total Revenue"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_boolean_discountPurchase_chart_totalRevenueV2"
                name="promotional_boolean_discountPurchase_chart_totalRevenueV2"
                description="Promotional Boolean Discount Purchase Chart Total Revenue for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_boolean_discountPurchase_chart_totalPurchases"
                name="promotional_boolean_discountPurchase_chart_totalPurchases"
                description="Promotional Boolean Discount Purchase Chart Total Purchases"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_boolean_discountPurchase_chart_totalPurchasesV2"
                name="promotional_boolean_discountPurchase_chart_totalPurchasesV2"
                description="Promotional Boolean Discount Purchase Chart Total Purchases for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_coupon"
                name="promotional_coupon"
                description="Promotional Coupon"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_label_chart_totalDiscount"
                name="promotional_label_chart_totalDiscount"
                description="Promotional Label Chart Total Discount"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_label_chart_totalDiscountV2"
                name="promotional_label_chart_totalDiscountV2"
                description="Promotional Label Chart Total Discount for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_time_series"
                name="promotional_time_series"
                description="Promotional Time Series"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_time_seriesV2"
                name="promotional_time_seriesV2"
                description="Promotional Time Series for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_time_series_chart_totalDiscount_totalRevenue"
                name="promotional_time_series_chart_totalDiscount_totalRevenue"
                description="Promotional Time Series Chart Total Discount Total Revenue"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_time_series_chart_totalDiscount_totalRevenueV2"
                name="promotional_time_series_chart_totalDiscount_totalRevenueV2"
                description="Promotional Time Series Chart Total Discount Total Revenue for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_time_series_chart_yield"
                name="promotional_time_series_chart_yield"
                description="Promotional Time Series Chart Yield"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_time_series_chart_yieldV2"
                name="promotional_time_series_chart_yieldV2"
                description="Promotional Time Series Chart Yield for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_segment"
                name="promotional_segment"
                description="Promotional Segment"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_segmentV2"
                name="promotional_segmentV2"
                description="Promotional Segment for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_summary"
                name="promotional_summary"
                description="Promotional Summary"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:promotional_summaryV2"
                name="promotional_summaryV2"
                description="Promotional Summary for New Analytic Subsystem"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:segmentEngagement"
                name="segmentEngagement"
                description="Segment population per day for a given month and segment ID"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:totalEventsPerDay"
                name="totalEventsPerDay"
                description="Total event count per day for a given month and event name"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventDepthBySession"
                name="eventDepthBySession"
                description="Session depth by session chart"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventDepthConversions"
                name="eventDepthConversions"
                description="Session depth by conversions chart"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventDepthRevenue"
                name="eventDepthRevenue"
                description="Session depth by revenue chart"/>
                
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:sessionFlowSankey"
                name="sessionFlowSankey"
                description="Return the Session Flow payload"/>

        <p:artifact type="config" subtype="analyticQuery" 
                id="udc.system.core.CommonOOTB:bp_bizCG"
                name="bp_bizCG"
                description="Control Group metrics for specific experience"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_bizTG"
                name="bp_bizTG"
                description="Target Group metrics for specific experience"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_bizNSCG"
                name="bp_bizNSCG"
                description="Control Group metrics for all experiences across namespace"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:bp_bizNSTG"
                name="bp_bizNSTG"
                description="Target Group metrics for all experiences across namespace"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:activeExpMetricsPerDay"
                name="activeExpMetricsPerDay"
                description="Get active experiences metric per day."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:activeSessionExpMetricsPerDayAll"
                name="activeSessionExpMetricsPerDayAll"
                description="Get totalSentSessions and totalNotSentSession for active experiences metric per day."/>
                
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:xp_bizMetricPerDay"
                    name="xp_bizMetricPerDay"
                    description="Get biz metrics for experiences per action per day."/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:xp_bizMetricPerAction"
                    name="xp_bizMetricPerAction"
                    description="Get biz metrics for experiences per action."/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:xp_sessionMetricPerDay"
                    name="xp_sessionMetricPerDay"
                    description="Get session metrics for experiences per action per day."/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_ns"
                    name="CVR_ns"
                    description="Get conversion rate namespace."/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_nsV2"
                    name="CVR_nsV2"
                    description="Get conversion rate namespace."/>                    
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_exp"
                    name="CVR_exp"
                    description="Get conversion rate experience level for all traffic any action"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:xp_totalProductViewPerDay"
                name="xp_totalProductViewPerDay"
                description="Process totalProductView and totalSKUView to get the Product View Event chart trend in each experience."/>
                    
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_exp_action_tg"
                    name="CVR_exp_action_tg"
                    description="Get conversion rate experience level for all traffic any action"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_exp_action_cg"
                    name="CVR_exp_action_cg"
                    description="Get conversion rate experience level for all traffic any action"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_exp_action"
                    name="CVR_exp_action"
                    description="Get conversion rate experience level for all campaigns or c1 all traffic any action"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_exp_action_lift"
                    name="CVR_exp_action_lift"
                    description="Get conversion rate lift experience level for all campaigns or c1 all traffic any action"/>
        
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_action"
                    name="CVR_action"
                    description="Get conversion rate experience level for all traffic specific Action"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_cg"
                    name="CVR_cg"
                    description="Get conversion rate for control group traffic"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_tg"
                    name="CVR_tg"
                    description="Get conversion rate for target group traffic"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_lift"
                    name="CVR_lift"
                    description="Get conversion rate lift for an experience"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_liftAll"
                    name="CVR_liftAll"
                    description="Get conversion rate all an experience"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_ns"
                    name="RPS_ns"
                    description="Get total revenue namespace."/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_nsV2"
                    name="RPS_nsV2"
                    description="Get total revenue namespace."/>
                    
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_exp_action"
                    name="RPS_exp_action"
                    description="Get total revenue experience level for all campaigns or c1 all traffic any action"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_action"
                    name="RPS_action"
                    description="Get total revenue experience level for all traffic specific Action"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_cg"
                    name="RPS_cg"
                    description="Get total revenue for control group traffic"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_tg"
                    name="RPS_tg"
                    description="Get total revenue for target group traffic"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_lift"
                    name="RPS_lift"
                    description="Get total revenue for an experience"/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_liftAll"
                    name="RPS_liftAll"
                    description="Get total revenue for an experience"/>
                    
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:AOV_ns_avg"
                    name="AOV_ns_avg"
                    description="Get AOV average namespace."/>                   
                    
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_action_all"
                    name="CVR_action_all"
                    description="Get conversion rate for all actions including CG traffic"/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_action_all_day"
                    name="CVR_action_all_day"
                    description="Get conversion rate for all actions and dates including CG traffic"/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_action_all"
                    name="RPS_action_all"
                    description="Get total revenue for for all actions including CG traffic"/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_action_all_day"
                    name="RPS_action_all_day"
                    description="Get conversion rate for all actions and dates including CG traffic"/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:BP_liftAll"
                    name="BP_liftAll"
                	description="CVR, RPV, AOV lift all and imcremental revenue all - Experience specific Business Performance Overview"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:BP_spark_liftAll"
                name="BP_spark_liftAll"
                description="Spark chart CVR, RPV, AOV lift all and imcremental revenue all - Experience specific Business Performance Overview"/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:ActionAll_lift"
                    name="ActionAll_lift"
                	description="Query IncrementalRevenue, CVR, CVR lift, RPS and RPS lift for all action including CG"/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_RPS_tg"
                    name="CVR_RPS_tg"
                	description="Query CVR and RPS for TG"/>

       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:incrementalRevenue"
                    name="incrementalRevenue"
                    description="Get incrementalRevenue for all action of experience "/>
       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:incrementalRevenue_All"
                    name="incrementalRevenue_All"
                    description="Get incrementalRevenue for experience"/>
                    
       	<p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:CVR_exp_date_tg"
                	name="CVR_exp_date_tg"
                	description="Target group conversion rate for a list of journeys metric per day."/>
       	<p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:CVR_exp_date_all"
                	name="CVR_exp_date_all"
                	description="Target/CG group conversion rate for a list of journeys metric per day."/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:IR_exp_date_tg"
                	name="IR_exp_date_tg"
                	description="Target group incremental revenue for a list of journeys metric per day."/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:CVR_RPS_AOV_exp_tg"
                	name="CVR_RPS_AOV_exp_tg"
                	description="Target group CVR, RPS and AOV for all journeys."/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:BP_exp_liftAll"
                    name="BP_exp_liftAll"
                	description="CVR, RPV, AOV lift all and imcremental revenue all for all experiences"/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:BP_exp_liftAllV2"
                    name="BP_exp_liftAllV2"
                	description="CVR, RPV, AOV lift all and imcremental revenue all for all experiences"/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:TE_Channel_Filter"
                    name="TE_Channel_Filter"
                	description="Query totalRevenue, totalSentSessions, totalConvertedSession and aov for a TE breakdown/filter by channel"/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:TE_Identity_Filter"
                    name="TE_Identity_Filter"
                	description="Query totalRevenue, totalSentSessions, totalConvertedSession and aov for a TE breakdown/filter by identity"/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:TE_Channel_ActionAll_lift"
                    name="TE_Channel_ActionAll_lift"
                	description="Query IncrementalRevenue, CVR, CVR lift, RPS and RPS lift of all action including CG for a TE breakdown/filter by channel"/>
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:TE_Identity_ActionAll_lift"
                    name="TE_Identity_ActionAll_lift"
                	description="Query IncrementalRevenue, CVR, CVR lift, RPS and RPS lift of all action including CG for a TE breakdown/filter by identity"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:homeSessionExplorerMetrics"
                name="homeSessionExplorerMetrics"
                description="Session Explorer Metrics for Home Dash doughnut charts"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:homeSessionExplorerMetricsV2"
                name="homeSessionExplorerMetricsV2"
                description="Session Explorer Metrics for Home Dash doughnut charts"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:sessionScoreBins"
                name="sessionScoreBins"
                description="Session scores binned together"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:weekly_metric_report"
                name="weekly_metric_report"
                description="Weekly metric report"/>
                
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:weekly_metric_reportV2"
                name="weekly_metric_reportV2"
                description="Weekly metric report"/>
                
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:ConversionRateLiftAll"
                    name="ConversionRateLiftAll"
                	description="Conversion rate lift all by experiences"/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:compareSameMetricPerDayV2"
                    name="compareSameMetricPerDayV2"
                	description="Compare the same metric per day."/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_nsV2"
                    name="CVR_nsV2"
                    description="Get conversion rate namespace."/>

				<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_nsV2"
                    name="RPS_nsV2"
                    description="Get total revenue namespace."/>
                    
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_exp_actionV2"
                    name="CVR_exp_actionV2"
                    description="Get conversion rate experience level for all campaigns or c1 all traffic any action"/>
                    
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_exp_actionV2"
                    name="RPS_exp_actionV2"
                    description="Get total revenue experience level for all campaigns or c1 all traffic any action"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventDepthBySessionV2"
                name="eventDepthBySessionV2"
                description="Session depth by session chart"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventDepthConversionsV2"
                name="eventDepthConversionsV2"
                description="Session depth by conversions chart"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:eventDepthRevenueV2"
                name="eventDepthRevenueV2"
                description="Session depth by revenue chart"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:sessionScoreBinsV2"
                name="sessionScoreBinsV2"
                description="Session scores binned together"/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:compareSameMetricPerDayWithFilterV2"
                name="compareSameMetricPerDayWithFilterV2"
                description="Compare the same metric per day with filter."/>
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:segmentEngagementV2"
                name="segmentEngagementV2"
                description="Segment population per day for a given month and segment ID"/>

				<p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:sessionFlowSankeyV2"
                name="sessionFlowSankeyV2"
                description="Return the Session Flow payload"/>
                
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:CVR_RPS_AOV_exp_tgV2"
                	name="CVR_RPS_AOV_exp_tgV2"
                	description="Target group CVR, RPS and AOV for all journeys."/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:CVR_exp_date_allV2"
                	name="CVR_exp_date_allV2"
                	description="Target/CG group conversion rate for a list of journeys metric per day."/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                	id="udc.system.core.CommonOOTB:IR_exp_date_tgV2"
                	name="IR_exp_date_tgV2"
                	description="Target group incremental revenue for a list of journeys metric per day."/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:activeSessionExpMetricsPerDayAllV2"
                name="activeSessionExpMetricsPerDayAllV2"
                description="Get totalSentSessions and totalNotSentSession for active experiences metric per day."/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:TE_Channel_ActionAll_liftV2"
                name="TE_Channel_ActionAll_liftV2"
                description="Query IncrementalRevenue, CVR, CVR lift, RPS and RPS lift of all action including CG for a TE breakdown/filter by channel"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:TE_Channel_FilterV2"
                name="TE_Channel_FilterV2"
                description="Query totalRevenue, totalSentSessions, totalConvertedSession and aov for a TE breakdown/filter by channel"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:TE_Identity_ActionAll_liftV2"
                name="TE_Identity_ActionAll_liftV2"
                description="Query IncrementalRevenue, CVR, CVR lift, RPS and RPS lift of all action including CG for a TE breakdown/filter by identity"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:TE_Identity_FilterV2"
                name="TE_Identity_FilterV2"
                description="Query totalRevenue, totalSentSessions, totalConvertedSession and aov for a TE breakdown/filter by identity"/>
                
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:ActionAll_liftV2"
                    name="ActionAll_liftV2"
                	description="Query IncrementalRevenue, CVR, CVR lift, RPS and RPS lift for all action including CG"/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_RPS_tgV2"
                    name="CVR_RPS_tgV2"
                	description="Query CVR and RPS for TG"/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:BP_liftAllV2"
                    name="BP_liftAllV2"
                	description="CVR, RPV, AOV lift all and imcremental revenue all - Experience specific Business Performance Overview"/>
                	
        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:BP_spark_liftAllV2"
                name="BP_spark_liftAllV2"
                description="Spark chart CVR, RPV, AOV lift all and imcremental revenue all - Experience specific Business Performance Overview"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:xp_bizMetricPerDayV2"
                    name="xp_bizMetricPerDayV2"
                    description="Get biz metrics for experiences per action per day."/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:xp_sessionMetricPerDayV2"
                    name="xp_sessionMetricPerDayV2"
                    description="Get session metrics for experiences per action per day."/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:CVR_action_all_dayV2"
                    name="CVR_action_all_dayV2"
                    description="Get conversion rate for all actions and dates including CG traffic"/>

       	<p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:RPS_action_all_dayV2"
                    name="RPS_action_all_dayV2"
                    description="Get conversion rate for all actions and dates including CG traffic"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:xp_totalProductViewPerDayV2"
                name="xp_totalProductViewPerDayV2"
                description="Process totalProductView and totalSKUView to get the Product View Event chart trend in each experience."/>
        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:significance_status_total_sessionsV2"
                    name="significance_status_total_sessionsV2"
                    description="Get total session with purchase an No purchase for statistical significance status"/>

        <p:artifact type="config" subtype="analyticQuery"
                id="udc.system.core.CommonOOTB:commandCenter"
                name="commandCenter"
                description="Process command center metrics for the command center page"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Exp_All_Axn"
                    name="Aggr_Exp_All_Axn"
                    description="Experience Detail Metrics"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Exp_All_Axn_VisitIden"
                    name="Aggr_Exp_All_Axn_VisitIden"
                    description="Experience Detail Metrics - Visitor Identity Breakdown"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Exp_All_Axn_Channel"
                    name="Aggr_Exp_All_Axn_Channel"
                    description="Experience Detail Metrics - Channel Breakdown"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Exp_All_Axn_Trends"
                    name="Aggr_Exp_All_Axn_Trends"
                    description="Experience Detail Metrics - Trends"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Experience_Overview"
                    name="Aggr_Experience_Overview"
                    description="Experience Overview Metrics"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Experience_Listing_Overview"
                    name="Aggr_Experience_Listing_Overview"
                    description="Experience Listing Overview Metrics"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Pred_ScoreBins"
                    name="Aggr_Pred_ScoreBins"
                    description="Predictions Chart"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Pred_ScoreSessions"
                    name="Aggr_Pred_ScoreSessions"
                    description="Predictions By Segment"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Session_Path"
                    name="Aggr_Session_Path"
                    description="Session Detail Metrics - Session Path"/>

        <p:artifact type="config" subtype="analyticQuery"
                    id="udc.system.core.CommonOOTB:Aggr_Session_Trends"
                    name="Aggr_Session_Trends"
                    description="Session Detail Metrics - Session trends"/>

        <!-- 
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:CreateTicket" />        
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:SalesforceCases" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:SalesforceLeads" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:AgentAlert" />
        <p:artifact type="config" subtype="action"
                id="udc.system.core.CommonOOTB:AgentEmail" /-->

</p:application>
                