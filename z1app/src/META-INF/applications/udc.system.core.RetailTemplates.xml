<?xml version="1.0" encoding="UTF-8"?>
<p:application id="udc.system.core.RetailTemplates"
        version="" listener="z1.SolutionInstallListener" xmlns:p="http://udichi/core/application/def"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://udichi/core/application/def ../../../../platform/schema/application.xsd ">
        
		<p:info version="3.0"
         overview="This is a overview but can't have any HTML tags in it"
         versionDetail="This is a new feature"
         images="res/alert_iphonescreen.png, res/alert_iphonescreen2.png, res/csb_1.png, res/csb_2.png, res/csb_3.png"/>
		
        <p:artifact type="config" subtype="segment"
                id="udc.system.core.RetailTemplates:FrequentUser" 
                name="Frequent Users"
                description="Users who are active at least 2 times in last 7 days."/>

		 <p:artifact type="config" subtype="campaign"
                id="udc.system.core.RetailTemplates:SampleCustomActionInteraction" 
                name="Sample Interaction"
                description="Sample triggered interaction with custom action."/>
          
		  <p:artifact type="config" subtype="journey"
                id="udc.system.core.RetailTemplates:OOTB Test Journey" 
                name="OOTB Test Journey"
                description="OOTB Test Journey"/>
		  		  
		  <p:artifact type="config" subtype="livelink"
                id="udc.system.core.RetailTemplates:Test Link" 
                name="My Test Link"
                description="My Test Link"/>
		  
          <p:artifact type="config" subtype="livelink"
                id="udc.system.core.RetailTemplates:Test Link 2" 
                name="My Test Link Two"
                description="My Test Link Two"/>
          
		  <p:artifact type="config" subtype="attribute"
                id="udc.system.core.RetailTemplates:Test Context Attribute" 
                name="Test Attribute"
                description="Test Attribute"/>
		  
		  <p:artifact type="config" subtype="event"
                id="udc.system.core.RetailTemplates:Test Event" 
                name="Show Banner"
                description="Show Banner"/>
		  
		  <p:artifact type="config" subtype="event"
                id="udc.system.core.RetailTemplates:Test Event2" 
                name="ABC Event"
                description="ABC Test Event"/>
		  
		  <p:artifact type="config" subtype="signal"
                id="udc.system.core.RetailTemplates:Test Signal" 
                name="Signal1"
                description="Signal1"/>
		  
		  <p:artifact type="config" subtype="eventprocessing"
                id="udc.system.core.RetailTemplates:Test Event Post Processing" 
                name="Show Banner Processing"
                description="Show Banner Processing"/>
                
          <p:artifact type="config" subtype="customaction"
                id="udc.system.core.RetailTemplates:TestAction" 
                name="Test Custom Action"
                description="A test custom action to print data to the output"/>
                
          <p:artifact type="config" subtype="contentpn"
                id="udc.system.core.RetailTemplates:TestPN" 
                name="Test Push Notification"
                description="A test push notification action"/>      
                
          <p:artifact type="config" subtype="contentbn"
                id="udc.system.core.RetailTemplates:TestBN" 
                name="Test Banner Notification"
                description="A test banner notification action"/>     
                
          <p:artifact type="config" subtype="contentfs"
                id="udc.system.core.RetailTemplates:TestFS" 
                name="Test FullScreen Notification"
                description="A test fullscreen notification action"/>
                
          <p:artifact type="config" subtype="contentalert"
                id="udc.system.core.RetailTemplates:TestAlert" 
                name="Test Alert Notification"
                description="A test alert notification action"/>  
                
          <p:artifact type="config" subtype="contentappbox"
                id="udc.system.core.RetailTemplates:TestAppBox" 
                name="Test AppBox Notification"
                description="A test appbox notification action"/>                      
                
        <p:artifact type="dashboard" 
            name="Page Views" 
            description="Page views over time"
            group="Page Views"
            image="res/campaign_overview_decor.png"
            id="udc.system.core.RetailTemplate:PageViewDash" />
                

        <!--p:artifact type="config" subtype="signal"
                id="udc.system.core.CommonOOTB:AppStart" /-->
                

</p:application>
                