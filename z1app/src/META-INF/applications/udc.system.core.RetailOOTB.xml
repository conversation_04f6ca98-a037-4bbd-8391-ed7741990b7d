<?xml version="1.0" encoding="UTF-8"?>
<p:application id="udc.system.core.RetailOOTB"
               version="" listener="z1.AppInstallListener" xmlns:p="http://udichi/core/application/def"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:schemaLocation="http://udichi/core/application/def ../../../../platform/schema/application.xsd ">

    <!--  success metrics -->
    <p:artifact type="config" subtype="successmetric"
                id="udc.system.core.RetailOOTB:z1_rpv"
                name="Revenue per Visitor"
                description="(Total revenue / unique visitors)"/>
    <p:artifact type="config" subtype="successmetric"
                id="udc.system.core.RetailOOTB:z1_revenue"
                name="Total Revenue"
                description="Sum of order amount"/>
    <p:artifact type="config" subtype="successmetric"
                id="udc.system.core.RetailOOTB:z1_purchases"
                name="Conversions"
                description="Number of orders"/>
    <p:artifact type="config" subtype="successmetric"
                id="udc.system.core.RetailOOTB:z1_conversionrate"
                name="Conversion Rate"
                description="(Number of orders / unique visitors)"/>
    <p:artifact type="config" subtype="successmetric"
                id="udc.system.core.RetailOOTB:z1_aov"
                name="Average Order Value"
                description="(Total revenue / number of orders)"/>

    <!-- ML EPP OOTB Segments -->
    <p:artifact type="config" subtype="segment"
                id="udc.system.core.RetailOOTB:OTF"
                name="OTF"
                description="Customers who are on-the-fence in an active session."/>
    <p:artifact type="config" subtype="segment"
                id="udc.system.core.RetailOOTB:HLB"
                name="HLB"
                description="Customers who are high-likely to buy in an active session."/>
    <p:artifact type="config" subtype="segment"
                id="udc.system.core.RetailOOTB:ULB"
                name="ULB"
                description="Customers who are unlikely to buy in an active session."/>

    <!--  ML Events -->
    <p:artifact type="config" subtype="event"
                id="udc.system.core.RetailOOTB:add_to_cart_prediction"
                name="add_to_cart_prediction"
                description="Add to Cart Prediction Event"/>
    <p:artifact type="config" subtype="event"
                id="udc.system.core.RetailOOTB:checkout_prediction"
                name="checkout_prediction"
                description="Checkout Prediction Event"/>
    <p:artifact type="config" subtype="event"
                id="udc.system.core.RetailOOTB:early_purchase_prediction"
                name="early_purchase_prediction"
                description="Early Purchase Prediction Event"/>
    <p:artifact type="config" subtype="event"
                id="udc.system.core.RetailOOTB:price_sensitivity"
                name="price_sensitivity"
                description="Price Sensitivity Event"/>

    <!--  ML Entities -->
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:EPPV2ProductCatalog"
                name="EPPV2ProductCatalog"
                description="ML Product Index Catalog"/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:EPPV2SequencePattern"
                name="EPPV2SequencePattern"
                description="ML Sequence Pattern Catalog"/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:EPPV2CLK4SequencePattern"
                name="EPPV2CLK4SequencePattern"
                description="ML 4 Click Sequence Pattern Catalog"/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:MLFeatureDebugLogger"
                name="MLFeatureDebugLogger"
                description="ML Feature Debug Logger"/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:PSV1PriceCategoryIndex"
                name="PSV1PriceCategoryIndex"
                description="ML Price Category Index"/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:PSV1PriceCategoryIndexWithCategorySuffix"
                name="PSV1PriceCategoryIndexWithCategorySuffix"
                description="ML Price Category Index with Category Suffix"/>
    <!--
    IO Entities
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:IOFeatureLogger"
                name="IOFeatureLogger"
                description="IO Feature Logger"/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:Offers"
                name="Offers"
                description="Offers Information"/>

    IO Feature Entities
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr001a"
                name="otr001a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr001b"
                name="otr001b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr001c"
                name="otr001c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr002a"
                name="otr002a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr002b"
                name="otr002b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr002c"
                name="otr002c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr003a"
                name="otr003a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr003b"
                name="otr003b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr003c"
                name="otr003c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr004a"
                name="otr004a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr004b"
                name="otr004b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr004c"
                name="otr004c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr005a"
                name="otr005a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr005b"
                name="otr005b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr005c"
                name="otr005c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr006a"
                name="otr006a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr006b"
                name="otr006b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr006c"
                name="otr006c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr007a"
                name="otr007a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr007b"
                name="otr007b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:otr007c"
                name="otr007c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer001a"
                name="oer001a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer001b"
                name="oer001b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer001c"
                name="oer001c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:ocr001a"
                name="ocr001a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:ocr001b"
                name="ocr001b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:ocr001c"
                name="ocr001c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer002a"
                name="oer002a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer002b"
                name="oer002b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer002c"
                name="oer002c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer003a"
                name="oer003a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer003b"
                name="oer003b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer003c"
                name="oer003c"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer004a"
                name="oer004a"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer004b"
                name="oer004b"
                description=""/>
    <p:artifact type="config" subtype="entity"
                id="udc.system.core.RetailOOTB:oer004c"
                name="oer004c"
                description=""/>
    -->

</p:application>
                