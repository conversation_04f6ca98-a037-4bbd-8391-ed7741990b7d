{"name": "<PERSON><PERSON>", "dimension": "mobile/push", "group": "In-App Actions", "icon": "msg-alert", "description": "In-app alert message", "ref": "udc.system.core.ZineOne Processing:PushNotification", "params": [{"name": "title", "display": "Title", "description": "Message Title"}, {"name": "msg", "display": "Message", "description": "Write a message to show to the mobile user."}, {"name": "sound", "display": "Sound", "description": ""}, {"name": "actions", "display": "Actions", "description": ""}, {"name": "deepLinkIOS", "display": "iOS Deep Link", "description": ""}, {"name": "deepLinkAndroid", "display": "Android Deep Link", "description": ""}, {"name": "z1_target", "display": "System variable", "description": ""}, {"name": "z1_ref", "display": "System variable", "description": ""}, {"name": "z1_tags", "display": "System variable", "description": ""}, {"name": "z1_isAB", "display": "System variable", "description": ""}]}