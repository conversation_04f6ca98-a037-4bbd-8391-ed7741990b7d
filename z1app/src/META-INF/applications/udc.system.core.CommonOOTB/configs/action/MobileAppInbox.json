{"name": "AppBox", "dimension": "mobile/appbox", "group": "In-App Actions", "icon": "msg-in-box", "description": "Push message to app inbox", "ref": "udc.system.core.ZineOne Processing:MobileAppInbox", "params": [{"name": "title", "display": "Message Title", "description": "Provide Message Title"}, {"name": "message", "display": "Message", "description": "Provide Message"}, {"name": "z1_target", "display": "System variable", "description": ""}, {"name": "z1_ref", "display": "System variable", "description": ""}, {"name": "z1_tags", "display": "System variable", "description": ""}, {"name": "z1_isAB", "display": "System variable", "description": ""}, {"name": "chat", "display": "Cha<PERSON>", "description": "Customer can start conversation for this message."}]}