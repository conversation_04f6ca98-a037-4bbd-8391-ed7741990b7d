{"name": "<PERSON><PERSON>", "dimension": "mobile/message", "group": "In-App Actions", "icon": "msg-alert", "description": "In-app alert message", "ref": "udc.system.core.ZineOne Processing:MobileAppAlertAction", "params": [{"name": "alertTitle", "display": "Title", "description": "Message Title"}, {"name": "alertMsg", "display": "Message", "description": "Write a message to show to the mobile user."}, {"name": "z1_target", "display": "System variable", "description": ""}, {"name": "z1_ref", "display": "System variable", "description": ""}, {"name": "z1_tags", "display": "System variable", "description": ""}, {"name": "z1_isAB", "display": "System variable", "description": ""}]}