{"name": "Banner", "dimension": "mobile/message", "group": "In-App Actions", "icon": "msg-banner", "description": "In-app banner message", "ref": "udc.system.core.ZineOne Processing:MobileAppBannerAction", "params": [{"name": "bannerMsg", "display": "Message", "description": "Write a message to show to the mobile user."}, {"name": "position", "display": "Position", "description": "Position of banner."}, {"name": "z1_target", "display": "System variable", "description": ""}, {"name": "z1_ref", "display": "System variable", "description": ""}, {"name": "z1_tags", "display": "System variable", "description": ""}, {"name": "z1_isAB", "display": "System variable", "description": ""}]}