{"payload": [{"metric": [{"source": "totalSentSessions", "name": "totalSentSessions"}], "datasetName": "totalSentSessionsCurrWeek", "timeUnit": "day", "fromDate": "${startDate}", "toDate": "${endDate}", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyType", "op": "=", "val": "campaign"}]}, {"metric": [{"source": "totalTargetedSessions", "name": "totalTargetedSessions"}], "datasetName": "totalTargetedSessionsCurrWeek", "timeUnit": "day", "fromDate": "${startDate}", "toDate": "${endDate}", "groupBy": ["date"]}, {"metric": [{"source": "totalTargetedSessions", "name": "totalTargetedSessions"}], "datasetName": "totalTargetedSessionsPrevWeek", "timeUnit": "day", "fromDate": "${prevStartDate}", "toDate": "${prevEndDate}", "groupBy": ["date"]}, {"metric": [{"source": "totalSessions", "name": "totalSessions"}], "datasetName": "totalSessions", "fromDate": "${startDate}", "toDate": "${endDate}", "timeUnit": "day"}]}