{"payload": [{"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenueTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date", "actionName"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1TG,z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalNotSentSessionCGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date", "actionName"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}