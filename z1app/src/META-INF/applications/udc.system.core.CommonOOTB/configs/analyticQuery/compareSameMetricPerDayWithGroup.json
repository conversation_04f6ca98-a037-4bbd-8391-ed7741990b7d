{"payload": [{"subsystem": "${ss}", "metric": "${m}", "datasetName": "${ds1}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}", "aggregateAs": "SUM", "groupBy": ["date", "${group}"]}, {"subsystem": "${ss}", "metric": "${m}", "datasetName": "${ds2}", "fromDate": "${${f} - ${period}d}", "toDate": "${${t} - ${period}d}", "timeUnit": "${tu}", "aggregateAs": "SUM", "groupBy": ["date", "${group}"]}]}