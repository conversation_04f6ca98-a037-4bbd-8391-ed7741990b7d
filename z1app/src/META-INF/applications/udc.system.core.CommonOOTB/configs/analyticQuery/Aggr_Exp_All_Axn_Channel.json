{"payload": [{"datasetName": "exp_all_axn_channel", "dataSource": "data_by_experience", "metric": [{"source": "totalSessions"}, {"source": "totalConversions"}, {"source": "totalRevenue"}, {"source": "conversionRate"}, {"source": "averageOrderValue"}, {"source": "revenuePerVisitor"}, {"source": "incrementalRevenue"}, {"source": "conversionRateLift"}, {"source": "averageOrderValueLift"}, {"source": "revenuePerVisitorLift"}, {"source": "averageDiscountRate"}, {"source": "totalDiscounts"}, {"source": "budgetUsed"}, {"source": "actionsCompleted"}], "filter": [{"attr": "journeyid", "op": "==", "val": "${journeyId}"}], "groupBy": ["actionname", "channel"], "fromDate": "${f}", "toDate": "${t}"}]}