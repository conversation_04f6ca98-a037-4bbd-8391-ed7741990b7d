{"payload": [{"metric": [{"source": "averageOrderValueLift", "name": "aovLiftAll"}, {"source": "averageOrderValueAdjustedLift", "name": "aovAdjustedLiftAll"}, {"source": "conversionRateLift", "name": "conversionRateLiftAll"}, {"source": "incrementalRevenueAdjusted", "name": "incrementalRevenueAdjustedAll"}, {"source": "incrementalRevenue", "name": "incrementalRevenueAll"}, {"source": "revenuePerSessionAdjustedLift", "name": "rpvAdjustedLiftAll"}, {"source": "revenuePerSessionLift", "name": "rpvLiftAll"}], "datasetName": "ds1", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["journeyId"], "filter": [{"attr": "journeyType", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}]}