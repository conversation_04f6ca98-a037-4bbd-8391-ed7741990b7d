{"payload": [{"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionPerExperience", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenueTGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "revenueAdjusted", "datasetName": "revenueAdjustedTGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "sessionCartValue", "datasetName": "sessionCartValueTGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "sessionAdd<PERSON>oCart", "datasetName": "sessionAddToCartTGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}]}