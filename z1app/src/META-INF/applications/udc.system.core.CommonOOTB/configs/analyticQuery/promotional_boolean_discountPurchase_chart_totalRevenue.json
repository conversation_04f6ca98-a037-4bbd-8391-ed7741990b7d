{"payload": [{"subsystem": "biz", "metric": "promotionalTotalRevenueNS", "datasetName": "promotionalTotalRevenueNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalTotalRevenueWithDiscountNS", "datasetName": "promotionalTotalRevenueWithDiscountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}]}