{"payload": [{"metric": [{"source": "${m}", "name": "${m}"}], "datasetName": "${ds}", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "notin", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "${mcg}", "name": "${mcg}"}], "datasetName": "${dscg}", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}