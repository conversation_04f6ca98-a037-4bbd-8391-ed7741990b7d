{"payload": [{"subsystem": "biz", "metric": "aovLiftAll", "datasetName": "aovLiftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionPerActionExceptCGandTG", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenuePerActionExceptCGandTG", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalNotSentSessionCGAction", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}