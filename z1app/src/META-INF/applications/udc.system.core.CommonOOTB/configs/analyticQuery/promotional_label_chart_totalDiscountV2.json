{"payload": [{"metric": [{"source": "totalDiscount", "name": "promotionalTotalDiscountNS"}], "datasetName": "${ds1}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}", "groupBy": ["epp<PERSON>abel"], "filter": [{"attr": "epp<PERSON>abel", "op": "!=", "val": "z1EppLabelAll"}, {"attr": "epp<PERSON>abel", "op": "!=", "val": "z1EppLabelScored"}, {"attr": "epp<PERSON>abel", "op": "!=", "val": "nl"}]}]}