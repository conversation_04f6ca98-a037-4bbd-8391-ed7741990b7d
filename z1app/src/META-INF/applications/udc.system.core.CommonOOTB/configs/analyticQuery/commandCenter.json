{"payload": [{"metric": [{"source": "totalSessions", "name": "totalSentSessions"}, {"source": "totalRevenue", "name": "totalRevenue"}], "datasetName": "commandCenterHistoricDataset", "timeUnit": "day", "toDate": "${f}", "fromDate": "20200101", "filter": [{"attr": "groupAllocation", "op": "=", "val": "z1TG"}]}, {"metric": [{"source": "totalSessions", "name": "totalSentSessions"}, {"source": "totalRevenue", "name": "totalRevenue"}], "datasetName": "commandCenterFromDataset", "timeUnit": "day", "toDate": "${f}", "fromDate": "${f}", "filter": [{"attr": "groupAllocation", "op": "=", "val": "z1TG"}]}, {"metric": [{"source": "totalRevenue", "name": "totalRevenue"}, {"source": "totalSessions", "name": "totalSentSessions"}, {"source": "totalOrderBeforeDiscount", "name": "totalOrderBeforeDiscount"}, {"source": "conversionRate", "name": "conversionRate"}, {"source": "totalDiscount", "name": "totalDiscount"}], "datasetName": "commandCenterPeriodDataset", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["groupAllocation"]}, {"metric": [{"source": "totalSessions", "name": "totalSentSessions"}, {"source": "totalRevenue", "name": "totalRevenue"}], "datasetName": "commandCenterSparkDataset", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["groupAllocation", "date"], "filter": [{"attr": "groupAllocation", "op": "=", "val": "z1TG"}]}, {"metric": [{"source": "totalSessions", "name": "totalSessions"}], "datasetName": "historicAudiences", "timeUnit": "day", "fromDate": "20200101", "toDate": "${t}", "groupBy": ["segmentId", "journeyId"]}, {"metric": [{"source": "totalSessions", "name": "totalSessions"}], "datasetName": "currentAudiences", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["segmentId", "journeyId"]}]}