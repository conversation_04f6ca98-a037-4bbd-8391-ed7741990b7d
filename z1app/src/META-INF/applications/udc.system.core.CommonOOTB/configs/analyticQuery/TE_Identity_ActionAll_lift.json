{"payload": [{"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionPerActionExceptTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenuePerActionExceptTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "revenueAdjusted", "datasetName": "totalRevenueAdjustedPerActionExceptTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "sessionCartValue", "datasetName": "sessionCartValuePerActionExceptTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "sessionAdd<PERSON>oCart", "datasetName": "sessionAddToCartPerActionExceptTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionPerAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalSentSessionCGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}