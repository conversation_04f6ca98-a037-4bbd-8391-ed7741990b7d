{"payload": [{"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenuePerActionExceptCGandTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalNotSentSessionCGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}