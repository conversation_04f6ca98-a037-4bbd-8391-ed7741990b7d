{"payload": [{"metric": [{"source": "conversionRate", "name": "conversionRate"}], "datasetName": "conversionRateTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"metric": [{"source": "conversionRate", "name": "conversionRate"}], "datasetName": "conversionRateCG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}