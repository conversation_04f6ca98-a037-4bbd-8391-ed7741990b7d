{"payload": [{"datasetName": "exp_all_axn_trends", "dataSource": "data_by_experience", "metric": [{"source": "totalSessions"}, {"source": "totalConversions"}, {"source": "totalRevenue"}, {"source": "conversionRate"}, {"source": "averageOrderValue"}, {"source": "revenuePerVisitor"}, {"source": "totalDiscounts"}], "filter": [{"attr": "journeyid", "op": "==", "val": "${journeyId}"}, {"attr": "actionname", "op": "notIn", "val": "z1Failed,Z1Blocked,z1Target"}], "groupBy": ["actionname", "date"], "fromDate": "${f}", "toDate": "${t}"}]}