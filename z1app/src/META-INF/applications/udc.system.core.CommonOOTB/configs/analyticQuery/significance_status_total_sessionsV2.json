{"payload": [{"metric": [{"source": "totalConvertedSession", "name": "totalConvertedSession"}, {"source": "totalSentSessions", "name": "totalSentSessions"}, {"source": "totalNotSentSessions", "name": "totalNotSentSessions"}], "datasetName": "totalSessions", "timeUnit": "month", "groupBy": ["actionName", "journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"metric": [{"source": "totalConvertedSession", "name": "totalConvertedSession"}, {"source": "totalSentSessions", "name": "totalSentSessions"}, {"source": "totalNotSentSessions", "name": "totalNotSentSessions"}], "datasetName": "totalSessionsWithChannel", "timeUnit": "month", "groupBy": ["actionName", "channel", "journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"metric": [{"source": "totalConvertedSession", "name": "totalConvertedSession"}, {"source": "totalSentSessions", "name": "totalSentSessions"}, {"source": "totalNotSentSessions", "name": "totalNotSentSessions"}], "datasetName": "totalSessionsWithIdentity", "timeUnit": "month", "groupBy": ["actionName", "visitIden", "journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}]}