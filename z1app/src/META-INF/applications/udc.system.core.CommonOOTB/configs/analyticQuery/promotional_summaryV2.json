{"payload": [{"metric": [{"source": "totalDiscount", "name": "promotionalTotalDiscountNS"}, {"source": "totalRevenue", "name": "promotionalTotalRevenueNS"}, {"source": "yield", "name": "promotionalYieldNS"}, {"source": "discountRate", "name": "promotionalAvgDiscountRateNS"}], "datasetName": "${ds1}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}", "groupBy": ["epp<PERSON>abel"], "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}]}