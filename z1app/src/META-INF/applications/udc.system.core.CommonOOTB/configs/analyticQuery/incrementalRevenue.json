{"payload": [{"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenuePerActionExceptTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionPerAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalNotSentSessionCGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}