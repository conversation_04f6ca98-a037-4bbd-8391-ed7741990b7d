{"payload": [{"subsystem": "session", "metric": "totalFlowRevenue", "datasetName": "totalFlowRevenue", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM"}, {"subsystem": "session", "metric": "totalFlowSessions", "datasetName": "totalFlowSessions", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM"}, {"subsystem": "session", "metric": "totalFlowConversions", "datasetName": "totalFlowConversions", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM"}, {"subsystem": "session", "metric": "totalFlowOrderCount", "datasetName": "totalFlowOrderCount", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM"}]}