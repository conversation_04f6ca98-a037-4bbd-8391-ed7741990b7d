{"payload": [{"subsystem": "biz", "metric": "promotionalTotalDiscountNS", "datasetName": "promotionalTotalDiscountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalTotalRevenueNS", "datasetName": "promotionalTotalRevenueNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalTotalSessionsNS", "datasetName": "promotionalTotalSessionsNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalYieldNS", "datasetName": "promotionalYieldNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalAvgDiscountRateNS", "datasetName": "promotionalAvgDiscountRateNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalCVRNS", "datasetName": "promotionalCVRNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalSessionConversionCountNS", "datasetName": "promotionalSessionConversionCountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}, {"subsystem": "biz", "metric": "promotionalTotalPurchasesNS", "datasetName": "promotionalTotalPurchasesNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM"}]}