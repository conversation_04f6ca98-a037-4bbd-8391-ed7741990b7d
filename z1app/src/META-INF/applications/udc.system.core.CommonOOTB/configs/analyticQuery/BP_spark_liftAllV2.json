{"payload": [{"metric": [{"source": "averageOrderValueLift", "name": "aovLiftAll"}], "datasetName": "aovLiftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "averageOrderValueAdjustedLift", "name": "aovAdjustedLiftAll"}], "datasetName": "aovAdjustedLiftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "conversionRateLift", "name": "conversionRateLiftAll"}], "datasetName": "CVR_liftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "revenuePerSessionLift", "name": "rpvLiftAll"}], "datasetName": "RPS_liftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "incrementalRevenue", "name": "incrementalRevenueAll"}], "datasetName": "incrementalRevenue_All", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "revenuePerSessionAdjustedLift", "name": "rpvAdjustedLiftAll"}], "datasetName": "RPSAdjusted_liftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "incrementalRevenueAdjusted", "name": "incrementalRevenueAdjustedAll"}], "datasetName": "incrementalRevenueAdjusted_All", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "in", "val": "z1Control,z1TG"}]}]}