{"payload": [{"subsystem": "session", "metric": "totalDepthConversions", "datasetName": "totalDepthConversions", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["depth", "${breakdown}"]}, {"subsystem": "session", "metric": "totalDepthSessions", "datasetName": "totalDepthConversions", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["depth", "${breakdown}"]}]}