{"payload": [{"metric": [{"source": "totalOrderCountWithDiscount", "name": "totalOrderCountWithDiscount"}, {"source": "totalDiscount", "name": "totalDiscount"}, {"source": "totalDiscount", "name": "totalDiscountPercent", "returnPercentageOf": true}, {"source": "totalRevenueWithDiscount", "name": "totalRevenueWithDiscount"}, {"source": "yield", "name": "yield"}, {"source": "discountRate", "name": "discountRate"}], "datasetName": "${ds1}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}", "groupBy": ["couponBucket"]}, {"metric": [{"source": "totalDiscount", "name": "totalDiscount"}], "datasetName": "${ds2}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}", "groupBy": ["couponBucket", "epp<PERSON>abel"]}, {"metric": [{"source": "totalRevenue", "name": "totalRevenue"}], "datasetName": "${ds3}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}"}]}