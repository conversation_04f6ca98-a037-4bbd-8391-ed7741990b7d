{"payload": [{"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionPerExperience", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "notin", "val": "z1TG,z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "journeyId"], "filter": [{"attr": "journeyType", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}]}]}