{"payload": [{"subsystem": "biz", "metric": "promotionalTotalPurchasesNS", "datasetName": "promotionalTotalPurchasesNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalDiscountPurchaseNS", "datasetName": "promotionalDiscountPurchaseNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}]}