{"payload": [{"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessions", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}], "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day"}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalNotSentSessions", "aggregateAs": "SUM", "groupBy": ["journeyId", "date", "actionName"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day"}]}