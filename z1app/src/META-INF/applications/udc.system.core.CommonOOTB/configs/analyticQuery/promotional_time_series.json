{"payload": [{"subsystem": "biz", "metric": "promotionalTotalDiscountNS", "datasetName": "promotionalTotalDiscountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalTotalRevenueNS", "datasetName": "promotionalTotalRevenueNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalTotalSessionsNS", "datasetName": "promotionalTotalSessionsNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalYieldNS", "datasetName": "promotionalYieldNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalAvgDiscountRateNS", "datasetName": "promotionalAvgDiscountRateNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalCVRNS", "datasetName": "promotionalCVRNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalSessionConversionCountNS", "datasetName": "promotionalSessionConversionCountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalTotalPurchasesNS", "datasetName": "promotionalTotalPurchasesNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalTotalRevenueWithDiscountNS", "datasetName": "promotionalTotalRevenueWithDiscountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}]}