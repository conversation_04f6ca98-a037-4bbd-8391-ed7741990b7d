{"payload": [{"subsystem": "biz", "metric": "totalProductView", "datasetName": "tss0", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "notin", "val": "z1Control,z1TG"}]}, {"subsystem": "biz", "metric": "totalProductViewCG", "datasetName": "tsscg0", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}, {"subsystem": "biz", "metric": "totalSKUView", "datasetName": "tss1", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "notin", "val": "z1Control,z1TG"}]}, {"subsystem": "biz", "metric": "totalSKUViewCG", "datasetName": "tsscg1", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}