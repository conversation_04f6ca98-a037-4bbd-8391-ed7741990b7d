{"payload": [{"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsCurrWeek", "timeUnit": "day", "fromDate": "${startDate}", "toDate": "${endDate}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "jtype", "op": "=", "val": "campaign"}, {"attr": "exeMode", "op": "=", "val": "live"}]}, {"subsystem": "session", "metric": "totalTargetedSessions", "datasetName": "totalTargetedSessionsCurrWeek", "timeUnit": "day", "fromDate": "${startDate}", "toDate": "${endDate}", "aggregateAs": "SUM", "groupBy": ["date"]}, {"subsystem": "session", "metric": "totalTargetedSessions", "datasetName": "totalTargetedSessionsPrevWeek", "timeUnit": "day", "fromDate": "${prevStartDate}", "toDate": "${prevEndDate}", "aggregateAs": "SUM", "groupBy": ["date"]}, {"subsystem": "session", "metric": "totalSessions", "datasetName": "totalSessions", "fromDate": "${startDate}", "toDate": "${endDate}", "timeUnit": "day", "aggregateAs": "SUM"}]}