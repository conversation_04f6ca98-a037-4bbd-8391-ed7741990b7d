{"payload": [{"metric": [{"source": "totalDiscount", "name": "promotionalTotalDiscountNS"}, {"source": "totalRevenue", "name": "promotionalTotalRevenueNS"}, {"source": "yield", "name": "promotionalYieldNS"}, {"source": "discountRate", "name": "promotionalAvgDiscountRateNS"}, {"source": "totalSessions", "name": "promotionalTotalSessionsNS"}, {"source": "conversionRate", "name": "promotionalCVRNS"}, {"source": "convertedSessions", "name": "promotionalSessionConversionCountNS"}, {"source": "totalOrderCount", "name": "promotionalTotalPurchasesNS"}, {"source": "totalRevenueWithDiscount", "name": "promotionalTotalRevenueWithDiscountNS"}], "datasetName": "${ds1}", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "${tu}", "groupBy": ["date"], "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}]}