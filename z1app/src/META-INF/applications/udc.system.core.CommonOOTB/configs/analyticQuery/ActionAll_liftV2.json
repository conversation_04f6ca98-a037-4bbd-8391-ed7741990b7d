{"payload": [{"metric": [{"source": "conversionRate", "name": "conversionRate"}, {"source": "conversionRateLift", "name": "conversionRateLift"}, {"source": "incrementalRevenue", "name": "incrementalRevenue"}, {"source": "incrementalRevenueAdjusted", "name": "incrementalRevenueAdjusted"}, {"source": "revenuePerSession", "name": "rpv"}, {"source": "revenuePerSessionAdjusted", "name": "rpvAdjusted"}, {"source": "revenuePerSessionAdjustedLift", "name": "rpvAdjustedLift"}, {"source": "revenuePerSessionLift", "name": "rpvLift"}, {"source": "sessionAddToCartRate", "name": "sessionAddToCartRate"}, {"source": "sessionCartConversionRate", "name": "sessionCartCVR"}], "datasetName": "ds0", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}]}