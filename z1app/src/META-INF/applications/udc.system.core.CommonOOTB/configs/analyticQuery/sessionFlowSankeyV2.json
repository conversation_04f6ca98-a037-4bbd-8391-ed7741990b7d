{"payload": [{"metric": [{"source": "totalRevenue", "name": "totalFlowRevenue"}, {"source": "totalSessions", "name": "totalFlowSessions"}, {"source": "totalConversions", "name": "totalFlowConversions"}, {"source": "totalOrderCount", "name": "totalFlowOrderCount"}], "datasetName": "sessionFlowDataset", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "groupBy": ["journeyIdList", "converted", "channel", "predictionLabel"]}]}