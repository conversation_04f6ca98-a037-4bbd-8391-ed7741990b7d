{"payload": [{"datasetName": "home_epp_label_distribution", "dataSource": "data_by_experience", "metric": [{"source": "totalSessions"}], "filter": [{"attr": "actionname", "op": "==", "val": "z1Target"}, {"attr": "epp_label", "op": "!=", "val": "none"}], "groupBy": ["epp_label"], "fromDate": "${f}", "toDate": "${t}"}, {"datasetName": "home_overview", "dataSource": "data_by_experience", "metric": [{"source": "totalRevenue"}, {"source": "conversionRate"}, {"source": "averageDiscountRate"}, {"source": "revenuePerVisitor"}, {"source": "totalSessions"}, {"source": "averageOrderValue"}, {"source": "actionsCompleted"}, {"source": "totalDiscounts"}], "groupBy": [], "filter": [{"attr": "actionname", "op": "!=", "val": "z1Target"}], "fromDate": "${f}", "toDate": "${t}"}]}