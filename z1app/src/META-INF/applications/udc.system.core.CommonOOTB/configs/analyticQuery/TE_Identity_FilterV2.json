{"payload": [{"metric": [{"source": "averageOrderValue", "name": "aov"}, {"source": "averageOrderValueAdjusted", "name": "aovAdjusted"}, {"source": "averageOrderValueAdjustedLift", "name": "aovAdjustedLift"}, {"source": "aovOfferRedemption", "name": "aovOfferRedemption"}, {"source": "averageOrderValueLift", "name": "aovLift"}, {"source": "averageItemPrice", "name": "averageItemPrice"}, {"source": "averageItemsPerOrder", "name": "averageItemsPerOrder"}, {"source": "averageDiscount", "name": "avgDiscount"}, {"source": "totalOrderCountWithDiscount", "name": "discountPurchase"}, {"source": "totalRevenueAdjusted", "name": "revenueAdjusted"}, {"source": "sessionAdd<PERSON>oCart", "name": "sessionAdd<PERSON>oCart"}, {"source": "sessionCartValue", "name": "sessionCartValue"}, {"source": "sessionOfferApplied", "name": "sessionOfferApplied"}, {"source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"source": "totalSessionSearched", "name": "sessionSearches"}, {"source": "sessionStartCheckout", "name": "sessionStartCheckout"}, {"source": "totalSessionsViewedProdOrSKU", "name": "sessionViewedProdOrSKU"}, {"source": "totalConvertedSession", "name": "totalConvertedSession"}, {"source": "totalDiscount", "name": "totalDiscount"}, {"source": "totalItemsOrdered", "name": "totalItemsOrdered"}, {"source": "totalItemsRevenue", "name": "totalItemsRevenue"}, {"source": "totalProductView", "name": "totalProductView"}, {"source": "totalOrderCount", "name": "totalPurchases"}, {"source": "totalRevenue", "name": "totalRevenue"}, {"source": "totalSKUView", "name": "totalSKUView"}, {"source": "totalSearched", "name": "totalSearched"}, {"source": "totalSentSessions", "name": "totalSentSessions"}, {"source": "totalNotSentSessions", "name": "totalNotSentSessions"}], "datasetName": "ds0", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}], "timeUnit": "day", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}]}