{"payload": [{"subsystem": "interactionOps", "metric": "totalSent", "datasetName": "totalSent", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["date", "actionName"], "filter": [{"attr": "exeMode", "op": "=", "val": "${exeMode}"}, {"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "interactionOps", "metric": "totalInteracted", "datasetName": "totalInteracted", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["date", "actionName"], "filter": [{"attr": "exeMode", "op": "=", "val": "${exeMode}"}, {"attr": "journeyId", "op": "=", "val": "${journeyId}"}]}]}