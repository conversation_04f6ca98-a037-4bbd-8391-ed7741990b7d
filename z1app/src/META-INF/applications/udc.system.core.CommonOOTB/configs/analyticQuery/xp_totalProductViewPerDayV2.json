{"payload": [{"metric": [{"source": "totalProductView", "name": "totalProductView"}], "datasetName": "tss0", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "notin", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "totalProductView", "name": "totalProductViewCG"}], "datasetName": "tsscg0", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}, {"metric": [{"source": "totalSKUView", "name": "totalSKUView"}], "datasetName": "tss1", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "notin", "val": "z1Control,z1TG"}]}, {"metric": [{"source": "totalSKUView", "name": "totalSKUViewCG"}], "datasetName": "tsscg1", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}