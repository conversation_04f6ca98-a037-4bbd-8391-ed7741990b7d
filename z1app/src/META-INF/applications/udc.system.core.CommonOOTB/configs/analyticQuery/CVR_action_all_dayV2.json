{"payload": [{"metric": [{"source": "conversionRate", "name": "conversionRate"}], "datasetName": "conversionRate", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"metric": [{"source": "conversionRate", "name": "conversionRate"}], "datasetName": "conversionRateCG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}