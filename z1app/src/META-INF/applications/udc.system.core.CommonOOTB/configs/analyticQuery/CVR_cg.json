{"payload": [{"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConversionSessionCGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalSentSessionCGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}