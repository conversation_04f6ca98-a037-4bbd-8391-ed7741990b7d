{"payload": [{"subsystem": "biz", "metric": "promotionalTotalDiscountNS", "datasetName": "promotionalTotalDiscountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["epp<PERSON>abel"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "!=", "val": "z1EppLabelAll"}, {"attr": "epp<PERSON>abel", "op": "!=", "val": "z1EppLabelScored"}, {"attr": "epp<PERSON>abel", "op": "!=", "val": "nl"}]}]}