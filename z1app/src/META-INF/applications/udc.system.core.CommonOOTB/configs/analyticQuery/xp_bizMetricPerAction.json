{"payload": [{"subsystem": "biz", "metric": "${m}", "datasetName": "${ds}", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "notin", "val": "z1Control,z1TG"}]}, {"subsystem": "biz", "metric": "${mcg}", "datasetName": "${dscg}", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}