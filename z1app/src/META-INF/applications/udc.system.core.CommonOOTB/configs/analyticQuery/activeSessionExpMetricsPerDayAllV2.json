{"payload": [{"metric": [{"source": "totalSentSessions", "name": "totalSentSessions"}], "datasetName": "totalSentSessions", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}], "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day"}, {"metric": [{"source": "totalNotSentSessions", "name": "totalNotSentSessions"}], "datasetName": "totalNotSentSessions", "groupBy": ["journeyId", "date", "actionName"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day"}]}