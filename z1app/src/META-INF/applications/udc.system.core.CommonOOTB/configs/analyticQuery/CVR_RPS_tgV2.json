{"payload": [{"metric": [{"source": "sessionCartConversionRate", "name": "sessionCartCVR"}, {"source": "revenuePerSession", "name": "rpv"}, {"source": "conversionRate", "name": "conversionRate"}, {"source": "revenuePerSessionAdjusted", "name": "rpvAdjusted"}, {"source": "sessionAddToCartRate", "name": "sessionAddToCartRate"}], "datasetName": "ds0", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["journeyId"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}]}