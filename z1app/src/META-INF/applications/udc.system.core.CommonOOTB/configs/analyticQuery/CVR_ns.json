{"payload": [{"subsystem": "biz", "metric": "totalConvertedSessionNS", "datasetName": "totalConvertedSessionNS", "timeUnit": "${tu}", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["date"]}, {"subsystem": "session", "metric": "totalSessions", "datasetName": "totalSessionsNS", "timeUnit": "${tu}", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["date"]}]}