{"payload": [{"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionPerExperience", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "journeyType", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}