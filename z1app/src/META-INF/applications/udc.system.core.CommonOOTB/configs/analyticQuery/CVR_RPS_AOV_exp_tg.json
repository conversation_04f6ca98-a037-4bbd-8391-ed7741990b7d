{"payload": [{"subsystem": "biz", "metric": "aov", "datasetName": "aov", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionPerExperience", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "totalRevenue", "datasetName": "totalRevenueTGAction", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessionsPerExperienceAnyActions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}]}, {"subsystem": "biz", "metric": "sessionCartValue", "datasetName": "sessionCartValue", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "biz", "metric": "sessionAdd<PERSON>oCart", "datasetName": "sessionAdd<PERSON>oCart", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId"], "filter": [{"attr": "jtype", "op": "=", "val": "${jtype}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}]}