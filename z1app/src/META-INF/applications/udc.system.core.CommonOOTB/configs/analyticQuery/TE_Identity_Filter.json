{"payload": [{"metric": "totalSentSessions", "datasetName": "totalSentSessions", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "interactionOps", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalNotSentSessions", "datasetName": "totalNotSentSessions", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "interactionOps", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalConvertedSession", "datasetName": "totalConvertedSession", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalConvertedSession", "datasetName": "totalConvertedSessionCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalPurchases", "datasetName": "totalPurchases", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalPurchases", "datasetName": "totalPurchasesCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalRevenue", "datasetName": "totalRevenue", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalRevenueCG", "datasetName": "totalRevenueCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "revenueAdjusted", "datasetName": "revenueAdjusted", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "revenueAdjustedCG", "datasetName": "revenueAdjustedCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalItemsRevenue", "datasetName": "totalItemsRevenue", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalItemsRevenueCG", "datasetName": "totalItemsRevenueCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalItemsOrdered", "datasetName": "totalItemsOrdered", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalItemsOrderedCG", "datasetName": "totalItemsOrderedCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "averageItemsPerOrder", "datasetName": "averageItemsPerOrder", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "averageItemsPerOrderCG", "datasetName": "averageItemsPerOrderCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "averageItemPrice", "datasetName": "averageItemPrice", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "averageItemPriceCG", "datasetName": "averageItemPriceCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionAdd<PERSON>oCart", "datasetName": "sessionAdd<PERSON>oCart", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionAddToCartCG", "datasetName": "sessionAddToCartCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionCartValue", "datasetName": "sessionCartValue", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionCartValueCG", "datasetName": "sessionCartValueCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionStartCheckout", "datasetName": "sessionStartCheckout", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionStartCheckoutCG", "datasetName": "sessionStartCheckoutCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionOfferApplied", "datasetName": "sessionOfferApplied", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionOfferAppliedCG", "datasetName": "sessionOfferAppliedCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "datasetName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionOfferRedeemedCG", "datasetName": "sessionOfferRedeemedCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionViewedProdOrSKU", "datasetName": "sessionViewedProdOrSKU", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionViewedProdOrSKUCG", "datasetName": "sessionViewedProdOrSKUCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionSearches", "datasetName": "sessionSearches", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "sessionSearchesCG", "datasetName": "sessionSearchesCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalProductView", "datasetName": "totalProductView", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalProductViewCG", "datasetName": "totalProductViewCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalSKUView", "datasetName": "totalSKUView", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalSKUViewCG", "datasetName": "totalSKUViewCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalSearched", "datasetName": "totalSearched", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalSearchedCG", "datasetName": "totalSearchedCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aov", "datasetName": "aov", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aovCG", "datasetName": "aovCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aovAdjusted", "datasetName": "aovAdjusted", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aovAdjustedCG", "datasetName": "aovAdjustedCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aovAdjustedLift", "datasetName": "aovAdjustedLift", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aovLift", "datasetName": "aovLift", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalDiscount", "datasetName": "totalDiscount", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "totalDiscountCG", "datasetName": "totalDiscountCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "avgDiscount", "datasetName": "avgDiscount", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "avgDiscountCG", "datasetName": "avgDiscountCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "discountPurchase", "datasetName": "discountPurchase", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "discountPurchaseCG", "datasetName": "discountPurchaseCG", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}, {"metric": "aovOfferRedemption", "datasetName": "aovOfferRedemption", "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}, {"attr": "actionName", "op": "!=", "val": "z1Control"}], "subsystem": "biz", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["actionName", "visitIden"], "fromDate": "${f}", "toDate": "${t}"}]}