{"payload": [{"metric": [{"source": "revenuePerSession", "name": "rpv"}], "datasetName": "rpv", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "!=", "val": "z1TG"}]}, {"metric": [{"source": "revenuePerSession", "name": "rpv"}], "datasetName": "rpvCG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "groupBy": ["actionName", "date"], "filter": [{"attr": "journeyId", "op": "=", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}