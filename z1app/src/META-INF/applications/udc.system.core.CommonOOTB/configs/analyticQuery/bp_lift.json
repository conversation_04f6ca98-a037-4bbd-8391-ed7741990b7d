{"payload": [{"subsystem": "biz", "metric": "conversionRateLiftAll", "datasetName": "conversionRateLiftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "experience", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "biz", "metric": "aovLiftAll", "datasetName": "aovLiftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "experience", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "biz", "metric": "rpvLiftAll", "datasetName": "rpvLiftAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "experience", "op": "=", "val": "${journeyId}"}]}, {"subsystem": "biz", "metric": "incrementalRevenueAll", "datasetName": "incrementalRevenueAll", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "cumulative", "aggregateAs": "SUM", "groupBy": ["actionName"], "filter": [{"attr": "experience", "op": "=", "val": "${journeyId}"}]}]}