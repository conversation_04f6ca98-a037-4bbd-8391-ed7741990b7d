{"payload": [{"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionTG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1TG"}]}, {"subsystem": "interactionOps", "metric": "totalSentSessions", "datasetName": "totalSentSessions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}]}, {"subsystem": "biz", "metric": "totalConvertedSession", "datasetName": "totalConvertedSessionCG", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}, {"subsystem": "interactionOps", "metric": "totalNotSentSessions", "datasetName": "totalNotSentSessions", "timeUnit": "day", "fromDate": "${f}", "toDate": "${t}", "aggregateAs": "SUM", "groupBy": ["journeyId", "date"], "filter": [{"attr": "journeyId", "op": "in", "val": "${journeyId}"}, {"attr": "actionName", "op": "=", "val": "z1Control"}]}]}