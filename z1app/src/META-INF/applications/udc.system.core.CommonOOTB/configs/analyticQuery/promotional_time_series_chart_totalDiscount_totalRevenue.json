{"payload": [{"subsystem": "biz", "metric": "promotionalTotalDiscountNS", "datasetName": "promotionalTotalDiscountNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}, {"subsystem": "biz", "metric": "promotionalTotalRevenueNS", "datasetName": "promotionalTotalRevenueNS", "fromDate": "${f}", "toDate": "${t}", "timeUnit": "day", "groupBy": ["date"], "aggregateAs": "SUM", "filter": [{"attr": "epp<PERSON>abel", "op": "=", "val": "z1EppLabelAll"}]}]}