{"payload": [{"subsystem": "session", "metric": "totalSuccesses", "datasetName": "sessionScoreMetrics", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["scoreBin"], "filter": [{"attr": "modelId", "op": "=", "val": "${channel}_${type}"}, {"attr": "version", "op": "=", "val": "${version}"}]}, {"subsystem": "session", "metric": "totalSessions", "datasetName": "sessionScoreMetrics", "fromDate": "${fromDate}", "toDate": "${toDate}", "timeUnit": "day", "aggregateAs": "SUM", "groupBy": ["scoreBin"], "filter": [{"attr": "modelId", "op": "=", "val": "${channel}_${type}"}, {"attr": "version", "op": "=", "val": "${version}"}]}]}