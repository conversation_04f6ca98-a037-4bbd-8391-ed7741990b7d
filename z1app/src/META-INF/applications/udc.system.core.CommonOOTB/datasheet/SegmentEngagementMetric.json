{"axes": [{"id": "date", "label": " ", "src": "date/*", "props": {"showAs": "date"}}], "control": {"chartType": "Areas", "props": {"vAxis": {"format": "short", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "viewWindowMode": "explicit", "minValue": 0, "viewWindow": {"min": 0}, "minorGridlines": {"count": 1}}, "hAxis": {"format": "MMM dd", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "legend": {"position": "top", "alignment": "end", "maxLines": 2, "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}}, "height": "400", "chartArea": {"width": "85%"}, "massageRowDataFunc": "handleMassageRowDataFunc"}}, "exec": {"uri": "/c3/analytics/query?id=${uriId}&segmentId=${id}&monthStart=${monthStart}&monthEnd=${monthEnd}${uriFFlag}", "process": {"totalPopulation": {"mergeKey": "${date}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "populationChange", "assignTo": "frequency"}]}}}, "dataPoints": [{"label": "Population", "id": "frequency", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}