{"axes": [{"id": "Attribute", "label": " ", "src": "${attr_name}/*"}, {"id": "sdk", "label": " ", "render": false, "src": "os/${sdk}"}], "control": {"chartType": "Geo", "props": {"legend": {"position": "bottom", "maxLines": 3}, "height": "400"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:DeviceStatsCube", "type": "cube"}, "dataPoints": [{"label": "Activity Count", "id": "Activities", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}