{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date"}, "src": "date/*"}, {"id": "key", "label": "", "props": {"values": {"format": {"promotionalTotalDiscountNS": {"displayName": "Discounts"}, "promotionalTotalRevenueNS": {"displayName": "Revenue"}}, "closedSet": false}, "sort": true}, "src": "key/*"}], "control": {"chartType": "StackedColumns", "props": {"colors": ["#7719E3", "#26ABA4"], "vAxis": {"minValue": 0, "format": {"prefix": "$", "pattern": "short"}, "viewWindowMode": "explicit", "viewWindow": {"min": 0}, "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": "MMM dd", "maxTextLines": 1, "titleTextStyle": {"color": "#333"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "legend": {"position": "bottom", "alignment": "start", "maxLines": 2}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "100%", "chartArea": {"width": "90%"}, "bar": {"groupWidth": "60%"}}}, "exec": {"uri": "c3/data/analyticquery/id?id=promotional_time_series_chart_totalDiscount_totalRevenue"}, "dataPoints": [{"label": "Value", "id": "value", "method": "udc.system.aggregation:sum", "src": "value", "props": {"format": {"type": "number", "options": {"prefix": "$"}}}}], "aggregate": "key", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}