{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date", "useDateTicks": true}, "src": "date/*"}, {"id": "journeyId", "label": "journeyId", "src": "journeyId/*"}], "control": {"chartType": "StackedLines", "props": {"curveType": "none", "vAxis": {"title": "", "format": "short", "viewWindowMode": "explicit", "viewWindow": {"min": 0}, "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": "MMM dd", "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [], "legend": {"position": "bottom", "alignment": "start", "maxLines": 3, "textStyle": {"fontSize": "12", "fontName": "Roboto", "color": "#595959"}, "toggleChartOnOff": true}, "massageRowDataFunc": "handleMassageRowDataFunc", "isCombined": "true", "tooltip": {"isHtml": true}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "300", "chartArea": {"width": "100%", "left": "5%", "top": "5%", "right": "5%"}}}, "exec": {"uri": "c3/${uriPath}?id=${uriId}&journeyId=${journeyId}&jtype=campaign${uriFFlag}", "process": {"totalSentSessions": {"mergeKey": "${date}_${journeyId}_TG", "assign": [{"field": "date", "assignTo": "date"}, {"field": "totalSentSessions", "value": "totalSentSessions", "assignTo": "Sessions"}, {"value": "${journeyId}_TG", "assignTo": "journeyId"}]}, "totalNotSentSessions": {"mergeKey": "${date}_${journeyId}_CG", "assign": [{"field": "date", "assignTo": "date"}, {"field": "totalNotSentSessions", "value": "totalNotSentSessions", "assignTo": "Sessions"}, {"value": "${journeyId}_CG", "assignTo": "journeyId"}]}}}, "dataPoints": [{"label": "Sessions", "id": "Sessions", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "Sessions"}], "aggregate": "date", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}