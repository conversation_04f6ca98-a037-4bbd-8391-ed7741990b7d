{"axes": [{"id": "time", "render": false, "src": "date/*/*"}, {"id": "segmentId", "label": " ", "render": false, "src": "segmentId/*"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Views", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:SegmentFrequencyCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.SortProcessor", "value": "dataPoint=time"}]}, "dataPoints": [{"label": " ", "id": "population", "method": "udc.system.aggregation:sum", "props": {}, "src": "frequency"}], "description": "", "filters": []}