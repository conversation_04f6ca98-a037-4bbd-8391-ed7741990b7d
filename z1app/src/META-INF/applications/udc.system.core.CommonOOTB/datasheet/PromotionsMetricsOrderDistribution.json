{"axes": [{"id": "type", "label": "", "props": {"values": {"format": {"noDiscount": {"displayName": "No Discount"}, "discount": {"displayName": "Discount"}}, "closedSet": false}}, "src": "type/*"}], "control": {"chartType": "Pie", "props": {"pieHole": 0.66, "colors": ["#26ABA4", "#7719E3", "#F5D73D", "#dd4477", "#990099", "#0099c6", "#dd4477", "#66aa00", "#b82e2e", "#316395", "#994499", "#22aa99", "#aaaa11", "#6633cc"], "vAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minValue": 0}, "hAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minValue": 0}, "pieSliceText": "none", "legend": {"position": "none"}, "massageRowDataFunc": "handleMassageRowDataFunc", "tooltip": {"isHtml": true, "genToolTipFunc": "_genDMDonutsHtmlTooltip", "htmlTemplate": "<div class='c3_rtTooltip c3-c-gray0 c3_fs10 c3_br4 c3_pd_8'><div class='c3_rows_wrap'><div class='c3_fw700'>${name}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${value}</div></div></div>"}, "height": "95%", "width": "95%", "chartArea": {"width": "95%", "top": "3%", "bottom": "5%"}}}, "exec": {"uri": "c3/data/analyticquery/id?id=promotional_boolean_discountPurchase_chart_totalPurchases"}, "dataPoints": [{"label": "Value", "id": "value", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number"}}, "src": "value"}], "aggregate": "true", "description": "", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}