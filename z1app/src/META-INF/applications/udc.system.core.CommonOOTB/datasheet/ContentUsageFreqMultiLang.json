{"axes": [{"id": "lang", "label": "Language", "props": {}, "src": "language/*"}, {"id": "contentType", "label": " ", "render": false, "src": "contentType/${contentType}"}], "control": {"chartType": "Pie", "props": {"height": "500", "pieHole": "0.4"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ContentStatCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.SelectTopNProcessor", "value": "dataPoint=Views,topN=5"}], "filter": [{"ref": "date/*", "value": "${fromDate}", "operator": ">="}, {"ref": "date/*", "value": "${toDate}", "operator": "<="}]}, "dataPoints": [{"label": " ", "id": "Views", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}