{"axes": [{"id": "scorePlot", "label": "Score Plot", "props": {"showAs": "number"}, "src": "scorePlot/*"}], "control": {"chartType": "<PERSON><PERSON><PERSON>", "props": {"title": "", "titleTextStyle": {"color": "#141414", "fontSize": "18", "fontName": "Roboto"}, "colors": ["#0E70C7"], "vAxis": {"title": "Checkout Rate", "baselineColor": "#E0E0E0", "titleTextStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto", "bold": true, "italic": false}, "format": {"suffix": "%"}, "minValue": 0, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}, "gridlines": {"color": "#E0E0E0"}, "minorGridlines": {"color": "#FFFFFF"}}, "hAxis": {"ticks": [{"v": 0, "f": "0"}, {"v": 0.05, "f": ".05"}, {"v": 0.1, "f": ".10"}, {"v": 0.15, "f": ".15"}, {"v": 0.2, "f": ".20"}, {"v": 0.25, "f": ".25"}, {"v": 0.3, "f": ".30"}, {"v": 0.35, "f": ".35"}, {"v": 0.4, "f": ".40"}, {"v": 0.45, "f": ".45"}, {"v": 0.5, "f": ".50"}, {"v": 0.55, "f": ".55"}, {"v": 0.6, "f": ".60"}, {"v": 0.65, "f": ".65"}, {"v": 0.7, "f": ".70"}, {"v": 0.75, "f": ".75"}, {"v": 0.8, "f": ".80"}, {"v": 0.85, "f": ".85"}, {"v": 0.9, "f": ".90"}, {"v": 0.95, "f": ".95"}, {"v": 1, "f": "1.0"}], "format": "0.00", "gridlines": {"color": "transparent"}, "baselineColor": "none", "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}, "title": "Checkout Score Range", "titleTextStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto", "bold": true, "italic": false}}, "handleOnReadyFunc": "_OnReadyFunc", "legend": "none", "tooltip": {"isHtml": true, "hideXVal": true, "genToolTipFunc": "_genSessionScoreToolTip", "htmlTemplate": "<div style=\"box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);border-radius: 4px;padding:6px 10px;color:#fff;background-color: #314A68;box-sizing:border-box;font-family:<PERSON><PERSON>;font-weight:400;font-size:14px;line-height:20px;display:flex;flex-flow:row nowrap\"><div><div><div style=\"font-size: 13px\">Range&nbsp;&nbsp;<strong style=\"font-size: 14px\">${range}</strong></div><div style=\"font-size: 13px\">Checkout Sessions&nbsp;&nbsp;<strong style=\"font-size: 14px\">${sessions}</strong></div><div style=\"font-size: 13px\">Checkout Rate&nbsp;&nbsp;<strong style=\"font-size: 14px\">${rate}%</strong></div></div></div></div>"}, "height": 250, "chartArea": {"width": "90%", "height": 180}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#314A68"}}}, "exec": {"uri": "c3/analytics/query?id=${uriIdChkByModel}&modelId=${modelId}&fromDate=${fromDate}&toDate=${toDate}&period=${period}${uriFFlag}"}, "dataPoints": [{"label": "Checkout Rate", "id": "successRate", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number", "options": {"suffix": "%"}}}, "src": "successRate"}], "description": "", "filters": []}