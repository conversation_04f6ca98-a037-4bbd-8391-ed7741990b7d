{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date"}, "src": "date/*"}, {"id": "propB", "label": "", "props": {"values": {"format": {"ulb": {"displayName": "Unlikely", "style": "color:#7719E3"}, "otf": {"displayName": "On The Fence", "style": "color:#067D77"}, "hlb": {"displayName": "High Likely", "style": "color:#F5D73D"}, "nl": {"displayName": "Not Labeled", "style": "color:#E3193B"}, "ULB": {"displayName": "Unlikely", "style": "color:#7719E3"}, "OTF": {"displayName": "On The Fence", "style": "color:#067D77"}, "HLB": {"displayName": "High Likely", "style": "color:#F5D73D"}, "NL": {"displayName": "Not Labeled", "style": "color:#E3193B"}}, "closedSet": true}, "sort": true}, "src": "propB/*"}], "control": {"chartType": "StackedLines", "props": {"curveType": "none", "vAxis": {"title": "", "format": "short", "viewWindowMode": "explicit", "viewWindow": {"min": 0}, "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"maxTextLines": 1, "format": "MMM dd", "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [{"color": "#7719E3"}, {"color": "#067D77"}, {"color": "#F5D73D"}, {"color": "#E3193B"}, {"color": "#81BEF7"}], "legend": {"position": "bottom", "alignment": "start", "maxLines": 3, "textStyle": {"fontSize": "12", "fontName": "Roboto", "color": "#595959"}}, "massageRowDataFunc": "handleMassageRowDataFunc", "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "100%", "chartArea": {"width": "95%", "left": "5%", "top": "5%", "right": "5%"}}}, "exec": {"uri": "c3/analytics/query?id=${uriId}&ss=explorer&m=totalSessions&group=propB&ds=totalMobileSessions&fltr=deviceContext&fltrVal=mobileWeb,tabletWeb${uriFFlag}"}, "process": {"propBField": {"mergeKey": "${date}+${propB}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "propB", "assignTo": "propB"}, {"field": "totalSessions", "assignTo": "totalSessions"}]}}, "dataPoints": [{"label": "totalSessions", "id": "totalSessions", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "totalSessions"}], "aggregate": "date", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}