{"axes": [{"id": "eventSrcChannel", "label": "", "props": {}, "src": "eventSrcChannel/*"}, {"id": "type", "label": "", "props": {"values": {"format": {"totalSession": {"displayName": "Total Sessions"}}, "closedSet": false}, "sort": true}, "src": "type/*"}], "control": {"chartType": "StackedBars", "props": {"colors": ["#F7A452", "#B0ADAB"], "vAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "viewWindowMode": "pretty", "minValue": 0, "viewWindow": {"min": 0}, "minorGridlines": {"count": 0}}, "hAxis": {"format": "short", "baseline": 0, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minValue": 0, "viewWindow": {"min": 0}, "gridlines": {"color": "#E0E0E0"}, "minorGridlines": {"count": 0}}, "legend": {"position": "bottom", "alignment": "start", "maxLines": 2}, "tooltip": {"isHtml": true, "genToolTipFunc": "_genChannelMetricsHtmlTooltip", "htmlTemplate": "<div style='color:#141414;box-sizing:border-box;padding:15px 15px;font-family:Roboto;font-weight:normal;font-size:14px;line-height:20px'><div><span style='height:16px;width:16px;background:${ttip-color};border-radius:4px;display:inline-block;position:relative;top:2px;margin-right:5px'></span><span>${ttip-label}</span><span style='font-weight:bold;padding-left:5px'>${ttip-value}</span></div></div>"}, "height": "100%", "chartArea": {"width": "60%", "height": "80%", "top": "1%"}}}, "exec": {"uri": "c3/analytics/query?id=sessionsPerChannel", "process": {"totalSessions": {"mergeKey": "${channel}_totalSession", "assign": [{"field": "channel", "assignTo": "eventSrcChannel"}, {"field": "totalSessions", "assignTo": "value"}, {"value": "totalSession", "assignTo": "type"}]}}}, "dataPoints": [{"label": "Value", "id": "value", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true}, "src": "value"}], "aggregate": "eventSrcChannel", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}