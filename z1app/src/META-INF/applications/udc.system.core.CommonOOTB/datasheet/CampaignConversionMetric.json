{"axes": [{"id": "time", "label": " ", "src": "date/*/*"}, {"id": "contentType", "label": " ", "render": false, "src": "contentType/article"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Views", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ContentStatCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.DateFormatterPostProcessor", "value": "id=time,read=yyyyMMdd,write=ddMonth"}]}, "dataPoints": [{"label": " ", "id": "Views", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}