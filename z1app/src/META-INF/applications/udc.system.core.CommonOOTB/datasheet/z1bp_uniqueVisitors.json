{"axes": [{"id": "date", "label": " ", "src": "date/*", "props": {"showAs": "date", "sort": true, "useDateTicks": true}}, {"id": "actionName", "label": "", "props": {"values": {"format": {"z1Control": {"displayName": "Control", "style": "color:#D7D6D7"}}, "closedSet": false}, "sort": true}, "src": "actionName/*"}], "control": {"chartType": "StackedLines", "props": {"title": "", "titleTextStyle": {"color": "#000", "fontSize": 16, "fontName": "Roboto"}, "colors": ["#0E70C7", "#EB7F13", "#7719E3", "#F5D73D", "#067D77", "#E3193B", "#D7D6D7", "#42639A", "#6C82AA", "#91A1BF", "#0176D3", "#1B96FF", "#57A3FD", "#78B0FD", "#AACBFF", "#D8E6FE", "#EEF4FF"], "curveType": "none", "hAxis": {"format": "MMM dd", "maxTextLines": 1, "titleTextStyle": {"color": "#333"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "vAxis": {"minValue": 0, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "legend": {"position": "top", "alignment": "end", "maxLines": 4, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "height": 200, "chartArea": {"width": "90%"}}}, "exec": {"uri": "/c3/analytics/query?id=bp_uniqueVisitor&journeyId=${journeyId}", "process": {"uniqueVisitor": {"mergeKey": "${date}+${actionName}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "actionName", "assignTo": "actionName"}, {"field": "uniqueVisitor", "assignTo": "uniqueVisitor"}]}}}, "dataPoints": [{"label": "Unique Visitors", "id": "uniqueVisitor", "method": "udc.system.aggregation:sum", "src": "uniqueVisitor"}], "aggregate": "date", "description": "Unique visitors/action/day over time - Experience specific Business Performance Overview", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}