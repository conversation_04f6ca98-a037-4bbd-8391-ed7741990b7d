{"axes": [{"id": "devicetype", "label": " ", "render": true, "src": "devicetype/*"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Activity Count", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:DeviceStatsCube", "type": "cube"}, "dataPoints": [{"label": "Activity Count", "id": "count", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}