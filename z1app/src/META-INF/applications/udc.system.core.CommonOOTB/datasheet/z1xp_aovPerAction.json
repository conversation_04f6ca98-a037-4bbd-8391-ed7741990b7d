{"axes": [{"id": "actionName", "label": "", "props": {"values": {"format": {"z1Control": {"displayName": "Control", "style": "color:#B0ADAB"}}, "closedSet": false}, "sort": true}, "src": "actionName/*"}], "control": {"chartType": "Columns", "props": {"title": "", "titleTextStyle": {"color": "#000", "fontSize": 16, "fontName": "Roboto"}, "colors": ["#B0ADAB", "#0E70C7", "#7719E3", "#067D77", "#3296ED", "#90D0FE", "#1B96FF", "#1F5199"], "hAxis": {"titleTextStyle": {"color": "#595959"}, "textStyle": {"color": "#595959"}}, "vAxis": {"minValue": 0, "gridlines": {"color": "transparent"}, "textPosition": "none", "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959"}}, "legend": {"position": "none"}, "height": 188, "width": "85%", "annotations": {"datum": {"stem": {"color": "none"}}, "textStyle": {"color": "#000000"}}}}, "exec": {"uri": "/c3/analytics/query?id=xp_bizMetricPerAction&m=aov&ds=aov&mcg=aovCG&dscg=aovcg&journeyId=${journeyId}", "process": {"aov": {"mergeKey": "${actionName}", "assign": [{"field": "actionName", "assignTo": "actionName"}, {"field": "aov", "assignTo": "aov"}]}, "aovcg": {"mergeKey": "z1Control", "assign": [{"value": "z1Control", "assignTo": "actionName"}, {"field": "aovCG", "assignTo": "aov"}]}}}, "dataPoints": [{"label": "Average Order Value", "id": "aov", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number", "options": {"prefix": "$"}}, "annotation": true}, "src": "aov"}], "aggregate": "a,e", "description": "Average order value/action - Experience specific Business Performance Overview", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}