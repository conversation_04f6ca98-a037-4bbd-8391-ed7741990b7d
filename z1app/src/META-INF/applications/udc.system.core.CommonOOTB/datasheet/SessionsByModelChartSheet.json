{"axes": [{"id": "scorePlot", "label": "Score Plot", "props": {"showAs": "number"}, "src": "scorePlot/*"}, {"id": "type", "label": "", "props": {"render": false}, "src": "type/*"}], "control": {"chartType": "Columns", "props": {"title": "", "titleTextStyle": {"color": "#000", "fontSize": 18, "fontName": "Roboto"}, "colors": ["#0E70C7"], "bar": {"groupWidth": "70%"}, "hAxis": {"textPosition": "none", "titleTextStyle": {"color": "#333"}, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}, "gridlines": {"color": "transparent"}}, "vAxis": {"title": "Sessions", "titleTextStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto", "bold": true, "italic": false}, "minValue": 0, "baselineColor": "#E0E0E0", "format": {"suffix": "%"}, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}, "gridlines": {"color": "#E0E0E0"}, "minorGridlines": {"color": "none"}}, "handleOnReadyFunc": "_OnReadyFunc", "legend": "none", "tooltip": {"isHtml": true, "genToolTipFunc": "_genSessionScoreToolTip", "htmlTemplate": "<div style=\"box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);border-radius: 4px;padding:6px 10px;color:#fff;background-color: #314A68;box-sizing:border-box;font-family:<PERSON><PERSON>;font-weight:400;font-size:14px;line-height:20px;display:flex;flex-flow:row nowrap\"><div><div><div style=\"font-size: 13px\">Range&nbsp;&nbsp;<strong style=\"font-size: 14px\">${range}</strong></div><div style=\"font-size: 13px\">Sessions&nbsp;&nbsp;<strong style=\"font-size: 14px\">${sessions}</strong></div><div style=\"font-size: 13px\">% of Session&nbsp;&nbsp;<strong style=\"font-size: 14px\">${rate}%</strong></div></div></div></div>"}, "height": 250, "chartArea": {"width": "90%", "height": "95%"}, "animation": {"startup": "true", "duration": 1000, "easing": "out"}}}, "exec": {"uri": "c3/analytics/query?id=${uriIdSessByModel}&modelId=${modelId}&channel=${channel}&type=${modelType}&version=${version}&fromDate=${fromDate}&toDate=${toDate}&period=${period}${uriFFlag}"}, "dataPoints": [{"label": "Sessions", "id": "percentOfSessions", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "percentOfSessions"}], "description": "", "filters": []}