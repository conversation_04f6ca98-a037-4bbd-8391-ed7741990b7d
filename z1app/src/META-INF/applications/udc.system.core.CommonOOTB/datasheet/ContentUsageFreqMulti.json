{"axes": [{"id": "time", "label": " ", "src": "date/*/*"}, {"id": "contentType", "label": " ", "render": false, "src": "contentType/${contentType}"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Reached count", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "150"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ContentStatCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.DateFormatterPostProcessor", "value": "id=time,read=yyyyMMdd,write=ddMonth"}], "filter": [{"ref": "date/*", "value": "${fromDate}", "operator": ">="}, {"ref": "date/*", "value": "${toDate}", "operator": "<="}]}, "dataPoints": [{"label": "Reached", "id": "Views", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}