{"axes": [{"id": "propB", "label": "", "props": {"values": {"format": {"OTF": {"displayName": "On the Fence"}, "NA": {"displayName": "NA"}, "ULB": {"displayName": "Unlikely"}, "HLB": {"displayName": "Likely"}, "NL": {"displayName": "Not Labeled"}}}}, "src": "propB/*"}], "control": {"chartType": "Pie", "props": {"pieHole": 0.72, "colors": ["#FA9191", "#FCC003", "#95D6D2"], "textColors": ["#FA9191", "#FCC003", "#95D6D2"], "title": "", "titleTextStyle": {"color": "#000", "fontSize": 16, "fontName": "Roboto"}, "hAxis": {"titleTextStyle": {"color": "#333"}, "textStyle": {"color": "#595959"}}, "vAxis": {"minValue": 0, "format": "short", "textStyle": {"color": "#595959"}}, "pieSliceText": "none", "legend": {"position": "none"}, "massageRowDataFunc": "handleMassageRowDataFunc", "tooltip": {"isHtml": true, "genToolTipFunc": "_genDMDonutsHtmlTooltip", "htmlTemplate": "<div class='c3_rtTooltip c3-c-gray0 c3_fs10 c3_br4 c3_pd_8'><div class='c3_rows_wrap'><div class='c3_fw700'>${name}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${value}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${avg}</div><div class=''>&nbsp;per day</div></div></div>"}, "height": "94%", "width": "94%", "chartArea": {"width": "100%", "height": "100%"}}}, "exec": {"uri": "/c3/analytics/query?id=${uriId}&group=propB&ds=propB&tabular=false${uriFFlag}"}, "dataPoints": [{"label": "Total Sessions", "id": "totalSessions", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number"}}, "src": "totalSessions"}], "aggregate": "propB", "description": "TotalSessions for each EPP Label", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}