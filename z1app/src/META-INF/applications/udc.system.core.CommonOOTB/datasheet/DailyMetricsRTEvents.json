{"control": {"chartType": "Columns", "props": {"vAxis": {"textPosition": "none", "gridlines": {"color": "none"}, "title": "", "scaleType": "mirrorLog", "viewWindow": {"min": 0}, "baselineColor": "#E5E5E5", "textStyle": {"color": "#aaa"}, "viewWindowMode": "pretty"}, "hAxis": {"textPosition": "none", "gridlines": {"color": "none", "units": {"hours": {"format": ["HH:mm", "ha"]}}}, "viewWindowMode": null, "title": "", "textStyle": {"color": "#aaa"}}, "legend": {"position": "none"}, "pointSize": "5", "titleTextStyle": {"color": "#ccc", "fontName": "Roboto", "fontSize": "20", "bold": true}, "massageRowDataFunc": "handleMassageRowDataFunc", "tooltip": {"isHtml": true, "genToolTipFunc": "_genRTEventsHtmlTooltip", "htmlTemplate": "<div class='c3_rtTooltip c3-c-gray0 c3_fs10 c3_br4 c3_pd_8'><div class='c3_rows_wrap'><div class=''>Time&nbsp;</div><div class='c3_fw700'>${time}</div></div><div class='c3_rows_wrap'><div class=''>Events&nbsp;</div><div class='c3_fw700'>${events}</div><div class='c3_fw700'>&nbsp(${eventsPerSec}/sec)</div></div><div class='c3_rows_wrap c3_hide'><div class=''>Events/Second&nbsp;</div><div class='c3_fw700'>${eventsPerSec}</div></div></div>"}, "title": "", "bar": {"groupWidth": "70%"}, "colors": ["#0E70C7"], "height": 55, "chartArea": {"width": "100%", "height": "100%", "bottom": "0%"}}}, "axes": [{"id": "system:currentTime", "src": "system:currentTime", "label": "Minutes", "props": {"showAs": "timeofday"}}], "dataPoints": [{"id": "eps", "src": "eps", "label": "", "props": "", "method": "udc.system.aggregation:sum"}], "exec": {"uri": "c3/data/streamQuery/query?queryName=system:eps"}, "description": "", "filters": []}