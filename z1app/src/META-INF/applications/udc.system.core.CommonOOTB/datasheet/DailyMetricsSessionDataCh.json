{"axes": [{"id": "eventSrcChannel", "label": "", "props": {"values": {"format": {"mobileWeb": {"displayName": "Mobile Web"}, "desktopWeb": {"displayName": "Desktop Web"}, "nativeApp": {"displayName": "Native App"}, "other": {"displayName": "Other"}}, "closedSet": false}}, "src": "eventSrcChannel/*"}], "control": {"chartType": "Pie", "props": {"pieHole": 0.65, "colors": ["#7719E3", "#26ABA4", "#F5D73D", "#dd4477", "#990099", "#0099c6", "#dd4477", "#66aa00", "#b82e2e", "#316395", "#994499", "#22aa99", "#aaaa11", "#6633cc"], "vAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minValue": 0}, "hAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minValue": 0}, "pieSliceText": "none", "legend": {"position": "none"}, "massageRowDataFunc": "handleMassageRowDataFunc", "tooltip": {"isHtml": true, "genToolTipFunc": "_genDMDonutsHtmlTooltip", "htmlTemplate": "<div class='c3_rtTooltip c3-c-gray0 c3_fs10 c3_br4 c3_pd_8'><div class='c3_rows_wrap'><div class='c3_fw700'>${name}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${value}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${avg}</div><div class=''>&nbsp;per day</div></div></div>"}, "height": "95%", "width": "95%", "chartArea": {"width": "95%", "top": "3%", "bottom": "5%"}}}, "exec": {"uri": "c3/analytics/query?id=${uriId}${uriFFlag}", "process": {"totalSessions": {"mergeKey": "${channel}_totalSession", "assign": [{"field": "channel", "assignTo": "eventSrcChannel"}, {"field": "totalSessions", "assignTo": "value"}]}}}, "dataPoints": [{"label": "Value", "id": "value", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number", "options": {}}}, "src": "value"}], "aggregate": "eventSrcChannel", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}