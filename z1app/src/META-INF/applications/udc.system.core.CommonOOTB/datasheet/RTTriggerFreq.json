{"control": {"chartType": "<PERSON><PERSON><PERSON>", "props": {"legend": {"position": "none"}, "backgroundColor": {"stroke": "", "strokeWidth": "", "fill": "#ffffff"}, "colors": ["#006699", "#be5150", "#91af65", "#8066a0", "#50acc4", "#f5964f", "#343434", "#7C5300"], "dataOpacity": "0.4", "height": "450", "pointSize": "15", "titleTextStyle": {"color": "#ccc", "fontName": "Roboto", "fontSize": "20", "bold": true}, "hAxis": {"gridlines": {"color": "none"}, "title": "", "baselineColor": "#FFFFFF", "textStyle": {"color": "#ffffff"}}, "title": "", "vAxis": {"gridlines": {"color": "#eeeeee"}, "title": "", "viewWindow": {"min": 0}, "baselineColor": "#eeeeee", "textStyle": {"color": "#9e9e9e"}, "format": "short"}}}, "axes": [{"id": "name", "src": "name", "label": "Trigger Name", "props": {"showAs": "string"}}], "dataPoints": [{"id": "count", "src": "count", "label": "Trigger <PERSON>", "props": {"showAs": "number"}, "method": "udc.system.aggregation:sum"}], "exec": {"uri": "c3/data/streamQuery/query?queryName=_z1_trigger_tps"}, "description": "", "filters": []}