{"axes": [{"id": "eventSrcChannel", "label": "", "props": {}, "src": "eventSrcChannel/*"}, {"id": "type", "label": "", "props": {"values": {"format": {"triggeredEvent": {"displayName": "Events Triggered"}, "totalEvent": {"displayName": "Total Events"}}, "closedSet": false}, "sort": true}, "src": "type/*"}], "control": {"chartType": "StackedBars", "props": {"colors": ["#F7A452", "#B0ADAB"], "vAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "viewWindowMode": "explicit", "minValue": 0, "viewWindow": {"min": 0}, "minorGridlines": {"count": 0}}, "hAxis": {"format": "short", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "viewWindowMode": "explicit", "minValue": 0, "viewWindow": {"min": 0}, "minorGridlines": {"count": 0}}, "legend": {"position": "bottom", "alignment": "start", "maxLines": 2}, "height": "300", "chartArea": {"width": "60%"}}}, "exec": {"uri": "c3/data/insights/stats/eventsChannelByDay/queryTimeRange"}, "dataPoints": [{"label": "Value", "id": "value", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true}, "src": "value"}], "aggregate": "eventSrcChannel", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}