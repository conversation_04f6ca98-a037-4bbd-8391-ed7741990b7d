{"axes": [{"id": "time", "label": " ", "src": "date/*", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.DateFormatterPostProcessor", "value": "id=time,read=yyyyMMdd,write=ddMonth"}]}, {"id": "action", "label": "Actions", "props": {}, "src": "action/*"}], "control": {"chartType": "<PERSON><PERSON><PERSON>", "props": {"vAxis": {"title": "Frequency", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "400", "interpolateNulls": true, "hAxis": {"title": "Day of Month", "gridlines": {"color": "transparent"}}}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ActionGoalStatsCube", "type": "cube"}, "dataPoints": [{"label": "Frequency", "id": "Frequency", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}