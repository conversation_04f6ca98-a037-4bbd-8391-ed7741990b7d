{"axes": [{"id": "time", "label": "Date", "props": {"showAs": "date"}, "src": "time/*"}, {"id": "content", "label": "content", "props": {}, "src": "content/*"}], "control": {"chartType": "StackedColumns", "props": {"title": "Actions Reached", "titleTextStyle": {"color": "#000", "fontSize": 16, "fontName": "Roboto"}, "colors": ["#42639A", "#6C82AA", "#91A1BF"], "vAxis": {"textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "viewWindowMode": "explicit", "minValue": 0, "viewWindow": {"min": 0}, "minorGridlines": {"count": 1}}, "hAxis": {"format": "MMM dd", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "legend": {"position": "top", "alignment": "end", "maxLines": 2, "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}}, "height": "400", "chartArea": {"width": "85%"}, "bar": {"groupWidth": "40%"}}}, "exec": {"uri": "/c3/analytics/query?id=operationalMetrics&exeMode=live&journeyId=${journeyId}", "process": {"totalSent": {"mergeKey": "${actionName}+${date}", "assign": [{"field": "date", "assignTo": "time"}, {"field": "totalSent", "assignTo": "sent"}, {"field": "actionName", "assignTo": "content"}]}, "totalInteracted": {"mergeKey": "${actionName}+${date}", "assign": [{"field": "date", "assignTo": "time"}, {"field": "totalInteracted", "assignTo": "interacted"}, {"field": "actionName", "assignTo": "content"}]}}}, "dataPoints": [{"label": "<PERSON><PERSON>", "id": "sent", "method": "udc.system.aggregation:sum", "props": {}, "src": "sent"}], "aggregate": "time,content", "description": "", "properties": {"dateFilterOptions": []}}