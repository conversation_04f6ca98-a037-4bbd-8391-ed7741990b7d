{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date"}, "src": "date/*"}], "control": {"chartType": "<PERSON><PERSON><PERSON>", "props": {"curveType": "none", "vAxis": {"title": "", "format": "short", "viewWindowMode": "explicit", "viewWindow": {"min": 0}, "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"maxTextLines": 1, "format": "MMM dd", "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [{"color": "#7719E3"}, {"color": "#26ABA4"}, {"color": "#F5D73D"}, {"color": "#3296ED"}, {"color": "#81BEF7", "lineDashStyle": [2, 4]}, {"color": "#7719E3"}, {"color": "#7719E3", "lineDashStyle": [2, 4]}, {"color": "#26ABA4"}, {"color": "#26ABA4", "lineDashStyle": [2, 4]}], "legend": {"position": "bottom", "alignment": "start", "maxLines": 3, "textStyle": {"fontSize": "12", "fontName": "Roboto", "color": "#595959"}, "toggleChartOnOff": false}, "tooltip": {"isHtml": true, "genToolTipFunc": "_genDMScoreHtmlTooltip", "htmlTemplate": "<div style='margin:15px;color:#141414;box-sizing:border-box;font-family:Roboto;font-weight:normal;font-size:14px;line-height:20px;display:flex;flex-flow: row nowrap'><div><div>Scored Sessions/Day</div><div>${dateTime}</div><div><span style='display: inline-block;width:100px'>${ttip-name}1</span><span style='color:${ttip-pct-color};padding-left:10px;display:${display}'><span style='padding-right:0px;position:relative;top:6px'></span><span style='display:inline;font-weight:bold'>${ttip-value}1</span></span></div><div><span style='display: inline-block;width:100px'>${ttip-name}2</span><span style='color:${ttip-pct-color};padding-left:10px;display:${display}'><span style='padding-right:0px;position:relative;top:6px'></span><span style='display:inline;font-weight:bold'>${ttip-value}2</span></span></div><div><span style='display: inline-block;width:100px'>${ttip-name}3</span><span style='color:${ttip-pct-color};padding-left:10px;display:${display}'><span style='padding-right:0px;position:relative;top:6px'></span><span style='display:inline;font-weight:bold'>${ttip-value}3</span></span></div><div><span style='display: inline-block;width:100px'>${ttip-name}4</span><span style='color:${ttip-pct-color};padding-left:10px;display:${display}'><span style='padding-right:0px;position:relative;top:6px'></span><span style='display:inline;font-weight:bold'>${ttip-value}4</span></span></div></div>"}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "massageRowDataFunc": "handleMassageRowDataFunc", "height": "100%", "chartArea": {"width": "95%", "left": "5%", "top": "5%", "right": "5%"}}}, "exec": {"uri": "c3/analytics/query?id=${uriId}${uriSs}&m=totalSessions&tu=${granular}&group=propB&ds1=thisWeekData&ds2=lastWeekData${uriFFlag}"}, "dataPoints": [{"label": "Likely", "id": "HLB", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "HLB"}, {"label": "On-the-fence", "id": "OTF", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "OTF"}, {"label": "Unlikely", "id": "ULB", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "ULB"}, {"label": "Not Labeled", "id": "NL", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "NL"}], "aggregate": "date", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}