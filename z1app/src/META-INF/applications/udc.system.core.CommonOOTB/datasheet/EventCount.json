{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date", "useDateTicks": true}, "src": "date/*"}, {"id": "event", "label": "", "props": {}, "src": "event/*"}], "control": {"chartType": "StackedLines", "props": {"curveType": "none", "vAxis": {"title": "", "format": "short", "viewWindowMode": "explicit", "viewWindow": {"min": 0}, "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": "MMM d", "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "maxTextLines": 1, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [], "legend": {"position": "none"}, "tooltip": {"isHtml": true}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "285", "chartArea": {"width": "100%", "left": "5%", "top": "5%", "right": "5%"}}}, "dataPoints": [{"label": "Value", "id": "value", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true}, "src": "value"}], "aggregate": "event", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}