{"axes": [{"id": "content", "label": " ", "src": "content/*"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Reached", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "exec": {"uri": "c3/data/${campaignTypeName}/messageStat?journeyId=${journeyId}&stepIndexStr=${stepIndex}&expChangeLogId=${changeLogId}"}, "dataPoints": [{"label": "Engagement", "id": "viewed", "method": "udc.system.aggregation:sum", "src": "viewed"}, {"label": "Conversions", "id": "converted", "method": "udc.system.aggregation:conversion", "src": "converted"}], "description": "", "filters": []}