{"axes": [{"id": "time", "label": " ", "src": "date/*/*"}, {"id": "content", "label": " ", "render": false, "props": {}, "src": "contentId/${articleId}"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "No. of Views", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ContentStatCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.DateFormatterPostProcessor", "value": "id=time,read=yyyyMMdd,write=ddMonth"}]}, "dataPoints": [{"label": "views", "id": "Frequency", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}