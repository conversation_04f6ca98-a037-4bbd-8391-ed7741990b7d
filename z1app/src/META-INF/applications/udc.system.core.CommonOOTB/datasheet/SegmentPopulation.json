{"axes": [{"id": "segmentId", "label": " ", "render": false, "src": "segmentId/*"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Population", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:SegmentTotalFrequencyCube", "type": "cube"}, "dataPoints": [{"label": " ", "id": "population", "method": "udc.system.aggregation:sum", "props": {}, "src": "frequency"}], "description": "", "filters": []}