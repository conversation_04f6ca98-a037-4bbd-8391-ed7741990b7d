{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date"}, "src": "date/*"}], "control": {"chartType": "<PERSON><PERSON><PERSON>", "props": {"curveType": "none", "vAxis": {"title": "", "format": {"suffix": "%"}, "viewWindowMode": "explicit", "viewWindow": {"min": 0}, "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": "MMM dd", "maxTextLines": 1, "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [{"color": "#045FB4"}, {"color": "#81BEF7", "lineDashStyle": [2, 4]}], "legend": {"position": "bottom", "alignment": "start", "maxLines": 3, "textStyle": {"fontSize": "12", "fontName": "Roboto", "color": "#595959"}}, "tooltip": {"isHtml": true, "genToolTipFunc": "_genDailyMetricsHtmlTooltip", "htmlTemplate": "<div style='margin:15px;color:#141414;box-sizing:border-box;font-family:Roboto;font-weight:normal;font-size:14px;line-height:20px;display:flex;flex-flow: row nowrap'><div><div style='display:flex;flex-flow: row nowrap;margin-bottom:10px;margin-right:5px'><div><span style='height:16px;width:16px;background:${ttip-cDateColor};border-radius:4px;display:inline-block;position:relative;top:2px'></span><span style='padding-left:5px'>${ttip-cDate}</span></div><div style='flex-grow:1;text-align:center'><span style='padding: 0 20px 0 20px;display:${display}; color:#595959'>compared to</span></div></div><div style='margin-right:5px'><div>Conversion Rate</div><div style='font-weight:bold;margin-top:-.5em'>${ttip-cValue}%<span style='color:${ttip-pct-color};padding-left:10px;display:${display}'><span style='padding-right:0px;position:relative;top:6px'><svg class='c3i' style='width:1.5em;height:1.5em'><use xlink:href='${ttip-pct-arrow}'></svg></span><span style='display:inline;font-weight:bold'>${ttip-percentage}</span></span></div></div></div><div><div style='margin-bottom:10px'><span style='display:${display}'><span style='height:16px;width:16px;background:${ttip-pDateColor};border-radius:4px;display:inline-block;position:relative;top:2px'></span></span><span style='padding-left:5px;display:${display}'>${ttip-pDate}</span></div><div>Conversion Rate</div><div style='font-weight:bold'>${ttip-pValue}%</div></div></div>"}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "100%", "chartArea": {"width": "95%", "left": "5%", "top": "5%", "right": "5%"}}}, "exec": {"uri": "c3/${uriPath}?id=${uriId}&tu=${granular}&m=${metric}&ds1=thisWeekData&ds2=lastWeekData${uriFFlag}", "process": {"thisWeekData": {"mergeKey": "${date}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "conversionRate", "assignTo": "conversions"}, {"field": "conversionRateNS", "assignTo": "conversions"}]}, "lastWeekData": {"mergeKey": "[[let d = String(${date}); (d.length > 8) ? (sameDateNextPeriod(d.substring(0,8)) + d.substring(8)) : sameDateNextPeriod(d)]]", "assign": [{"field": "date", "assignTo": "previousDate"}, {"field": "conversionRate", "assignTo": "previousConversions"}, {"field": "conversionRateNS", "assignTo": "previousConversions"}, {"value": "[[${conversions} - ${previousConversions}]]", "assignTo": "delta"}, {"value": "[[let den = ${delta} == 0 ? 1 : (${previousConversions} == 0 ? ${conversions}: ${previousConversions}); (${delta} / den) * 100]]", "assignTo": "deltaPercent"}]}}}, "dataPoints": [{"label": "Current Period", "id": "conversions", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "conversions"}, {"label": "Previous Period", "id": "previousConversions", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "previousConversions"}], "aggregate": "date", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}