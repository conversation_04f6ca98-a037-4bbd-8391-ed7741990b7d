{"axes": [{"id": "id", "label": "Your Goals", "props": {}, "src": "goalId/*", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.GoalQueryProcessor", "value": "id=id"}]}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "GoalStatBuilder:cube", "solution": "GoalStatBuilder", "type": "cube"}, "dataPoints": [{"label": "Customers", "id": "My Customers", "method": "udc.system.aggregation:sum", "props": {}, "src": "customers"}, {"label": "Signals", "id": "My Signals", "method": "udc.system.aggregation:sum", "props": {}, "src": "signals"}, {"id": "My Actions", "method": "udc.system.aggregation:sum", "props": {}, "src": "actions"}], "description": "", "filters": []}