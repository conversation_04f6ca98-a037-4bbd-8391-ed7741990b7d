{"axes": [{"id": "name", "label": " ", "src": "name/*"}, {"id": "ack", "label": " ", "render": false, "src": "ack/success"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "No. of Views", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:BXActionAckStatCube", "type": "cube"}, "dataPoints": [{"label": " ", "id": "views", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}