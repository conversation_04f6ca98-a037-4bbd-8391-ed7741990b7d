{"control": {"chartType": "Columns", "props": {"legend": {"position": "none"}, "animation": {"startup": false}, "chartArea": {"left": 20, "top": 10, "width": "100%", "height": "60%"}, "pointSize": "1", "titleTextStyle": {"color": "#ccc", "fontName": "Roboto", "fontSize": "20", "bold": true}, "hAxis": {"gridlines": {"color": "none", "count": -1, "units": {"hours": {"format": ["HH:mm:ss", "ha"]}}}, "title": "", "textStyle": {"color": "#aaa"}}, "title": "", "height": "100", "vAxis": {"gridlines": {"color": "none"}, "title": "", "scaleType": "mirrorLog", "viewWindow": {"min": 0}, "baselineColor": "#000000", "textStyle": {"color": "#aaa"}}}}, "axes": [{"id": "system:currentTime", "src": "system:currentTime", "label": "Seconds", "props": {"showAs": "timeofday"}}], "dataPoints": [{"id": "eps", "src": "eps", "label": "", "props": "", "method": "udc.system.aggregation:sum"}], "exec": {"uri": "c3/data/streamQuery/timeseries?queryName=_z1_eps&window=10m&interval=1s&attribute=eps"}, "description": "", "filters": []}