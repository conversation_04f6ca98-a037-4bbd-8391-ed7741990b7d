{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date", "useDateTicks": true}, "src": "date/*"}, {"id": "journeyId", "label": "journeyId", "src": "journeyId/*"}], "control": {"chartType": "StackedLines", "props": {"curveType": "none", "vAxis": {"title": "", "format": {"suffix": "%"}, "viewWindowMode": "explicit", "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": "MMM dd", "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [], "legend": {"position": "bottom", "alignment": "start", "maxLines": 3, "textStyle": {"fontSize": "12", "fontName": "Roboto", "color": "#595959"}, "toggleChartOnOff": true}, "massageRowDataFunc": "handleMassageRowDataFunc", "isCombined": "true", "tooltip": {"isHtml": true}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "300", "chartArea": {"width": "100%", "left": "5%", "top": "5%", "right": "5%"}}}, "exec": {"uri": "/c3/${uriPath}?id=${uriId}&journeyId=${journeyId}&jtype=campaign${uriFFlag}", "process": {"conversionRateCG": {"mergeKey": "${date}_${journeyId}_CG", "assign": [{"field": "date", "assignTo": "date"}, {"value": "${journeyId}_CG", "assignTo": "journeyId"}, {"field": "conversionRate", "value": "conversionRate", "assignTo": "conversionRate"}]}, "conversionRateTG": {"mergeKey": "${date}_${journeyId}_TG", "assign": [{"field": "date", "assignTo": "date"}, {"value": "${journeyId}_TG", "assignTo": "journeyId"}, {"field": "conversionRate", "value": "conversionRate", "assignTo": "conversionRate"}]}}}, "dataPoints": [{"label": "Conversion Rate", "id": "conversionRate", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number", "options": {"suffix": "%"}, "addTooltip": true, "render": true}, "annotation": true}, "src": "conversionRate"}], "aggregate": "date", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}