{"axes": [{"id": "Attribute", "label": " ", "src": "${attr_name}/*"}, {"id": "sdk", "label": " ", "render": false, "src": "os/${sdk}"}], "control": {"chartType": "Bars", "props": {"hAxis": {"title": "Activity Count", "format": "short", "gridlines": {"color": "transparent"}}, "legend": {"position": "bottom", "maxLines": 3}, "height": "400", "interpolateNulls": true}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:DeviceStatsCube", "type": "cube"}, "dataPoints": [{"label": "Activity Count", "id": "Activities", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}