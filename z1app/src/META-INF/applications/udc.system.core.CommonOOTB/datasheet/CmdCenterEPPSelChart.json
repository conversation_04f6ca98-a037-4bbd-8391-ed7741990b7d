{"axes": [{"id": "scorePlot", "label": "Score Plot", "props": {"showAs": "number"}, "src": "scorePlot/*"}], "control": {"chartType": "Columns", "props": {"titleTextStyle": {"color": "#141414", "fontSize": 16, "fontName": "Roboto"}, "colors": ["#D9D9D9", "#7719E3", "#26ABA4", "#FCC003"], "hAxis": {"titleTextStyle": {"color": "#333", "bold": true}, "baselineColor": "transparent", "gridlines": {"color": "none"}, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "vAxis": {"title": "Sessions", "titleTextStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto", "bold": true, "italic": false}, "minValue": 0, "viewWindow": {"min": 0}, "baselineColor": "#000000", "format": "short", "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}, "gridlines": {"count": 8, "color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "minorGridlines": {"color": "none", "count": 0}}, "bar": {"groupWidth": "90%"}, "handleOnReadyFunc": "handleOnReadyFunc", "massageRowDataFunc": "handleMassageRowDataFunc", "legend": "none", "tooltip": {"isHtml": true, "genToolTipFunc": "_genSessionScoreToolTip", "htmlTemplate": "<div style=\"box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);border-radius: 4px;padding:6px 10px;color:#fff;background-color: #314A68;box-sizing:border-box;font-family:<PERSON><PERSON>;font-weight:400;font-size:14px;line-height:20px;display:flex;flex-flow:row nowrap\"><div><div><div style=\"font-size: 13px\">Column&nbsp;&nbsp;<strong style=\"font-size: 14px\">${column}</strong></div><div style=\"font-size: 13px\">Range&nbsp;&nbsp;<strong style=\"font-size: 14px\">${range}</strong></div><div style=\"font-size: 13px\">Sessions&nbsp;&nbsp;<strong style=\"font-size: 14px\">${sessions}</strong></div><div style=\"font-size: 13px\">% of Session&nbsp;&nbsp;<strong style=\"font-size: 14px\">${rate}%</strong></div></div></div></div>"}, "height": 220, "chartArea": {"height": "90%", "width": "85%", "right": ".5%", "left": "9%"}}}, "dataPoints": [{"label": "Sessions", "id": "totalSessions", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true, "render": true}, "src": "totalSessions"}], "description": "", "filters": []}