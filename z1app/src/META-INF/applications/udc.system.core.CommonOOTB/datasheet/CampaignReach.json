{"axes": [{"id": "ack", "label": " ", "src": "caller/*"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Views", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ActionAckStatCube", "type": "cube"}, "dataPoints": [{"label": " ", "id": "frequency", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": [{"ack": "0", "type": "c"}]}