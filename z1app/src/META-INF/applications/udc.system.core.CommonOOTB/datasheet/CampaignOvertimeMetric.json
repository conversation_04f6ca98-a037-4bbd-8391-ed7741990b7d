{"axes": [{"id": "time", "src": "date/*/*", "props": {"showAs": "day"}}, {"id": "ack", "label": " ", "render": false, "src": "ack/1"}, {"id": "journeyId", "label": " ", "render": false, "src": "caller/${journeyId}:${stepIndex}"}], "control": {"chartType": "Areas", "props": {"vAxis": {"title": "Messages Reached", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "400", "interpolateNulls": true}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:BXActionAckStatCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.DateFormatterPostProcessor", "value": "id=time,read=yyyyMMdd,write=dd"}]}, "dataPoints": [{"label": "Engagement", "id": "views", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}