{"axes": [{"id": "time", "src": "date/${month}/*", "props": {"showAs": "day"}}, {"id": "journeyId1", "label": " ", "render": false, "src": "segmentId/${journeyIdStep1}"}, {"id": "actionName", "label": " ", "render": false, "src": "actionName/${action}"}], "control": {"chartType": "Areas", "props": {"vAxis": {"title": "Conversion", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "400", "interpolateNulls": true}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:BXJourneyTransitionCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.DateFormatterPostProcessor", "value": "id=time,read=yyyyMMdd,write=dd"}]}, "dataPoints": [{"label": " ", "id": "conversion", "method": "udc.system.aggregation:sum", "src": "frequency"}], "description": "", "filters": []}