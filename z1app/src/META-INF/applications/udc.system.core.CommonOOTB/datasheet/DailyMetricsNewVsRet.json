{"axes": [{"id": "visitRec", "label": "", "props": {"values": {"format": {"new": {"displayName": "New User"}, "returning": {"displayName": "Returning User"}}}}, "src": "visitRec/*"}], "control": {"chartType": "Pie", "props": {"pieHole": 0.65, "colors": ["#7719E3", "#26ABA4", "#F5D73D", "#dd4477", "#990099", "#0099c6", "#dd4477", "#66aa00", "#b82e2e", "#316395", "#994499", "#22aa99", "#aaaa11", "#6633cc"], "title": "", "titleTextStyle": {"color": "#000", "fontSize": 16, "fontName": "Roboto"}, "hAxis": {"titleTextStyle": {"color": "#333"}, "textStyle": {"color": "#595959"}}, "vAxis": {"minValue": 0, "format": "short", "textStyle": {"color": "#595959"}}, "pieSliceText": "none", "legend": {"position": "none"}, "massageRowDataFunc": "handleMassageRowDataFunc", "tooltip": {"isHtml": true, "genToolTipFunc": "_genDMDonutsHtmlTooltip", "htmlTemplate": "<div class='c3_rtTooltip c3-c-gray0 c3_fs10 c3_br4 c3_pd_8'><div class='c3_rows_wrap'><div class='c3_fw700'>${name}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${value}</div></div><div class='c3_rows_wrap'><div class='c3_fw700'>${avg}</div><div class=''>&nbsp;per day</div></div></div>"}, "height": "95%", "width": "95%", "chartArea": {"width": "95%", "top": "3%", "bottom": "5%"}}}, "exec": {"uri": "/c3/analytics/query?id=${uriId}&group=visitRec&ds=visitRec&tabular=false${uriFFlag}"}, "dataPoints": [{"label": "Revenue per visitor", "id": "totalSessions", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number"}}, "src": "totalSessions"}], "aggregate": "true", "description": "Revenue/visitor/action - Experience specific Business Performance Overview", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}