{"axes": [{"id": "type", "label": "", "props": {"values": {"format": {"lessThan": {"displayName": "0 to 3 <PERSON>licks"}, "moreThan": {"displayName": "4 or More Clicks"}}}}, "src": "type/*"}], "control": {"chartType": "Pie", "props": {"pieHole": 0.65, "title": "Reach for Early Prediction Scores", "titleTextStyle": {"color": "#141414", "fontSize": "16", "fontName": "Roboto"}, "colors": ["#26ABA4", "#9D53F2"], "vAxis": {"format": {"suffix": "%"}, "minValue": 0, "titleTextStyle": {"color": "#8C8C8C", "fontSize": "12", "fontName": "Roboto", "bold": true, "italic": false}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": {"suffix": "%"}, "minValue": 0, "titleTextStyle": {"color": "#8C8C8C", "fontSize": "12", "fontName": "Roboto", "bold": true, "italic": false}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "pieSliceText": "none", "legend": {"position": "none"}, "chartArea": {"right": "4%", "width": "89%"}}}, "exec": {"uri": "c3/analytics/query?id=${uriIdSessTrfPi}&breakdown=${breakdown}&fromDate=${startDate}&toDate=${endDate}${uriFFlag}"}, "dataPoints": [{"label": "Share", "id": "percentage", "method": "udc.system.aggregation:sum", "props": {"addTooltip": true}, "src": "percentage"}], "description": "", "filters": []}