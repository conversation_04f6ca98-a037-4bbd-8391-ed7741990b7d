{"axes": [{"id": "content", "label": " ", "props": {}, "src": "contentId/*"}, {"id": "contentType", "label": " ", "render": false, "src": "contentType/${contentType}"}], "control": {"chartType": "List", "props": {"x": "content", "y": "Views"}}, "cube": {"description": null, "id": "cube", "ref": "udc.system.core.ZineOne Processing:ContentStatCube", "type": "cube", "hint": [{"type": "postprocessor", "name": "com.z1.postprocessors.ContentQueryProcessor", "value": "id=content"}, {"type": "postprocessor", "name": "com.z1.postprocessors.SelectTopNProcessor", "value": "dataPoint=Views,topN=5"}], "filter": [{"ref": "date/*", "value": "${fromDate}", "operator": ">="}, {"ref": "date/*", "value": "${toDate}", "operator": "<="}]}, "dataPoints": [{"label": " ", "id": "Views", "method": "udc.system.aggregation:sum", "props": {"includeZero": false, "majorTickStep": 10, "stroke": "white", "font": "normal normal 10pt Tahoma", "fontColor": "#ccc", "minorTicks": true, "majorTick": {"color": "gray", "length": 0}, "minorTick": {"color": "gray", "length": 0}, "natural": true, "fixed": true}, "src": "frequency"}], "description": "", "filters": []}