{"axes": [{"id": "date", "label": "Date", "props": {"showAs": "date", "useDateTicks": true}, "src": "date/*"}, {"id": "journeyId", "label": "journeyId", "src": "journeyId/*"}], "control": {"chartType": "StackedLines", "props": {"curveType": "none", "vAxis": {"title": "", "format": {"prefix": "$", "pattern": "short"}, "viewWindowMode": "explicit", "minorGridlines": {"count": 0, "color": "#E0E0E0"}, "gridlines": {"color": "#E0E0E0", "noDataDisplay": {"min": 0, "max": 100}}, "baselineColor": "#E0E0E0", "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}}, "hAxis": {"format": "MMM dd", "titleTextStyle": {"color": "#000000"}, "textStyle": {"color": "#595959", "fontSize": "12", "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "lineWidth": 2, "pointSize": 0, "series": [], "legend": {"position": "bottom", "alignment": "start", "maxLines": 3, "textStyle": {"fontSize": "12", "fontName": "Roboto", "color": "#595959"}, "toggleChartOnOff": true}, "tooltip": {"isHtml": true, "genToolTipFunc": "_genIncRevTtp", "htmlTemplate": "<div style='margin:15px;color:#141414;box-sizing:border-box;font-family:Roboto;font-weight:400;font-size:14px;line-height:20px;display:flex;flex-flow:row nowrap'><div id='ttip-container'><div><strong>${ttip-dayOfTheWeek}</strong></div><div><strong>${ttip-dollarVal}</strong></div></div></div>"}, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "height": "300", "chartArea": {"width": "100%", "left": "5%", "top": "5%", "right": "5%"}}}, "exec": {"uri": "/c3/${uriPath}?id=${uriId}&journeyId=${journeyId}&jtype=campaign${uriFFlag}", "process": {"incrementalRevenue": {"mergeKey": "${date}_${journeyId}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "incrementalRevenue", "assignTo": "incrementalRevenue"}, {"field": "journeyId", "assignTo": "journeyId"}]}}}, "dataPoints": [{"label": "Incremental Revenue", "id": "incrementalRevenue", "method": "udc.system.aggregation:sum", "props": {"format": {"type": "number", "options": {"prefix": "$"}, "addTooltip": true, "render": true}}, "src": "incrementalRevenue"}], "aggregate": "date", "description": "", "properties": {"dateFilterOptions": [], "tz": "UTC"}}