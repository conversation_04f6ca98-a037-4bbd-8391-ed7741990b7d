{"axes": [{"id": "attributeName", "label": " ", "src": "${attributeName}/*"}, {"id": "journeyId", "label": " ", "render": false, "src": "journeyId/${journeyId}"}], "control": {"chartType": "Columns", "props": {"vAxis": {"title": "Message Count", "format": "short"}, "legend": {"position": "bottom", "maxLines": 3}, "height": "300"}}, "cube": {"description": null, "id": "cube", "ref": "ZineOne:JourneyContextAttrStatRecorder", "type": "cube"}, "dataPoints": [{"label": " ", "id": "views", "method": "udc.system.aggregation:sum", "src": "${stage}"}], "description": "", "filters": []}