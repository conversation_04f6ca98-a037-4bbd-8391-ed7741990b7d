{"axes": [{"id": "date", "label": " ", "src": "date/*", "props": {"showAs": "date", "sort": true, "useDateTicks": true}}, {"id": "actionName", "label": "", "props": {"values": {"format": {"z1Control": {"displayName": "Control", "style": "color:#B0ADAB"}}, "closedSet": false}, "sort": true}, "src": "actionName/*"}], "control": {"chartType": "StackedLines", "props": {"title": "", "titleTextStyle": {"color": "#000", "fontSize": 16, "fontName": "Roboto"}, "colors": ["#333333", "#7719E3", "#26ABA4", "#F5D73D", "#FC57C4", "#198AEE", "#FA8C1F", "#BCE931", "#B29EFF", "#FFC164", "#73DCC3", "#70BCFF"], "series": {"0": {"lineDashStyle": [4, 2]}}, "curveType": "none", "hAxis": {"format": "MMM dd", "maxTextLines": 1, "titleTextStyle": {"color": "#333"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "vAxis": {"minValue": 0, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}}, "lineWidth": 2, "pointSize": 0, "legend": {"position": "top", "alignment": "end", "maxLines": 4, "textStyle": {"color": "#595959", "fontFamily": "Roboto", "fontStyle": "normal", "fontWeight": "normal", "fontSize": 12, "lineHeight": 14}, "toggleChartOnOff": true}, "height": 170, "crosshair": {"orientation": "vertical", "trigger": "focus", "color": "#E0E0E0"}, "chartArea": {"width": "90%"}}}, "exec": {"uri": "/c3/analytics/query?id=${uriId}&m=ratioCart&mcg=ratioCart&ds=tss&dscg=tsscg&journeyId=${journeyId}${uriFFlag}", "process": {"tss": {"mergeKey": "${date}+${actionName}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "actionName", "assignTo": "actionName"}, {"field": "ratioCart", "assignTo": "sessionCount"}]}, "tsscg": {"mergeKey": "${date}+z1Control", "assign": [{"field": "date", "assignTo": "date"}, {"value": "z1Control", "assignTo": "actionName"}, {"field": "ratioCart", "assignTo": "sessionCount"}]}}}, "dataPoints": [{"label": "Ratio of Carts:Sessions", "id": "sessionCount", "method": "udc.system.aggregation:sum", "src": "sessionCount"}], "aggregate": "date", "description": "Total Sent Sessions/action/day over time - Experience specific Business Performance Overview", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}