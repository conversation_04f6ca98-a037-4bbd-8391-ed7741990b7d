{"axes": [], "control": {"chartType": "Columns"}, "exec": {"uri": "/c3/data/analyticquery/id?id=BP_liftAll&jid=${journeyId}", "process": {"aovLiftAll": {"mergeKey": "none", "assign": [{"field": "aovLiftAll", "assignTo": "aovLiftAll"}]}, "CVR_liftAll": {"mergeKey": "none", "assign": [{"field": "conversionRateLiftAll", "assignTo": "conversionRateLiftAll"}]}, "RPS_liftAll": {"mergeKey": "none", "assign": [{"field": "rpvLiftAll", "assignTo": "rpvLiftAll"}]}, "incrementalRevenue_All": {"mergeKey": "none", "assign": [{"field": "incrementalRevenueAll", "assignTo": "incrementalRevenueAll"}]}}}, "dataPoints": [{"label": "Conversion Rate", "id": "conversionRateLiftAll", "method": "udc.system.aggregation:sum", "props": {}, "src": "conversionRateLiftAll"}, {"label": "Revenue per visitor", "id": "rpvLiftAll", "method": "udc.system.aggregation:sum", "props": {}, "src": "rpvLiftAll"}, {"label": "Average Order Value", "id": "aovLiftAll", "method": "udc.system.aggregation:sum", "props": {}, "src": "aovLiftAll"}, {"label": "Incremental Revenue", "id": "incrementalRevenueAll", "method": "udc.system.aggregation:sum", "props": {}, "src": "incrementalRevenueAll"}], "aggregate": "conversionRateLiftAll,rpvLiftAll,aovLiftAll,incrementalRevenueAll", "description": "Test values for CVR, RPV & AOV - Experience specific Business Performance Overview", "filters": [], "properties": {"dateFilterOptions": [], "tz": "UTC"}}