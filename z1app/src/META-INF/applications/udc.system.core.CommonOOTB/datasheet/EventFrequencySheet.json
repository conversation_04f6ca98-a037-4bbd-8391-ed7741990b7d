{"axes": [{"id": "date", "src": "date/*", "props": {"showAs": "date", "useDateTicks": true}}], "control": {"chartType": "<PERSON><PERSON><PERSON>", "props": {"curveType": "none", "vAxis": {"format": "short", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "viewWindowMode": "explicit", "minValue": 0, "viewWindow": {"min": 0}, "minorGridlines": {"count": 1}}, "hAxis": {"format": "MMM dd", "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}, "minorGridlines": {"count": 0}, "gridlines": {"color": "none"}}, "legend": {"position": "top", "alignment": "end", "maxLines": 2, "textStyle": {"color": "#595959", "fontSize": 12, "fontName": "Roboto"}}, "height": "400", "chartArea": {"width": "85%"}}}, "exec": {"uri": "c3/analytics/query?id=totalEventsPerDay&fromDate=${firstDayOfMonth}&toDate=${lastDayOfMonth}&eventName=${eventName}", "process": {"totalEventsPerDayForSelectedMonth": {"mergeKey": "${date}", "assign": [{"field": "date", "assignTo": "date"}, {"field": "totalEvents", "assignTo": "count"}]}}}, "dataPoints": [{"id": "count", "label": "Event Count", "method": "udc.system.aggregation:sum", "src": "frequency"}], "aggregate": "date", "description": "", "filters": []}