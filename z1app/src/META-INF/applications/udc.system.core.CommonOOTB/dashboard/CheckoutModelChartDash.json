{"description": "", "sheets": ["udc.system.core.CommonOOTB:SessionsByModelChartSheet", "udc.system.core.CommonOOTB:CheckoutsByModelChartSheet"], "page": {"context": [{"name": "uriFFlag", "type": "system:string"}, {"name": "uriIdSessByModel", "type": "system:string"}, {"name": "uriIdChkByModel", "type": "system:string"}, {"name": "modelId", "type": "system:string"}, {"name": "fromDate", "type": "system:string"}, {"name": "toDate", "type": "system:string"}, {"name": "period", "type": "system:string"}], "layout": "single", "model": {"execute": [{"id": "querySessions", "object": "udc.system.core.CommonOOTB:SessionsByModelChartSheet", "staticData": true}, {"id": "queryChk", "object": "udc.system.core.CommonOOTB:CheckoutsByModelChartSheet", "staticData": true}]}, "bindings": {"chart": [{"id": "viewSessionsChart", "executes": "querySessions"}, {"id": "viewChkRateChart", "executes": "queryChk"}]}, "view": [{"id": "main", "attach": "main", "templateUrl": "c3/desktop/page/templates/insights/CheckoutDashboardTemplate.html", "control": [{"id": "sessionsChartCtrl", "attach": "<PERSON><PERSON><PERSON>", "binds": "viewSessionsChart", "type": "udc.system.chart"}, {"id": "chkRateChartCtrl", "attach": "chkRateChart", "binds": "viewChkRateChart", "type": "udc.system.chart"}]}]}, "properties": {"dateFilterOptions": [], "tz": "UTC"}}