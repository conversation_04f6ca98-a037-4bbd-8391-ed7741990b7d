{"description": "", "sheets": ["udc.system.core.CommonOOTB:z1bp_uniqueVisitors", "udc.system.core.CommonOOTB:z1bp_conversionRate", "udc.system.core.CommonOOTB:z1bp_testValues", "udc.system.core.CommonOOTB:z1bp_rpv", "udc.system.core.CommonOOTB:z1bp_aov"], "templates": {"main": "<div class='c3_flex_1 c3_cols_nowrap c3_genericChartTpl' style='width:100%;height:100%'> <style>.card{height: auto; border-radius: 4px; background-color: #fff; color: #252422; margin-bottom: 20px; position: relative; border: 1px solid #DFDFDF; -webkit-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease; -moz-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease; -o-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease; -ms-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease; transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease}.card-info{margin-bottom: 0}.card-hidden{border: none; box-shadow: none}.card .card-body{padding: 15px 15px 10px 15px}.card .card-body.table-full-width{padding-left: 0; padding-right: 0}.card .card-header{padding: 15px 15px 0; border: 0}.card .card-header:not([data-background-color]){background-color: transparent}.card .card-header .card-title{margin: 16px 0px 0px 16px; font-size: 18px}.card .map{border-radius: 3px}.card .map.map-big{height: 400px}.card[data-background-color=orange]{background-color: #51cbce}.card[data-background-color=orange] .card-header{background-color: #51cbce}.card[data-background-color=orange] .card-footer .stats{color: #fff}.card[data-background-color=red]{background-color: #ef8157}.card[data-background-color=yellow]{background-color: #fbc658}.card[data-background-color=blue]{background-color: #51bcdb}.card[data-background-color=green]{background-color: #6bd098}.card .image{overflow: hidden; height: 200px; position: relative}.card .avatar{width: 30px; height: 30px; overflow: hidden; border-radius: 50%; margin-bottom: 15px}.card .numbers{font-size: 2em}.card .big-title{font-size: 12px; text-align: center; font-weight: 500; padding-bottom: 15px}.card label{font-size: .8571em; margin-bottom: 5px; color: #9a9a9a}.card .card-footer{background-color: transparent; border: 0}.card .card-footer .stats i{margin-right: 5px; position: relative; top: 0; color: #66615b}.card .card-footer .btn{margin: 0}.card.card-plain{background-color: transparent; box-shadow: none; border-radius: 0}.card.card-plain .card-body{padding-left: 5px; padding-right: 5px}.card.card-plain img{border-radius: 12px}.card-plain{background: 0 0; box-shadow: none}.card-plain .card-footer, .card-plain .card-header{margin-left: 0; margin-right: 0; background-color: transparent}.card-plain:not(.card-subcategories).card-body{padding-left: 0; padding-right: 0}.card-chart .card-header .card-title{margin-top: 10px; margin-bottom: 0}.card-chart .card-header .card-category{margin-bottom: 5px}.card-chart .table{margin-bottom: 0}.card-chart .table td{border-top: none; border-bottom: 1px solid #e9ecef}.card-chart .card-progress{margin-top: 30px}.card-chart .chart-area{height: 190px; width: calc(100% + 30px); margin-left: -15px; margin-right: -15px}.card-chart .card-footer{margin-top: 15px}.card-chart .card-footer .stats{color: #9a9a9a}.card-chart .dropdown{position: absolute; right: 20px; top: 20px}.card-chart .dropdown .btn{margin: 0}.card-user .image{height: 130px}.card-user .image img{border-radius: 12px}.card-user .author{text-align: center; text-transform: none; margin-top: -77px}.card-user .author a+p.description{margin-top: -7px}.card-user .avatar{width: 124px; height: 124px; border: 1px solid #fff; position: relative}.card-user .card-body{min-height: 240px}.card-user hr{margin: 5px 15px 15px}.card-user .card-body+.card-footer{padding-top: 0}.card-user .card-footer h5{font-size: 1.25em; margin-bottom: 0}.card-user .button-container{margin-bottom: 6px; text-align: center}.map{height: 500px}.card-stats .card-body{padding: 15px 15px 0}.card-stats .card-body .numbers{text-align: right; font-size: 2em}.card-stats .card-body .numbers p{margin-bottom: 0}.card-stats .card-body .numbers .card-category{color: #9a9a9a; font-size: 16px; line-height: 1.4em}.card-stats .card-footer{padding: 0 15px 15px}.card-stats .card-footer .stats{color: #9a9a9a}.card-stats .card-footer hr{margin-top: 10px; margin-bottom: 15px}.card-stats .icon-big{font-size: 3em; min-height: 64px}.card-stats .icon-big i{line-height: 59px}.container{padding-left: 10px}.card-title{font-size: 1em}.iconbox{margin-top: 0; padding: 25px}.icon-circle{font-size: 120%; font-style: normal; cursor: pointer; color: #979797; background: #e9e9e9; padding: 9px 10px; -webkit-border-radius: 19px; -moz-border-radius: 19px; border-radius: 19px}h4.figures{font-size: 2.2em !important}.z_flex_center{display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap; -webkit-justify-content: center; justify-content: center; -webkit-align-items: center; align-items: center}.c3hide{display: none !important}.liftRow{display: grid; grid-gap: 10px; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); width: 95%; height: 150px}.liftCard{margin-top: 0; height: 120px}.twoPanelRow{display: grid; grid-template-columns: 4fr 1.5fr; grid-gap: 10px}.threePanelRow{display: grid; grid-template-columns: repeat(3, 1fr); grid-gap: 10px}.budgetNumbers{font-weight: 500; font-size: 34px; line-height: 44px; color: #141414}.budgetLbl{font-size: 16px; padding-bottom: 10px; line-height: 19px}</style> <div class='c3_cols_spacing_xx'> <div class='c3_rows_nowrap c3_rows_spacing_x c3_bpo_c1'> <div class='c3_flex_1 c3_rows_nowrap_center card card-info'> <div class='c3_flex_1 c3_cols_nowrap_center card-body c3_cols_spacing c3_align_center'> <div class='budgetNumbers c3_rows_nowrap'> <div data-dojo-attach-point='conversionRateLiftAll'>0.00</div></div><div class='budgetLbl'>Conversion Rate Lift</div></div></div><div class='c3_flex_1 c3_rows_nowrap_center card card-info'> <div class='c3_flex_1 c3_cols_nowrap_center card-body c3_cols_spacing c3_align_center'> <div class='budgetNumbers c3_rows_nowrap'> <div data-dojo-attach-point='rpvLiftAll'>0.00</div></div><div class='budgetLbl'>Revenue Per Visit Lift</div></div></div><div class='c3_flex_1 c3_rows_nowrap_center card card-info'> <div class='c3_flex_1 c3_cols_nowrap_center card-body c3_cols_spacing c3_align_center'> <div class='budgetNumbers c3_rows_nowrap'> <div data-dojo-attach-point='aovLiftAll'>0.00</div></div><div class='budgetLbl'>Average Order Value Lift</div></div></div><div class='c3_flex_1 c3_rows_nowrap_center card card-info'> <div class='c3_flex_1 c3_cols_nowrap_center card-body c3_cols_spacing c3_align_center'> <div class='budgetNumbers c3_rows_nowrap'> <div data-dojo-attach-point='incRev'>0</div></div><div class='budgetLbl'>Incremental Revenue</div></div></div></div><div class='c3_bpo_c2'> <div class='card'> <div class='card-header'> <h5 class='card-title'>Unique Visitors</h5> </div><div class='card-body'> <div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='uniqueVisitorsChart'></div></div><div class='card-footer'></div></div><div class='threePanelRow'> <div class='leftSide'> <div class='card'> <div class='card-header'> <h5 class='card-title'>Conversion Rate</h5> </div><div class='card-body'> <div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='conversionRateChart'></div></div><div class='card-footer'></div></div></div><div class='middle'> <div class='card'> <div class='card-header'> <h5 class='card-title'>Revenue Per Visit</h5> </div><div class='card-body'> <div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='rpvChart'></div></div><div class='card-footer'></div></div></div><div class='rightSide'> <div class='card'> <div class='card-header'> <h5 class='card-title'>Avg. Order Value</h5> </div><div class='card-body'> <div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='aovChart'></div></div><div class='card-footer'></div></div></div></div><div class='c3hide' data-dojo-attach-point='chartPlaceHolder'></div></div></div></div>"}, "templateAttachPoints": {"main": ["conversionRateLiftAll", "rpvLiftAll", "aovLiftAll", "incRev", "uniqueVisitorsChart", "conversionRateChart", "rpvChart", "aovChart", "chartPlaceHolder"]}, "page": {"context": [{"name": "journeyId", "type": "system:string"}, {"name": "fromDate", "type": "system:string"}, {"name": "toDate", "type": "system:string"}], "layout": "single", "model": {"execute": [{"id": "queryUniqueVisitors", "object": "udc.system.core.CommonOOTB:z1bp_uniqueVisitors", "staticData": true}, {"id": "queryConversionRate", "object": "udc.system.core.CommonOOTB:z1bp_conversionRate", "staticData": true}, {"id": "queryValuesForTest", "object": "udc.system.core.CommonOOTB:z1bp_testValues", "staticData": true}, {"id": "queryRPV", "object": "udc.system.core.CommonOOTB:z1bp_rpv", "staticData": true}, {"id": "queryAOV", "object": "udc.system.core.CommonOOTB:z1bp_aov", "staticData": true}]}, "bindings": {"chart": [{"id": "viewUniqueVisitorsChart", "executes": "queryUniqueVisitors"}, {"id": "viewConversionRateChart", "executes": "queryConversionRate"}, {"id": "calcValuesForTestChart", "executes": "queryValuesForTest"}, {"id": "viewRpvChart", "executes": "queryRPV"}, {"id": "viewAovChart", "executes": "queryAOV"}], "value": [{"id": "conversionRateLiftAll", "compute": {"aggregate": "SUM", "field": "conversionRateLiftAll"}, "listenTo": "calcValuesForTestChart"}, {"id": "rpvLiftAll", "compute": {"aggregate": "SUM", "field": "rpvLiftAll"}, "listenTo": "calcValuesForTestChart"}, {"id": "aovLiftAll", "compute": {"aggregate": "SUM", "field": "aovLiftAll"}, "listenTo": "calcValuesForTestChart"}, {"id": "incrementalRevenueAll", "compute": {"aggregate": "SUM", "field": "incrementalRevenueAll"}, "listenTo": "calcValuesForTestChart"}]}, "view": [{"id": "main", "attach": "main", "template": "main", "control": [{"id": "uniqueVisitorsChartCtrl", "attach": "uniqueVisitorsChart", "binds": "viewUniqueVisitorsChart", "type": "udc.system.chart"}, {"id": "conversionRateChartCtrl", "attach": "conversionRateChart", "binds": "viewConversionRateChart", "type": "udc.system.chart"}, {"id": "rpvChartCtrl", "attach": "rpvChart", "binds": "viewRpvChart", "type": "udc.system.chart"}, {"id": "aovChartCtrl", "attach": "aovChart", "binds": "viewAovChart", "type": "udc.system.chart"}, {"id": "conversionRateLiftAllCtrl", "attach": "conversionRateLiftAll", "binds": "conversionRateLiftAll", "type": "udc.system.value", "format": ".2n", "fragment": "${data}%"}, {"id": "rpvLiftAllCtrl", "attach": "rpvLiftAll", "binds": "rpvLiftAll", "type": "udc.system.value", "format": ".2n", "fragment": "${data}%"}, {"id": "aovLiftAllCtrl", "attach": "aovLiftAll", "binds": "aovLiftAll", "type": "udc.system.value", "format": ".2n", "fragment": "${data}%"}, {"id": "incRevenueCtrl", "attach": "incRev", "binds": "incrementalRevenueAll", "type": "udc.system.value", "format": ".0d", "fragment": "[[Number('${data}'.replaceAll(',','')) < 0 ? ('${data}'.replace('-','-$')) : '$${data}']]"}, {"id": "calcValuesForTestChartCtrl", "attach": "chartPlaceHolder", "binds": "calcValuesForTestChart", "type": "udc.system.chart"}]}]}, "properties": {"dateFilterOptions": ["last90Days", "last30Days", "last7Days", "previousMonth", "previousWeek", "yesterday", "thisQuarter", "thisMonth", "thisWeek", "today", "customDate"], "tz": "UTC"}}