{"description": "", "page": {"context": [{"name": "type", "type": "system:string"}, {"name": "yyyymmDate", "type": "system:string"}], "bindings": {"chart": [{"executes": "EventImpactMetrics", "id": "EventImpactMetrics"}], "value": [{"id": "EventImpactMetricsValue", "compute": {"aggregate": "SUM", "field": "views"}, "listenTo": "EventImpactMetrics"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "EventImpactMetrics", "object": "udc.system.core.CommonOOTB:EventImpactMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "eventImpactMetricsChart", "binds": "EventImpactMetrics", "id": "EventImpactMetrics", "type": "udc.system.chart"}, {"attach": "eventImpactValue", "id": "EventImpactMetricsValue", "type": "udc.system.value", "binds": "EventImpactMetricsValue"}], "id": "main", "templateUrl": "c3/desktop/page/templates/exploreData/edCharts/EventImpactChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:EventImpactMetric"]}