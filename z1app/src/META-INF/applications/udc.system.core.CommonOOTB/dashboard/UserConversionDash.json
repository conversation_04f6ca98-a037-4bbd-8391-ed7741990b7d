{"description": "", "page": {"context": [{"name": "month", "type": "system:string"}], "bindings": {"chart": [{"executes": "UserConversionMetrics", "id": "UserConversionMetrics"}], "value": [{"id": "UserConversionMetricsValue", "compute": {"aggregate": "SUM", "field": "views"}, "listenTo": "UserConversionMetrics"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "UserConversionMetrics", "object": "udc.system.core.CommonOOTB:UserConversionMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "userConversionMetricChart", "binds": "UserConversionMetrics", "id": "UserConversionMetrics", "type": "udc.system.chart"}, {"attach": "userConversionMetricValue", "id": "UserConversionMetricsValue", "type": "udc.system.value", "binds": "UserConversionMetricsValue"}], "id": "main", "templateUrl": "c3/desktop/page/templates/interactions/UserConversionMetricChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:UserConversionMetric"]}