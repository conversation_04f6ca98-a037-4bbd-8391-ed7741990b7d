{"description": "", "page": {"context": [{"name": "month", "type": "system:string"}], "bindings": {"chart": [{"executes": "UserEngagementMetrics", "id": "UserEngagementMetrics"}], "value": [{"id": "UserEngagementMetricsValue", "compute": {"aggregate": "SUM", "field": "views"}, "listenTo": "UserEngagementMetrics"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "UserEngagementMetrics", "object": "udc.system.core.CommonOOTB:UserEngagementMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "userEngagementMetricChart", "binds": "UserEngagementMetrics", "id": "UserEngagementMetrics", "type": "udc.system.chart"}, {"attach": "userEngagementMetricValue", "id": "UserEngagementMetricsValue", "type": "udc.system.value", "binds": "UserEngagementMetricsValue"}], "id": "main", "templateUrl": "c3/desktop/page/templates/interactions/UserEngagementMetricChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:UserEngagementMetric"]}