{"description": "", "page": {"context": [{"name": "uriId<PERSON>ess<PERSON>ev", "type": "system:string"}, {"name": "uriIdSessConv", "type": "system:string"}, {"name": "uriIdSessTrfShare", "type": "system:string"}, {"name": "uriIdSessTrfPi", "type": "system:string"}, {"name": "uriFFlag", "type": "system:string"}, {"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}, {"name": "breakdown", "type": "system:string"}], "bindings": {"chart": [{"executes": "SessionTrafficShare", "id": "SessionTrafficShare"}, {"executes": "SessionConversionRate", "id": "SessionConversionRate"}, {"executes": "SessionRevenue", "id": "SessionRevenue"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "SessionTrafficShare", "object": "udc.system.core.CommonOOTB:SessionTrafficShare", "staticData": true}, {"id": "SessionConversionRate", "object": "udc.system.core.CommonOOTB:SessionConversionRate", "staticData": true}, {"id": "SessionRevenue", "object": "udc.system.core.CommonOOTB:SessionRevenue", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "SessionTrafficShare", "id": "SessionTrafficShare ", "type": "udc.system.chart"}, {"attach": "_chartCont2", "binds": "SessionConversionRate", "id": "SessionConversionRate ", "type": "udc.system.chart"}, {"attach": "_chartCont3", "binds": "SessionRevenue", "id": "SessionRevenue ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/insights/SessionDepthChartTemplate.html"}]}, "sheets": ["udc.system.core.CommonOOTB:SessionTrafficShare", "udc.system.core.CommonOOTB:SessionConversionRate", "udc.system.core.CommonOOTB:SessionRevenue"]}