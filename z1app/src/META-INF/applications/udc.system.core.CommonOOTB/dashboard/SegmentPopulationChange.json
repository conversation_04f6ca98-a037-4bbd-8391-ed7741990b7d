{"description": "", "page": {"context": [], "bindings": {"chart": [{"executes": "SegPop", "id": "SegPop"}], "value": [{"id": "populationChange", "compute": {"aggregate": "CHANGE", "field": "population", "groupBy": "segmentId"}, "listenTo": "SegPop"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "SegPop", "object": "udc.system.core.CommonOOTB:SegmentPopulationChange", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "SegPop", "binds": "SegPop", "id": "SegPop", "type": "udc.system.chart"}, {"attach": "SegPop", "binds": "populationChange", "id": "populationChange", "type": "udc.system.value"}], "id": "main", "template": "main"}]}, "sheets": ["udc.system.core.CommonOOTB:SegmentPopulationChange"], "templateAttachPoints": {"main": ["SegPop"]}, "templates": {"main": "<div data-dojo-attach-point='SegPop'></div>"}}