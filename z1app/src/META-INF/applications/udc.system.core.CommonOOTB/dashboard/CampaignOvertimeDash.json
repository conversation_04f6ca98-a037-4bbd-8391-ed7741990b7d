{"description": "", "page": {"context": [{"name": "journeyId", "type": "system:string"}, {"name": "stepIndex", "type": "system:string"}], "bindings": {"chart": [{"executes": "OvertimeMetrics", "id": "OvertimeMetrics"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "OvertimeMetrics", "object": "udc.system.core.CommonOOTB:CampaignOvertimeMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "metricsOverTimeChart", "binds": "OvertimeMetrics", "id": "OvertimeMetrics", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/campaigns/CampaignOvertimeMetricChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:CampaignOvertimeMetric"]}