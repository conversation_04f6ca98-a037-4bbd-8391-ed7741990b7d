{"description": "", "page": {"context": [{"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}, {"name": "granular", "type": "system:string"}], "bindings": {"chart": [{"executes": "PromotionsMetricsRevenueDistribution", "id": "PromotionsMetricsRevenueDistribution"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "PromotionsMetricsRevenueDistribution", "object": "udc.system.core.CommonOOTB:PromotionsMetricsRevenueDistributionV2", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "PromotionsMetricsRevenueDistribution", "id": "PromotionsMetricsRevenueDistribution ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/home/<USER>/DailyMetricsChartTemplate.html"}]}, "sheets": ["udc.system.core.CommonOOTB:PromotionsMetricsRevenueDistributionV2"]}