{"description": "", "page": {"context": [{"name": "thisMonth", "type": "system:string"}, {"name": "nextMonth", "type": "system:string"}, {"name": "granular", "type": "system:string"}], "bindings": {"chart": [{"executes": "dailyActiveUserMetric", "id": "dailyActiveUserMetric"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "dailyActiveUserMetric", "object": "udc.system.core.CommonOOTB:DailyActiveUserMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "pageviewChart", "binds": "dailyActiveUserMetric", "id": "dailyActiveUserMetric", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/metrics/charts/DailyUserStatsChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:DailyActiveUserMetric"]}