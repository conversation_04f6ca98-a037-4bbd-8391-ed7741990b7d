{"description": "", "sheets": ["udc.system.core.CommonOOTB:SessionsByModelChartSheet", "udc.system.core.CommonOOTB:AddToCartsByModelChartSheet"], "page": {"context": [{"name": "uriFFlag", "type": "system:string"}, {"name": "uriIdSessByModel", "type": "system:string"}, {"name": "uriIdAtcByModel", "type": "system:string"}, {"name": "modelId", "type": "system:string"}, {"name": "fromDate", "type": "system:string"}, {"name": "toDate", "type": "system:string"}, {"name": "period", "type": "system:string"}], "layout": "single", "model": {"execute": [{"id": "querySessions", "object": "udc.system.core.CommonOOTB:SessionsByModelChartSheet", "staticData": true}, {"id": "queryAtc", "object": "udc.system.core.CommonOOTB:AddToCartsByModelChartSheet", "staticData": true}]}, "bindings": {"chart": [{"id": "viewSessionsChart", "executes": "querySessions"}, {"id": "viewAtcRateChart", "executes": "queryAtc"}]}, "view": [{"id": "main", "attach": "main", "templateUrl": "c3/desktop/page/templates/insights/AddToCartDashboardTemplate.html", "control": [{"id": "sessionsChartCtrl", "attach": "<PERSON><PERSON><PERSON>", "binds": "viewSessionsChart", "type": "udc.system.chart"}, {"id": "atcRateChartCtrl", "attach": "atcRateChart", "binds": "viewAtcRateChart", "type": "udc.system.chart"}]}]}, "properties": {"dateFilterOptions": [], "tz": "UTC"}}