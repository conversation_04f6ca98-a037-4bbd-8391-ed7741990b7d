{"description": "", "page": {"context": [{"name": "goalId", "type": "system:string"}], "bindings": {"chart": [{"executes": "ActionFreq", "id": "ActionFreq"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "ActionFreq", "object": "udc.system.core.CommonOOTB:ActionFreq", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "ActionFreq", "binds": "ActionFreq", "id": "ActionFreq", "type": "udc.system.chart"}], "id": "main", "template": "main"}]}, "sheets": ["udc.system.core.CommonOOTB:ActionFreq"], "templateAttachPoints": {"main": ["ActionFreq"]}, "templates": {"main": "<div style='width:100%;height:100%'><div style='width:100%;height:100%' data-dojo-attach-point='ActionFreq'  class='udc_dash_component_div'></div><div data-dojo-attach-point='w1/c2'  class='udc_dash_component_div'></div></div>"}}