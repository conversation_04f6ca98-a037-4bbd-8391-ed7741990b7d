{"description": "", "page": {"context": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "system:string"}, {"name": "uriId", "type": "system:string"}, {"name": "uriFFlag", "type": "system:string"}, {"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}], "bindings": {"chart": [{"executes": "DailyMetricsRevenueVisitor", "id": "DailyMetricsRevenueVisitor"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "DailyMetricsRevenueVisitor", "object": "udc.system.core.CommonOOTB:DailyMetricsRevenueVisitor", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "DailyMetricsRevenueVisitor", "id": "DailyMetricsRevenueVisitor ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/home/<USER>/DailyMetricsChartTemplate.html"}]}, "sheets": ["udc.system.core.CommonOOTB:DailyMetricsRevenueVisitor"]}