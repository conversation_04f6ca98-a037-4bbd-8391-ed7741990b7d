{"description": "", "page": {"context": [{"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}, {"name": "granular", "type": "system:string"}], "bindings": {"chart": [{"executes": "PromotionsMetricsDiscountsByPrediction", "id": "PromotionsMetricsDiscountsByPrediction"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "PromotionsMetricsDiscountsByPrediction", "object": "udc.system.core.CommonOOTB:PromotionsMetricsDiscountsByPredictionV2", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "PromotionsMetricsDiscountsByPrediction", "id": "PromotionsMetricsDiscountsByPrediction ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/home/<USER>/DailyMetricsChartTemplate.html"}]}, "sheets": ["udc.system.core.CommonOOTB:PromotionsMetricsDiscountsByPredictionV2"]}