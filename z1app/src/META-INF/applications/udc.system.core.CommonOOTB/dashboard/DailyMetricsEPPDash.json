{"description": "", "page": {"context": [{"name": "uriId", "type": "system:string"}, {"name": "uriFFlag", "type": "system:string"}, {"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}, {"name": "journeyId", "type": "system:string"}], "bindings": {"chart": [{"executes": "DailyMetricsEPP", "id": "DailyMetricsEPP"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "DailyMetricsEPP", "object": "udc.system.core.CommonOOTB:DailyMetricsEPP", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "DailyMetricsEPP", "id": "DailyMetricsEPP ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/home/<USER>/DailyMetricsChartTemplate.html"}]}, "sheets": ["udc.system.core.CommonOOTB:DailyMetricsEPP"]}