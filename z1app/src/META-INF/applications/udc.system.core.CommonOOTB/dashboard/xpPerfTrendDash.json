{"description": "", "sheets": ["udc.system.core.CommonOOTB:z1xp_sessionsPerDay", "udc.system.core.CommonOOTB:z1xp_revenuePerDay", "udc.system.core.CommonOOTB:z1xp_uniqueVisitorsPerDay", "udc.system.core.CommonOOTB:z1xp_conversionRatePerDay", "udc.system.core.CommonOOTB:z1xp_aovPerDay", "udc.system.core.CommonOOTB:z1xp_rpvPerDay", "udc.system.core.CommonOOTB:z1xp_sessProdViews", "udc.system.core.CommonOOTB:z1xp_sessSearch", "udc.system.core.CommonOOTB:z1xp_sessNonEmptyCart", "udc.system.core.CommonOOTB:z1xp_sessAddCart", "udc.system.core.CommonOOTB:z1xp_sessChkout", "udc.system.core.CommonOOTB:z1xp_prodViewEvent", "udc.system.core.CommonOOTB:z1xp_searchEvent", "udc.system.core.CommonOOTB:z1xp_purchaseEvent", "udc.system.core.CommonOOTB:z1xp_ratioCart", "udc.system.core.CommonOOTB:z1xp_ratioOrder", "udc.system.core.CommonOOTB:z1xp_offerApplied", "udc.system.core.CommonOOTB:z1xp_offerRedeemed"], "templates": {"main": "<div class='c3_flex_1 c3_cols_nowrap c3_genericChartTpl c3_chrtExpTrnd' style='width:100%;height:100%'><style>.containerParent {background-color: #fff;padding: 15px;border-radius: 4px}.card2 {height: auto;display: flex;align-items: center;flex: 1;color: #252422;position: relative;-webkit-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;-moz-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;-o-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;-ms-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease}.card-info {margin-bottom: 0}.card-hidden {border: none;box-shadow: none}.card2 .card-body {flex: 1;}.card2 .card-body.table-full-width {padding-left: 0;padding-right: 0;}.card-header {border: 0;padding: 0 15px 0 15px;white-space: nowrap;}.card-header:not([data-background-color]) {background-color: transparent}.card-header .card-title {font-size: 16px;font-weight: 500;margin: 18px 0 -10px 0;}.card2 .map {border-radius: 3px}.card2 .map.map-big {height: 400px}.card2[data-background-color=orange] {background-color: #51cbce}.card2[data-background-color=orange] .card-header {background-color: #51cbce}.card2[data-background-color=orange] .card-footer .stats {color: #fff}.card2[data-background-color=red] {background-color: #ef8157}.card2[data-background-color=yellow] {background-color: #fbc658}.card2[data-background-color=blue] {background-color: #51bcdb}.card2[data-background-color=green] {background-color: #6bd098}.card2 .image {overflow: hidden;height: 200px;position: relative}.card2 .avatar {width: 30px;height: 30px;overflow: hidden;border-radius: 50%;margin-bottom: 15px}.card2 .numbers {font-size: 2em}.card2 .big-title {font-size: 12px;text-align: center;font-weight: 500}.card2 label {font-size: .8571em;margin-bottom: 5px;color: #9a9a9a}.card2 .card-footer {background-color: transparent;border: 0}.card2 .card-footer .stats i {margin-right: 5px;position: relative;top: 0;color: #66615b}.card2 .card-footer .btn {margin: 0}.card2.card-plain {background-color: transparent;box-shadow: none;border-radius: 0}.card2.card-plain .card-body {padding-left: 5px;padding-right: 5px}.card2.card-plain img {border-radius: 12px}.card-plain {background: 0 0;box-shadow: none}.card-plain .card-footer,.card-plain .card-header {margin-left: 0;margin-right: 0;background-color: transparent}.card-plain:not(.card-subcategories).card-body {padding-left: 0;padding-right: 0}.card-chart .card-header .card-title {margin-bottom: 0}.card-chart .card-header .card-category {margin-bottom: 5px}.card-chart .table {margin-bottom: 0}.card-chart .table td {border-top: none;border-bottom: 1px solid #e9ecef}.card-chart .card-progress {margin-top: 30px}.card-chart .chart-area {height: 190px;width: calc(100% + 30px);margin-left: -15px;margin-right: -15px}.card-chart .card-footer {margin-top: 15px}.card-chart .card-footer .stats {color: #9a9a9a}.card-chart .dropdown {position: absolute;right: 20px;top: 20px}.card-chart .dropdown .btn {margin: 0}.card-user .image {height: 130px}.card-user .image img {border-radius: 12px}.card-user .author {text-align: center;text-transform: none;margin-top: -77px}.card-user .author a+p.description {margin-top: -7px}.card-user .avatar {width: 124px;height: 124px;border: 1px solid #fff;position: relative}.card-user .card-body {min-height: 240px}.card-user hr {margin: 5px 15px 15px}.card-user .card-body+.card-footer {padding-top: 0}.card-user .card-footer h5 {font-size: 1.25em;margin-bottom: 0}.card-user .button-container {margin-bottom: 6px;text-align: center}.map {height: 500px}.card-stats .card-body {padding: 15px 15px 0}.card-stats .card-body .numbers {text-align: right;font-size: 2em}.card-stats .card-body .numbers p {margin-bottom: 0}.card-stats .card-body .numbers .card-category {color: #9a9a9a;font-size: 16px;line-height: 1.4em}.card-stats .card-footer {padding: 0 15px 15px}.card-stats .card-footer .stats {color: #9a9a9a}.card-stats .card-footer hr {margin-top: 10px;margin-bottom: 15px}.card-stats .icon-big {font-size: 3em;min-height: 64px}.card-stats .icon-big i {line-height: 59px}.container {padding-left: 10px}.card-title {font-size: 1em}.iconbox {margin-top: 0;padding: 25px}.icon-circle {font-size: 120%;font-style: normal;cursor: pointer;color: #979797;background: #e9e9e9;padding: 9px 10px;-webkit-border-radius: 19px;-moz-border-radius: 19px;border-radius: 19px}h4.figures {font-size: 2.2em!important}.z_flex_center {display: -webkit-flex;display: flex;-webkit-flex-flow: column nowrap;flex-flow: column nowrap;-webkit-justify-content: center;justify-content: center;-webkit-align-items: center;align-items: center}.c3hide {display: none!important}.liftRow {display: grid;grid-gap: 10px;grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));width: 95%;height: 150px}.liftCard {margin-top: 0;height: 120px}.twoPanelRow {display: grid;grid-template-columns: 4fr 1.5fr;grid-gap: 10px}.threePanelRow {display: grid;grid-template-columns: repeat(3, 1fr);grid-gap: 10px}.budgetNumbers {font-weight: 500;font-size: 34px;line-height: 44px;color: #141414}.budgetLbl {font-size: 16px;padding-bottom: 10px;line-height: 19px}</style><div class='c3_cols_spacing_xx'><div class='c3_bpo_c2 containerParent'><div  data-dojo-attach-point='sessionsChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Sessions</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='sessionsChart'></div></div></div></div><div data-dojo-attach-point='revenueChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Revenue</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='revenueChart'></div></div></div></div><div data-dojo-attach-point='conversionRateChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Conversion Rate</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='conversionRateChart'></div></div></div></div><div data-dojo-attach-point='aovChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Average Order Value</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='aovChart'></div></div></div></div><div data-dojo-attach-point='rpvChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Revenue Per Visit</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='rpvChart'></div></div></div></div><div data-dojo-attach-point='sessProdVwChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Sessions with Product Views</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='sessProdVwChart'></div></div></div></div><div data-dojo-attach-point='sessSearchChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Sessions with Search</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='sessSearchChart'></div></div></div></div><div data-dojo-attach-point='sessNonEmptyCartChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Sessions with Non Empty Cart</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='sessNonEmptyCartChart'></div></div></div></div><div data-dojo-attach-point='sessAddCartChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Session with Add to Carts</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='sessAddCartChart'></div></div></div></div><div data-dojo-attach-point='sessChkoutChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Sessions with Checkout</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='sessChkoutChart'></div></div></div></div><div data-dojo-attach-point='prodVwEventChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Product View Events</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='prodVwEventChart'></div></div></div></div><div data-dojo-attach-point='srchEventChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Search Events</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='srchEventChart'></div></div></div></div><div data-dojo-attach-point='purcEventChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Purchase Events</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='purcEventChart'></div></div></div></div><div data-dojo-attach-point='ratioCartChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Ratio of Carts:Sessions</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='ratioCartChart'></div></div></div></div><div data-dojo-attach-point='ratioOrderChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Ratio of Orders:Carts</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='ratioOrderChart'></div></div></div></div><div data-dojo-attach-point='offerAppliedChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Offers Applied</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='offerAppliedChart'></div></div></div></div><div data-dojo-attach-point='offerRedmChartCont'><div class='card-header c3_lv_text_s'><div class='card-title'>Offers Redeemed</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='offerRedmChart'></div></div></div></div><div class='c3hide' data-dojo-attach-point='chartPlaceHolder'></div></div></div></div>"}, "templateAttachPoints": {"main": ["<PERSON><PERSON><PERSON>", "revenueChart", "conversionRateChart", "aovChart", "rpvChart", "sessProdVwChart", "sessSearch<PERSON>hart", "sessNonEmptyCartChart", "sessAddCartChart", "sessChkoutChart", "prodVwEventChart", "srchEventChart", "purcEventChart", "ratioCartChart", "ratioOrderChart", "offerAppliedChart", "offerRedmChart", "chartPlaceHolder"]}, "page": {"context": [{"name": "journeyId", "type": "system:string"}, {"name": "fromDate", "type": "system:string"}, {"name": "toDate", "type": "system:string"}], "layout": "single", "model": {"execute": [{"id": "querySessions", "object": "udc.system.core.CommonOOTB:z1xp_sessionsPerDay", "staticData": true}, {"id": "queryRevenue", "object": "udc.system.core.CommonOOTB:z1xp_revenuePerDay", "staticData": true}, {"id": "queryCR", "object": "udc.system.core.CommonOOTB:z1xp_conversionRatePerDay", "staticData": true}, {"id": "queryAOV", "object": "udc.system.core.CommonOOTB:z1xp_aovPerDay", "staticData": true}, {"id": "queryRPV", "object": "udc.system.core.CommonOOTB:z1xp_rpvPerDay", "staticData": true}, {"id": "querySessProdViews", "object": "udc.system.core.CommonOOTB:z1xp_sessProdViews", "staticData": true}, {"id": "querySessSearch", "object": "udc.system.core.CommonOOTB:z1xp_sessSearch", "staticData": true}, {"id": "querySessNonEmptyCart", "object": "udc.system.core.CommonOOTB:z1xp_sessNonEmptyCart", "staticData": true}, {"id": "querySessAddCart", "object": "udc.system.core.CommonOOTB:z1xp_sessAddCart", "staticData": true}, {"id": "querySessCh<PERSON>ut", "object": "udc.system.core.CommonOOTB:z1xp_sessChkout", "staticData": true}, {"id": "queryProdVwEvent", "object": "udc.system.core.CommonOOTB:z1xp_prodViewEvent", "staticData": true}, {"id": "querySrchEvent", "object": "udc.system.core.CommonOOTB:z1xp_searchEvent", "staticData": true}, {"id": "queryPurcEvent", "object": "udc.system.core.CommonOOTB:z1xp_purchaseEvent", "staticData": true}, {"id": "queryRatioCart", "object": "udc.system.core.CommonOOTB:z1xp_ratioCart", "staticData": true}, {"id": "queryRatioOrder", "object": "udc.system.core.CommonOOTB:z1xp_ratioOrder", "staticData": true}, {"id": "queryOfferApplied", "object": "udc.system.core.CommonOOTB:z1xp_offerApplied", "staticData": true}, {"id": "queryOfferRedm", "object": "udc.system.core.CommonOOTB:z1xp_offerRedeemed", "staticData": true}]}, "bindings": {"chart": [{"id": "viewSessionsChart", "executes": "querySessions"}, {"id": "viewRevenueChart", "executes": "queryRevenue"}, {"id": "viewConversionRateChart", "executes": "queryCR"}, {"id": "viewRPVChart", "executes": "queryRPV"}, {"id": "viewAOVChart", "executes": "queryAOV"}, {"id": "viewSessProdVwChart", "executes": "querySessProdViews"}, {"id": "viewSessSearchChart", "executes": "querySessSearch"}, {"id": "viewSessNonEmptyCartChart", "executes": "querySessNonEmptyCart"}, {"id": "viewSessAddCartChart", "executes": "querySessAddCart"}, {"id": "viewSessChkoutChart", "executes": "querySessCh<PERSON>ut"}, {"id": "viewProdVwEventChart", "executes": "queryProdVwEvent"}, {"id": "viewSrchEventChart", "executes": "querySrchEvent"}, {"id": "viewPurcEventChart", "executes": "queryPurcEvent"}, {"id": "viewRatioCartChart", "executes": "queryRatioCart"}, {"id": "viewRatioOrderChart", "executes": "queryRatioOrder"}, {"id": "viewOfferAppliedChart", "executes": "queryOfferApplied"}, {"id": "viewOfferRedmChart", "executes": "queryOfferRedm"}]}, "view": [{"id": "main", "attach": "main", "template": "main", "control": [{"id": "sessionsChartCtrl", "attach": "<PERSON><PERSON><PERSON>", "binds": "viewSessionsChart", "type": "udc.system.chart"}, {"id": "revenueChartCtrl", "attach": "revenueChart", "binds": "viewRevenueChart", "type": "udc.system.chart"}, {"id": "conversionRateChartCtrl", "attach": "conversionRateChart", "binds": "viewConversionRateChart", "type": "udc.system.chart"}, {"id": "rpvChartCtrl", "attach": "rpvChart", "binds": "viewRPVChart", "type": "udc.system.chart"}, {"id": "aovChartCtrl", "attach": "aovChart", "binds": "viewAOVChart", "type": "udc.system.chart"}, {"id": "sessProdVwChartCtrl", "attach": "sessProdVwChart", "binds": "viewSessProdVwChart", "type": "udc.system.chart"}, {"id": "sessSearchChartCtrl", "attach": "sessSearch<PERSON>hart", "binds": "viewSessSearchChart", "type": "udc.system.chart"}, {"id": "sessNonEmptyCartChartCtrl", "attach": "sessNonEmptyCartChart", "binds": "viewSessNonEmptyCartChart", "type": "udc.system.chart"}, {"id": "sessAddCartChartCtrl", "attach": "sessAddCartChart", "binds": "viewSessAddCartChart", "type": "udc.system.chart"}, {"id": "sessChkoutChartCtrl", "attach": "sessChkoutChart", "binds": "viewSessChkoutChart", "type": "udc.system.chart"}, {"id": "prodVwEventChartCtrl", "attach": "prodVwEventChart", "binds": "viewProdVwEventChart", "type": "udc.system.chart"}, {"id": "srchEventChartCtrl", "attach": "srchEventChart", "binds": "viewSrchEventChart", "type": "udc.system.chart"}, {"id": "purcEventChartCtrl", "attach": "purcEventChart", "binds": "viewPurcEventChart", "type": "udc.system.chart"}, {"id": "ratioCartChartCtrl", "attach": "ratioCartChart", "binds": "viewRatioCartChart", "type": "udc.system.chart"}, {"id": "ratioOrderChartCtrl", "attach": "ratioOrderChart", "binds": "viewRatioOrderChart", "type": "udc.system.chart"}, {"id": "offerAppliedChartCtrl", "attach": "offerAppliedChart", "binds": "viewOfferAppliedChart", "type": "udc.system.chart"}, {"id": "offerRedmChartCtrl", "attach": "offerRedmChart", "binds": "viewOfferRedmChart", "type": "udc.system.chart"}, {"id": "calcValuesForTestChartCtrl", "attach": "chartPlaceHolder", "binds": "calcValuesForTestChart", "type": "udc.system.chart"}]}]}, "properties": {"dateFilterOptions": [], "tz": "UTC"}}