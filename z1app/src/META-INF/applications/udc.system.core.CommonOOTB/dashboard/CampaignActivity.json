{"description": "", "page": {"bindings": {"chart": [{"executes": "CampaignReach", "id": "CampaignReach"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "CampaignReach", "object": "udc.system.core.CommonOOTB:CampaignReach", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "campaign<PERSON>iew<PERSON>hart", "binds": "CampaignReach", "id": "CampaignReach", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/metrics/dashboard/CampaignViewChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:CampaignReach"]}