{"description": "", "page": {"context": [{"name": "eventName", "type": "system:string"}, {"name": "fromMonth", "type": "system:string"}, {"name": "toMonth", "type": "system:string"}], "bindings": {"chart": [{"executes": "EventFreq", "id": "EventFreqChart"}], "value": [{"id": "totalCount", "compute": {"aggregate": "SUM", "field": "count"}, "listenTo": "EventFreqChart"}, {"id": "dailyAvg", "compute": {"aggregate": "AVG", "round": true, "field": "count"}, "listenTo": "EventFreqChart"}, {"id": "minCount", "compute": {"aggregate": "MIN", "round": true, "field": "count"}, "listenTo": "EventFreqChart"}, {"id": "maxCount", "compute": {"aggregate": "MAX", "round": true, "field": "count"}, "listenTo": "EventFreqChart"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "EventFreq", "object": "udc.system.core.CommonOOTB:EventFrequencySheet", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "dailyCountChart", "binds": "EventFreqChart", "id": "EventFreqChartCtrl", "type": "udc.system.chart"}, {"attach": "event_total_count", "id": "total", "type": "udc.system.value", "binds": "totalCount"}, {"attach": "event_daily_avg", "id": "dailyAvg", "type": "udc.system.value", "binds": "dailyAvg"}, {"attach": "event_min_count", "id": "minCount", "type": "udc.system.value", "binds": "minCount"}, {"attach": "event_max_count", "id": "maxCount", "type": "udc.system.value", "binds": "maxCount"}], "id": "main", "templateUrl": "c3/desktop/page/templates/developer/EventFrequencyDashTemplate.html"}]}, "properties": {"refresh": "10S"}, "sheets": ["udc.system.core.CommonOOTB:EventFrequencySheet"]}