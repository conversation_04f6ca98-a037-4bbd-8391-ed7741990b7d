{"description": "", "page": {"context": [{"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}, {"name": "journeyIds", "type": "system:string"}], "bindings": {"chart": [{"executes": "TargetUsersActExp", "id": "TargetUsersActExp"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "TargetUsersActExp", "object": "udc.system.core.CommonOOTB:TargetUsersActExp", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "TargetUsersActExp", "id": "TargetUsersActExp ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/metrics/dashboard/insights/TriggeredExperiencesChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:TargetUsersActExp"]}