{"description": "", "page": {"context": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "system:string"}, {"name": "uriId", "type": "system:string"}, {"name": "uriFFlag", "type": "system:string"}, {"name": "startDate", "type": "system:string"}, {"name": "endDate", "type": "system:string"}, {"name": "journeyId", "type": "system:string"}], "bindings": {"chart": [{"executes": "InsTriggeredExpsConverRate", "id": "InsTriggeredExpsConverRate"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "InsTriggeredExpsConverRate", "object": "udc.system.core.CommonOOTB:InsTriggeredExpsConverRate", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "_chartCont", "binds": "InsTriggeredExpsConverRate", "id": "InsTriggeredExpsConverRate ", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/metrics/dashboard/insights/TriggeredExperiencesChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:InsTriggeredExpsConverRate"]}