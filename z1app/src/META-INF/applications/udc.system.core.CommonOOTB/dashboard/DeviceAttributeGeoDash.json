{"description": "", "page": {"context": [{"name": "attr_name", "type": "system:string"}, {"name": "sdk", "type": "system:string"}], "bindings": {"chart": [{"executes": "deviceAttributeMetric", "id": "deviceAttributeMetric"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "deviceAttributeMetric", "object": "udc.system.core.CommonOOTB:DeviceAttributeGeoMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "deviceAttributeChart", "binds": "deviceAttributeMetric", "id": "deviceAttributeMetric", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/metrics/charts/DeviceAttributeChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:DeviceAttributeGeoMetric"]}