{"description": "", "sheets": ["udc.system.core.CommonOOTB:z1xp_totalItemsOrderedPerDay"], "templates": {"main": "<div class='c3_flex_1 c3_cols_nowrap c3_genericChartTpl c3_chrtExpTrnd' style='width:100%;height:100%'><style>.containerParent {background-color: #fff;padding: 15px;border-radius: 4px}.card2 {height: auto;display: flex;align-items: center;flex: 1;color: #252422;position: relative;-webkit-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;-moz-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;-o-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;-ms-transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease;transition: transform .3s cubic-bezier(.34, 2, .6, 1), box-shadow .2s ease}.card-info {margin-bottom: 0}.card-hidden {border: none;box-shadow: none}.card2 .card-body {flex: 1;}.card2 .card-body.table-full-width {padding-left: 0;padding-right: 0;}.card-header {border: 0;padding: 0 15px 0 15px;white-space: nowrap;}.card-header:not([data-background-color]) {background-color: transparent}.card-header .card-title {font-size: 16px;font-weight: 500;margin: 18px 0 -10px 0;}.card2 .map {border-radius: 3px}.card2 .map.map-big {height: 400px}.card2[data-background-color=orange] {background-color: #51cbce}.card2[data-background-color=orange] .card-header {background-color: #51cbce}.card2[data-background-color=orange] .card-footer .stats {color: #fff}.card2[data-background-color=red] {background-color: #ef8157}.card2[data-background-color=yellow] {background-color: #fbc658}.card2[data-background-color=blue] {background-color: #51bcdb}.card2[data-background-color=green] {background-color: #6bd098}.card2 .image {overflow: hidden;height: 200px;position: relative}.card2 .avatar {width: 30px;height: 30px;overflow: hidden;border-radius: 50%;margin-bottom: 15px}.card2 .numbers {font-size: 2em}.card2 .big-title {font-size: 12px;text-align: center;font-weight: 500}.card2 label {font-size: .8571em;margin-bottom: 5px;color: #9a9a9a}.card2 .card-footer {background-color: transparent;border: 0}.card2 .card-footer .stats i {margin-right: 5px;position: relative;top: 0;color: #66615b}.card2 .card-footer .btn {margin: 0}.card2.card-plain {background-color: transparent;box-shadow: none;border-radius: 0}.card2.card-plain .card-body {padding-left: 5px;padding-right: 5px}.card2.card-plain img {border-radius: 12px}.card-plain {background: 0 0;box-shadow: none}.card-plain .card-footer,.card-plain .card-header {margin-left: 0;margin-right: 0;background-color: transparent}.card-plain:not(.card-subcategories).card-body {padding-left: 0;padding-right: 0}.card-chart .card-header .card-title {margin-bottom: 0}.card-chart .card-header .card-category {margin-bottom: 5px}.card-chart .table {margin-bottom: 0}.card-chart .table td {border-top: none;border-bottom: 1px solid #e9ecef}.card-chart .card-progress {margin-top: 30px}.card-chart .chart-area {height: 190px;width: calc(100% + 30px);margin-left: -15px;margin-right: -15px}.card-chart .card-footer {margin-top: 15px}.card-chart .card-footer .stats {color: #9a9a9a}.card-chart .dropdown {position: absolute;right: 20px;top: 20px}.card-chart .dropdown .btn {margin: 0}.card-user .image {height: 130px}.card-user .image img {border-radius: 12px}.card-user .author {text-align: center;text-transform: none;margin-top: -77px}.card-user .author a+p.description {margin-top: -7px}.card-user .avatar {width: 124px;height: 124px;border: 1px solid #fff;position: relative}.card-user .card-body {min-height: 240px}.card-user hr {margin: 5px 15px 15px}.card-user .card-body+.card-footer {padding-top: 0}.card-user .card-footer h5 {font-size: 1.25em;margin-bottom: 0}.card-user .button-container {margin-bottom: 6px;text-align: center}.map {height: 500px}.card-stats .card-body {padding: 15px 15px 0}.card-stats .card-body .numbers {text-align: right;font-size: 2em}.card-stats .card-body .numbers p {margin-bottom: 0}.card-stats .card-body .numbers .card-category {color: #9a9a9a;font-size: 16px;line-height: 1.4em}.card-stats .card-footer {padding: 0 15px 15px}.card-stats .card-footer .stats {color: #9a9a9a}.card-stats .card-footer hr {margin-top: 10px;margin-bottom: 15px}.card-stats .icon-big {font-size: 3em;min-height: 64px}.card-stats .icon-big i {line-height: 59px}.container {padding-left: 10px}.card-title {font-size: 1em}.iconbox {margin-top: 0;padding: 25px}.icon-circle {font-size: 120%;font-style: normal;cursor: pointer;color: #979797;background: #e9e9e9;padding: 9px 10px;-webkit-border-radius: 19px;-moz-border-radius: 19px;border-radius: 19px}h4.figures {font-size: 2.2em!important}.z_flex_center {display: -webkit-flex;display: flex;-webkit-flex-flow: column nowrap;flex-flow: column nowrap;-webkit-justify-content: center;justify-content: center;-webkit-align-items: center;align-items: center}.c3hide {display: none!important}.liftRow {display: grid;grid-gap: 10px;grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));width: 95%;height: 150px}.liftCard {margin-top: 0;height: 120px}.twoPanelRow {display: grid;grid-template-columns: 4fr 1.5fr;grid-gap: 10px}</style><div class='c3_cols_spacing_xx'><div class='c3_bpo_c2 containerParent'><div  data-dojo-attach-point='itemsOrderedChartCont'><div class='card-header c3_lv_text_s' data-dojo-attach-point='itemsOrderedChartTitle'><div class='card-title'>Items Purchased</div></div><div class='card2'><div class='card-body'><div class='c3_dash_component_div c3_genericChartTplIn udc_dash_component_div' data-dojo-attach-point='itemsOrderedChart'></div></div></div></div><div class='c3hide' data-dojo-attach-point='chartPlaceHolder'></div></div></div></div>"}, "templateAttachPoints": {"main": ["items<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "page": {"context": [{"name": "uriId", "type": "system:string"}, {"name": "uriFFlag", "type": "system:string"}, {"name": "journeyId", "type": "system:string"}, {"name": "fromDate", "type": "system:string"}, {"name": "toDate", "type": "system:string"}], "layout": "single", "model": {"execute": [{"id": "queryItemsOrdered", "object": "udc.system.core.CommonOOTB:z1xp_totalItemsOrderedPerDay", "staticData": true}]}, "bindings": {"chart": [{"id": "viewItemsOrderedChart", "executes": "queryItemsOrdered"}]}, "view": [{"id": "main", "attach": "main", "template": "main", "control": [{"id": "itemsOrderedChartCtrl", "attach": "items<PERSON><PERSON><PERSON><PERSON><PERSON>", "binds": "viewItemsOrderedChart", "type": "udc.system.chart"}]}]}, "properties": {"dateFilterOptions": [], "tz": "UTC"}}