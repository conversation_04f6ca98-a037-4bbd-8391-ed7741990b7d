{"description": "", "page": {"context": [{"name": "uriId", "type": "system:string"}, {"name": "uriFFlag", "type": "system:string"}, {"name": "monthStart", "type": "system:string"}, {"name": "monthEnd", "type": "system:string"}, {"name": "id", "type": "system:string"}], "bindings": {"chart": [{"executes": "SegmentEngagementMetrics", "id": "SegmentEngagementMetrics"}], "value": [{"id": "SegmentEngagementMetricsValue", "compute": {"aggregate": "SUM", "field": "views"}, "listenTo": "SegmentEngagementMetrics"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "SegmentEngagementMetrics", "object": "udc.system.core.CommonOOTB:SegmentEngagementMetric", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "segEngagementMetricChart", "binds": "SegmentEngagementMetrics", "id": "SegmentEngagementMetrics", "type": "udc.system.chart"}, {"attach": "segEngagementMetricValue", "id": "SegmentEngagementMetricsValue", "type": "udc.system.value", "binds": "SegmentEngagementMetricsValue"}], "id": "main", "templateUrl": "c3/desktop/page/templates/interactions/SegmentEngagementMetricChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:SegmentEngagementMetric"]}