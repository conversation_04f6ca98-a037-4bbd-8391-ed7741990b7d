{"description": "", "page": {"context": [{"name": "journeyId", "type": "system:string"}, {"name": "stage", "type": "system:string"}, {"name": "attributeName", "type": "system:string"}], "bindings": {"chart": [{"executes": "CampaignAttributeQuery", "id": "CampaignAttributeQuery"}]}, "id": {}, "layout": "single", "model": {"execute": [{"id": "CampaignAttributeQuery", "object": "udc.system.core.CommonOOTB:CampaignAttributesQuery", "staticData": true}]}, "version": "1.0", "view": [{"attach": "main", "control": [{"attach": "attribute<PERSON><PERSON>y", "binds": "CampaignAttributeQuery", "id": "CampaignAttributeQuery", "type": "udc.system.chart"}], "id": "main", "templateUrl": "c3/desktop/page/templates/campaigns/CampaignAttributeQueryChart.html"}]}, "sheets": ["udc.system.core.CommonOOTB:CampaignAttributesQuery"]}