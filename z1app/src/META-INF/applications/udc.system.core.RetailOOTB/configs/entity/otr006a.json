[{"name": "id", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "channel", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "rank", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "min", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "max", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}]