[{"name": "id", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "channel", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "offerName", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "cpv", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "threshold", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}, {"name": "offerValueType", "type": "system:string", "useAs": "system:custom", "ref": "system:sdk", "isEncrypted": false}]