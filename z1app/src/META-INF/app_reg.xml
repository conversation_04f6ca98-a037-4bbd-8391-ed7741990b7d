<?xml version="1.0" encoding="UTF-8"?>
<p:app_registry
  xmlns:p="http://udichi/core/application/def"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://udichi/core/application/def ../../../platform/schema/appreg.xsd ">
  <p:app
    id="udc.system.core.CommonOOTB"
    name="ZineOne common components" 
    description="Out-of-the-box definitions of commonly used segments, triggers, actions and dashboards."
    type="plugin"
    state="ready"
    tag="generic"
    category="ZineOne"
    publisherId="zineone.com"/>
    
  <p:app
    id="udc.system.core.RetailOOTB"
    name="ZineOne Retail Solutions" 
    description="Provides a collection of artifacts specific to retail business use cases."
    type="plugin"
    state="ready"
    tag="retail"
    category="retail"
    publisherId="zineone.com"/>
   
  <p:app
    id="udc.system.core.RetailTemplates"
    icon="res/add.png"
    name="ZineOne Retail Solutions" 
    description="Provides a collection of segments and campaigns to target retail business use cases."
    type="template"
    state="ready"
    tag="retail"
    category="retail"
    publisherId="zineone.com"/>
    
</p:app_registry>