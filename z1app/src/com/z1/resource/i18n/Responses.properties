resourceNotFound=Resource not found.
resourseDeletedSuccessfully=Successfully deleted the resource.
resourceDeletionFailed=Failed to delete the resource.
emailSent=An email has been sent to your account to follow further steps.
emailAlreadySent=An email has already been sent to your account.
noProfileFound=No profile found in the system for the given information.
userExists=Validation failure. 
userDoesNotExist=User doesn't exist.
userExistsSelectDifferentId=User already exist, please select a different ID.
userSuccessfullyRegistered=Successfully registered the user.
userRegistrationFailed=Failed to register the user.
emailIdInUserUseAnother=The email ID already exist, please select another ID.
userDataSuccessfullyUpdated=Successfully updated user.
userDataUpdateFailed=Failed to update user.
userDeletedSuccessfully=User is deleted successfully.
userDeletionFailed=Failed to delete the user.
failedCaptcha=Failed to validate CAPTCHA, please try again.
useCompanyEmailToSignUp=Please use your company email ID to sign up.
sysadminReservedWord="sysadmin" is a reserve word, please select a different ID.
sysanalystReservedWord="sysanalyst" is a reserve word, please select a different ID.
emailCantBeEmpty=Email field can't be empty.
accountIsActive=Account is already active.
registrationReceived=We have received your request to register for an account on ZineOne. Someone from our customer success team will get back to you shortly.
registrationWelcome=Thank you for registering an account with ZineOne. An email will be sent to you shortly for verification. Click on the link provided in the email and login to our application.
docIsNotImage=The document is not an image file.
invalidImageSubtype=Invalid image type. 
invalidImageSize=Image size can't be more than 2 MB.
badLogin=Login failed, please try again. 
noAccount=Login failed, please try again. 
inactiveAccount=Your account is deactivated. Please contact your account administrator.
tooManyRetries=Too many retries. Please contact your account administrator.
querySaveFailed=Failed to save queries.
queryRunFailed=Failed to run query.
queryUpdateFailed=Failed to update query.
mlFeatureSaveFailed=Failed to save feature.
uploadSuccess=Upload done!
uploadFailed=Upload failed!
uploadProcessing=Processing upload!
downloadSuccess=Download done!
downloadFailed=Download failed!
downloadProcessing=Processing download!
segmentRecountProcessing=Processing all segments recount!
segmentRecountFailed=Segments recount failed!
siProcessing=Processing schedule interaction!
siFailed=Schedule interaction processing failed!
siDisabled=Schedule interaction was not enabled!
segmentCreateOrUpdateSuccess=Segment created/updated!
segmentCreateOrUpdateFailed=Failed to create/update segment!
moduleAppSubscriptionFailed=Failed to subscribe module's App!
moduleAppUnsubscriptionFailed=Failed to unsubscribe module's App!
moduleAppSubscriptionSuccess=Module's App subscription success!
moduleAppUnsubscriptionSuccess=Module's App unsubscription success!
moduleAppPublishingFailed=Failed to publish module's App!
moduleAppUnpublishingFailed=Failed to unpublish module's App!
moduleAppPublishingSuccess=Module's App publishing success!
moduleAppUnpublishingSuccess=Module's App unpublishing success!
moduleLoadArtifactSuccess=Done fetching module's artifact payloads!
moduleLoadArtifactFailed=Failed fetching module's artifact payloads!
moduleAppGetDefinitionSuccess=Done fetching module's App definition!
moduleAppGetDefinitionFailed=Failed fetching module's App definition!
moduleCreateSuccess=Module instance created!
moduleCreateFailed=Failed to create module instance!
channelCreateOrUpdateFailed=Failed to save!
contentCreateOrUpdateFailed=Failed to save!
triggerCreateOrUpdateFailed=Failed to create or update trigger instance!
contentDeletionFailed=This Content is being used in one or more interaction. To delete the Content, first remove it from all interactions. Used by: 
uniqueCheckFailed=Name is not unique.
uniqueCheckSuccess=Name is unique.
invalidParams=Invalid or missing params.
instanceDeletionSuccessful=Instance deleted successfully.
instanceDeletionFailed=Instance deletion failed.
requestProcessingDone=Request processing done.
requestProcessingFailed=Request processing failed.
actionTemplateUpgraded=ActionTemplate upgraded successfully.
moduleUpgradeFailed=Failed to upgrade module instance!
moduleUpgradeDone=Upgrade module instances successfully.
ssoAuthFailed=Failed to authenticate via SSO.
ssoMissingUserName=The SSO configuration for user name is incorrect.
dashboardCreateFailed=Failed to create the dashboard. 
chartCreateFailed=Failed to create the chart.
configUnavailable=Config unavailable.
configExists=Config already exists.
invalidPayload=Invalid or missing payload.
configSaved=Configuration saved.
