/**css for the fullscreen content **/
.z1_fullscreen_content{
	width:100vw;
	height:100vh;
	border: 0px;
	background-color:#FFFFFF;
	overflow:auto;
}

@charset "utf-8";
/* Do not display fullscreen msg in landscape */
@media only screen and (orientation: landscape) {
	.z1_fs_box {
		display: none;
	}
}
/* Display FS msg in Potrait mode */
@media only screen and (orientation: portrait) {
	.z1_fs_box {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		display: -ms-flexbox;
		-ms-flex-direction: column;
		-ms-flex-wrap: none;
		-ms-flex-align: stretch;
		display: -webkit-flex;
		-webkit-flex-flow: column nowrap;
		-webkit-align-items: stretch;
		-webkit-justify-content: center;
		display: flex;
		flex-flow: column nowrap;
		align-items: stretch;
		justify-content: center;
	}
	.z1_fs_rowTop {
		-webkit-flex: 1;
		flex: 1;
	}
	.z1_fs_rowBottom {
		-webkit-flex: 1;
		flex: 1;
	}
	.z1_fs_rowMiddle {
		position: relative;
	}
	.z1_fs_rowTop img, .z1_fs_rowMiddle img, .z1_fs_rowBottom img {
		display: block;
		width: 100%;
	}
	.z1_fs_close {
		position: absolute;
		width:23px;
		height:23px;
		top:5%;
		right:4%;
		z-index:1000;
	}
}