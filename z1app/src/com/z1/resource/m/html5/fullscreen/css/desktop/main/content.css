/**css for the fullscreen container div . Slide from right to left animation.**/
.z1_fullscreen_container{
	position:fixed;
	top:15%;
	left: 100%; 
	right: -100%;
	bottom:0px;
	z-index:80;
	border:1px solid #d9d9d9;
	transition:all 1.5s;
	box-shadow: -6px -4px 8px rgba(241, 240, 240, 0.99);
}

/**css that gets applied for end transition state**/
.z1_end_transition_style {
	left:60%;
	right:0px;
}

/** css for fullscreen close button container. This div holds the sdk container's
    close button which is redundant with the new template. Hence hiding it permanently**/
.z1_fs_close_btn_container {
    display:none;
	cursor: pointer;
	float: right;
	right: 10px;
	position: fixed;
	z-index:120;
	width:40px;
	height:30px;
	text-align:center;
} 

/** hover style for the close button that appears on fs container. Won't work as the sdk
    close button is hidden */
div.z1_fs_close_btn_container:hover {
	border: 1px solid #BBB9B9;
	right:10px;
	padding: 6px 0px 0px 0px;
	background-color: #EFEDED;
} 

/**css to style the close button that shows up on fs **/
.z1_fs_close_btn{}

/* fs close */
.z1_fs_box .z1_fs_close {
	position: fixed;
	top: 8px;
	right: 8px;
	width: 23px;
	height: 23px;
	z-index: 4;
}

.z1_fs_close *{
	pointer-events: none;
}