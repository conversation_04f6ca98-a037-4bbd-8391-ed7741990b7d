/*css attached to the container div that holds the banner div*/
.z1_b_container {
	position: fixed; 
	padding: 0px; 
	z-index: 1000; 
	box-shadow: 0px 0px 25px 3px #888888;
	/** defualt white background color **/
	background-color: #FFFFFF;  
	/** Note: Transition animation and position are closely tied. Please change with caution **/
	transform: translate3d(0,0,0);
	-webkit-transform: translate3d(0,0,0); 
	-webkit-transition:all 1.5s;
	-moz-transition:all 1.5s;
	-o-transition:all 1.5s;
	transition:all 1.5s;  
	/** animation to get the banner to slide in to view based on position **/
	left: 0px;
	right: 0px;
	/** Position as specified by the user e.g. top/bottom/center to be substituted 
	    dynamically at runtime  **/
	{z1_position_val} 
}