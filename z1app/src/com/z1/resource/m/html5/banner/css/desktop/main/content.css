/*css attached to the container div that holds the banner div*/
.z1_b_container {
	max-width: 100%;
	position: fixed; 
	padding: 0px;
	z-index: 1000; 
	box-shadow: 0px 0px 25px 3px #888888;
	background-color: #FFFFFF;  
	/** Note: Transition animation and position are closely tied. 
	    Please change with caution **/
	transition: all 0.5s;  
	/** fly-by banner look for desktop */
	right:-80px;
	/** animation to get the banner to slide in to view based on position. Position can be 
	    e.g. top/bottom/center. It will be substituted dynamically at runtime**/
	{z1_position_val}  
}
.z1_b_container_ticker {
    width: 100%;
    height:30px;
    position: fixed;
    padding: 0px;
    z-index: 1000;
    box-shadow: 0px 0px 0px 0px #888888;
    background-color:rgba(0, 0, 0, 0.95)!important;
    transition: all 0.5s;
    border: 0 solid red;
    margin-left: auto;
    margin-right: auto;
    left: 0;
    padding-left:3%;
    padding-right:3%;
}