/** css custom classes for the banner content. Css for div holding the payload/content */
.z1_b_box {
    display: -webkit-flex; 
    -webkit-flex-flow: row nowrap; 
    -webkit-align-items: stretch; 
    -webkit-justify-content: flex-start; 
	display: -ms-flexbox; 
	-ms-flex-direction: row; 
	-ms-flex-wrap: none; 
	-ms-flex-align: stretch; 
	display: flex; 
	flex-flow: row nowrap; 
	align-items: stretch; 
	justify-content: flex-start; 
    overflow: hidden;
}    

/** Css pertaining to the look and feel of the banner div. Can be customized.*/    
.z1_b_box_style {
	padding: 15px 15px 15px 5px;
	-webkit-box-shadow: 0px 4px 7px 0px rgba(50, 50, 50, 0.49); 
	-moz-box-shadow: 0px 4px 7px 0px rgba(50, 50, 50, 0.49); 
	box-shadow: 0px 4px 7px 0px rgba(50, 50, 50, 0.49); 
	opacity: 0.95; 
	font-family: 'Helvetica Nueu', 'open sans', Helvetica; 
	font-size: 115%; 
	line-height: 22px;
	border:1px solid;
}

/** css for column containing the image content */   
.z1_b_c1 {
	display: -webkit-flex; 
	-webkit-flex-flow: column nowrap; 
	-webkit-align-items: center; 
	-webkit-justify-content: center; 
	display: -ms-flexbox; 
	-ms-flex-direction: column; 
	-ms-flex-wrap: none; 
	-ms-flex-align: center; 
	display: flex; 
	flex-flow: column nowrap; 
	align-items: center; 
	justify-content: center; 
	min-width:66px;
	max-width:66px;
	overflow: hidden;
}
.z1_b_c1 img {
	display: block;
	max-width: 100%;
	
}

/** css for the column containing the text/content data */
.z1_b_c2 {
    display: -webkit-flex;
	-webkit-flex-flow: row nowrap;
	-webkit-align-items: center;
	-webkit-justify-content: flex-start;
	display: -ms-flexbox;
	-ms-flex-direction: row;
	-ms-flex-wrap: none;
	-ms-flex-align: center;
	display: flex;
	flex-flow: row nowrap;
	align-items: center;
	justify-content: flex-start;
	overflow: hidden;
	flex: 1;
	-webkit-flex: 1;
	padding: 0 20px;
}

.z1_b_c2_1 {
}

.z1_b_c1 img {
	width:40px;
	height:40px;
}

/** css for the column containing the close button in the offer template */
.z1_b_c3{
	display: -webkit-flex;
	-webkit-flex-flow: column nowrap;
	-webkit-align-items: center;
	-webkit-justify-content: center;
	display: -ms-flexbox;
	-ms-flex-direction: column;
	-ms-flex-wrap: none;
	-ms-flex-align: center;
	display: flex;
	flex-flow: column nowrap;
	align-items: center;
	justify-content: center;
}

.z1_b_c3 img {
	cursor:pointer;
}


/** css for the column containing the offer code in the offer template */
.z1_b_c1_d1 {
    display: flex;
	flex-flow: column nowrap;
	justify-content: center;
}

/** css for the column containing the offer label in the offer template */
.z1_b_c1_d1_t1 {
    text-transform: uppercase;
	letter-spacing: 1px;
	font-size: 70%;
}

/** css for the column containing the offer code in the offer template */
.z1_b_c1_d1_t2 {
    font-weight: 600;
	text-transform: uppercase;
	font-size: 140%;
}

/** css for the column containing the offer date in the offer template */
.z1_b_c1_d1_t3 {
	font-size: 70%;
}

