/**css for the overlay that covers the entire screen**/	
.z1_modal_wrapper.overlay {
    position: fixed;
    z-index: 100;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, .7)
}
/** css for the modal dialog box that pops up **/
.z1_modal_window {
    background-color: #E2E2E2;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    box-shadow: 0 0 10px rgba(0, 0, 0, .5);
    max-width: 65%;
    min-width: 45%;
    max-height: 70%;
    position: relative
}

/** Layout classes **/
.z1_cols_nowrap_edge {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-align-items: stretch;
    align-items: stretch;
    overflow: hidden
}
.z1_flex_center_1 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center
}
.z1_cols_nowrap {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-align-items: stretch;
    align-items: stretch
}
.z1_rows_nowrap {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -webkit-justify-content: space-between;
    justify-content: space-between
}
.z1_flex_1 {
    -webkit-flex: 1;
    flex: 1
}

 
