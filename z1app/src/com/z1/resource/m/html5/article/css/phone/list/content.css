@charset "utf-8";
/* CSS Document */

/**css for the kblist container div . **/
.z1_article_container{
	display: block;
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 80;
}

/** css for the container (div tag) holding the list of articles  */
.z1_article_list_wrapper {
	padding: 0;
	margin: 0;
	overflow: auto;
	bottom: 2px;
	background: #FFFFFF;
	height:100%;
	width:100%;
}

/** css for the article header in both list and detail view **/
.z1_article_header {
	background: #ebebeb;
	padding: 9px 1% 9px 2%;
	font-size: 16px;
	color: #4e4e4e;
	font-weight: 600;
	clear: both;
	overflow: hidden;
	height: 20px;
}

/** css for close btn appearing in the header */
.z1_article_header .z1_article_close_btn {
    float: right;
    font-weight: 700;
    color: #a1acbc;
    text-decoration: none;
    cursor: pointer;
	background-image: url("https://d2p4r375zfkzm8.cloudfront.net/html5sdk/3.0.0/close_list.png");
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
}

/**css for back btn appearing in detail view **/
.z1_articles_backBtn{
	float: left;
	width: 20px;
	height: 20px;
	padding: 0 10 0 0;
	font-size: 14px;
	cursor: pointer;
	background-image: url("https://d2p4r375zfkzm8.cloudfront.net/html5sdk/3.0.0/arrow_left.png");
	background-repeat: no-repeat;
}

/**css to hide a dom element **/
.z1_hide {
	display:none;
}

/** hover css for close btn appearing in the header */
.z1_article_header .z1_article_close_btn:hover {
	color: #222;
}


/** style exclusively for appbox unread messages */
.z1_appbox_unread_title {
	color:blue !important;
}

/** style exclusively for appbox unread messages */
.z1_appbox_unread_date {
	color:blue !important;
}

.clearbox {
	clear: both;
	overflow: hidden;
}

/** css for article list row**/
.z1_list_row
{
    background-color: #ffffff;
	border-bottom: 2px solid #e5e5e5;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-align-items: stretch;
	align-items: stretch;
}

/** hover css when the user moves over the article list row **/
.z1_list_row:hover {
	background: #f7f7f7;
}

.z1_list_row h1 {
    font-size: 13px;
    padding: 0 0 4px 0;
    margin: 0 0 0 0;
    color: #08458e;
}

/** css for appbox left bar which has delete button**/
.z1_appbox_leftBar {
    position: relative;
    z-index: 999;
    border-left: 5px solid #b30000;
    background: #b30000;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
}

/** css for appbox left bar which has delete button which comes up on slide / mouse hover**/
.z1_appbox_leftBar .deleteBox {
    position: absolute;
    right: 0%;
    top: 0;
    left: -210%;
    width: 100%;
    height: 100%;
    background-color: #e41212;
    transition: all 0.5s ease;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
}

/** css for hover on the left drop down button for appbox **/
.z1_appbox_leftBar:hover .deleteBox {
	right:-60%;
	left:0%;
	width:50px;
	color:#fff;
	z-index:9999;
}

/** css for the dropdown & delete img **/
.z1_appbox_leftBar img {
    padding-left: 2px;
    padding-right: 8px;
}

/** css for the delete img **/
.z1_appbox_leftBar .deleteBox img {
    margin-top: 8px;
    margin-left: 12px;
	cursor:pointer;
}

/** css for list containing title and date**/
.z1_list_midContainer {
    -webkit-flex: 2 0 0;
    flex: 2 0 0;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
    padding-left: 5px;
	cursor: pointer;
}

/**css for date displayed in the list **/
.z1_list_midContainer .dateContainer {
    font-size: 10px;
    color: #666;
}

/** css for right arrow displayed in the list control **/
.z1_list_rightArrow {
    margin-right: 10px;
	cursor: pointer;
}

/** Css to make the article list and detail header flex **/
.z1_article_header_flex {
	display: -webkit-flex;
	display: flex;
	-webkit-flex-direction: row;
	flex-direction: row;
	-webkit-align-items: flex-start;
	align-items: flex-start;
	-webkit-align-items: center;
	align-items: center;
}

/** Total # of articles title flex css **/
.z1_articles_total_num_flex {
	-webkit-flex: 2 0 0;
	flex: 2 0 0;
}

