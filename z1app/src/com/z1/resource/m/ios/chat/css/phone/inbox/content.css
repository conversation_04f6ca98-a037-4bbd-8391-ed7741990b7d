@charset "utf-8";
/* CSS Document */
html {
    font-size: 100%;
    overflow-y: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-size: 13px;
    line-height: 1.231;
    background: #ebebeb;
}

body,button,input,select,textarea {
    font-family: 'Open Sans', sans-serif;
    color: #222;
}

img,object,embed,video {
    max-width: 99%;
}

.ie6 img {
    width: 99%;
}

.z1_chat_bodyContainer {
    background: #fff;
}

.clearbox {
    clear: both;
    overflow: hidden;
}

.z1_chat_list {
    padding: 5px 2%;
    border-bottom: 2px solid #ebebeb;
    background: #fff;
    position: relative;
}

.z1_chat_list .z1_chat_unread {
    position: absolute;
    top: 9px;
    left: 5px;
    cursor: pointer;
    height: 10px;
    width:10px;
}

.z1_chat_list .z1_icon_arrow {
    position: absolute;
    top: 33%;
    right: 10px;
    cursor: pointer;
    height: 10px;
    width: 10px;
}

.z1_chat_list .z1_icon_date_time {
    position: absolute;
    top: 5px;
    right: 35px;
    cursor: pointer;
    color: #9B9BA3;
    font-size: 10px;
    text-transform: uppercase;
}

.z1_chat_list .z1_icon_subject {
    color: #838389;
    font-size: 11px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 7px 5px 0;
    line-height: 19px;
    text-overflow: -o-ellipsis-lastline;
    margin: 0 0 0 22px;
}


.z1_chat_list:hover {
    background: #f7f7f7;
}

.z1_chat_list>h1 {
    padding: 3px 5px 6px 0;
    margin: 0 0 0 22px;
    color: #333333;
    font-size: 12px;
    font-weight: 600; /* width:92%; */
    border: 0 solid #000;
    width: 62%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.z1_chat_wrapper {
    padding: 0;
    margin: 0;
    overflow: auto;
    position: absolute;
    top: 0px;
    width: 100%;
    bottom: 2px;
    background: #ffffff;
}

@media only screen and (max-width: 480px) {
    .z1_chat_bodyContainer {
        min-height: 200px;
        margin-left: auto;
        margin-right: auto;˛
    }
}

@media only screen and (min-width: 481px) and (max-width: 768px) {
}

.z1_msg_txt_format {
  margin:0 10px 0 0 !important;
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  word-wrap: break-word;
  text-overflow: ellipsis;
  font-size: 11px;

}