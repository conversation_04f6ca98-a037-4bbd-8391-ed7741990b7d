<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport"
	content="width=device-width, initial-scale=1,user-scalable=no">
<meta name="description" content="">
<meta name="author" content="z1">
<link
	href='http://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,400,300,600,700,800'
	rel='stylesheet' type='text/css'>
<title>Inbox</title>

<style type="text/css">
${z1_css}
</style>


</head>

<body>

	<div class="z1_chat_bodyContainer">
		<div class="z1_chat_wrapper"></div>
	</div>


	<script type="text/javascript">
    function init(chatMsgs)
    {
      // Replace single quote      
      chatMsgs = chatMsgs.replace(/%27/g, "'");
      //the backend sends a string of all kb articles which we nees to parse to form a JSON object
      var chatData = JSON.parse(chatMsgs);
      //show the list of chat conversations
      this._displayChatInbox(chatData);

    };

    /**
     * On clicking a Chat Message, show its detailed contents
     **/
    function _viewChatMsg(id)
    {
      window.location.href = 'z1://viewChat?' + id;
    };

    /**
     * show the list of chat conversations
     **/
    function _displayChatInbox(chatData)
    {
      var wrapperDiv = document.querySelector(".z1_chat_wrapper");
      data = chatData;
      for (var i = 0; i < data.length; i++)
      {
        var divOuter = document.createElement("div");
        divOuter.classList.add("z1_chat_list");
        //add the kb id as an attribute
        divOuter.setAttribute("data-chat-id", data[i].id);

        //add click event for the whole div
        if (divOuter.addEventListener)
        { // all browsers except IE before version 9
          divOuter.addEventListener("click", function()
          {
            var id = this.getAttribute("data-chat-id");
            _viewChatMsg(id);
          }, false);
        }
        else
        {
          if (divOuter.attachEvent)
          { // IE before version 9
            divOuter.attachEvent("click", function()
            {
              var id = this.getAttribute("data-chat-id");
              _viewChatMsg(id);
            });
          }
        }

        //Append the unread img icon if a msg is not read 
        if (data[i].unread && data[i].unread != "0")
        {
          var divUnreadImgOuter = document.createElement("section");
          divUnreadImgOuter.classList.add("z1_chat_unread")
          var divUnreadImg = document.createElement("img");
          divUnreadImg.src = "/res/dot_blue.png";
          divUnreadImgOuter.appendChild(divUnreadImg);
          divOuter.appendChild(divUnreadImgOuter);
        }

        //Append the arrow img to show there is more content on click
        var divArrImgOuter = document.createElement("section");
        divArrImgOuter.classList.add("z1_icon_arrow")
        var divArrImg = document.createElement("img");
        divArrImg.src = "/res/icon_arrow2.png";
        divArrImgOuter.appendChild(divArrImg);
        divOuter.appendChild(divArrImgOuter);

        //get the update date of the chat msg
        var chatDate = document.createElement("section");
        chatDate.classList.add("z1_icon_date_time");
        var dt = _formatDate(data[i].lastUpdateTime);
        chatDate.appendChild(document.createTextNode(dt));
        divOuter.appendChild(chatDate);

        // get the customer name 
        var chatTitle = document.createElement("h1");
        chatTitle.appendChild(document.createTextNode(data[i].customerName));
        divOuter.appendChild(chatTitle);

        //get the chat msg subject
        var chatMsg = document.createElement("section");
        chatMsg.classList.add("z1_icon_subject");

        var preDiv = document.createElement("pre");
        preDiv.classList.add("z1_msg_txt_format");
        preDiv.appendChild(document.createTextNode(unescape(data[i].subject)));
        chatMsg.appendChild(preDiv);

        //chatMsg.appendChild(document.createTextNode(data[i].subject));
        divOuter.appendChild(chatMsg);

        //divOuter.appendChild(dvHead);
        wrapperDiv.appendChild(divOuter);
      }

    };

    function _formatDate(timeinMs)
    {
      var monthNames = ["January", "February", "March", "April", "May", "June", "July", "August",
        "September", "October", "November", "December"];

      var dt = new Date(parseInt(timeinMs));
      var h = dt.getHours();
      var m = dt.getMinutes();
      var s = dt.getSeconds();
      var ampm = h >= 12 ? 'PM' : 'AM';
      h = h % 12;
      h = h ? h : 12; // the hour '0' should be '12'
      m = m < 10 ? '0' + m : m;
      s = s < 10 ? '0' + s : s;

      var dtStr = monthNames[dt.getMonth()].substr(0, 3) + " " + dt.getDate() + ", " + h + ':'
        + m + " " + ampm;
      return dtStr;
    };
  </script>


</body>
</html>