@charset "utf-8";
/* Banner Content Box Style */
html {
	font-size: 100%;
	overflow: hidden;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}

body {
	margin: 0;
	font-size: 13px;
	line-height: 1.231;
	background: #FFFFFF;
}

body,button,input,select,textarea {
	font-family: 'Helvetica Neue','Open Sans', sans-serif;
	color: #222;
}

img,object,embed,video {
	display: block;
	max-width: 100%;
}

/** Layout Css for div holding the payload/content */
.z1_b_box {
    position:absolute;left:0;top:0;right:0;
    display: -webkit-flex; -webkit-flex-flow: row nowrap; -webkit-align-items: stretch; -webkit-justify-content: flex-start; 
	display: -ms-flexbox; -ms-flex-direction: row; -ms-flex-wrap: none; -ms-flex-align: stretch; 
	display: flex; flex-flow: row nowrap; align-items: stretch; justify-content: flex-start; 
    overflow: hidden;}
    
/** Css pertaining to the look and feel of the banner div. Can be customized.*/    
.z1_b_box_style {
	padding: 15px 15px 15px 5px;
	-webkit-box-shadow: 0px 4px 7px 0px rgba(50, 50, 50, 0.49); 
	-moz-box-shadow: 0px 4px 7px 0px rgba(50, 50, 50, 0.49); 
	box-shadow: 0px 4px 7px 0px rgba(50, 50, 50, 0.49); 
	opacity: 0.95; 
	font-family: 'Helvetica Nueu', 'open sans', Helvetica; 
	font-size: 115%; 
	line-height: 22px;
	border:1px solid;
}

    
.z1_b_c1 {
	display: -webkit-flex; -webkit-flex-flow: column nowrap; -webkit-align-items: center; -webkit-justify-content: center; 
	display: -ms-flexbox; -ms-flex-direction: column; -ms-flex-wrap: none; -ms-flex-align: center; 
	display: flex; flex-flow: column nowrap; align-items: center; justify-content: center; 
	min-width:66px;max-width:66px;overflow: hidden;
}
.z1_b_c2 {
	display: -webkit-flex; -webkit-flex-flow: row nowrap; -webkit-align-items: center; -webkit-justify-content: flex-start; 
	display: -ms-flexbox; -ms-flex-direction: row; -ms-flex-wrap: none; -ms-flex-align: center; 
	display: flex; flex-flow: row nowrap; align-items: center; justify-content: flex-start; 
    overflow: hidden;
    flex:1; -webkit-flex:1;
}
.z1_b_c2_1 {
}
.z1_b_c1 img {display:block;width:40px;height:40px;}