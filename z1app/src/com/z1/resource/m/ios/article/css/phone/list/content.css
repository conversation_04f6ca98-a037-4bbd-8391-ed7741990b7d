@charset "utf-8";
/* CSS Document */
html, body {
	background-color: #FFF;
	overflow-y: scroll;
}

body {
	font-family:'Helvetica Neue',Helvetica,Arial,sans-serif;
	font-size: 12px;
	font-weight: 400;
	margin: 0;
	padding: 8px 0 0;
	color: #222;
}

img, object, embed, video {
	max-width: 99%;
}

/* Typography Settings */
h1, h2, h3, h4, h5, h6 {
    font-weight: 400;
}
h1 { font-size: 14pt; }
h2 { font-size: 13pt; }
h3 { font-size: 12pt; }

/* KB Body container */
.z1_kb_bodyContainer {
    -webkit-touch-callout: none;
    -webkit-user-select: none; /* Disable selection/copy in UIWebView */
    position: absolute;
    top: 0;right: 0;bottom: 0;left: 0;
    overflow:hidden;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-align-items: stretch;
    align-items: stretch;
}

/* Article List Header */
.z1_kb_total_articles {
    font-size: 10pt;
    font-weight: 700;
    text-transform: uppercase;
    border-bottom: 2px solid #9A9A9A;
    padding: 4px;
}

/* Article List Container */
.z1_kb_article_wrapper {
    background-color: #EFEFEF;
    padding: 0 0 0 15px;
    margin: 0;
   -webkit-flex: 1;
    flex: 1;
    overflow-y: auto;
    width: 95%;
    width: -webkit-calc(100% - 15px);
    width: calc(100% - 15px);
}

/* Article List Item */
.z1_kb_article_list {
    position: relative;
    border-bottom: 1px solid #CACACA;
    padding: 14px 14px 14px 0px;
}

/* emulate native for hover, focus, active */
::-webkit-scrollbar {
    width: 0.2em;
   -webkit-appearance: none;
}
::-webkit-scrollbar-track {
   -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
    border-radius: 8px;
}
::-webkit-scrollbar-thumb {
    background-color: #FFF; 
    outline: 1px solid #7A7A7A; 
    border-radius: 8px;
}
.select {
    background: #7A7A7A;
    color: #CACACA;
    margin: 0 0 0 -15px; /* remove left padding of z1_kb_article_wrapper */
    padding: 14px 15px !important;       
}
.z1_kb_article_list:last-child {
  border-bottom: none;
}
.z1_kb_article_list h1 {
  font-size: 12pt;
  margin: 0;
  padding: 0;
}
.z1_kb_icon_arrow {
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translate(0, -45%) scale(0.6);
}

.z1_kb_btn_close {
	float:right;
}

.z1_kb_total_num {
	padding: 0px 0px 0px 4px;
}
/* Unused elements, hidden */
.z1_kb_date,
.z1_faq_btn_close {
  display: none;
}
