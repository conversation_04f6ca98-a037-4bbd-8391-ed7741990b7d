<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport"
  content="width=device-width, initial-scale=1,user-scalable=no">
<meta name="description" content="">
<meta name="author" content="z1">
<link rel="icon" href="../../favicon.ico">

<title>Support</title>

<style type="text/css">
${z1_css}
</style>


</head>

<body>
  <div class="z1_kb_bodyContainer">
    <div class="z1_kb_total_articles">
      <span class="z1_kb_total_num"></span>
      <section class="z1_kb_btn_close">
        <a href="#" onclick="_closeListView()"><img
          src="/res/close_3.png"></a>
      </section>
    </div>
    <div class="z1_kb_article_wrapper"></div>
  </div>
  
  <script type="text/javascript">
      var gKbList = [];
  
      function init(kbStr)
      {
        // Replace single quote      
        kbStr = kbStr.replace(/%27/g, "'");
        //the backend sends a string of all kb articles which we nees to parse to form a JSON object
        var kbData = JSON.parse(kbStr);
        // kbData contains -> listTile + items (as list of objects)
        var kbTitle = kbData["listTitle"];
        gKbList = kbData["items"];
        //show the kb header with the total docs retrieved
        this._updateKBLength(gKbList, kbTitle);
        //show the list of kb articles
        this._displayKBList(gKbList);
      };

      /**
       *Total kb articles found matching the relevant context and pushed by the action.
       **/
      function _updateKBLength(kbData, kbTitle)
      {
        if (kbTitle)
        {
          var header = document.querySelector(".z1_kb_total_num");
          header.innerHTML = kbTitle;
        }
        else if (kbData)
        {
          var header = document.querySelector(".z1_kb_total_num");
          header.innerHTML = kbData.length + " Articles found";
          console.log(kbData.length);
        }
      };

      /**
       * On clicking a KB Article, show its detailed contents
       **/
      function _viewKB(id)
      {
        var jsonStr = null;
        // Get the item for the selected ID
        for (var i = 0; i < gKbList.length; i++)
        {
          if (id === gKbList[i].id)
          {
            jsonStr = JSON.stringify(gKbList[i]);
          }
        }
        window.location.href = 'z1://viewKB?' + jsonStr;
      };

      /**
       *Click handler when the user hits the close button
       **/
      function _closeListView()
      {
        window.location.href = 'z1://closeListView';
      };

      /**
       * Show the list of KB articles
       **/
      function _displayKBList(listData)
      {
        var wrapperDiv = document.querySelector(".z1_kb_article_wrapper");
        wrapperDiv.innerHTML = "";
        var data = listData;
        for (var i = 0; i < data.length; i++)
        {
          var divOuter = document.createElement("div");
          divOuter.classList.add("z1_kb_article_list");
          //add the kb id as an attribute
          divOuter.setAttribute("data-kb-id", data[i].id);
          //add click event for the whole div
          if (divOuter.addEventListener)
          {
            // all browsers except IE before version 9
            divOuter.addEventListener("click", function()
            {
              var id = this.getAttribute("data-kb-id");
              _viewKB(id);
            }, false);
            
            divOuter.addEventListener("click", function()
              {
                var id = this.getAttribute("data-kb-id");
                _viewKB(id);
              }, false);
            
            divOuter.addEventListener("onscroll", function()
              {
                var self = this; 
                setTimeout(function(){ 
                  self.classList.remove("select"); 
                }, 1);
              
              }, false);
            divOuter.addEventListener("touchmove", function()
              {
                var self = this; 
                setTimeout(function(){ 
                  self.classList.remove("select"); 
                }, 1);
              
              }, false);
            divOuter.addEventListener("mousedown", function()
              {
              this.classList.add("select");
              }, false);
            divOuter.addEventListener("mouseup", function()
              {
                var self = this; 
                setTimeout(function(){ 
                  self.classList.remove("select"); 
                }, 1000);
                
              }, false);
            divOuter.addEventListener("touchstart", function()
              {
              this.classList.add("select");
              }, false);
            divOuter.addEventListener("touchend", function()
              {
                var self = this; 
                setTimeout(function(){ 
                  self.classList.remove("select"); 
                }, 1000);
                
              }, false);
            
          }
          else
          {
            if (divOuter.attachEvent)
            { // IE before version 9
              divOuter.attachEvent("click", function()
              {
                var id = this.getAttribute("data-kb-id");
                _viewKB(id);
              });
            }
          }
          //Append the img to show there is more content on click
          var divImgOuter = document.createElement("section");
          divImgOuter.classList.add("z1_kb_icon_arrow");
          var divImg = document.createElement("img");
          divImg.src = "/res/icon_arrow2.png";
          divImgOuter.appendChild(divImg);
          divOuter.appendChild(divImgOuter);

          // get the kb title
          var kbTitle = document.createElement("h1");
          kbTitle.appendChild(document.createTextNode(data[i].title));
          divOuter.appendChild(kbTitle);
          //get the creation date of the article
          var kbDate = document.createElement("section");
          kbDate.classList.add("z1_kb_date");
          kbDate.appendChild(document.createTextNode(data[i].creationDate));
          divOuter.appendChild(kbDate);

          // Create stars holder
          /*var kbRatingOuter = document.createElement("span");
          kbRatingOuter.classList.add("stars");
          //get the rating for the article
          var val = parseFloat(data[i].rating);
          var kbRatingInner = document.createElement("span");
          // Make sure that the value is in 0 - 5 range, multiply to get width
          var size = Math.max(0, (Math.min(5, val))) * 17 + "px";
          kbRatingInner.setAttribute("style", "width:"+size);
          kbRatingOuter.appendChild(kbRatingInner);
          divOuter.appendChild(kbRatingOuter);*/

          //divOuter.appendChild(dvHead);
          wrapperDiv.appendChild(divOuter);
        }

      };
    </script>

</body>

</html>