@charset "utf-8";
/* CSS Document */
html {
    font-size: 100%;
    overflow-y: hidden;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-size: 13px;
    line-height: 1.231;
    background: #ebebeb;
}

body, button, input, select, textarea {
    font-family: 'Helvetica Neue', 'Helvetica', 'Open Sans', sans-serif;
    color: #222;
}

img, object, embed, video {
    max-width: 99%;
}

::-webkit-scrollbar {
    width: 0.2em;
   -webkit-appearance: none;
}
 
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(f,f,f);
}
 
::-webkit-scrollbar-thumb {
  background-color: grey;
  outline: 1px solid grey;
}

.z1_kb_bodyContainer {
    background: #fff;
    overflow:auto;
   -webkit-touch-callout: none;
   -webkit-user-select: none; /* Disable selection/copy in UIWebView */ 
}

.z1_kb_total_articles {
    background: #ebebeb;
    padding: 9px 0;
    font-size: 14px;
    color: #4e4e4e;
    font-weight: 600;
    clear: both;
    overflow: hidden;
    position: absolute;
    top: 0px;
    width: 100%;
    height: 18px;
}

.z1_kb_total_num {
    padding-left: 6px;
}
 
.z1_kb_total_articles > h1 {
    color:${list_title_color};
    background-color: ${list_title_backgroundColor};
    font-size: ${list_title_font_size};
    font-style: ${list_title_font_style};
    font-weight: 400;
    clear: both;
}
 
.z1_kb_total_articles .z1_kb_btn_close {
    position: absolute;
    right: 5px;
    top: 7px;
    /* display:none; */
}

.z1_kb_total_articles .z1_kb_btn_close a {
    font-weight: 700;
    color: #a1acbc;
    text-decoration: none;
    font-size: 12px;
    padding: 2px 5px;
}

.z1_kb_total_articles .z1_kb_btn_close a:hover {
    color: #222;
}

.z1_kb_total_articles .z1_kb_date {
    color: #acacac;
    font-size: 15px;
    padding-bottom: 4px;
    text-transform: uppercase;
    font-weight: 400;
    float: left;
}

/* .z1_kb_total_articles .star_rating {
    color: #7a91af;
    font-size: 14px;
    padding-bottom: 4px;
    float: right;
}
*/
 
/* .clearbox {
    clear: both;
    overflow: hidden;
}
 */

.z1_kb_article_list {
    padding: 5px 2%;
    border-bottom: 2px solid #ebebeb;
    background: #fff;
    position: relative;
    background-color:${list_item_backgroundColor};
    border-bottom-color:${list_item_border_color};
}

.z1_kb_article_list .z1_kb_icon_arrow {
    position: absolute;
    top: 33%;
    right: 10px;
    cursor: pointer;
    height: 10px;
    width: 10px;
}

.select {
    background: #f7f7f7;
}


.z1_kb_article_list>h1 {
    padding: 3px 0 6px 0;
    margin: 0;
    color: #777777;
    font-size: 14px;
    font-weight: 600;
    width: 92%;
    border: 0 solid #000;
}

.z1_kb_article_list > h1 {
    color: ${list_item_color};
    font-size: ${list_item_font_size}; 
    font-style: ${list_item_font_style}; 
}

.z1_kb_date {
    color: #acacac;
    font-size: 12px;
    padding-bottom: 4px;
    text-transform: uppercase;
}

/* .star_rating {
    color: #7a91af;
    font-size: 14px;
    padding-bottom: 4px;
} */

/* .star_rating_deselect {
    color: #d3dae4;
} */

.z1_kb_article_wrapper {
    padding: 0;
    margin: 0;
    overflow: auto;
    position: absolute;
    top: 35px;
    width: 100%;
    bottom: 2px;
    background: #ffffff;
    background-color:${list_item_backgroundColor};
}

@media only screen and (max-width: 480px) {
    .z1_kb_bodyContainer {
        min-height: 200px;
        margin-left: auto;
        margin-right: auto;
    }
}

@media only screen and (min-width: 481px) and (max-width: 768px) {
}

span.stars, span.stars span {
    display: block;
    background: url(/res/star_rating.png) 0 -17px repeat-x;
    width: 84px;
    height: 17px;
}

span.stars span {
    background-position: 0 0;
}
