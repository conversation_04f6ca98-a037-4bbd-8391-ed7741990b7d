/** css for the alert content box **/ 
.confirmText {
    overflow: auto;
    padding: 0 12px 12px;
    text-align: center
}
.confirm a {
    text-decoration: none;
    color: #157dfb;
    height: 100%;
    padding-top: 6px;
    padding-bottom: 6px
}
.confirm span:nth-of-type(1) {
    border-right: 1px solid rgba(189, 189, 189, .64);
    left: 0;
    border-radius: 0 0 0 15px
}
.confirm span:nth-of-type(2) {
    right: 0;
    border-radius: 0 0 15px
}
.confirm span:focus,
.confirm span:hover {
    background: #EFEFEF
}
.confirm span:active {
    background: #D6D6D6
}
.confirm span {
    background: 0 0;
    border: none;
    color: #157dfb;
    height: 2rem;
    font-size: 1rem;
    text-align: center;
    bottom: 0;
    cursor: pointer
}
.z1_axn_btns_cont {
    border-top: 1px solid rgba(189, 189, 189, .64)
}

/** Layout classes **/
.z1_cols_nowrap_edge {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-align-items: stretch;
    align-items: stretch;
    overflow: hidden
}
.z1_flex_center_1 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center
}
.z1_cols_nowrap {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-align-items: stretch;
    align-items: stretch
}
.z1_rows_nowrap {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -webkit-justify-content: space-between;
    justify-content: space-between
}
.z1_flex_1 {
    -webkit-flex: 1;
    flex: 1
}
