 @charset "utf-8";
/* CSS Document */
html {
    font-size: 100%;
    overflow-y: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 50px 0;
    font-size: 13px;
    line-height: 1.231;
    background-color:#E2E2E2;
}

body, button, input, select, textarea {
    font-family: 'Open Sans', sans-serif;
    color: #222;
}

.z1_clickable {
    cursor: pointer;
}

.z1_msg_header {
    position: absolute;
    background-color: #E2E2E2;
    height: 41pt;
    width: 100%;
    top: 0;
    left: 0;
}

.z1_msg_header_text {
    font-size: large;
    color: black;
    position: absolute;
    left: 10px;
    bottom: 0;
    width: 80%;
}

.z1_msg_btn_close {
    position: absolute;
    right: 10px;
    top: 10px;
    color: cadetblue;
    display:none;
}

.z1_msg_body {
    width: 100%;
    padding-left: 12px;
    position:absolute;
    top:36pt;
    background-color:#E2E2E2;
    color: black;
}

.z1_msg_footer {
    position: absolute;
    bottom: 0px;
    height: 40px;
    background: #F7F7F7;
    width: 100%;
    color: cornflowerblue;
}

.z1_msg_btnok {
    height:40px;
    padding-left:45%;
    padding-top: 12px;
}