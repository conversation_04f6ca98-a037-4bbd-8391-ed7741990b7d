@charset "utf-8";
/* CSS Document */
html {
    font-size: 100%;
    overflow-y: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 50px 0;
    font-size: 13px;
    line-height: 1.231;
    background-color: #E2E2E2;
}

body, button, input, select, textarea {
	font-family: 'Helvetica', 'Open Sans', 'Lucida Grande', sans-serif;
    color: #222;
    
}

.z1_clickable {
    cursor: pointer;
}

.z1_msg_header {
    position: absolute;
    /* background-color: #F8F8F8; */
    height: 34pt;
    width: 100%;
    top: 0;
    left: 0;
    background-color: #E2E2E2;
}

.z1_msg_header_text {
    font-size: large;
	font-weight: 600;
    position: absolute;
    bottom: 0;
    width: 100%;
    color: black;
	text-align: center;
}

.z1_msg_btn_close {
    position: absolute;
    right: 10px;
    top: 10px;
    color: #157DFB;
    display:none;
}

.z1_msg_body {
    width: 100%;
    color: black;
    background-color: #E2E2E2;
    position:absolute;
    top:36pt;
	text-align: center;
}

.z1_msg_footer {
    position: absolute;
    bottom: 0px;
    height: 34px;
    background: #F8F8F8;
    width: 100%;
    color: #222;
}

.z1_msg_btnok {
    height:40px;
    padding-left:45%;
    padding-top: 12px;
    color: #157DFB;
    background-color: #fff;
}