@charset "utf-8";
/* CSS Document */
html {
	font-size: 100%;
	overflow-x: hidden;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}

body {
	margin: 0;
	font-size: 13px;
	line-height: 1.231;
	background: #FFFFFF;
}

body,button,input,select,textarea {
	font-family: <PERSON><PERSON>,<PERSON>o,'Open Sans',sans-serif;
	color: #222;
}

img,object,embed,video {
	display: block;
	max-width: 100%;
}

/* Do not display fullscreen msg in landscape */
@media only screen 
  and (orientation: landscape) {
  .z1_fs_box {display:none;}
}
/* Display fullscreen msg in Potrait mode */
@media only screen 
  and (orientation: portrait) {
	.z1_fs_box {
		position:absolute;left:0;top:0;right:0;bottom:0;
		display: -ms-flexbox; -ms-flex-direction: column; -ms-flex-wrap: none; -ms-flex-align: stretch; 
		display: -webkit-flex; -webkit-flex-flow: column nowrap; -webkit-align-items: stretch; -webkit-justify-content: center;
		display: flex; flex-flow: column nowrap; align-items: stretch; justify-content: center; 
	}    
	.z1_fs_rowTop {-webkit-flex:1; flex:1;}
	.z1_fs_rowBottom {-webkit-flex:1; flex:1;}
	.z1_fs_rowMiddle {position:relative;}
	.z1_fs_rowMiddle img {display:block;width:100%;}
	.z1_fs_close {position: absolute;width:23px;height:23px;top:5px;right:8px;z-index:99;}
	.z1_fs_close *{pointer-events: none;}
	/* android hide close btn */
	.z1_fs_close {display: none;}
}
/* android hide close btn */
.z1_fs_close {display: none;}