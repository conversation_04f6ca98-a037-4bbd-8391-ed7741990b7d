@charset "utf-8";
    /* CSS Document */

    html { font-size: 100%;overflow-y:hidden; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
    body { margin: 0; font-size: 13px; line-height: 1.231; background:#ebebeb; }
    body, button, input, select, textarea { font-family: 'Open Sans', sans-serif; color: #222; }


    img, object, embed, video {
        max-width: 99%;
    }

    .ie6 img {
        width:99%;
    }
    .z1_kb_bodyContainer {
        background:#fff;
    }
    .z1_kb_total_articles {background:#ebebeb;padding:9px 1.5%; font-size:16px; color:#4e4e4e;font-weight:600; clear:both; overflow:hidden; position: absolute;top: 1.5%;width: 100%;height: 50px; }
    .z1_kb_total_articles .z1_kb_btn_close { position: absolute;right: 5%; top:6px;}
    .z1_kb_total_articles .z1_kb_btn_close a{font-weight:700; color:#a1acbc;text-decoration:none; font-size:12px; padding:2px 5px;}
    .z1_kb_total_articles .z1_kb_btn_close a:hover {color:#222; }
    
    .z1_kb_total_articles .z1_kb_date {color:#acacac; font-size:15px; padding-bottom:4px; text-transform:uppercase; font-weight:400; float:left;}
    .z1_kb_total_articles .star_rating {color:#7a91af; font-size:14px; padding-bottom:4px; float:right;}

    .clearbox {clear:both; overflow:hidden;}
    .z1_kb_article_list {padding:5px 2%; border-bottom:2px solid #ebebeb; background:#fff; position:relative; }
    .z1_kb_article_list .z1_kb_icon_arrow { position:absolute; top:30%; right:10px; cursor:pointer;}
    .z1_kb_article_list:hover {background:#f7f7f7; }
    .z1_kb_article_list > h1 { padding:3px 0 6px 0; margin:0; color:#333333;font-size:14px; font-weight:600; width:92%; border:0 solid #000;  }

    .z1_kb_date {color:#acacac; font-size:12px; padding-bottom:4px; text-transform:uppercase;}
    .star_rating {color:#7a91af; font-size:14px; padding-bottom:4px;}
    .star_rating_deselect {color:#d3dae4;}

    .z1_kb_article_wrapper {padding:0; margin:0; overflow:auto; position: absolute;top: 7%;width: 100%;bottom: 2px;background: #ffffff;}
    .z1_faq_list_wrapper {padding:0; margin:0; overflow:auto; position: absolute;top: 0;width: 100%;bottom: 2px;background: #ffffff;}
    

    @media only screen and (max-width: 480px) {
        .z1_kb_bodyContainer {
            min-height:200px;
            margin-left: auto;
            margin-right: auto;
        }
    }

    @media only screen and (min-width: 481px) and (max-width: 768px) {

    }
    
    span.stars, span.stars span {
        display: block;
        background: url(/res/star_rating.png) 0 -17px repeat-x;
        width: 84px;
        height: 17px;
    }


    span.stars span {
        background-position: 0 0;
    }