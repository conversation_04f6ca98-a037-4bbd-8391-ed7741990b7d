@charset "utf-8";
   /* CSS Document */

html { font-size: 100%; overflow-y:hidden; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
body { margin: 0; font-size: 13px; line-height: 1.231; background:#ebebeb; }
body, button, input, select, textarea { font-family: 'Open Sans', sans-serif; color: #222; }


img, object, embed, video {
    max-width: 100%;
}

.ie6 img {
    width:100%;
}
.z1_kb_bodyContainer {
    background:#fff;
}

.z1_kb_total_articles {
	background: #ebebeb;
	padding: 8px 8px;
	font-size: 14px;
	color: #4e4e4e;
	font-weight: 600;
	clear: both;
	overflow: hidden;
	position: absolute;
	top: 0px;
	width: 100%;
	height: 60px;
	background-color: ${content_title_backgroundColor};
}
.z1_kb_total_articles > h1 {color:#717070;font-size:17px; font-weight:400;}
.z1_kb_total_articles > h1.z1_kb_title {color:#717070;font-size:16px; font-weight:400; padding:0 0 5px 0; margin:0;color: ${content_title_color};}
.z1_kb_total_articles .z1_kb_btn_close { position: absolute;right: 3%;top: 15px;}

.z1_kb_total_articles .z1_kb_btn_close a{font-weight:700; color:#a1acbc;text-decoration:none; font-size:12px; padding:2px 5px;}
.z1_kb_total_articles .z1_kb_btn_close a:hover {color:#222;}


.z1_kb_total_articles .z1_kb_date {color:#acacac; font-size:14px; padding-bottom:2px; text-transform:uppercase; font-weight:400; float:left;}
.z1_kb_total_articles .star_rating {color:#7a91af; font-size:14px; padding-bottom:4px; float:right;position:absolute;right:98px;}

.clearbox {clear:both; overflow:hidden;}
.z1_kb_artcile_details {font-size: 14px;line-height: 22px;
            color: #939393;width: 100%;height: 100%;}
.z1_kb_artcile_details > h2 { color:#404040; padding:0 0 8px 0; margin:0; font-size:16px; font-weight:400;}

.z1_kb_article_wrapper {
        padding: 0;
        margin: 0;
        position: absolute;
        top: 55px;
        right: 0px;
        bottom: 0px;
        left: 0px;
        background: #FFFFFF;
    }

.z1_kb_article_details_bottom {
	padding: 5px 2% 10px 2%;
	border-top: 1px solid #e4e4e4;
	color: #777b7f;
	clear: both;
	overflow: hidden;
	position: absolute;
	bottom: 2px;
	background-color: ${content_footer_backgroundColor};
}

.z1_kb_article_details_bottom > h3 {
	color: #68a4e2;
	color: ${content_footer_title_color};
	padding: 8px 0;
	margin: 0;
	font-size: 16px;
	font-weight: 400;
	clear: both;
}

.z1_kb_article_details_bottom label {
	margin-right: 50px;
	float: left;
	font-size: 14px !important;
	color: ${content_footer_options_color};
}


.helpbox {background:#eeeeee; color:#364457;font-size:15px; padding:8px; margin:8px 0; clear:both; overflow:hidden;}
.helpbox > h4 {float:left;color:#364457;font-size:15px; width:65%; padding:0; margin:0;font-weight:400;}
.helpbox .icon_chat {float:right; width:50px; color:#5d6671; font-size:23px;}


@media only screen and (max-width: 480px) {
.z1_kb_bodyContainer {
    min-height:200px;
    margin-left: auto;
    margin-right: auto;
}
.z1_kb_article_details_bottom label { padding:0 8px 5px 0; margin:0; font-size:14px;}
}

@media only screen and (min-width: 481px) and (max-width: 768px) {

}

span.stars, span.stars span {
    display: block;
    background: url(/res/star_rating.png) 0 -17px repeat-x;
    width: 84px;
    height: 17px;
}

span.stars span {
    background-position: 0 0;
}