/* Article List css for Android Phone */
@charset "utf-8";
* {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 100%;
  overflow-y: hidden;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
body {
  font-family: 'Roboto', 'Open Sans', sans-serif;
  font-size: 12pt;
  font-weight: 400;
  margin: 0;
  padding: 8px 15px 0;
  color: #333;
}

/* Typography Resets */
h1, h2, h3, h4, h5, h6 {
  font-weight: 400;
}
h1 { font-size: 14pt; }
h2 { font-size: 13pt; }
h3 { font-size: 12pt; }

.z1_kb_bodyContainer {
	background:${list_item_backgroundColor};
}

/* Article List Header */
.z1_kb_total_articles {
  font-size:${list_title_font_size};
  font-style:${list_title_font_style}; 
  font-weight: 700;
  text-transform: uppercase;
  border-bottom: 2px solid #D6D6D6;
  padding: 0 10px 6px 10px;
  background:${list_title_backgroundColor}; 
  color:${list_title_color}; 
}

/* Article List Container */
.z1_kb_article_wrapper {
  background: ${list_item_backgroundColor};
}

/* Article List Item */
.z1_kb_article_list {
  border-bottom: 1px solid #EEE;
  padding: 20px 10px;
  border-bottom-color:${list_item_border_color}; 
  background-color:${list_item_backgroundColor};   
}
.z1_kb_article_list:active {
  background-color: #F8F8F8;
}
.z1_kb_article_list:last-child {
  border-bottom: none;
}
.z1_kb_article_list h1 {
  margin: 0;
  padding: 0;
  color:${list_item_color};
  background-color:${list_item_backgroundColor};
  font-size:${list_item_font_size};
  font-style:${list_item_font_style};  
}


/* Unused elements, hidden */
.z1_kb_icon_arrow,
.z1_kb_date,
.z1_kb_btn_close {
  display: none;
}