<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport"
	content="width=device-width, initial-scale=1,user-scalable=no">
<meta name="description" content="">
<meta name="author" content="z1">
<link rel="icon" href="../../favicon.ico">

<title>Support</title>

<style type="text/css">
${z1_css}
</style>


</head>

<body>
	<div class="z1_kb_bodyContainer">
		<div class="z1_kb_total_articles">
			<section class="z1_kb_btn_close">
				<a href="#" onclick="_closeDetailView()"><img
					src="/res/close_3.png"></a>
			</section>
			<h1 class="z1_kb_title">%z1.content.title%</h1>
			<section class="z1_kb_date">%z1.content.creationDate%</section>
			<!--  >span class="stars star_rating"></span-->
		</div>
		<!-- end msgnote_display -->

		<div class="z1_kb_article_wrapper">
			<object class="z1_kb_artcile_details" data="%z1.content.url%">
			</object>
			<!-- end artcile_details -->

			<!--  >div class="z1_kb_article_details_bottom">
                <h3>How helpful is this article?</h3>
                <div>
                    <label><input type="radio" value="1" name="help_articles" onclick="_sendFeedback(this);"> Not at all</label>
                    <label><input type="radio" value="2" name="help_articles" onclick="_sendFeedback(this);"> Not very</label>
                    <label><input type="radio" value="3" name="help_articles" onclick="_sendFeedback(this);"> Somewhat</label>
                    <label><input type="radio" value="4" name="help_articles" onclick="_sendFeedback(this);"> Very</label>
                    <label><input type="radio" value="5" name="help_articles" onclick="_sendFeedback(this);"> Extremely</label>
                </div>
         
                <!--div class="z1_kb_helpbox">
                    <h4>You need any help ?</h4>
                    <section class="icon_chat"><i class="fa fa-fw"></i></section>
                </div-->
		</div>
		<!-- end article_details_bottom -->

		<!--  >/div><!-- end article_wrapper -->
	</div>


	<script type="text/javascript">


    function _closeDetailView()
    {
      contentModel.back();
    };
    
    /*function _updateKBHeader(kbData) {
      if(kbData) {
       
          // Create stars holder
          var kbRatingOuter = document.querySelector(".stars");
          //get the rating for the article
          var val = parseFloat(kbData.rating);
          var kbRatingInner = document.createElement("span");
          // Make sure that the value is in 0 - 5 range, multiply to get width
          var size = Math.max(0, (Math.min(5, val))) * 17 + "px";
          kbRatingInner.setAttribute("style", "width:"+size);
          kbRatingOuter.appendChild(kbRatingInner);
      }
    };*/

    /*function _sendFeedback(radio) {
      if(radio && radio.value){
        kbModel.sendFeedback(radio.value);
      }
    };*/
  </script>

</body>

</html>