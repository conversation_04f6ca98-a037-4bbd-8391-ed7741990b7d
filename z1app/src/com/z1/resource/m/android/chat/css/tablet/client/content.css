html {
	position: relative;
	min-height: 100%;
}

/* Different screen sizes CSS
-------------------------------------------------- */
body {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 14px;
	line-height: 1.42857143;
	color: #333;
	background-color: #fff;
}

@media ( min-width : 1200px) .z1_container {
	width : 1170px;
	

}

@media ( min-width : 992px) .z1_container {
	width : 970px;
	

}

@media ( min-width : 768px) .z1_container {
	width : 750px;
	

}

.z1_container {
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto;
}

/* page CSS
-------------------------------------------------- */
body {
	overflow: hidden;
	background-color: #EAEFF2;
}

.z1_container {
	width: auto;
	max-width: 680px;
	padding: 0 15px;
}

.z1_chat_container {
	height: 100%;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-box-sizing: border-box;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	background-color: #fff;
}

.z1_chat_body {
	position: absolute;
	top: 5px;
	bottom: 5px;
	overflow: auto;
	left: 5px;
	right: 5px;
}

.z1_chat_inp_ctrls {
	width: 100%;
	/* this should fix width spill */
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.z1_chat_msg_row {
	clear: both;
}

.z1_chat_msg_header_cont>div {
	float: left;
}

.z1_chat_msg_date {
	color: #aaa;
	font-size: 12px;
}

.z1_ul {
	list-style-type: none;
	padding-left: 4px;
	padding-right: 4px;
}

.z1_ul>li {
	border-bottom: 1px solid #EBEBEB;
	padding-bottom: 8px;
	padding-top: 12px;
	padding-right: 12px;
	padding-left: 10px;
}

.z1_chat_ul {
	list-style-type: none;
	padding-left: 4px;
	position: absolute;
	left: 0px;
	right: 0px;
	top: 0px;
	bottom: 0px;
}

.z1_chat_ul>li {
	border-bottom: 1px solid #EBEBEB;
	padding-bottom: 8px;
	padding-top: 12px;
	padding-right: 12px;
	padding-left: 10px;
	margin-right: 12px;
	margin-left: 12px;
	max-width: 90%;
}

.z1_chat_ul>li.bubblearrow_left {
	position: relative;
	background-color: #E5E5EA;
	border-bottom-color: #E5E5EA;
	padding-top: 5px;
	padding-bottom: 5px;
	border-bottom: 3px solid #E5E5EA;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	margin-bottom: 3%;
	float: left;
	clear: both;
}

.z1_chat_ul>li.bubblearrow_right {
	position: relative;
	background-color: #0B93F6;
	border-bottom-color: #0B93F6;
	padding-top: 5px;
	padding-bottom: 5px;
	border-bottom: 3px solid #0B93F6;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
	margin-bottom: 3%;
	float: right;
	clear: both;
}

.z1_chat_ul>li.bubblearrow_system {
    border-bottom: 0px !important;
    padding: 12px !important;
}

.z1_chat_ul>li.bubble_feedback {
    border-bottom: 0px !important;
    padding: 12px;
    position: relative;
    background-color: #E5E5EA;
    border-bottom-color: #E5E5EA;
    padding-top: 5px;
    padding-bottom: 5px;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
    margin-bottom: 3%;
    clear: both;
}

.z1_chat_ul>li.bubblearrow_right::after {
	content: "";
	position: absolute;
	right: -10px;
	top: 20%;
	border-style: solid;
	border-width: 15px 10px 0px 0px;
	border-color: #0B93F6 transparent transparent transparent;
	display: block;
	width: 0;
	z-index: 1;
}

.z1_chat_ul>li.bubblearrow_left::after {
	content: "";
	position: absolute;
	left: -10px;
	top: 20%;
	border-style: dashed;
	border-width: 10px 0px 15px 10px;
	border-color: #E5E5EA transparent transparent transparent;
	display: block;
	width: 0;
	z-index: 1;
}

.z1_msg_txt_format {
	margin: 0px !important;
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 14pt;
	white-space: pre-wrap;
	word-wrap: break-word;
	-ms-word-break: break-all;
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
    -ms-hyphens: auto;
     hyphens: auto;
}

.bubblearrow_right .z1_msg_txt_format {
	color: white;
}

.bubblearrow_right .z1_chat_msg_date {
	color: #DEDEDE;
	font-size: 10pt;
}

.bubblearrow_left .z1_chat_msg_date {
	color: #7E7C7C;
	font-size: 10pt;
}

.bubblearrow_system .z1_msg_txt_format {
	/*text-align: center;*/
}

.z1_chat_msg_f_btns {
	position: relative;
	clear: both;
	margin: 10px 0px;
}

.z1_chat_msg_f_btn {
	position: relative;
	float: right;
	padding: 4px 10px;
	background-color: dimgray;
	color: white;
	-webkit-border-radius: 6px;
	-moz-border-radius: 6px;
	border-radius: 6px;
	margin: 2px 14px;
	min-width: 32px;
	text-align: center;
}

.z1_chat_msg_f_btn_yes {
}

.z1_chat_msg_f_btn_no {
}