<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport"
	content="width=device-width, initial-scale=1,user-scalable=no">
<meta name="description" content="">
<meta name="author" content="z1">
<link rel="icon" href="../../favicon.ico">

<title>Chat</title>

<style type="text/css">
${z1_css}
</style>


</head>

<body>
	<div class="z1_container z1_chat_container">
		<div class="z1_chat_body">
			<ul class="z1_ul z1_chat_ul">
			</ul>
		</div>
	</div>

	<script type="text/javascript">
    var chatMsgs = [];
    var chatId = "";
	this.init();
    function init()
    {
      var chatId = chatViewModel.getChatId();
      this.setChatId(chatId);
      this.showChatMsgs(chatViewModel.getData());
    };

    // debug
    function dbg(msg)
    {
      try
      {
       // production change to false.
        var dbgOn = false;
        if (!dbgOn)
        {
          return;
        }
        if (!msg) return;
        var ul = document.querySelector(".z1_chat_ul");
        if (!ul)
        {
          return;
        }

        var liDbg = document.createElement("li");
        liDbg.classList.add("bubblearrow_system");
        liDbg.innerHTML = "DEBUG_" + (new Date()).getTime() + "_: " + "<br/>"
          + JSON.stringify(msg, null, '\t');
        ul.appendChild(liDbg);
      }
      catch (e)
      {
      }
    };

    function showChatMsgs(msgs)
    {
      if (msgs)
      {
        var msgJson = JSON.parse(msgs);
        if (msgJson)
        {
          this.chatMsgs = msgJson;
          for (var i = 0; i < msgJson.length; i++)
          {
            this._createChatUI(msgJson[i]);
          }
        }
      }
    };

    function setChatId(id)
    {
      if (this.chatId != id)
      {
        this.chatMsgs = [];
      }
      this.chatId = id;

    };

    function onReceive(msg)
    {
      var agentMsg = JSON.parse(msg);
      this._addMessage(agentMsg);

    };

    function _addMessage(message)
    {
      if (!message) return;

      if (this.chatId && this.chatId !== "")
      {
        if (this.chatId === message.id)
        {
          // add unique class for the li box
          var idCls = "_" + (new Date()).getTime() + "_" + Math.ceil(Math.random() * 1000) + "_";
          message.idCls = message.idCls || idCls;
          this.chatMsgs.push(message);
          // window.location.href = 'z1://printChatMsg?' + "addMessage() "  + this.chatMsgs;
          this._showConversation(this.chatMsgs);
        }
      }

    };

    function _showConversation(chat)
    {
      var ul = document.querySelector(".z1_chat_ul");
      if (ul)
      {
        ul.innerHTML = "";
      }
      this.dbg({txt: "_showConversation"});
      if (chat)
      {
        for (var i = 0; i < chat.length; i++)
        {
          this._createChatUI(chat[i]);
        }
      }
    };

    function _onFeedbackBtn(ctx)
    {
      if (!ctx)
      {
        return;
      }
      chatViewModel.feedback(ctx.answer);

      // delete the feedback box
      if (ctx && ctx.idCls)
      {
        var el = document.querySelector("." + ctx.idCls);
        if (el)
        {
          el.parentNode.removeChild(el);
        }
        // remove item from local array
        if (this.chatMsgs && this.chatMsgs.length)
        {
          var item;
          for (var i = 0; i < this.chatMsgs.length; i++)
          {
            item = this.chatMsgs[i];
            if (item && item.idCls === ctx.idCls)
            {
              this.chatMsgs.splice(i, 1);
              break;
            }
          }
        }
      }
    };

    function _createChatUI(message)
    {
      if (!message) return;
      var ul = document.querySelector(".z1_chat_ul");
      if (!ul)
      {
        console.log("ul not found");
        return;
      }
      var isMe = false;

      this.dbg(message);

      var li = document.createElement("li");
      if (message.authorType.toLowerCase() === "customer")
      {
        isMe = true;
        li.classList.add("bubblearrow_right");
      }
      else if (message.authorType.toLowerCase() === "system")
      {
        isMe = false;
        li.classList.add("bubblearrow_system");
      }
      else if (message.type && message.type === "feedback")
      {
        isMe = false;
        li.classList.add("bubble_feedback");
      }
      else
      {
        isMe = false;
        li.classList.add("bubblearrow_left");
      }

      // unique class for the li box
      var idCls = message.idCls || "_" + (new Date()).getTime() + "_"
        + Math.ceil(Math.random() * 1000) + "_";
      li.classList.add(idCls);

      // chat message text

      // Ask For Feedback - message
      if (message.type && message.type === "feedback")
      {
        var dvBody = document.createElement("div");
        dvBody.classList.add("z1_chat_msg_row");
        dvBody.classList.add("z1_chat_msg_body_cont");
        var preDiv = document.createElement("pre");
        preDiv.classList.add("z1_msg_txt_format");
        //preDiv.appendChild(document.createTextNode(unescape(message.data)));
        preDiv.innerHTML = unescape(message.data);
        dvBody.appendChild(preDiv);
        var dvBtns = document.createElement("div");
        dvBtns.classList.add("z1_chat_msg_f_btns");
        var spnBtn2 = document.createElement("span");
        spnBtn2.innerHTML = "No";
        spnBtn2.classList.add("z1_chat_msg_f_btn");
        spnBtn2.classList.add("z1_chat_msg_f_btn_no");
        dvBtns.appendChild(spnBtn2);
        spnBtn2.addEventListener("click", function(e)
        {
          _onFeedbackBtn({
            "answer": "no",
            "idCls": idCls,
            "evt": e
          });
        }, false);
        var spnBtn1 = document.createElement("span");
        spnBtn1.innerHTML = "Yes";
        spnBtn1.classList.add("z1_chat_msg_f_btn");
        spnBtn1.classList.add("z1_chat_msg_f_btn_yes");
        dvBtns.appendChild(spnBtn1);
        spnBtn1.addEventListener("click", function(e)
        {
          _onFeedbackBtn({
            "answer": "yes",
            "idCls": idCls,
            "evt": e
          });
        }, false);
        dvBody.appendChild(dvBtns);
        li.appendChild(dvBody);
      }
      else
      {
        var dvBody = document.createElement("div");
        dvBody.classList.add("z1_chat_msg_row");
        dvBody.classList.add("z1_chat_msg_body_cont");
        var preDiv = document.createElement("pre");
        preDiv.classList.add("z1_msg_txt_format");
        //preDiv.appendChild(document.createTextNode(unescape(message.data)));
        preDiv.innerHTML = unescape(message.data);
        dvBody.appendChild(preDiv);
        li.appendChild(dvBody);
      }

      // chat messge date & time
      var dvFooter = document.createElement("div");
      dvFooter.classList.add("z1_chat_msg_row");
      dvFooter.classList.add("z1_chat_msg_footer_cont");
      var dvDate = document.createElement("span");
      dvDate.classList.add("z1_chat_msg_date");
      var dt = _formatDate(message.time);
      dvDate.appendChild(document.createTextNode(unescape(dt)));
      dvFooter.appendChild(dvDate);
      li.appendChild(dvFooter);

      ul.appendChild(li);
      var mainDiv = document.querySelector(".z1_chat_body");
      mainDiv.scrollTop = mainDiv.scrollHeight;

    };

    function _formatDate(timeinMs)
    {
      var monthNames = ["January", "February", "March", "April", "May", "June", "July", "August",
        "September", "October", "November", "December"];

      var dt = new Date(parseInt(timeinMs));
      var h = dt.getHours();
      var m = dt.getMinutes();
      var s = dt.getSeconds();
      var ampm = h >= 12 ? 'PM' : 'AM';
      h = h % 12;
      h = h ? h : 12; // the hour '0' should be '12'
      m = m < 10 ? '0' + m : m;
      s = s < 10 ? '0' + s : s;

      var dtStr = monthNames[dt.getMonth()].substr(0, 3) + " " + dt.getDate() + ", " + h + ':' + m
        + " " + ampm;
      return dtStr;
    };
  </script>

</body>

</html>