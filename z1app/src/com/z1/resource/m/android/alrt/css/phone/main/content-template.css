@charset "utf-8";
/* CSS Document */
html {
    font-size: 100%;
    overflow-y: hidden;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 50px 0;
    font-size: 13px;
    line-height: 1.231;
}

body, button, input, select, textarea {
    font-family: 'Open Sans', sans-serif;
    color: #222;
}

.z1_clickable {
    cursor: pointer;
}

.z1_msg_header {
    position: absolute;
    background-color: #E2E2E2;
    height: 41px;
    width: 100%;
    top: 0;
    left: 0;
	background-color: ${message_title_backgroundColor};
}

.z1_msg_header_text {
    font-size: large;
    color: #157dfb;
    position: absolute;
    left: 10px;
    top: 10px;
    width: 80%;
	color: ${message_title_color};
}

.z1_msg_btn_close {
    position: absolute;
    right: 10px;
    top: 10px;
    color: cadetblue;
}

.z1_msg_body {
    position: absolute;
    top: 40px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background-color: #E2E2E2;
    color:black;
	color: ${message_body_color};
	background-color: ${message_body_backgroundColor};
}

.z1_msg_body div {
	padding: 10px;
}
