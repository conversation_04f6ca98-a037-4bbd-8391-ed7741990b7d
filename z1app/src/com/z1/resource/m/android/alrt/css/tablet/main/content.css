@charset "utf-8";
/* CSS Document */
html {
	font-size: 100%;
	overflow-y: hidden;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
}

body,button,input,select,textarea {
	font-family: 'Open Sans', sans-serif;
	color: #222;
}

.z1_clickable {
	cursor: pointer;
}

.z1_alrt_body {
	font-size: 13px;
	line-height: 1.231;
	display: -webkit-flex;
	display: flex;
	-webkit-flex-flow: column wrap;
    flex-flow: column wrap;
    -webkit-flex-grow: 1;
    flex-grow: 1;
    background-color: #FFFFFF;
}

.z1_msg_header {
	height: 4rem;
	width: 100%;
	border-bottom: 3px solid #33b5e5;
	display: -webkit-flex;
	display: flex;
	-webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
}

.z1_msg_header_text {
	font-size: large;
	color: #33b5e5;
}

.z1_msg_btn_close {
	position: absolute;
	right: 10px;
	top: 10px;
	color: cadetblue;
	display: none;
}

.z1_msg_body {
    color:black;
}

.z1_msg_body div {
}