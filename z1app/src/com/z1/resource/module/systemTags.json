{"channel": {"displayName": "Experience Channel", "values": ["Omnichannel", "In-App Web", "In-App Native", "<PERSON><PERSON>", "SMS", "Email", "Store"]}, "goal": {"displayName": "Goals", "values": ["Revenue", "Retention", "Loyalty"]}, "targetlogic": {"displayName": "Targeting Logic", "values": ["ML Driven", "Rules Driven", "File Upload"]}, "experienceType": {"displayName": "Experience Type", "values": ["Triggered", "Scheduled"]}, "triggerType": {"displayName": "Trigger Type", "values": ["User Activity", "External Trends", "Emerging Trends"]}}