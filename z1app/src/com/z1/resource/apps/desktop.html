<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Udichi Workbench</title>
    <link rel="stylesheet" type="text/css" href="https://ajax.googleapis.com/ajax/libs/dojo/1.8/dijit/themes/claro/claro.css">
    <link rel="stylesheet" type="text/css" href="/common.css"/>
    <link rel="stylesheet" type="text/css" href="/runtime.css"/>
    <link rel="stylesheet" type="text/css" href="/z1.css"/>
  </head>
  <style type="text/css">
    @import url("https://ajax.googleapis.com/ajax/libs/dojo/1.8/dojox/layout/resources/ExpandoPane.css");
    @import "https://ajax.googleapis.com/ajax/libs/dojo/1.8/dojox/grid/resources/claroGrid.css";
    @import "https://ajax.googleapis.com/ajax/libs/dojo/1.8/dojox/grid/resources/Grid.css";
    .dojoxGrid table {
      margin: 0;
    }
  </style>

  <!--
  The main workbench page and its components
  -->


  <body class="claro">
     <div class="z1_main" id="z1_main" style="width: 100%; height: 100%">
        <!-- Content goes here -->
     </div>
     <div class="z1_msg_area" id="z1_msg_area"></div> 
     <div id="mainStandby" data-dojo-type="dojox/widget/Standby" 
       data-dojo-props="target:'z1_main'">  
     </div>    
  </body>

  <!-- 
    1. Enabling the AMD loader. (async: 1)
    2. Fixing the loader to use normal AMD resolution of unregistered module paths (relative to baseDir)
    instead of the traditional Dojo resolution method (relative to the parent of baseDir).
    (tlmSiblingOfDojo: 0)
    3. Enabling debugging. (isDebug: 1)
  
    The remainder of Dojo configuration, as well as our application’s instantiation, occurs within
    z1/main.js.
  
  -->
  <script data-dojo-config="async: 1, tlmSiblingOfDojo: 0, isDebug: 0, parseOnLoad: 1, gfxRenderer:'canvas,svg,silverlight,vml'" 
  src="https://ajax.googleapis.com/ajax/libs/dojo/1.8.0/dojo/dojo.js"></script>

  <!--script data-dojo-config="async: 1, tlmSiblingOfDojo: 0, isDebug: 1, parseOnLoad: 1" 
  src="lib/dojo/dojo.js"></script-->

  <!-- Load the loader configuration script. Note that this module ID is hard-coded in build.sh in order to provide
  an optimised build that loads as few as one script for the entire application. If you change the name or
  location of this module, you will need to update build.sh too. -->
  <script src="lib/${z1.release.version}/z1/apps/desktop/main.js"></script>


</html>
