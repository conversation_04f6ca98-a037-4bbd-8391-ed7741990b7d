<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name='viewport' content='width=device-width, initial-scale=1'>
<title>Session AI - Password Reset</title>
<link rel="stylesheet" type="text/css" href="/common.css">
<link rel="stylesheet" type="text/css" href="/runtime.css">
<link rel="stylesheet" type="text/css" href="/c3.css">
<link rel="stylesheet" type="text/css" href="/op.css">
<link media="all" rel="stylesheet" type="text/css" href="css/font-awesome.css">
<link media="all" rel="stylesheet" type="text/css" href="css/themify-icons.css">
<link media="all" rel="stylesheet" type="text/css" href="css/fontello.css">
<link href='https://fonts.googleapis.com/css?family=Open+Sans'
	rel='stylesheet' type='text/css'>
<!-- experimental fonts -->
<link href='https://fonts.googleapis.com/css?family=Open+Sans:400,300,300italic,400italic,600,600italic,700,700italic,800,800italic'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300italic,400italic,500,500italic,700,700italic,900,900italic'
	rel='stylesheet' type='text/css'>
<link href='https://fonts.googleapis.com/css?family=Play:400,700'
	rel='stylesheet' type='text/css'>
<!-- end - experimental fonts -->
<link rel="icon" type="image/png" href="/favicon.png">
<!--script type="text/javascript" src="z1m.js"></script-->
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.0.0/core.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/md5.js"></script>
<script type="text/javascript"
	src="https://d2p4r375zfkzm8.cloudfront.net/html5sdk/4.202.0/z1m.js"></script>

</head>


<body class="carlo c3_cols_nowrap" onload="init()">
	<svg xmlns="http://www.w3.org/2000/svg" style="display: none;" class="c3_svgDefs" id="uniqName_1_0" widgetId="uniqName_1_0">
		<symbol id="zi-eye-inactive" viewBox="0 0 20 20" fill="none">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3375 9.7875C17.835 5.90106 14.1638
				3.28158 9.99998 3.125C5.83617 3.28158 2.16496 5.90106 0.662478 9.7875C0.612833 9.92482
				0.612833 10.0752 0.662478 10.2125C2.16496 14.0989 5.83617 16.7184 9.99998 16.875C14.1638
				16.7184 17.835 14.0989 19.3375 10.2125C19.3871 10.0752 19.3871 9.92482 19.3375
				9.7875ZM6.24976 10C6.24976 7.92893 7.92869 6.25 9.99976 6.25C12.0708 6.25 13.7498 7.92893
				13.7498 10C13.7498 12.0711 12.0708 13.75 9.99976 13.75C7.92869 13.75 6.24976 12.0711
				6.24976 10ZM7.49976 10C7.49976 11.3807 8.61904 12.5 9.99976 12.5C10.6628 12.5 11.2987
				12.2366 11.7675 11.7678C12.2364 11.2989 12.4998 10.663 12.4998 10C12.4998 8.61929 11.3805
				7.5 9.99976 7.5C8.61904 7.5 7.49976 8.61929 7.49976 10ZM1.91873 10C3.18748 13.1687
				6.68748 15.625 9.99998 15.625C13.3125 15.625 16.8125 13.1687 18.0812 10C16.8125 6.83125
				13.3125 4.375 9.99998 4.375C6.68748 4.375 3.18748 6.83125 1.91873 10Z" fill="currentColor"/>
		</symbol>

		<symbol id="zi-eye-active" preserveAspectRatio="xMidYMid meet" viewBox="0 0 32 32" fill="none">
			<path d="M5.24,22.51l1.43-1.42A14.06,14.06,0,0,1,3.07,16C5.1,10.93,10.7,7,16,7a12.38,12.38,
				0,0,1,4,.72l1.55-1.56A14.72,14.72,0,0,0,16,5,16.69,16.69,0,0,0,1.06,15.66a1,1,0,0,0,0,
				.68A16,16,0,0,0,5.24,22.51Z" fill="currentColor"></path>
			<path d="M12 15.73a4 4 0 013.7-3.7l1.81-1.82a6 6 0 00-7.33 7.33zM30.94 15.66A16.4 16.4 0
				0025.2 8.22L30 3.41 28.59 2 2 28.59 3.41 30l5.1-5.1A15.29 15.29 0 0016 27 16.69 16.69
				0 0030.94 16.34 1 1 0 0030.94 15.66zM20 16a4 4 0 01-6 3.44L19.44 14A4 4 0 0120 16zm-4
				9a13.05 13.05 0 01-6-1.58l2.54-2.54a6 6 0 008.35-8.35l2.87-2.87A14.54 14.54 0 0128.93
				16C26.9 21.07 21.3 25 16 25z" fill="currentColor"></path>
		</symbol>
	</svg>
	<div class="c3_flex_1 c3_cols_nowrap c3_login_page_containerWrp c3_hidden c3_y_auto"
		data-dojo-attach-point="mainContainer">
		<div class="c3_flex_1 c3_rows_nowrap c3_loginPgC c3_login_page_container"
			data-dojo-attach-point="_cont">
			<div class="c3_flex_1 c3_flex_center_1 c3_loginPgC1"
				data-dojo-attach-point="_cont1">
				<div class="c3_login_page_effects_container c3_flex_1 c3_rows_nowrap_center">
					<div class="c3_cols_nowrap_center c3_login_container c3_gp20">
						<div class="c3_rows_nowrap_center">
							<a target="_blank" href="https://www.sessionai.com"><img src="/res/logo.png" class="z1_logo_dim" /></a>
						</div>
						<div class="c3_cols_nowrap">
							<form data-dojo-type="dijit/form/Form" data-dojo-attach-point="pwdResetForm" encType="multipart/form-data">
								<div class="form-group">

									<div id="successPasswordReset" class="c3_register_new_loginbox c3_cols_spacing_xxe c3_align_center c3_hide">
										<div class="c3_flex_1 c3_rows_nowrap_center">
											<svg id="z1-check-circle" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 80" height="80px" width="80px">
												<path fill-rule="evenodd" clip-rule="evenodd" d="M39.5 0.5625C17.9954 0.5625 0.5625 17.9954 0.5625 39.5C0.5625 61.0046 17.9954 78.4375 39.5 78.4375C61.0046 78.4375 78.4375 61.0046 78.4375 39.5C78.4375 29.1731 74.3352 19.2692 67.033 11.967C59.7308 4.66483 49.8269 0.5625 39.5 0.5625ZM34.0855 53.3201C33.6956 53.7066 33.067 53.7064 32.6773 53.3198L20.1923 40.9347C19.7974 40.5429 19.7981 39.9041 20.194 39.5132L23.1932 36.5517C23.5832 36.1666 24.2106 36.1672 24.5998 36.553L33.3812 45.2572L53.2854 25.5142C53.6756 25.1272 54.3048 25.1275 54.6945 25.5148L57.6977 28.4991C58.0916 28.8905 58.0912 29.5278 57.6968 29.9187L34.0855 53.3201ZM6.125 39.5C6.125 57.9325 21.0675 72.875 39.5 72.875C57.9325 72.875 72.875 57.9325 72.875 39.5C72.875 21.0675 57.9325 6.125 39.5 6.125C21.0675 6.125 6.125 21.0675 6.125 39.5Z" fill="green"/>
											</svg>
										</div>
										<div class="c3_pgCDscr c3_pgCTtl c3_pgContent_header_item_name forgot_pwd_cls c3_intents_list_dlg_chk" id="successTitleField">Password Changed!</div>
										<div class="c3_cols_spacing_x">
											<div class="c3_flex_1 c3_rows_nowrap_center">
												<div class="password_policy_txt c3-c-gray100 success">Your password has been changed successfully.</div>
											</div>
										</div>
										<div class="c3_flex_1 c3_cols_nowrap">
											<span class="btn-n btn-primary"
												role="button"
												tabindex="0"
												id="successC3login"
												onclick="c3Login()">Return to Login</span>
										</div>
									</div>
									<div id="errorTokenValidation" class="c3_register_new_loginbox c3_cols_spacing_xxe c3_align_center c3_hide">
										<div class="c3_pgCDscr c3_pgCTtl c3_pgContent_header_item_name forgot_pwd_cls c3_intents_list_dlg_chk" id="errorTitleField">Expired or Invalid Reset Link</div>
										<div class="c3_cols_spacing_x">
											<div class="c3_flex_1 c3_rows_nowrap_center">
												<div class="password_policy_txt c3-c-gray100 c3_flex_1 c3_rows_nowrap_center">Your password reset link has either expired or is invalid. Please return to the login page to send a new password reset link.</div>
											</div>
										</div>
										<div class="c3_flex_1 c3_cols_nowrap">
											<span class="btn-n btn-primary"
												role="button"
												tabindex="0"
												id="failC3login"
												onclick="c3Login()">Return to Login</span>
										</div>
									</div>
									<div id="pwdReset" class="c3_register_new_loginbox c3_cols_spacing_xxe c3_align_center">
										<div class="c3_pgCDscr c3_pgCTtl c3_pgContent_header_item_name forgot_pwd_cls c3_intents_list_dlg_chk" id="titleField">New Password</div>
										<div class="c3_cols_spacing_x">
											<div class="c3_flex_1">
												<div class="c3_pgCDscr c3_pgCTtl forgot_pwd_cls">New Password</div>
												<div class="c3_rows_nowrap_around c3_posR">
													<i class="c3_pwdEyeC c3_btn c3-c-gray80 c3_flex_center_1 c3_posA"
														onclick="newPasswordEye(this)"
														data-dojo-attach-point="eyeIconC1">
														<svg class="c3i c3i_txt" role="img" tabindex="-1">
															<use id="eyeBtnNew" xlink:href="#zi-eye-inactive"
																data-dojo-attach-point="eyeIconNP"></use>
														</svg>
													</i>
													<input id="password"
														placeholder="New password"
														class="inputfield c3_password_inputBox"
														tabindex="0"
														type="password"
														onchange="passwordValidator()"
														required
													>
												</div>
											</div>
											<div class="c3_flex_1">
												<div class="c3_pgCDscr c3_pgCTtl forgot_pwd_cls">Confirm Password</div>
												<div class="c3_rows_nowrap_around c3_posR">
													<i class="c3_pwdEyeC c3_btn c3-c-gray80 c3_flex_center_1 c3_posA"
														onclick="cnfmPasswordEye(this)"
														data-dojo-attach-point="eyeIconC2">
														<svg class="c3i c3i_txt" role="img" tabindex="-1">
															<use id="eyeBtnCnfm" xlink:href="#zi-eye-inactive"
																data-dojo-attach-point="eyeIconCnfm"></use>
														</svg>
													</i>
													<input id="confirm_password"
														placeholder="Confirm Password"
														class="inputfield c3_password_inputBox"
														tabindex="0"
														type="password"
														onchange="passwordValidator()"
														required
													>
												</div>
											</div>
										</div>
										<div class="c3_flex_1 c3_rows_nowrap_center">
											<div class="password_policy_txt c3-c-gray100">Use 8 or more characters with a mix of letters, numbers & symbols</div>
										</div>
										<div class="c3_flex_1 text_message_title c3_err_inline c3_hide" id="errorMessage"></div>

										<div class="c3_flex_1 c3_cols_nowrap">
											<button class="btn-n btn-reset-disabled"
												role="button"
												type="button"
												tabindex="0"
												id="reset"
												onclick="submitRequest()" disabled>Change Password</button>
										</div>

									</div>

								</div>

							</form>
						</div>
					</div>
				</div>
			</div>
			<div class="c3_loginPgC2 c3_flex_1 c3_flex_center_1 c3-bg-c-purple100"
				data-dojo-attach-point="_cont2">
			</div>
		</div>
	</div>
	<div data-zineone-widget="z1_content_widget" style="display: none"></div>

<!-- Load the loader configuration script. Note that this module ID is hard-coded in build.sh in order to provide
  an optimised build that loads as few as one script for the entire application. If you change the name or
  location of this module, you will need to update build.sh too. -->
<script src="/lib/${z1.release.version}/z1/resetpassword/main.js"></script>

</body>
</html>
