<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta name="viewport" content="width=device-width,initial-scale=1" http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Session AI Platform</title>
<link rel="stylesheet"  type="text/css"
	href="css/winbox/winbox.min.css">
<link rel="stylesheet" type="text/css"
	href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dijit/themes/claro/claro.css">
<link rel="stylesheet" type="text/css" href="/common.css?version=${z1.release.version}" />
<link rel="stylesheet" type="text/css" href="/runtime.css?version=${z1.release.version}" />
<link rel="stylesheet" type="text/css" href="/c3.css?version=${z1.release.version}" />
<link rel="stylesheet" type="text/css" href="/journey.css?version=${z1.release.version}" />
<link rel="stylesheet" type="text/css" href="/agent.css?version=${z1.release.version}" />
<link rel="stylesheet" type="text/css" href="/op.css?version=${z1.release.version}" />
<link media="all" rel="stylesheet" type="text/css"
	href="css/font-awesome.css"/>
<link media="all" rel="stylesheet" type="text/css"
	href="css/themify-icons.css"/>
<link media="all" rel="stylesheet" type="text/css"
	href="css/fontello.css"/>
<script type="text/javascript"
	src="https://ajax.googleapis.com/ajax/libs/jquery/1.4.2/jquery.min.js"></script>
<link href='https://fonts.googleapis.com/css?family=Open+Sans'
	rel='stylesheet' type='text/css'>
<!-- experimental fonts -->
<link href='https://fonts.googleapis.com/css?family=Open+Sans:400,300,300italic,400italic,600,600italic,700,700italic,800,800italic'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Archivo+Narrow:400,400italic,700,700italic'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300italic,400italic,500,500italic,700,700italic,900,900italic'
	rel='stylesheet' type='text/css'>
<link href='https://fonts.googleapis.com/css?family=Play:400,700'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Arimo:400,700,400italic,700italic'
	rel='stylesheet' type='text/css'>
<!-- end - experimental fonts -->
<link rel="icon" type="image/png" href="/favicon.png">
<!--script type="text/javascript" src="z1m.js"></script-->
<script type="text/javascript"
	src="https://d2p4r375zfkzm8.cloudfront.net/html5sdk/4.202.0/z1m.js"></script>
	
<!--  Loading google charts -->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript" src="https://www.google.com/jsapi"></script>

<script type="text/javascript" src="/lib/winbox/winbox.min.js"></script>
<!-- date picker lib -->
<script type="text/javascript" src="/lib/litepickerbundle.js"></script>
<script type="text/javascript" src="/lib/luxon.min.js"></script>

<!-- d3 chart lib -->
<link media="all" rel="stylesheet" type="text/css" href="css/billboard.min.css"/>
<script type="text/javascript" src="/lib/billboard.pkgd.min.js"></script>

</head>


<style type="text/css">
@import
	url("https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/layout/resources/ExpandoPane.css")
	;
@import
	"https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/resources/claroGrid.css"
	;
@import
	"https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/resources/Grid.css"
	;
@import
	"https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/enhanced/resources/claro/EnhancedGrid.css"
	;
@import
	"https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/enhanced/resources/EnhancedGrid_rtl.css"
	;
		

.dojoxGrid table {
	margin: 0;
}
</style>

<!--
  The main workbench page and its components
  -->


<body class="claro">
	<div class="z1_main index-main-wrap reset_password_box" id="z1_main">
		<!-- Content goes here -->
	</div>


	<div id="alertSection" style="display: none">
		<div id="alertImg"><span id="alertImgIcon"></span></div>
		<div id="alertText"></div>
        <div id="alertClose" onclick="document.getElementById('alertSection').style.display='none'">
          <i class="ti-close"></i>
        </div>
	</div>

    <div id="helpWindow" class="helpWindow-init">
        <div id="helpHeader">
          <div id="helpTitle"></div>
          <div id="helpClose" onclick="document.getElementById('helpBody').src='about:blank'; document.getElementById('helpWindow').classList.remove('helpWindow-final'); document.getElementById('helpWindow').classList.add('helpWindow-init');">
            <i class="fa fa-close"></i>
          </div>
        </div>
        <div id="helpBodyContainer">
          <iframe id="helpBody" src=""></iframe>
        </div>
    </div>

	<div id="mainStandby" data-dojo-type="dojox/widget/Standby"
		data-dojo-props="target:'z1_main'"></div>
    
    <div data-zineone-widget="z1_content_widget" style="display: none"></div>

</body>

<!-- d3.js - use different CDN if using https or load locally -->
<script src="/lib/d3.v3.min.js" charset="utf-8"></script>
<script type="text/javascript" charset="utf-8" src="https://cdnjs.cloudflare.com/ajax/libs/jshint/2.9.5/jshint.min.js"></script>
<script type="text/javascript" charset="utf-8" src="/lib/js-beautify/1.10.2/beautify.min.js"></script>
<script type="text/javascript" charset="utf-8" src="/lib/js-beautify/1.10.2/beautify-html.min.js"></script>
<script type="text/javascript" charset="utf-8" src="/lib/js-beautify/1.10.2/beautify-css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.2/rollups/aes.js"></script> 
<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.2/rollups/pbkdf2.js"></script>

<!-- 
    1. Enabling the AMD loader. (async: 1)
    2. Fixing the loader to use normal AMD resolution of unregistered module paths (relative to baseDir)
    instead of the traditional Dojo resolution method (relative to the parent of baseDir).
    (tlmSiblingOfDojo: 0)
    3. Enabling debugging. (isDebug: 1)
  
    The remainder of Dojo configuration, as well as our application’s instantiation, occurs within
    z1/main.js.
  
  -->
<script
	data-dojo-config="async: 1, tlmSiblingOfDojo: 0, isDebug: 0, parseOnLoad: 1, gfxRenderer:'canvas,svg,silverlight,vml', packages: [
  ]"
	src="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojo/dojo.js"></script>
<!--script data-dojo-config="async: 1, tlmSiblingOfDojo: 0, isDebug: 1, parseOnLoad: 1" 
  src="lib/dojo/dojo.js"></script-->

<script type="text/javascript">
  /*       window.onbeforeunload = function () {
   return "You are about to leave the Session AI Platform."
   }
   */
</script>


<!-- Load the loader configuration script. Note that this module ID is hard-coded in build.sh in order to provide
  an optimised build that loads as few as one script for the entire application. If you change the name or
  location of this module, you will need to update build.sh too. -->
<script src="lib/${z1.release.version}/z1/c3/desktop/main.js"></script>


</html>
