<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name='viewport' content='width=device-width, initial-scale=1'>
<title>Session AI Dashboard</title>
<link rel="stylesheet" type="text/css"
	href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dijit/themes/claro/claro.css">
<link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css'>
<link rel="stylesheet" type="text/css" href="/common.css" />
<link rel="stylesheet" type="text/css" href="/runtime.css" />
<link rel="stylesheet" type="text/css" href="/s.css" />
<link media="all" rel="stylesheet" type="text/css"
	href="css/font-awesome.css"/>
<link media="all" rel="stylesheet" type="text/css"
	href="css/themify-icons.css"/>
<link media="all" rel="stylesheet" type="text/css"
	href="css/fontello.css"/>
<script type="text/javascript"
	src="https://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
<script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js'></script>
<script src='https://maxcdn.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js'></script>
<script type="text/javascript" src="/lib/litepickerbundle.js"></script>
<link href='https://fonts.googleapis.com/css?family=Open+Sans'
	rel='stylesheet' type='text/css'>
<!-- experimental fonts -->
<link href='https://fonts.googleapis.com/css?family=Open+Sans:400,300,300italic,400italic,600,600italic,700,700italic,800,800italic'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Archivo+Narrow:400,400italic,700,700italic'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Roboto:400,100,100italic,300,300italic,400italic,500,500italic,700,700italic,900,900italic'
	rel='stylesheet' type='text/css'>
<link href='https://fonts.googleapis.com/css?family=Play:400,700'
	rel='stylesheet' type='text/css'>
<link
	href='https://fonts.googleapis.com/css?family=Arimo:400,700,400italic,700italic'
	rel='stylesheet' type='text/css'>
<!-- end - experimental fonts -->
<link rel="icon" type="image/png" href="/favicon.png">
<!--script type="text/javascript" src="z1m.js"></script-->

<link rel='stylesheet' href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/layout/resources/ExpandoPane.css" />
<link rel='stylesheet' href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/resources/claroGrid.css" />
<link rel='stylesheet' href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/resources/Grid.css" />
<link rel='stylesheet' href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/enhanced/resources/claro/EnhancedGrid.css" />
<link rel='stylesheet' href="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojox/grid/enhanced/resources/EnhancedGrid_rtl.css" />
<!--  Loading google charts -->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<style>
.dojoxGrid table {
	margin: 0;
}
</style>

</head>


<body class="claro x cnw" data-z1-chart-bgImg>
	<div class="s_main index-main-wrap x1 x cnw c3_hidden c3_y_auto" id="s_main">
		<!-- Content goes here -->
	</div>

	<div id="mainStandby" data-dojo-type="dojox/widget/Standby"
		data-dojo-props="target:'s_main'"></div>
    


<!-- 
    1. Enabling the AMD loader. (async: 1)
    2. Fixing the loader to use normal AMD resolution of unregistered module paths (relative to baseDir)
    instead of the traditional Dojo resolution method (relative to the parent of baseDir).
    (tlmSiblingOfDojo: 0)
    3. Enabling debugging. (isDebug: 1)
  
    The remainder of Dojo configuration, as well as our application’s instantiation, occurs within
    z1/main.js.
  
  -->
<script
	data-dojo-config="async: 1, tlmSiblingOfDojo: 0, isDebug: 0, parseOnLoad: 1, gfxRenderer:'canvas,svg,silverlight,vml', packages: []"
	src="https://ajax.googleapis.com/ajax/libs/dojo/1.10.0/dojo/dojo.js"></script>

<script>
  window.z1dashboard = ${dashboard};
  window.z1workspace = ${workspace};
  window._udc_tkey = ${udc_tkey};
  window.z1ns = ${z1ns};
</script>

<!-- Load the loader configuration script. Note that this module ID is hard-coded in build.sh in order to provide
  an optimised build that loads as few as one script for the entire application. If you change the name or
  location of this module, you will need to update build.sh too. -->
<script src="/lib/${z1.release.version}/z1/s/main.js"></script>

</body>
</html>
