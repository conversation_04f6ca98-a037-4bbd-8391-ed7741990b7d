<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="">
<meta name="author" content="Session AI">
<link rel="icon" href="../../favicon.ico">

<title>Session AI - Message preview</title>

<style type="text/css">
body {
/* 	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 14px;
	line-height: 1.42857143;
	color: #333;
 */	
 background-color: #fff;
}
</style>

<style type="text/css">

html {
	position: relative;
	min-height: 100%;
}

/* iphone5 */ 
.c3_msglibrary_pview_d_iphone 
{ 
	width : 300px;
	height: 590px;
	background-image: url("/res/iphone.png");
	overflow: auto;
	position: relative;
	overflow: hidden;
}

.c3_msglibrary_pview_obj {
	position: absolute;
	background-color: white;
}

.c3_msglibrary_pview_d_iphone .c3_msglibrary_pview_obj {
	top: 89px;
	left: 32px;
	right: 36px;
	height: 409px;
	overflow: hidden;
	left: 32px;
	right: 36px;
	height: 409px;
	width: 233px;
}

</style>

<script type="text/javascript">
  
</script>

</head>

<body>
	<section class="pv_main pv_container">

		<div
			class="c3_msglibrary_pview_d_iphone"
			data-device-name="iphone4" id="content">
			
			<iframe class="c3_msglibrary_pview_obj" frameborder=0 src="/template/kbpreview/ios?model=iosphone&url=${url}&header=${header}&date=${date}" seamless/>
			
		</div>
	</section>
</body>

</html>