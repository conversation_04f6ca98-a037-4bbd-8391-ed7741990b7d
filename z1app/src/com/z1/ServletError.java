/**
 * ServletError.java
 * Copyright 2011 by Udichi.com,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of Udichi.com
 */
package com.z1;

/**
 * Defines a servlet error object. This object represents the exception occurred 
 * during the servlet handing.
 */
@SuppressWarnings("serial")
public class ServletError extends Exception
{  
  public ServletError setMessageId(String msgId)
  {
    // TODO: load the message 
    return this;
  }
  
  public ServletError setMessage(String msg)
  {
    this._msg = msg;
    return this;
  }
  
  public ServletError setReason(Exception e)
  {
    if (this._msg.length() > 0)
    {
      this._msg += "[" + e.getLocalizedMessage() + "]";
    }
    else
    {
      this._msg = e.getLocalizedMessage();
    }
    
    return this;
  }
  
  /**
   * Returns the error message in a json format.
   */
  public String getMessage()
  {
    return "{error: \"" + this._msg + "\"}";
  }
  
  private String _msg = "";
}
