/**
 * AbstractCommandHandler.java
 * Copyright 2012 by Udichi.com.,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of Udichi Inc. 
 */
package com.z1;


/**
 * Creates handlers for different REST verbs.
 */
public interface CommandHandlerFactory
{
  /**
   * Returns a GET request handler.
   */
  public CommandHandler get();
  
  /**
   * Returns a POST request handler.
   */
  public CommandHandler post();
}
