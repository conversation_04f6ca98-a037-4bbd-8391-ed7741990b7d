/**
 * Copyright 2011 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.SharedUrl;
import z1.c3.SharedUrlService;

/**
 * Launches the stand-alone page to serve the shared URL to show a dashboard
 * etc..
 */
@SuppressWarnings("serial")
public class C3PublicUrlProviderServlet extends HttpServlet
{
  // String used to capture anonymous namespace stored in the cache.
  public static final String ANONYMOUS = "anonymous";

  private static final String SHARED_PARAM = "shared";
  private static final String STANDALONE_HTML = "com/z1/resource/c3/standalone.html";

  static
  {
    // Set the login handler for udichi system
    App.setLoginHandler(LoginServlet.getLoginHandler());
  }

  @Override
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/html");
    resp.setCharacterEncoding("UTF-8");

    UContext uctx = UContext.getInstance(req);

    try
    {

      String urlId = req.getParameter(SHARED_PARAM);

      if (urlId == null || urlId.isEmpty())
      {
        resp.sendError(HttpServletResponse.SC_NOT_FOUND);
        return;
      }

      SharedUrlService ss = new SharedUrlService(uctx);
      SharedUrl url = ss.getSharedUrlById(urlId);
      if (url == null)
      {
        resp.sendError(HttpServletResponse.SC_NOT_FOUND);
        return;
      }

      if (!url.getItemType().equals(SharedUrl.Fields.itemType.getValue()))
      {
        resp.sendError(HttpServletResponse.SC_NOT_FOUND);
        return;
      }

      String namespace = url.getNamespace();
      if (namespace == null || namespace.isEmpty())
      {
        resp.sendError(HttpServletResponse.SC_NOT_FOUND);
        return;
      }

      // APPROACH:
      // - We will create the anonymous user for the given namespace and store
      // the
      // info in the cache.
      // - Set the generated token key to browser "window" object.
      // --- loader.js looks for the variable "_udc_tkey" in the "udc.context"
      // or "window" object. If available it sends the value as HTTP header.
      // --- "system/context" call tries to find out a user
      Map<String, Object> map = new java.util.HashMap<>();
      map.put(ANONYMOUS, namespace);
      map.put("s", urlId);
      final String anonNamespace = new JsonMarshaller().serialize(map);
      final String tkey = ServletUtil.putTokenToCache(anonNamespace);

      // Fetch dashboard ID and workspace ID from shared url
      JsonMarshaller jm = new JsonMarshaller();
      @SuppressWarnings("unchecked")
      List<Map<String, Object>> params = jm.readAsObject(url.getParams(),
          List.class);

      String dId = url.getItemId();
      String wId = null;
      if (params != null && !params.isEmpty())
      {
        for (Map<String, Object> param : params)
        {
          String name = (String) param.get("name");
          if (name.equals("workspace"))
          {
            wId = (String) param.get("value");
            break;
          }
        }
      }

      // Get the input stream from the current class loader for the
      // standalone.html resource
      // substitute value for dashboard and workspace var
      InputStream is = ServletUtil.class.getClassLoader()
          .getResourceAsStream(STANDALONE_HTML);
      String content = new BufferedReader(new InputStreamReader(is)).lines()
          .parallel().collect(Collectors.joining("\n"));

      content = content.replaceAll(Pattern.quote("${dashboard}"),
          "\"" + dId + "\"");
      content = content.replaceAll(Pattern.quote("${workspace}"),
          "\"" + wId + "\"");
      content = content.replaceAll(Pattern.quote("${udc_tkey}"),
          "\"" + tkey + "\"");
      content = content.replaceAll(Pattern.quote("${z1ns}"),
          "\"" + namespace + "\"");
      is.close();

      resp.getWriter().println(content);
    }
    catch (Exception e)
    {
      ServletError err = new ServletError().setReason(e);
      resp.getWriter().print(err.getMessage());
    }

  }
}
