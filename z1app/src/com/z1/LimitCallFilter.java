package com.z1;

import java.io.IOException;
import java.io.InputStream;
import java.util.Calendar;
import java.util.Properties;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import udichi.core.USessionStore;

/**
 * Checks the frequency of the api calls and limit them.
 */
public class LimitCallFilter implements Filter
{
  private static final String K_CALL_COUNTER = "__klkc"; 
  private static final String K_LAST_TIME = "__klt";
  private static final String CALL_LIMIT_PROP = "api.call.limit";
  private static final String TIME_GAP_PROP = "api.call.time.gap";

  // Time gap to compute the api frequency
  private static final long DEFAULT_TIME_GAP = (1 * 10 * 1000);
  private static long TIME_GAP = DEFAULT_TIME_GAP;
  private static final long DEFAULT_CALL_LIMIT = 50;

  private static long CALL_LIMIT = DEFAULT_CALL_LIMIT;

  @Override
  public void destroy()
  {
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response,
      FilterChain chain) throws IOException, ServletException
  {
    HttpServletRequest req = (HttpServletRequest) request;
    String reqURI = req.getRequestURI();
    
    if (com.z1.handler.ModulesHandler.GetCommand.getUris().stream().anyMatch(reqURI::contains)
        || com.z1.handler.ModulesHandler.PostCommand.getUris().stream().anyMatch(reqURI::contains)
        || com.z1.handler.SignalsHandler.GetCommand.getUris().stream().anyMatch(reqURI::contains))
    {
      chain.doFilter(request, response);
    }
    else
    {
      USessionStore.Store sessionStore = UContext.getInstance(req).sessionStore(false);
      if (sessionStore == null) return;

      long now = Calendar.getInstance().getTimeInMillis();
      Object o1 = sessionStore.get(K_LAST_TIME);
      Long lastTime = (o1 == null) ? now : ((Long) o1);

      Object o2 = sessionStore.get(K_CALL_COUNTER);
      Integer counter = (o2 == null) ? 0 : ((Integer) o2);

      boolean isAllowed = true;

      if (((now - lastTime) == 0) || (now - lastTime > TIME_GAP))
      {
        sessionStore.put(K_LAST_TIME, now);
        sessionStore.put(K_CALL_COUNTER, 1);
      }
      else
      {
        counter++;
        sessionStore.put(K_CALL_COUNTER, counter);

        if (counter > CALL_LIMIT)
        {
          isAllowed = false;
        }
      }

      if (!isAllowed)
      {
        throw new ServletException(
            "Calls are too frequent for a logged in session.");
      }

      chain.doFilter(request, response);
    }
  }

  @Override
  public void init(FilterConfig filterConfig) throws ServletException
  {
    Properties z1props = new Properties();

    try (InputStream is = z1.commons.Utils.class.getClassLoader()
        .getResourceAsStream("META-INF/zineone.properties"))
    {
      if (is != null)
      {
        z1props.load(is);

        String callLimitProp = z1props.getProperty(CALL_LIMIT_PROP);

        if (callLimitProp != null && !callLimitProp.isEmpty()
            && !callLimitProp.equals(String.valueOf(DEFAULT_CALL_LIMIT)))
        {
          Long callLimit = Long.valueOf(callLimitProp);

          if (callLimit != null && callLimit > 0)
          {
            CALL_LIMIT = callLimit;
          }
        }

        String timeGapProp = z1props.getProperty(TIME_GAP_PROP);
        TIME_GAP = timeGapProp != null && !timeGapProp.isEmpty()
            && !timeGapProp.equals(String.valueOf(DEFAULT_TIME_GAP))
                ? Long.parseLong(timeGapProp)
                : DEFAULT_TIME_GAP;
      }
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }
  }

}
