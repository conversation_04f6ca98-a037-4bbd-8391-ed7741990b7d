/**
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import udichi.core.ApiKey;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import z1.commons.Utils;
import z1.core.utils.Misc;
import z1.css.CssLoader;
import z1.css.CssLoader.ContentCssType;
import z1.css.CssLoader.Target;

import com.z1.Utils.ApiUtils;
import com.z1.Utils.ResponseMessage;

/**
 * Implements REST support for c3/system/* commands.
 */
@SuppressWarnings("serial")
public class C3ResServlet extends HttpServlet
{
  public static final String HEADER_APIKEY = "apikey";

  private enum Command
  {
    chatdetail,
    chatinbox,
    kblist,
    faqlist,
    kb,
    faq,
    message,
    kbpreview,
    faqpreview,
    fullscreen,
    appbox
  }


  @Override
  protected void doOptions(HttpServletRequest req, HttpServletResponse resp)
      throws ServletException, IOException
  {
    // Set the header to support CORS (x-origin requests)
    resp.setHeader("Access-Control-Allow-Origin", "*");
    resp.setHeader("Access-Control-Allow-Headers", "apikey, accesstoken");

    super.doOptions(req, resp);
  }

  // ..................................................
  // Gets
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/html");
    // Set the header to support CORS (x-origin requests)
    resp.setHeader("Access-Control-Allow-Origin", "*");
    resp.setHeader("Access-Control-Allow-Headers", "apikey, accesstoken");
    
    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/res/<artifact type>
      // So we are expecting at least 2 parts after splitting, the first one
      // being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 3)
      {
        return;
      }

      // Get the api key from the header
      UContext ctx = UContext.getInstance(req);
      if (ctx.getNamespace() == null)
      {
        // Get the api key from the request
        ApiKey key = ApiUtils.getApiKey(ctx, req);
        ApiUtils.updateContextNamespace(ctx, key);
      }

      Command type = Command.valueOf(pathParts[1].toLowerCase());

      String os = "ios";
      if (pathParts.length > 2)
      {
        os = pathParts[2].toLowerCase();
      }

      String deviceType = req.getParameter("devicetype"); //phone,tablet,etc
      
      /**
       * TODO: This is temporary till the sdks are upto date
       */
      if (deviceType == null)
      {
        deviceType = req.getParameter("model"); //older sdk sends model=androidphone
        if (deviceType == null)
        {
          deviceType = "phone";
        }
        else
        {
          if (deviceType.endsWith("phone") || deviceType.contains("phone"))
          {
            deviceType = "phone";
          }
          else if (deviceType.endsWith("pad") || deviceType.contains("pad"))
          {
            deviceType = "pad";
          }
          else if (deviceType.endsWith("tablet")
              || deviceType.contains("tablet"))
          {
            deviceType = "tablet";
          }
          else
          {
            deviceType = "phone";
          }
        }
      }
      
      
      String onlyCss = req.getParameter("onlycss");
      boolean onlyCssFlag = false;
      if (onlyCss != null)
      {
        onlyCssFlag = Boolean.parseBoolean(onlyCss);
      }

      switch (type)
      {
        default:
        {
          return;
        }
        // Returns the chat initial messages that renders the text and the chat
        // button to initiate a chat
        // /template/chatinit/[android | ios]
        case chatdetail:
        {
          String response = CssLoader.loadCss(ctx, false, "chat", os, deviceType, "client");
          if(!onlyCssFlag) response = loadHtml(ctx, "chat", os, deviceType, "client", response);    
          resp.getWriter().print(response);
          break;
        }

        // Returns the chat client resources that will render a chat view on the
        // client
        // /template/chatInbox/[android | ios]
        case chatinbox:
        {       
          String response = CssLoader.loadCss(ctx, false, "chat", os, deviceType, "inbox");
          if(!onlyCssFlag) response = loadHtml(ctx, "chat", os, deviceType, "inbox", response);    
          resp.getWriter().print(response);                  
          break;
        }

        // for now we are reusing kb
        case faqlist:
        {
          String response = CssLoader.loadCss(ctx, false, "article", os, deviceType, "list");
          if(!onlyCssFlag) response = loadHtml(ctx, "faq", os, deviceType, "list", response);    
          resp.getWriter().print(response); 
          break;
        }

        // client
        // /template/kblist/[android | ios]
        case kblist:
        {
          String response = CssLoader.loadCss(ctx, false, "article", os, deviceType, "list");
          if(!onlyCssFlag) response = loadHtml(ctx, "article", os, deviceType, "list", response);    
          resp.getWriter().print(response);
          break;
        }

        // for now we are reusing kb
        case faq:
        {
          String response = CssLoader.loadCss(ctx, false, "article", os, deviceType, "content");
          if(os.equalsIgnoreCase(Target.html5.name()))
          {
            String contentBoxCss = CssLoader.loadContentBoxCss(ctx, false, "article", os, deviceType, "content");
            if(contentBoxCss!=null) response+= contentBoxCss;
          }
          
          if(!onlyCssFlag) response = loadHtml(ctx, "faq", os, deviceType, "content", response);    
          resp.getWriter().print(response);
          break;
        }

        // client
        // /template/kb/[android | ios]
        case kb:
        {
          String response = CssLoader.loadCss(ctx, false, "article", os, deviceType, "content");
          if(os.equalsIgnoreCase(Target.html5.name()))
          {
            String contentBoxCss = CssLoader.loadContentBoxCss(ctx, false, "article", os, deviceType, "content");
            if(contentBoxCss!=null) response+= contentBoxCss;
          }
          
          if(!onlyCssFlag) response = loadHtml(ctx, "article", os, deviceType, "content", response);    
          resp.getWriter().print(response);
          break;
        }

        case message:
        {
          String response = CssLoader.loadCss(ctx, false, "alrt", os, deviceType, "main");
          if(!onlyCssFlag) response = loadHtml(ctx, "alrt", os, deviceType, "main", response);    
          resp.getWriter().print(response);
          break;
        }

        // /template/kbpreview?url=<url>
        case faqpreview:
        {
          String url = req.getParameter("url");
          String header = req.getParameter("header");
          String date = req.getParameter("date");

          String response = CssLoader.loadCss(ctx, false, "article", os, deviceType, "content");
          if(!onlyCssFlag) response = loadHtml(ctx, "article", os, deviceType, "content", response);    
          
          // We'll substitute the "uel" var with the url data sent to us to
          // preview
          response = response.replaceAll("\\%z1.content.url\\%", url);
          response = response.replaceAll("\\%z1.content.title\\%", header);
          response = response.replaceAll("\\%z1.content.date\\%", date);
          resp.getWriter().print(response);
          break;
        }

        // /template/kbpreview?url=<url>
        case kbpreview:
        {
          String url = req.getParameter("url");
          String header = req.getParameter("header");
          String date = req.getParameter("date");

          String response = CssLoader.loadCss(ctx, false, "article", os, deviceType, "content");
          if(!onlyCssFlag) response = loadHtml(ctx, "article", os, deviceType, "content", response);    

          // We'll substitute the "uel" var with the url data sent to us to
          // preview
          response = response.replaceAll("\\%z1.content.url\\%", url);
          response = response.replaceAll("\\%z1.content.title\\%", header);
          response = response.replaceAll("\\%z1.content.date\\%", date);
          resp.getWriter().print(response);
          break;
        }
        
        case fullscreen:
        {
          String response = CssLoader.loadCss(ctx, false, "fullscreen", os, deviceType, "main");
          if(!onlyCssFlag) response = loadHtml(ctx, "fullscreen", os, deviceType, "main", response); 
          resp.getWriter().print(response);
          break;
        }
        
        case appbox:
        {
          String response = CssLoader.loadCss(ctx, false, "appbox", os, deviceType, "content");
          if(!onlyCssFlag) response = loadHtml(ctx, "appbox", os, deviceType, "content", response); 
          resp.getWriter().print(response);
          break;
        }
        
      }

      resp.getWriter().println("");
      return;
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), "Server side error.");
      resp.getWriter().println("}");
    }

  }

  // ..................................................
  public void doPost(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/plain");
    // Set the header to support CORS (x-origin requests)
    resp.setHeader("Access-Control-Allow-Origin", "*");
    resp.setHeader("Access-Control-Allow-Headers", "apikey, accesstoken");

    try
    {
      UContext ctx = UContext.getInstance(req);
      // Get the api key from the request
      ApiKey key = ApiUtils.getApiKey(ctx, req);
      ApiUtils.updateContextNamespace(ctx, key);
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), "Server side error.");      
      resp.getWriter().println("}");
    }

  }
  
  public String loadHtml(UContext ctx, String module, String os,
      String deviceType, String subCategory, String cssPayload)
      throws Throwable
  {
    ULogger logger = ctx.getLogger(CssLoader.class);
    Target target = Target.valueOf(os);
    final String resourcePathPrefix = "com/z1/resource/m/" + target.name();
    logger.log("Resource path prefix: " + resourcePathPrefix);
    
    // getting html.
    String htmlPayload = null;
    
    //com/z1/resource/m/<target>/<module>/<css|html>/<deviceType>/<resourcename> 
    StringBuilder htmlResName = new StringBuilder(resourcePathPrefix).append("/").append(module).append("/html/").append(deviceType).append("/").append(subCategory).append("/content.html");
    
    InputStream deviceDefault = CssLoader.class.getClassLoader().getResourceAsStream(htmlResName.toString());    
    if (deviceDefault == null) // if default device specific css file is not present
    {
      ResponseMessage rm = new ResponseMessage(ctx, ResponseMessage.Status.fail, ResponseMessage.Type.resourceNotFound);
      return rm.toString();
    }
    else
    {
      htmlPayload = Misc.readString(deviceDefault);
    }

    HashMap<String, Object> hm = new HashMap<String, Object>();
    hm.put("z1_css", cssPayload);
    String fullHtml = StringUtil.substitueValue(htmlPayload, hm, "",
        new HashMap<String, Object>());

    return fullHtml;
  }
  


}
