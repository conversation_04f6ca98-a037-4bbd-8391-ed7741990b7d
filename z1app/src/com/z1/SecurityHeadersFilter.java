package com.z1;

import com.z1.Utils.ApiUtils;
import udichi.core.UContext;
import z1.c3.SystemConfig;
import z1.commons.Const;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * This class is for adding security headers to response
 * 
 * Some explanations at
 * https://geekflare.com/http-header-implementation/#Content-Security-Policy
 */
public class SecurityHeadersFilter implements Filter
{

  public static final String HEADER_X_XSS_PROTECTION = "X-XSS-Protection";
  public static final String HEADER_STRICT_TRANSPORT_SEC = "Strict-Transport-Security";
  public static final String HEADER_X_CONTENT_TYPE_OPT = "X-Content-Type-Options";
  public static final String HEADER_X_Frame_OPT = "X-Frame-Options";
  public static final String HEADER_CACHE_CONTROL = "Cache-Control";
  public static final String HEADER_PRAGMA = "Pragma";
  public static final String HEADER_CONTENT_SEC_POLICY = "Content-Security-Policy";

  @Override
  @SuppressWarnings("unchecked")
  public void doFilter(ServletRequest request, ServletResponse response,
      FilterChain chain) throws IOException, ServletException
  {
    HttpServletRequest httpRequest = (HttpServletRequest) request;
    String pathinfo = Optional.ofNullable(httpRequest.getPathInfo()).orElse("");
    HttpServletResponse resp = (HttpServletResponse) response;
    resp.setHeader(HEADER_X_XSS_PROTECTION, "1; mode=block");
    resp.setHeader(HEADER_STRICT_TRANSPORT_SEC,
        "max-age=31536000; includeSubDomains");
    resp.setHeader(HEADER_X_CONTENT_TYPE_OPT, "nosniff");
    resp.setHeader(HEADER_X_Frame_OPT, "DENY");

    if (pathinfo.contains("v1/kbdoc"))
      resp.setHeader(HEADER_CONTENT_SEC_POLICY,
          "default-src * 'unsafe-inline' 'unsafe-eval'");
    else if (!pathinfo.isEmpty())
      resp.setHeader(HEADER_CONTENT_SEC_POLICY, "default-src 'self';");

    if (pathinfo.contains("v1/"))
    {
      resp.setHeader(HEADER_CACHE_CONTROL, "no-store");
      resp.setHeader(HEADER_PRAGMA, "no-cache");

      // Add additional response headers for the v1 endpoints
      addAdditionalResponseHeaders(httpRequest, resp);
    }

    chain.doFilter(request, resp);

  }

  /**
   * Add additional response headers
   * @param httpRequest
   * @param resp
   */
  private void addAdditionalResponseHeaders(HttpServletRequest httpRequest,
      HttpServletResponse resp)
  {
    UContext ctx = UContext.getInstance(httpRequest, true);
    // context namespace can be null if it's not c3 data endpoints
    if (ctx.getNamespace() == null)
    {
      ApiUtils.updateContextNamespaceFromApiKey(ctx, httpRequest);
    }

    if (ctx.getNamespace() != null)
    {
      String respHeaderStr = SystemConfig
          .getStringValue(ctx, "z1.response.headers", "").trim();
      if (!respHeaderStr.isEmpty())
      {
        Arrays.stream(respHeaderStr.split(Pattern.quote(Const.valSeparator)))
            .map(String::trim).map(header -> header.split(Const.colonSeparator))
            .filter(headerParts -> headerParts.length == 2
                && headerParts[0] != null && headerParts[1] != null)
            .forEach(headerParts -> {
              String headerName = headerParts[0].trim();
              String headerValue = headerParts[1].trim();
              if (!headerName.isEmpty() && !headerValue.isEmpty())
              {
                resp.setHeader(headerName, headerValue);
              }
            });
      }
    }
  }

  @Override
  public void init(FilterConfig arg0) throws ServletException
  {
  }

  @Override
  public void destroy()
  {
  }

}
