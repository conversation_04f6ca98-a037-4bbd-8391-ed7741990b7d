/**

 * AppsAuthServlet.java
 * Copyright 2011 by Udichi.com,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of Udichi.com
 */
package com.z1;

import com.z1.Utils.ApiUtils;
import com.z1.Utils.ResponseMessage;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHeaders;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.log.ULogger;
import udichi.core.system.AuthException;
import udichi.core.system.AuthException.Reason;
import udichi.core.system.LoginHandler;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.SIPUtils;
import udichi.core.util.ServletUtil;
import z1.account.Z1Account;
import z1.account.Z1AccountService;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.centmon.collectors.LoginInfoCollector;
import z1.commons.encryption.AesUtil;
import z1.commons.encryption.Encryption;
import z1.core.utils.Utils;
import z1.sso.saml.RelayState;
import z1.sso.saml.SamlAuth;
import z1.users.User;
import z1.users.User.LoginStatus;
import z1.users.User.LoginStatus.Status;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.InetAddress;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Authenticates the access to the Apps runtime.
 * </p>
 * This servlet processes a POST request that passes the "userid" and
 * "password". Based on the request parameters, it either returns the status of
 * the authentication or forward the request to application start page.
 * <p/>
 * <b>Request payload</b></br>
 * The request will contain a JSON payload as follows.
 *
 * <pre>
 * {
 *   "userid": "[some user id]",
 *   "password": "[password in plane text]"
 * }
 * </pre>
 *
 * </p>
 * <h3>Case: Forward to the start page</h3> Following request will forward the
 * request to the application start page after a successful login. If the login
 * fails, it returns the failure status with the reason of the failure. </br>
 * </br>
 *
 * <i>.../apps/login.sp?forward</i>
 * </p>
 *
 * <h3>Case: Authenticate API</h3> Following will send a JSON payload with
 * status information.
 * </p>
 * <i>.../apps/login.sp</i>
 * </p>
 * <b>Response</b></br>
 * If the login is a valid login it returns the response as follows.
 *
 * <pre>
 * {
 *   status: "success",
 *   user: {
 *     id: "[user id]",
 *     fname: "[user first name]",
 *     lname: "[user last name]",
 *     zip: "[zip code]"
 *   },
 *   apps: [
 *     {
 *       [application data]
 *     },
 *     {
 *       [application data]
 *     },
 *     ...
 *   ]
 * }
 * </pre>
 */
@SuppressWarnings("serial")
public class LoginServlet extends HttpServlet
{
  public static final String TOKEN = "token";

  private static final String USERID = "userid";
  private static final String PASSWORD = "password";
  public static final String POSTMAN_PREFIX = "PostmanRuntime";

  private boolean _isPostmanLogin = false;

  static
  {
    // Set the login handler for udichi system
    App.setLoginHandler(getLoginHandler());
  }

  // ...............................................................
  /**
   * Returns a login handler insatnce.
   *
   * @return
   */
  static LoginHandler getLoginHandler()
  {
    return new LoginHandler() {

      @Override
      public udichi.core.system.User login(UContext ctx, String user,
          String password) throws AuthException
      {
        LoginStatus loginStatus = User.login(ctx, user, password);
        if (loginStatus.getStatus() == Status.fail)
        {
          throw new AuthException(Reason.INVALID_PWD);
        }
        return loginStatus.getUser();
      }

      @Override
      public boolean canAccessWorkbench(udichi.core.system.User user)
      {
        // If the role is sysadmin, user can access workbench
        User u = (User) user;
        return u.getRole().hasRole(User.Role.sysadmin);
      }

      @Override
      public udichi.core.system.User getUser(UContext ctx, String userId)
      {
        // The userId can be null. In that case, we will check if there is
        // an anonymous user stored in the token cache
        if (userId == null)
        {
          // Do we have a request associated with the ctx?
          return Optional.ofNullable(ctx.getHttpRequest()).map(r -> {
            String payload = ServletUtil.getTokenFromCache(r);
            if (payload == null) return null;
            final String ANON = C3PublicUrlProviderServlet.ANONYMOUS;
            if (payload.startsWith("{"))
            {
              Map<String, Object> map = new JsonMarshaller().readAsMap(payload);
              String namespace = (String) map.get(ANON);
              if (namespace == null) return null;
              // This is a separate window, we need an anonymous login here
              ctx.setNamespace(namespace);
              User user = User.loadOrCreateZ1anon(ctx, namespace);
              ctx.setUser(user);
              return user;
            }
            return null;
          }).orElse(null);
        }

        // Otherwise load the user
        return User.load(ctx, userId);
      }

    };
  }

  // ...........................................................
  @Override
  public void doPost(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    try
    {
      UContext ctx = UContext.getInstance(req);
      RelayState relayState = this.getRelayState(req);
      String namespace = this.getNamespace(req, relayState);
      // For SSO the namespace is passed in through the relay state object.
      // Strict SAML validation fails when passing namespace in the url parameters.
      if (relayState != null)
      {
        ctx.setNamespace(namespace);
      }
      resp.setContentType("text/plain");

      ULogger logger = ctx.getLogger(getClass());

      Z1AccountService accountService = null;
      Z1Account account = null;

      // Check for postman and 'qa' option flag
      final String userAgent = req.getHeader(HttpHeaders.USER_AGENT);
      _isPostmanLogin = (userAgent != null && userAgent.startsWith(POSTMAN_PREFIX)
          && "true".equalsIgnoreCase(req.getParameter("qa")));

      String token = this.getToken(ctx, req, relayState);

      // If no token found, we can't do anything here
      if (token == null && !_isPostmanLogin)
      {
        logger.warning(String.format(
            "[Login] Login request received for '%s'. But no token found to decrypt the message.",
            namespace));
        throw new UException("No token found for the given token key.");
      }

      // ZMOB-16831
      // If namespace is null, we can't login anymore for a multi sub cloud infra.
      // So we will exit here. The login from postman should include the namespace
      // as the request parameter.
      if (namespace != null)
      {
        accountService = new Z1AccountService(ctx);
        account = accountService.getAccount(namespace);
      }
      // Return if there is no account available
      if (account == null)
      {
        logger.warning(String.format(
            "[Login] Login request received - no active account found for namespace '%s'.",
            namespace));
        return;
      }

      // ZMOB-16831
      // Create the session if not present. This can happen when a new browser
      // session starts in a multiple sub-cloud env. One of the sub-cloud might
      // get the login call before it gets the "system/context" call. Because a
      // "system/context" call without a namespace (when a browser is started for
      // the first time) can go to the default sub-cloud, which may not be the
      // same one receiving the "login" call.

      Optional.ofNullable(ctx.sessionStore(true))
          .ifPresent(s -> s.put(TOKEN, token));

      // reading ip address from request
      String ipAddress = ApiUtils.getCustomerIpAddress(req);
      if (ipAddress != null)
      {
        // if the IP address having multiple IPs based on load balancer,
        // extract first one as request routing from that IP to load balancers.
        ipAddress = ipAddress.contains(",") ? ipAddress.split(",")[0] : ipAddress;
        // if ip address is local then setting host address as ip address
        // authenticate the user and sets the user as a current user
        if (ipAddress.equalsIgnoreCase("0:0:0:0:0:0:0:1"))
        {
          InetAddress inetAddress = InetAddress.getLocalHost();
          ipAddress = inetAddress.getHostAddress();
        }
      }

      // Is this a SSO?
      boolean useSso = account.getProperties().getIsSsoEnabled();
      if (useSso)
      {
        this._doSSOLogin(ctx, account, ipAddress, namespace, req, resp);
      }
      else
      {
        this._doC3Login(ctx, account, token, ipAddress, req, resp);
      }

    }
    catch (Throwable e)
    {
      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), e.getLocalizedMessage());
      resp.getWriter().println("}");
    }
  }

  /**
   * Returns the token from the given token key in the request/relay state.
   *
   * @param ctx
   * @param req
   * @param relayState
   * @return
   */
  private String getToken(UContext ctx, HttpServletRequest req, RelayState relayState)
  {
    HttpServletRequest httpServletRequest = req;
    // In the case of SSO the token key is passed in through the relay state object
    if (relayState != null)
    {
      Z1HttpServletRequestWrapper z1HttpServletRequestWrapper = new Z1HttpServletRequestWrapper(req);
      z1HttpServletRequestWrapper.addHeader("x-udc-tkey", relayState.getTokenKey());
      httpServletRequest = z1HttpServletRequestWrapper;
    }
    
    return this.getTokenFromStoreOrCache(ctx, httpServletRequest);
  }
  
  /**
   * Gets the token from store or cache.
   *
   * @param ctx
   * @param req
   * @return
   */
  private String getTokenFromStoreOrCache(UContext ctx, HttpServletRequest req)
  {
    // Note that this is a separate function because of the way this code is
    // written the req object must be final/effectively final.
    return Optional.ofNullable(ctx.sessionStore(false))
        .map(s -> (String) s.get(TOKEN))
        .orElseGet(() -> ServletUtil.getTokenFromCache(req));
  }

  /**
   * Returns the relay state object from the request. Otherwise returns null.
   * This only returns a relay state object if the login came from the SSO route.
   *
   * @param req
   * @return
   */
  private RelayState getRelayState(HttpServletRequest req)
  {
    String relayStateString = req.getParameter("RelayState");
    if (!StringUtils.isEmpty(relayStateString))
    {
      JsonMarshaller jm = new JsonMarshaller();
      return jm.readAsObject(relayStateString,
          RelayState.class);
    }
    
    return null;
  }

  // /////////////////////////////////////////////////////////////////////////////////////////
  // /////////////////////////////////////////////////////////////////////////////////////////
  // Class internals
  // /////////////////////////////////////////////////////////////////////////////////////////
  // /////////////////////////////////////////////////////////////////////////////////////////

  // ..........................................................
  private void _doC3Login(UContext ctx, Z1Account account, String token,
      String ipAddress, HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    ULogger logger = ctx.getLogger(getClass());
    // Get the salt
    String salt = !_isPostmanLogin ? new SIPUtils().parseSaltFromToken(token)
        : null;
    String iv = !_isPostmanLogin ? new SIPUtils().parseIVFromToken(token)
        : null;
    AbstractMap.SimpleImmutableEntry<String, String> pair = this
        ._getUserIdPasswordPair(req, salt, iv, ctx);
    if (pair == null || pair.getKey() == null) return;

    String userId = pair.getKey();
    String password = pair.getValue();

    if (logger.canLog()) logger
        .log(String.format("[Login] Login request received for user ID: %s", userId));

    LoginStatus loginStatus = User.login(ctx, userId, password);

    User user = loginStatus.getUser();
    if (user != null)
    {
      if (req.getRequestURI().endsWith("workbench")
          && !App.loginHandler.canAccessWorkbench(user))
      {
        throw new UException("[Login] Not authorized to access workbench.");
      }

      String userNS = (String) user.getValues().get(udichi.core.system.User.NS);
      if (!ctx.getNamespace().equalsIgnoreCase(userNS))
      {
        if (logger.canLog()) logger.log(
            String.format("[Login] User '%s' is not authorized to use namespace: %s",
                userId, userNS));
        throw new UException(
            "[Login] Not authorized to access namespace: " + ctx.getNamespace());
      }

      // set the user to the runtime context in order to audit the login
      // attempt
      ctx.setUser(user);
    }

    // Log
    _captureLoginInfo(ctx, loginStatus, userId, ipAddress);

    // Audit the login attempt
    this._auditLoginAttempt(ctx, loginStatus, userId, user,
        req.getHeader("User-Agent"), ipAddress);

    // create the json payload for the user object
    JsonMarshaller m = new JsonMarshaller();
    HashMap<String, Object> hm = new HashMap<>();

    if (account != null)
    {
      hm.put("industry", account.getIndustry());
    }
    hm.put("user", user);
    hm.put("reason",
        ResponseMessage.createMessage(ctx, loginStatus.getReason()));
    hm.put("status", loginStatus.getStatus().name());
    hm.put("mode", loginStatus.getMode().name());
    Optional.ofNullable(ServletUtil.getTokenKey(req))
        .ifPresent(k -> hm.put("tkey", k));

    // We'll add the api key to connect to the backend z1 server for real time
    // engagement.
    if (user != null)
    {
      String apiKey = z1.commons.Utils.getPropertyValue("z1.apikey");
      if (apiKey != null)
      {
        hm.put("apikey", apiKey);
      }
    }

    String jsonUser = m.serializeMap(hm);
    String encuser = new AesUtil().encrypt(salt, iv, null, jsonUser);
    resp.getWriter().print(encuser);
  }

  // ...............................................................
  /**
   * Uses SSO to authenticate.
   *
   * @param ctx
   * @param account
   * @param ipAddress
   * @param req
   * @param resp
   * @throws IOException
   */
  private void _doSSOLogin(UContext ctx, Z1Account account, String ipAddress,
      String namespace, HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    SamlAuth samlAuth = new SamlAuth(ctx, account, req, resp);
    LoginStatus loginStatus = User.login(ctx, samlAuth);
    User user = loginStatus.getUser();
    String userId = samlAuth.getLoginId();

    if (loginStatus.getStatus() != Status.success)
    {
      String loginErrorCode = getLoginErrorCode(loginStatus);
      Cookie cookie = new Cookie("LoginError", loginErrorCode);
      cookie.setSecure(true);
      cookie.setPath("/;HttpOnly");
      resp.addCookie(cookie);
    }

    // set the user to the runtime context in order to audit the login
    // attempt
    ctx.setUser(user);

    // Log
    _captureLoginInfo(ctx, loginStatus, userId, ipAddress);

    // Audit the login attempt
    this._auditLoginAttempt(ctx, loginStatus, userId, user,
        req.getHeader("User-Agent"), ipAddress);

    String c3Url = String.format("%s/c3?namespace=%s",
        Utils.getDomainNameUrl(req.getRequestURL().toString(), false), namespace);
    
    String industry = account.getIndustry();
    Cookie domainIndustryCookie = new Cookie("domainIndustry", industry);
    //String domain = Utils.getDomainNameUrl(req.getRequestURL().toString(), false);
    //domainIndustryCookie.setDomain(domain);
    resp.addCookie(domainIndustryCookie);
    
    resp.sendRedirect(c3Url);
  }

  // ...............................................................
  /**
   * Capture the audit trail for the login attempt.
   */
  private void _auditLoginAttempt(UContext ctx, LoginStatus loginStatus,
      String userId, User user, String userAgent, String ipAddress)
  {
    // Audit the login attempt
    ArtifactAudit aa = null;
    if (userId == null) userId = "null";

    if (loginStatus.getStatus().equals(LoginStatus.Status.success))
    {
      aa = ArtifactAudit.newInstance(ctx, ItemTypes.user, userId.toLowerCase(),
          userId.toLowerCase(), Operations.login);
    }
    else
    {
      // Status failure
      int numTries = 1;
      if (user != null)
      {
        numTries = Optional
            .ofNullable(user.getValues().get(User.Fields.numTries.name()))
            .map(Integer.class::cast).orElse(1);
      }

      switch (numTries)
      {
        case 1:
          aa = ArtifactAudit.newInstance(ctx, ItemTypes.user,
              userId.toLowerCase(), userId.toLowerCase(),
              Operations.loginFailed);
          break;
        case 2:
          aa = ArtifactAudit.newInstance(ctx, ItemTypes.user,
              userId.toLowerCase(), userId.toLowerCase(),
              Operations.loginFailedTwice);
          break;
        case 3:
          if (!loginStatus.getReason()
              .equals(LoginStatus.Reason.tooManyRetries))
          {
            aa = ArtifactAudit.newInstance(ctx, ItemTypes.user,
                userId.toLowerCase(), userId.toLowerCase(),
                Operations.loginFailedThrice);
          }
          break;
        default:
          break;
      }
    }

    if (aa != null)
    {
      aa.getValues().put(ArtifactAudit.Fields.browser.name(), userAgent);
      aa.getValues().put(ArtifactAudit.Fields.ip.name(), ipAddress);
      aa.save();
    }
  }

  // ...............................................................
  /**
   * Extracts the user ID and password pair passed via the request object.
   *
   * @param req
   * @param salt
   * @param uctx
   * @return The user ID, password as key value pair.
   */
  private AbstractMap.SimpleImmutableEntry<String, String> _getUserIdPasswordPair(
      HttpServletRequest req, String salt, String iv, UContext uctx)
  {
    try
    {
      StringBuilder payload = new StringBuilder(100);
      String userId = null;
      String password = null;

      java.io.BufferedReader reader = req.getReader();
      String s;
      while ((s = reader.readLine()) != null)
      {
        payload.append(s);
      }

      if (payload.length() == 0)
      {
        throw new UException("No payload sent for login request");
      }

      String pl = null;
      if (_isPostmanLogin)
      {
        pl = payload.toString();
        Map<String, Object> data = new JsonMarshaller().readAsMap(pl);
        userId = (String) data.get(USERID);
        String ptx = (String) data.get(PASSWORD);
        if (ptx != null)
        {
          password = Encryption.encodeBase64(ptx);
        }
      }
      else
      {
        pl = new AesUtil().decrypt(salt, iv, null, payload.toString());
        if (pl != null)
        {
          // Parse the JSON payload now.
          Map<String, Object> data = new JsonMarshaller().readAsMap(pl);
          userId = (String) data.get(USERID);
          password = (String) data.get(PASSWORD);
        }
      }

      return new AbstractMap.SimpleImmutableEntry<>(userId, password);

    }
    catch (Throwable e)
    {
      // Everything else
      ULogger logger = uctx.getLogger(getClass());
      logger.info("Failed to login C3 user. " + e.toString());
      return null;
    }
  }

  // ...............................................................
  /**
   * Writes no account error to the response.
   *
   * @param ctx
   * @param salt
   * @param resp
   */
  private void _sendNoAccountError(UContext ctx, String salt,
      HttpServletResponse resp)
  {
    try
    {
      HashMap<String, Object> hm = new HashMap<>();
      hm.put("user", null);
      hm.put("reason",
          ResponseMessage.createMessage(ctx, LoginStatus.Reason.noAccount));
      hm.put("status", LoginStatus.Status.fail);
      hm.put("mode", LoginStatus.Mode.unknown);

      JsonMarshaller jm = new JsonMarshaller();
      String jsonUser = jm.serializeMap(hm);

      if (salt == null)
      {
        resp.getWriter().print(jsonUser);
      }
      else
      {
        String encuser = new AesUtil().encrypt(salt, null, null, jsonUser);
        resp.getWriter().print(encuser);
      }
    }
    catch (IOException e)
    {
      // Ignore
    }
  }

  // ...............................................................
  /**
   * @param loginStatus
   * @return
   */
  private static String getLoginErrorCode(LoginStatus loginStatus)
  {
    return loginStatus.getReason().errorCode;
  }

  // ...............................................................
  /**
   * Returns namespace value from namespace/RelayState parameter.
   *
   * @param req
   * @param relayState
   * @return
   */
  private String getNamespace(HttpServletRequest req, RelayState relayState)
  {
    String namespace = req.getParameter("namespace");
    if (!StringUtils.isEmpty(namespace))
    {
      return namespace;
    }
    if (relayState != null && !StringUtils.isEmpty(relayState.getNamespace()))
    {
      return relayState.getNamespace();
    }

    return null;
  }

  // ...............................................................
  private void _captureLoginInfo(UContext ctx, LoginStatus lg, String loginId,
      String ip)
  {
    // Send the login info to the central monitors
    new LoginInfoCollector(lg.getStatus() == Status.success, loginId).send(ctx);
  
    String status = (lg != null) ? lg.getStatus().name()
        : LoginStatus.Status.unknown.name();
    String reason = (lg != null && lg.getStatus() == LoginStatus.Status.fail)
        ? lg.getReason().name()
        : "na";

    // Capture the login incident as an INFO log
    String info = String.format(
        "[Login] Status=%s, ID=\"%s\", IP=%s, Reason=%s", status, loginId, ip,
        reason);

    ctx.getLogger(User.class).info(info);

  }

}
