package com.z1;

import org.apache.http.HttpHeaders;
import org.eclipse.jetty.util.MultiMap;
import udichi.core.UContext;
import udichi.core.util.SIPUtils;
import z1.c3.SystemConfig;
import z1.commons.encryption.AesUtil;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

public class EncryptionFilter implements Filter
{
  // HTTP parameter to pass namespace value
  private static final String NS_PARAM_NAME = "namespace";

  boolean end2EndEncryption = false;

  // File upload endpoints which bypass encryption.
  private static final String[] uploadEndpoints = new String[] {
      "/profile/upload", "/entity/upload", "/segments/upload",
      "/sslCertUpload/create", "/pushconfig" };

  @Override
  public void destroy()
  {
  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response,
      FilterChain chain) throws IOException, ServletException
  {
    HttpServletRequest httpRequest = (HttpServletRequest) request;
    String pathinfo = Optional.ofNullable(httpRequest.getPathInfo()).orElse("");
    UContext ctx = UContext.getInstance(httpRequest);

    // systemconfig/etesec, login will always be encrypted.
    if (pathinfo.contains("systemconfig/etesec"))
    {
      end2EndEncryption = true;
    }
    else
    {
      end2EndEncryption = SystemConfig.getBooleanValue(ctx,
          "z1.security.end2EndEncryption", false);
    }

    // Check for postman and 'qa' option flag
    final String userAgent = Optional
        .ofNullable(httpRequest.getHeader(HttpHeaders.USER_AGENT)).orElse("");
    final boolean isPostmanRequest = userAgent
        .startsWith(LoginServlet.POSTMAN_PREFIX);
    final String qaOverrideFlag = Optional
        .ofNullable(httpRequest.getParameter("qa")).orElse("false");

    // Do not filter if e2e is disabled OR request coming for
    // 'c3/data/eventflow'
    // '/profile/upload/'
    // '/entity/upload/'
    // '/segments/upload/'
    // '/images'
    // since such requests do not pass through loader.js on UI and are
    // never encrypted.
    // Also allow for qa override parameter option only from postman.
    //
    // NOTE:
    // Since we use /images/<content-id>, we will only do a partial check for
    // '/images' url
    if (!end2EndEncryption || pathinfo.equals("/eventflow")
        || _isUploadRequest(pathinfo, httpRequest.getMethod())
        || pathinfo.contains("/images")
        || (isPostmanRequest && qaOverrideFlag.equalsIgnoreCase("true")))
    {
      chain.doFilter(request, response);
      return;
    }

    // instantiate a HtmlServletResponseModifier to capture its output stream
    HtmlServletResponseModifier responseModifier = new HtmlServletResponseModifier(
        (HttpServletResponse) response);

    // instantiate a HtmlServletRequestModifier to modify its query string
    HtmlServletRequestModifier requestModifier = new HtmlServletRequestModifier(
        (HttpServletRequest) request);

    chain.doFilter((HttpServletRequest) requestModifier,
        (HttpServletResponse) responseModifier);

    if (response.getContentType() != null)
    {
      String token = _getTokenFromRequest(httpRequest);
      SIPUtils sipUtil = new SIPUtils();
      String salt = sipUtil.parseSaltFromToken(token);
      String iv = sipUtil.parseIVFromToken(token);
      String content = responseModifier.getCaptureAsString();
      String encryptedContent = (new AesUtil()).encrypt(salt, iv, null,
          content);

      response.getWriter().write(encryptedContent);
    }
  }

  private boolean _isUploadRequest(String pathinfo, String method)
  {
    if (pathinfo == null || method == null) return false;

    return "POST".equalsIgnoreCase(method)
        && Arrays.stream(uploadEndpoints).anyMatch(pathinfo::equals);
  }

  @Override
  public void init(FilterConfig arg0) throws ServletException
  {
    // Auto-generated method stub
  }

  // -----------------------------------------------------------------------
  /**
   * Private class to modify the ServletRequest's query string
   *
   */
  protected class HtmlServletRequestModifier extends HttpServletRequestWrapper
  {
    private Map<String, String> _queryParameters = null;
    private String _queryString = null;
    private String _body = null;
    private HttpServletRequest _request = null;

    public HtmlServletRequestModifier(HttpServletRequest request)
        throws IOException
    {
      super(request);

      this._request = request;

      StringBuilder stringBuilder = new StringBuilder();
      BufferedReader bufferedReader = null;
      try
      {
        InputStream inputStream = request.getInputStream();
        if (inputStream != null)
        {
          bufferedReader = new BufferedReader(
              new InputStreamReader(inputStream));
          char[] charBuffer = new char[128];
          int bytesRead = -1;
          while ((bytesRead = bufferedReader.read(charBuffer)) > 0)
          {
            stringBuilder.append(charBuffer, 0, bytesRead);
          }
        }
        else
        {
          stringBuilder.append("");
        }
      }
      catch (IOException ex)
      {
        throw ex;
      }
      finally
      {
        if (bufferedReader != null)
        {
          try
          {
            bufferedReader.close();
          }
          catch (IOException ex)
          {
            throw ex;
          }
        }
      }

      String body = stringBuilder.toString();

      String token = _getTokenFromRequest(request);
      SIPUtils sipUtil = new SIPUtils();
      String salt = sipUtil.parseSaltFromToken(token);
      String iv = sipUtil.parseIVFromToken(token);

      _body = (body != null) ? new AesUtil().decrypt(salt, iv, null, body)
          : body;

      _queryParameters = _getParameters();
    }

    public String getBody()
    {
      return this._body;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException
    {
      final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(
          _body.getBytes());

      return new ServletInputStream() {
        public int read() throws IOException
        {
          return byteArrayInputStream.read();
        }

      };
    }

    @Override
    public BufferedReader getReader() throws IOException
    {
      return new BufferedReader(new InputStreamReader(this.getInputStream()));
    }

    /*
     * (non-Javadoc)
     * 
     * @see javax.servlet.http.HttpServletRequestWrapper#getQueryString()
     * 
     * This method is only called when end2endencryption is true. It constructs
     * the _queryParameter map to aid retrieval of individual parameter in
     * getParameter(param). Also decode param's value.
     */
    @Override
    public String getQueryString()
    {
      try
      {
        String query = _request.getQueryString();

        if (query == null || query.isEmpty()) return _queryString;

        String namespace = "";

        // From C3Util._massageReqUrl(), we know that 'namespace' is always the
        // first param and encrypted 'query' will have only 2 forms:
        // 1. _/c3/data/some/endpoint?namespace=t_com
        // 2. _/c3/data/some/endpoint?namespace=t_com&enCrYPteD_PaYL0ad

        if (query.contains(NS_PARAM_NAME))
        {
          // If no other param found other than 'namespace', we return the
          // query as is because there is nothing to decrypt.
          if (!query.contains("&")) return query;

          String[] params = query.split("&");
          namespace = params[0];
          query = params[1];
        }

        String token = _getTokenFromRequest(_request);
        SIPUtils sipUtil = new SIPUtils();
        String salt = sipUtil.parseSaltFromToken(token);
        String iv = sipUtil.parseIVFromToken(token);
        query = new AesUtil().decrypt(salt, iv, null, query);
        if (query == null) return _queryString;

        _queryString = Arrays.asList(query.split("&")).stream().map(elem -> {
          String[] parts = elem.split("=");
          String decoded = _decode(parts[1]);
          elem = (decoded != null) ? parts[0] + "=" + decoded : null;
          return elem;
        }).filter(Objects::nonNull).collect(Collectors.joining("&"));

        if (namespace != null && !namespace.isEmpty())
          _queryString = _queryString + "&" + namespace; // adding namespace
                                                         // back to query
      }
      catch (Exception e)
      {
        z1.commons.Utils.showStackTraceIfEnable(e, null);;
      }

      return _queryString;
    }

    /*
     * (non-Javadoc)
     * 
     * @see javax.servlet.ServletRequestWrapper#getParameterMap()
     * 
     * This method gets the query string pre-decrypted and decoded via
     * getQueryString()
     */
    @Override
    public Map<String, String[]> getParameterMap()
    {
      String queryString = getQueryString();
      String[] parts = queryString.split("&");
      Map<String, String> p = new HashMap<>();
      Arrays.stream(parts).forEach(part -> {
        String key = part.split("=")[0];
        String value = part.split("=")[1];

        p.put(key, value);
      });

      MultiMap<String> queryStrings = new MultiMap<>();
      queryStrings.putAllValues(p);

      return Collections.unmodifiableMap(queryStrings.toStringArrayMap());
    }

    /*
     * (non-Javadoc)
     * 
     * @see javax.servlet.ServletRequestWrapper#getParameter(java.lang.String)
     */
    @Override
    public String getParameter(String param)
    {
      if (_queryParameters == null)
      {
        getQueryString();
      }

      return _queryParameters.get(param);
    }

    /**
     * @param value
     * @return
     */
    private String _decode(String value)
    {
      String decoded = null;
      try
      {
        decoded = URLDecoder.decode(value, StandardCharsets.UTF_8.toString());
      }
      catch (UnsupportedEncodingException e)
      {
        z1.commons.Utils.showStackTraceIfEnable(e, null);
      }
      return decoded;
    }

    /**
     * constructs (key,value) pairs map of parameters
     * 
     * @return
     */
    private Map<String, String> _getParameters()
    {
      // call to getQueryString here so that decryption and decoding
      // are already handled.
      String queryString = getQueryString();
      Map<String, String> queryParameters = new HashMap<>();

      if (queryString != null)
      {
        // Get the <key,value> pair(s) separated by ampersand(s)
        String[] parameters = queryString.split("&");

        for (String parameter : parameters)
        {
          String[] keyValuePair = parameter.split("=");
          // length is one if no value is available.
          String value = (keyValuePair.length == 1) ? "" : keyValuePair[1];
          queryParameters.put(keyValuePair[0], value);
        }
      }
      return queryParameters;
    }

  }

  // -----------------------------------------------------------------------
  /**
   * Private class to modify the ServletResponse to capture and modify its
   * output stream
   *
   */
  protected class HtmlServletResponseModifier extends HttpServletResponseWrapper
  {

    private final ByteArrayOutputStream capture;
    private ServletOutputStream output;
    private PrintWriter writer;

    public HtmlServletResponseModifier(HttpServletResponse response)
    {
      super(response);
      capture = new ByteArrayOutputStream(response.getBufferSize());
    }

    @Override
    public ServletOutputStream getOutputStream()
    {
      if (writer != null)
      {
        throw new IllegalStateException(
            "getWriter() has already been called on this response.");
      }

      if (output == null)
      {
        output = new EncryptedServletOutputStream();
      }

      return output;
    }

    public class EncryptedServletOutputStream extends ServletOutputStream
    {
      @Override
      public void write(int b) throws IOException
      {
        capture.write(b);
      }

      @Override
      public void flush() throws IOException
      {
        capture.flush();
      }

      @Override
      public void close() throws IOException
      {
        capture.close();
      }

    }

    @Override
    public PrintWriter getWriter() throws IOException
    {
      if (output != null)
      {
        throw new IllegalStateException(
            "getOutputStream() has already been called on this response.");
      }

      if (writer == null)
      {
        writer = new PrintWriter(
            new OutputStreamWriter(capture, getCharacterEncoding()));
      }

      return writer;
    }

    @Override
    public void flushBuffer() throws IOException
    {
      super.flushBuffer();

      if (writer != null)
      {
        writer.flush();
      }
      else if (output != null)
      {
        output.flush();
      }
    }

    public byte[] getCaptureAsBytes() throws IOException
    {
      if (writer != null)
      {
        writer.close();
      }
      else if (output != null)
      {
        output.close();
      }

      return capture.toByteArray();
    }

    public String getCaptureAsString() throws IOException
    {
      return new String(getCaptureAsBytes(), getCharacterEncoding());
    }
  }

  private static String _getTokenFromRequest(HttpServletRequest req)
  {
    if (req == null) return null;
    UContext ctx = UContext.getInstance(req)
        .setNamespace(req.getParameter(NS_PARAM_NAME));

    return Optional.ofNullable(ctx.sessionStore(false))
        .map(s -> (String) s.get("token")).orElse(null);
  }

}
