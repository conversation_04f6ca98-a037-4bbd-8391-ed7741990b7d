package com.z1;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

public class Z1HttpServletRequestWrapper extends HttpServletRequestWrapper
{
  private Map<String, String> headers = new HashMap<>();

  public Z1HttpServletRequestWrapper(HttpServletRequest request)
  {
    super(request);
  }

  public void addHeader(String key, String value)
  {
    headers.put(key, value);
  }
  
  @Override
  public String getHeader(String key)
  {
    String value = super.getHeader(key);
    if (value == null)
    {
      value = this.headers.get(key);
    }
    
    return value;
  }
 
}
