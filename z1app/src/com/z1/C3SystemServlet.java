/**
 * Copyright 2011 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.Utils.ResponseMessage;
import com.z1.handler.SystemHandler;
import com.z1.handler.TrafficTagConfigHandler;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import z1.c3.Journey;
import z1.commons.Const;
import z1.commons.encryption.Encryption;
import z1.core.utils.Utils;
import z1.email.Email;
import z1.email.EmailSender;
import z1.users.User;

import z1.config.NSFeatureSwitches;
import z1.config.NSFeatureService;

/**
 * Implements REST support for c3/system/* commands.
 */
@SuppressWarnings("serial")
public class C3SystemServlet extends HttpServlet
{
  private enum Command
  {
    registration,
    authentication,
    forgotPassword,
    agentLogin,
    accountNumber,
    agentForgotPassword,
    validate,
    validateResetToken,
    passwordReset,
    landingPageInfo,
    accountSettings,
    accountFeatureFlags,
    context
  }

  private static final String _Z1APIKEY = "z1.apikey";
  private static final String _Z1NEWS = "z1.enable.news";
  private static final String _Z1FRILLID = "z1.frill.id";
  private static final String _Z1FULLSTORY = "z1.enable.full.story";
  private static final String _Z1FULLSTORYNS = "z1.full.story.namespaces";

  // ..................................................
  // Gets
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/plain");
    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/data/<artifact type>/*
      // So we are expecting at least 2 parts after splitting, the first one
      // being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      UContext ctx = UContext.getInstance(req);

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }

      Command type = Command.valueOf(pathParts[1]);
      switch (type)
      {
      // c3/system/context
        case context:
        {
          new SystemHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
      // c3/system/registration
        case registration:
        {
          new com.z1.handler.RegistrationHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case accountNumber:
        {
          /*
           * blocked due to ZMOB-9502
           * "Enumeration Of Namespaces“ issue #5 reported by SECURITY BRIGADE
           */
          resp.sendError(HttpServletResponse.SC_FORBIDDEN);
          return;
        }
        case validate:
        {
          // We will get the base64 encoded string here
          String email = req.getParameter("user");
          String token = req.getParameter("token");

          email = Encryption.decodeBase64(email);
          token = Encryption.decodeBase64(token);
          
          User u = User.load(ctx, email);
          boolean success = false;
          String scheme = Utils.getReqScheme(req);
          String redirectStr = scheme + "://" + req.getServerName();
          int port = scheme.equalsIgnoreCase("https") ? -1 : req.getServerPort();
          redirectStr += (port == -1 ? "" : (":" + port))  + "/c3?validation=";

          if (u != null)
          {
            String uToken = (String) u.getValues().get(
                User.Fields.regToken.name());
            if (token != null
                && uToken != null
                && token.equals(uToken)
                && ((String) u.getValues().get(User.Fields.status.name()))
                    .equals(User.Status.waiting.name()))
            {
              u.getValues().put(User.Fields.status.name(),
                  User.Status.active.name());
              // u.getValues().put(User.Fields.regToken.name(), "");
              u.save();

              success = true;
            }
          }

          resp.sendRedirect(redirectStr + (success ? "success" : "failed"));

          return;
        }
        case landingPageInfo:
        {
          Map<String, Object> map = new java.util.HashMap<>();
          String z1Api = z1.commons.Utils.getAccountPropertyValue(_Z1APIKEY);
          if (z1Api != null && !z1Api.isEmpty())
            map.put("apikey", z1Api);
          String z1News = z1.commons.Utils.getAccountPropertyValue(_Z1NEWS);
          String z1FrillId = z1.commons.Utils.getAccountPropertyValue(_Z1FRILLID);
          if (z1News != null && !z1News.isEmpty())
            map.put("activateNews", z1News);
          if (z1FrillId != null && !z1FrillId.isEmpty())
            map.put("z1FrillId", z1FrillId);
          String z1FS = z1.commons.Utils.getAccountPropertyValue(_Z1FULLSTORY);
          String z1FSNamespaces = z1.commons.Utils.getAccountPropertyValue(_Z1FULLSTORYNS);
          if (z1FS != null && !z1FS.isEmpty())
            map.put("activateFullStory", z1FS);
          if (z1FSNamespaces != null && !z1FSNamespaces.isEmpty())
            map.put("fullStoryNamespaces", z1FSNamespaces);
          map.put("enableScheduledExperience", Journey.checkIfEnabledScheduledExperience());
          map.put("maxGroupCount", z1.commons.Utils.getAccountPropertyValue(
              TrafficTagConfigHandler.MAX_GROUP_COUNT));
          resp.getWriter().print(new JsonMarshaller().serializeMap(map));
          return;
        }
        case accountSettings:
        {
          // We will get the namespace string here
          // if there is no namespace string return {}
          String namespace = req.getParameter("namespace");
          if (namespace == null || namespace.isEmpty())
          {
            resp.getWriter().print("{}");
          }
          else
          {
            Map<String, Object> map = new java.util.HashMap<>();
            String sckey = z1.commons.Utils.getAccountPropertyValue(namespace);
            // for phase 1 this property only stores the sub-cloud mapping
            // in the future this can be extended to hold
            // multiple key-value pairs that are relevant for the given namespace
            if (sckey != null && !sckey.isEmpty())
              map.put("scKey", z1.commons.Utils.getAccountPropertyValue(namespace));
            else
            {
              ULogger logger = ctx.getLogger(getClass());
              if (logger.canLog())
              {
                logger.log(String.format("Account settings not found for the following namespace: %s, using default sub cloud.", namespace));
              }
            }

            resp.getWriter().print(new JsonMarshaller().serializeMap(map));
          }
          return;
        }
        case accountFeatureFlags:
        {
          // requires namespace info in request
          Map<String, Object> map = new java.util.HashMap<>();

          NSFeatureSwitches nsSwitches = NSFeatureService.getInstance().getConfiguration(ctx, true);

          boolean isEPPV2Disabled = nsSwitches.isEPPV2Disabled();
          map.put("enableEPPV2", !isEPPV2Disabled );

          boolean isEPPV2CLK4Disabled = nsSwitches.isEPPV2CLK4Disabled();
          map.put("enableEPPV2CLK4", !isEPPV2CLK4Disabled );

          boolean isChampionChallengerDisabled = nsSwitches.isChampionChallengerDisabled();
          map.put("enableChampionChallenger", !isChampionChallengerDisabled);

          boolean enableNewQueryService = !nsSwitches.isNewQueryServiceDisabled();
          map.put("enableNewQueryService", enableNewQueryService);

          boolean enableTEForAudience = !nsSwitches.isTEForAudienceDisabled();
          map.put("enableTEForAudience", enableTEForAudience);

          boolean enableCommandCenter = !nsSwitches.isCommandCenterDisabled();
          map.put("enableCommandCenter", enableCommandCenter);

          boolean enableCommandCenterCatalog = !nsSwitches.isCommandCenterCatalogDisabled();
          map.put("enableCommandCenterCatalog", enableCommandCenterCatalog);

          boolean enableCreditReports = !nsSwitches.isCreditReportsDisabled();
          map.put("enableCreditReports", enableCreditReports);

          boolean enableCustomReports = !nsSwitches.isCustomReportsDisabled();
          map.put("enableCustomReports", enableCustomReports);

          String primaryModelType = nsSwitches.getPrimaryModelType().name();
          map.put("primaryModelType", primaryModelType);

          resp.getWriter().print(new JsonMarshaller().serializeMap(map));
          return;
        }
      }
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), e.getMessage());
      resp.getWriter().println("}");
    }

  }

  // ..................................................
  public void doPost(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/plain");
    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/data/<artifact type>/*
      // So we are expecting 5 parts after splitting, the first one being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      UContext ctx = UContext.getInstance(req);

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }

      Command type = Command.valueOf(pathParts[1]);
      switch (type)
      {
      // c3/system/context
        case context:
        {
          new SystemHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
      // c3/system/registration
        case registration:
        {
          new com.z1.handler.RegistrationHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/system/login
        case authentication:
        {
          // new com.z1.handler.LoginHandler().get().handle(ctx, subParts, req,
          // resp);
          return;
        }
        case forgotPassword:
        {
          ULogger logger = ctx.getLogger(getClass());
          String payload = ServletUtil.getPayload(req);
          JsonMarshaller jm = new JsonMarshaller();
          Map<String, Object> e = jm.readAsMap(payload);
          String id = (String) e.get("userId");
          if (id == null || id.isEmpty())
          {
            if (logger.canLog())
            {
              logger.log("forgotPassword: A valid userId was not passed in.");
            }
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.fail);
            resp.getWriter().print(rm.toString());
            return;
          }
          User user = User.load(ctx, id);

          // If no user is found or then user is not active then fail.
          if (user == null || !user.isActive())
          {
            if (logger.canLog())
            {
              logger
                  .log(String.format("forgotPassword: user %s is not found or active.", id));
            }
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.success);
            resp.getWriter().print(rm.toString());
            return;
          }

          // If the user does not have an email address associated with it then
          // fail.
          if (user.getEmailAddress() == null
              || user.getEmailAddress().isEmpty())
          {
            if (logger.canLog())
            {
              logger.log(String
                  .format("forgotPassword: user %s does not have an email address.", id));
            }
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.success);
            resp.getWriter().print(rm.toString());
            return;
          }

          // Generates reset token for the user.
          String resetToken = user.generateResetToken();
          user.save();

          // Send email with link to reset password
          EmailSender es = new EmailSender();
          Email email = new Email();
          email.addSubject("Reset your Session AI password");
          email.addToAddress(user.getEmailAddress());

          String scheme = Utils.getReqScheme(req);
          int serverPort = req.getServerPort();
          String port = "";
          if (scheme.equalsIgnoreCase("https"))
          {
            serverPort = -1;
          }
          // provide the c3 apikey so that UI has access to this information
          // in the standalone reset password component
          // ${schema}://${servername}:${port}/resetpassword/${resetToken}?apikey=${c3key}
          String urlPrefix = String.format("%s://%s", scheme,
              req.getServerName());
          if (serverPort != -1)
          {
            port = String.format(":%s", serverPort);
          }
          String sckey = z1.commons.Utils.subcloudKey.toString();
          String apikey = sckey.isEmpty() == false ? z1.commons.Const.C3_KEY_PREFIX.toString().concat(":").concat(sckey) :
                  z1.commons.Const.C3_KEY_PREFIX.toString();
          String resetUrl = String.format("%s%s/resetpassword/%s?%s=%s", urlPrefix,
              port, resetToken, z1.commons.Const.C3_KEY, apikey);

          HashMap<String, Object> hm = new HashMap<String, Object>();
          hm.put("username", user.getId());
          hm.put("resetUrl", resetUrl);

          email.addBody(
              StringUtil.substitueValue(Const.getTemplate("PasswordReset.txt"),
                  hm, "", new HashMap<String, Object>()));

          es.send(email);
          ResponseMessage rm = new ResponseMessage(ctx,
              ResponseMessage.Status.success);
          resp.getWriter().print(rm.toString());
          return;
        }
        // c3/system/validateResetToken
        // The purpose of this end point is to validate if a reset token is valid
        // or not. The response message will contain either a status fail or
        // success.
        case validateResetToken:
        {
          String payload = ServletUtil.getPayload(req);
          JsonMarshaller jm = new JsonMarshaller();
          Map<String, Object> m = jm.readAsMap(payload);
          String resetToken = (String) m.get("resetToken");
          User user = User.getUserForResetToken(ctx, resetToken);
          // If no user matches the reset token or it has expired fail.
          if (user == null)
          {
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.fail);
            resp.getWriter().print(rm.toString());
            return;
          }
          ResponseMessage rm = new ResponseMessage(ctx,
              ResponseMessage.Status.success);
          resp.getWriter().print(rm.toString());
          return;
        }
        // c3/system/passwordReset
        case passwordReset:
        {
          String payload = ServletUtil.getPayload(req);
          JsonMarshaller jm = new JsonMarshaller();
          Map<String, Object> m = jm.readAsMap(payload);
          String resetToken = (String) m.get("resetToken");
          User user = User.getUserForResetToken(ctx, resetToken);
          // If no user matches the reset token or it has expired fail.
          if (user == null)
          {
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.fail);
            resp.getWriter().print(rm.toString());
            return;
          }
          String newPassword = Encryption
              .decodeBase64((String) m.get("newPassword"));

          boolean successfullySetPassword = user.setPassword(newPassword);
          user.clearResetToken();
          user.save();

          if (!successfullySetPassword)
          {
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.fail);
            resp.getWriter().print(rm.toString());
            return;
          }

          ResponseMessage rm = new ResponseMessage(ctx,
              ResponseMessage.Status.success);
          resp.getWriter().print(rm.toString());
          return;
        }
      }

    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), e.getMessage());
      resp.getWriter().println("}");
    }

  }

  private void generateAccountList(List<HashMap<String, String>> l, String value)
  {
    HashMap<String, String> h = new HashMap<String, String>();
    h.put("accountNumber", value);
    l.add(h);
  }

}
