package com.z1.blockrules;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 
 * RuleFilter can have one or more header, body, and event rules
 *
 */
public class RuleFilter
{

  private Map<String, List<String>> header = new HashMap<>();
  private List<String> body = new ArrayList<>();
  private List<String> event = new ArrayList<>();

  public List<String> getBody()
  {
    return body;
  }

  public List<String> getEvent()
  {
    return event;
  }

  public Map<String, List<String>> getHeader()
  {
    return header;
  }

  @JsonIgnore
  public boolean isPresent()
  {
    return !(body.isEmpty() && event.isEmpty() && header.isEmpty());

  }

}