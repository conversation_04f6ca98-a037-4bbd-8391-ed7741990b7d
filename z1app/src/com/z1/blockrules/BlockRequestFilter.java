package com.z1.blockrules;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.io.IOUtils;

import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;

/**
 * Blocks and or allows requests that are matching a given rule. It checks for a
 * file "META-INF/blockrule.json" to load the rules to match. If the file is not
 * present, it allows all request.
 * <p>
 * The file will have the following structure.
 * 
 * <pre>
{  
   "block":{  
      "header":{  
         "apikey":[  
            "xxx1",
            "xxx2",
            "..."
         ]
      },
      "body":[  
         "xxx3",
         "xxx4",
         "..."
      ],
      "event":[  
         "xxx5",
         "xxx6",
         "..."
      ]
   },
   "allow":{  
      "header":{  
         "apikey":[  
            "yyy1",
            "yyy2",
            "..."
         ]
      },
      "body":[  
         "yyy3",
         "yy4",
         "..."
      ],
      "event":[  
         "yyy5",
         "yy6",
         "..."
      ]
   }
}
 * </pre>
 */

public class BlockRequestFilter implements Filter
{

  private enum Result
  {
    MATCHED_ALL(true, true),
    NOT_MATCHED_ALL(false, true),
    MATCHED_ONE(true, false),
    NOT_MATCHED_ONE(false, false),
    NOT_APPLICABLE(null, null);
    private Boolean matched;
    private Boolean all;

    Result(Boolean matched, Boolean all)
    {
      this.matched = matched;
      this.all = all;
    }

    Boolean getMatched()
    {
      return matched;
    }

    Boolean getAll()
    {
      return all;
    }
  }

  private static final String SDK_EVENTS = "sdk_events";
  private static final String API_EVENTS = "events";
  private static final String EVENT = "event";
  private static final String FILE = "META-INF/blockrule.json";

  // We will load the file data at the very beginning to match with each request
  // as it comes

  private static RuleFilters ruleFilters;
  private static JsonMarshaller jsonMarshaller = new JsonMarshaller();

  static
  {
    // Load the config file at the beginning.
    loadConfig();

  }

  /**
   * Loads the config file. This can be called from a JMX console to reload the
   * configuration.
   */
  public static void loadConfig()
  {

    // Load the file as JSON
    ruleFilters = loadJsonFromFile(FILE);
  }

  @Override
  public void destroy()
  {
  }

  @Override
  /**
   * If both allow and block rules will be present we do:
   * 
   * - apply block rules - apply allow rules
   * 
   * If only block rules will be present we do:
   * 
   * - apply block rules
   * 
   * If only allow will be present we do:
   * 
   * - block ALL - apply allow rules
   */
  public void doFilter(ServletRequest request, ServletResponse response,
      FilterChain chain) throws IOException, ServletException
  {
    HttpServletRequest req = (HttpServletRequest) request;
    if (ruleFilters != null)
    {
      String path = req.getPathInfo();
      if (!isRelevantPath(path))
      {
        chain.doFilter(req, response);
        return;
      }
      /*
       * We have to wrap request to be able to use ServletUtil.getPayload ()
       * down to the chain
       */
      req = new RequestWrapper((HttpServletRequest) request);

      try
      {
        String body = ServletUtil.getPayload(req);

        Map<String, Object> payloadAsMap = getPayloadAsMap(body, path);
        List<String> eventNames = getEventNames(payloadAsMap);
        Set<String> blockedEventNames = new HashSet<>();

        if (ruleFilters.isAllowPresent() && ruleFilters.isBlockPresent())
        {
          // Do we have anything to block?
          Collection<Result> results = isRulesMatched(ruleFilters.getBlock(),
              req, path, eventNames);
          if (shouldBlock(eventNames, results, Result.MATCHED_ONE,
              blockedEventNames))
          {
            return;
          }
          // Do we have anything to not allow?
          results = isRulesMatched(ruleFilters.getAllow(), req, body,
              eventNames);
          if (shouldBlock(eventNames, results, Result.NOT_MATCHED_ONE,
              blockedEventNames))
          {
            // rule matches, we'll return without calling the chain
            return;
          }
        }
        else if (!ruleFilters.isAllowPresent() && ruleFilters.isBlockPresent())
        {
          // Do we have anything to block?
          Collection<Result> results = isRulesMatched(ruleFilters.getBlock(),
              req, body, eventNames);
          if (shouldBlock(eventNames, results, Result.MATCHED_ONE,
              blockedEventNames))
          {
            // rule matches, we'll return without calling the chain
            return;
          }
        }
        else if (ruleFilters.isAllowPresent() && !ruleFilters.isBlockPresent())
        {
          // Do we have anything to not allow?
          Collection<Result> results = isRulesMatched(ruleFilters.getAllow(),
              req, body, eventNames);
          if (shouldBlock(eventNames, results, Result.NOT_MATCHED_ONE,
              blockedEventNames))
          {
            // rule matches, we'll return without calling the chain
            return;
          }
        }
        if (!blockedEventNames.isEmpty()
            && eventNames.size() > blockedEventNames.size())
        {
          // need to modify request because of partial blocking (some events are
          // blocked)
          req = adjustRequest(req, blockedEventNames, payloadAsMap);
        }
      }
      catch (Exception e)
      {
        // ignoring exception here
      }
    }
    // process down to the chain
    chain.doFilter(req, response);
  }

  /**
   * Checks whether or not to block a call
   * 
   * @param eventNames
   * @param results
   * @param checkAgainst
   * @param blockedEventNames
   *          - list of events to be blocked
   * @return <code>true</code> if entire call should be blocked
   * @throws IOException
   */
  private boolean shouldBlock(List<String> eventNames,
      Collection<Result> results, Result checkAgainst,
      Set<String> blockedEventNames) throws IOException
  {
    if (results.contains(checkAgainst))
    {
      if (checkAgainst.getAll())
      {
        return true;
      }
      if (eventNames.size() == 1)
      {
        // only one event so we can allow/block all
        return true;
      }
      else
      {
        Iterator<Result> rIter = results.iterator();
        Iterator<String> enIter = eventNames.iterator();
        boolean allBlocked = true;
        while (rIter.hasNext() && enIter.hasNext())
        {
          Result result = rIter.next();
          String eventName = enIter.next();
          if (result == checkAgainst)
          {
            blockedEventNames.add(eventName);
          }
          else
          {
            allBlocked = false;
          }
        }
        if (allBlocked)
        {
          return true;
        }
      }
    }
    else if (results.size() == 1)
    {
      // all use case
      Result result = results.iterator().next();
      if (result.getMatched().equals(checkAgainst.getMatched()))
      {
        return true;
      }
    }
    return false;
  }

  /**
   * Gets list of results one per event name for Header and body rules we cannot
   * do individual check, so returning Singleton MATCHED_ALL or NOT_MATCHED_ALL
   * 
   * @param ruleFilter
   * @param req
   * @param body
   * @param eventNames
   * @return
   */
  private Collection<Result> isRulesMatched(RuleFilter ruleFilter,
      HttpServletRequest req, String body, List<String> eventNames)
  {
    if (isHeaderRueMatched(ruleFilter, req))
    {
      return Collections.singleton(Result.MATCHED_ALL);
    }
    if (ruleFilter.getBody() != null)
    {
      if (body != null && !body.isEmpty())
      {
        if (isBodyRuleMatched(ruleFilter, body))
        {
          return Collections.singleton(Result.MATCHED_ALL);
        }
        return isEventRuleMatched(ruleFilter, eventNames);
      }
      else
      {
        return Collections.singleton(Result.NOT_APPLICABLE);
      }
    }
    return Collections.singleton(Result.NOT_MATCHED_ALL);
  }

  private Collection<Result> isEventRuleMatched(RuleFilter ruleFilter,
      List<String> eventNames)
  {
    // Anything defined to match event?
    if (ruleFilter.getEvent() != null)
    {
      List<String> events2check = ruleFilter.getEvent();
      if (events2check.isEmpty())
      {
        return Collections.singleton(Result.NOT_APPLICABLE);
      }
      if (eventNames.isEmpty())
      {
        return Collections.singleton(Result.NOT_APPLICABLE);
      }
      Collection<Result> results = new ArrayList<>();
      for (String eventName : eventNames)
      {
        if  (events2check.stream().anyMatch(eventName::equalsIgnoreCase))
        {
          results.add(Result.MATCHED_ONE);
        }
        else
        {
          results.add(Result.NOT_MATCHED_ONE);
        }
      }
      return results;
    }
    return Collections.singleton(Result.NOT_APPLICABLE);
  }

  private boolean isBodyRuleMatched(RuleFilter ruleFilter, String body)
  {
    // Anything defined to match the request payload?
    List<String> bodyItems2Block = ruleFilter.getBody();
    if (!bodyItems2Block.isEmpty())
    {
      for (String s : bodyItems2Block)
      {
        if (body.contains(s))
        {
          return true;
        }
      }
    }
    return false;
  }

  private boolean isHeaderRueMatched(RuleFilter ruleFilter,
      HttpServletRequest req)
  {
    if (ruleFilter.getHeader() != null)
    {
      for (Map.Entry<String, List<String>> entry : ruleFilter.getHeader()
          .entrySet())
      {
        String item = entry.getKey();
        List<String> markers = entry.getValue();
        String vals = req.getHeader(item);
        if (vals != null && !vals.isEmpty())
        {
          for (String marker : markers)
          {
            if (vals.contains(marker)) return true;
          }
        }
      }
    }
    return false;
  }

  @Override
  public void init(FilterConfig cfg) throws ServletException
  {
    // NOOP
  }

  @SuppressWarnings("unchecked")
  private HttpServletRequest adjustRequest(HttpServletRequest req,
      Set<String> blockedEventNames, Map<String, Object> payloadAsMap)
  {
    String body = null;
    if (payloadAsMap.size() == 1 && payloadAsMap.containsKey(SDK_EVENTS))
    {
      List<Map<String, Object>> eventList = (List<Map<String, Object>>) payloadAsMap
          .get(SDK_EVENTS);
      List<Map<String, Object>> allowedEventList = getAllowedList(
          blockedEventNames, eventList);
      body = jsonMarshaller.serialize(allowedEventList);
    }
    else if (!payloadAsMap.isEmpty() && payloadAsMap.containsKey(API_EVENTS))
    {
      List<Map<String, Object>> eventList = (List<Map<String, Object>>) payloadAsMap
          .get(API_EVENTS);
      List<Map<String, Object>> allowedEventList = getAllowedList(
          blockedEventNames, eventList);
      payloadAsMap.put(API_EVENTS, allowedEventList);
      body = jsonMarshaller.serializeMap(payloadAsMap);
    }
    if (body != null)
    {
      req = new RequestWrapper(req, body);
    }
    return req;
  }

  private List<Map<String, Object>> getAllowedList(
      Set<String> blockedEventNames, List<Map<String, Object>> eventList)
  {
    List<Map<String, Object>> allowedEventList = new ArrayList<>();
    for (Map<String, Object> element : eventList)
    {
      Object name = element.get(EVENT);
      if (name != null && !blockedEventNames.contains(name))
      {
        allowedEventList.add(element);
      }
    }
    return allowedEventList;
  }

  private static RuleFilters loadJsonFromFile(String fileName)
  {
    try (InputStream is = z1.commons.Utils.class.getClassLoader()
        .getResourceAsStream(fileName))
    {
      if (is == null) return null;
      String s = IOUtils.toString(is);
      if (s == null || s.isEmpty()) return null;
      s = s.trim();
      return jsonMarshaller.readAsObject(s, RuleFilters.class);

    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }
    return null;

  }

  private Map<String, Object> getPayloadAsMap(String body, String path)
  {
    Map<String, Object> map = new HashMap<>();
    // case for Webhook receiver
    // public/api/v1/event
    if (body.contains("events"))
    {

      map = jsonMarshaller.readAsMap((body));
    }
    // case for sdk
    // api/v1/event
    else
    {
      // Load the list of events from the payload
      @SuppressWarnings("unchecked")
      List<Map<String, Object>> inputEList = jsonMarshaller.readAsObject(body,
          List.class);
      map.put(SDK_EVENTS, inputEList);
    }
    return map;
  }

  private boolean isRelevantPath(String path)
  {
    return path != null && path.startsWith("/v1/event");
  }

  @SuppressWarnings("unchecked")
  private List<String> getEventNames(Map<String, Object> input)
  {
    if (input.size() == 1 && input.containsKey(SDK_EVENTS))
    {
      List<Map<String, Object>> inputEList = (List<Map<String, Object>>) input
          .get(SDK_EVENTS);
      return getNames(inputEList);
    }
    else if (!input.isEmpty() && input.containsKey(API_EVENTS))
    {
      List<Map<String, Object>> inputEList = (List<Map<String, Object>>) input
          .get(API_EVENTS);
      return getNames(inputEList);
    }
    return Collections.emptyList();
  }

  private List<String> getNames(List<Map<String, Object>> inputEList)
  {
    List<String> names = new ArrayList<>();
    for (Map<String, Object> element : inputEList)
    {
      Object name = element.get(EVENT);
      if (name != null)
      {
        names.add((String) name);
      }
    }
    return names;
  }

}
