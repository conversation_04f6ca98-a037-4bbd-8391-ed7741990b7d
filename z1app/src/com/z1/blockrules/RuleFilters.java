package com.z1.blockrules;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * RuleFilters to express allow and block rules
 *
 */
public class RuleFilters

{

  private RuleFilter allow;
  private RuleFilter block;

  public RuleFilters()
  {
  }

  RuleFilters(RuleFilter allow, RuleFilter block)
  {
    this.allow = allow;
    this.block = block;
  }

  public RuleFilter getAllow()
  {
    return allow;
  }

  public RuleFilter getBlock()
  {
    return block;
  }

  @JsonIgnore
  public boolean isBlockPresent() {
    return block!= null && block.isPresent();
  }
  
  @JsonIgnore
  public boolean isAllowPresent() {
    return allow!= null && allow.isPresent();
  }

}
