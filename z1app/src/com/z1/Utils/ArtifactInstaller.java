package com.z1.Utils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.system.User;
import udichi.core.util.JsonMarshaller;
import udichi.core.workspace.WorkspaceService;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.accountcontext.AccountContextWrapper;
import z1.c3.CustomActionDef;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.DataItemWritePolicy;
import z1.c3.CustomConfig.Type;
import z1.c3.Journey;
import z1.c3.Segment;
import z1.c3.SignalPart;
import z1.c3.SystemConfigListener;
import z1.channel.ChannelDefWrapper;
import z1.kb.KBProcessor;

public class ArtifactInstaller
{
  // Holds artifact subtype for config object. Since CustomConfig Class does not
  // define all subtypes which mongo config holds, we have created others
  // to match up.
  // displayName for each Subtype is "custom" field for config and "type" field
  // for content in mongo defs.
  //
  // --------------NOTE------------------
  // Use 'unknown' subtype to handle scenarios where new artifact
  // type is introduced to the system which this class may not have identified
  // as known artifact yet.
  // Such artifacts will get added into a dir named 'unknown' in the jar
  // package.
  // Please introduce a new enum if any definition gets saved as 'unknown'
  // type.
  // ------------------------------------
  public enum Subtype
  {
    unknown("unknown"), // Read NOTE above
    signal("signal"),
    segment("segment"),
    campaign("campaign"),
    journey("journey"),
    onetimecampaign("c1"),
    customaction("customaction"),
    contentpush("push"),
    contentfullscreen("fullscreen"),
    contentbanner("banner"),
    contentalert("alert"),
    contentappbox("appbox"),
    contentarticle("article"),
    contentimage("image"),

    goal("goal"),
    channel("channel"),
    event("event"),

    chatsubjectresponse(Type.chatSubjectResponse.name()),
    eventinfo(Type.eventInfo.name()),
    ipncert(Type.ipnCert.name()),
    messagepolicy(Type.messagePolicy.name()),
    outcome(Type.outcome.name()),
    systemconfig(Type.systemConfig.name()),
    androidpushconfig(Type.androidPushConfig.name()),
    uwppushconfig(Type.uwpPushConfig.name()),
    chromepushconfig(Type.chromePushConfig.name()),
    eventmapping(Type.systemConfig.name()),
    eventblacklist(Type.eventBlacklist.name()),
    deepLinks(Type.deepLinks.name()),
    profileschema(Type.profileSchema.name()),
    eventpostprocessing(Type.eventPostProcessing.name()), // old
    livelinks(Type.livelinks.name()), // old
    iosphonecontentcss(Type.iosPhoneContentCss.name()),
    iostabcontentcss(Type.iosTabContentCss.name()),
    androidphonecontentcss(Type.androidPhoneContentCss.name()),
    androidtabcontentcss(Type.androidTabContentCss.name()),
    prioritizecampaign(Type.prioritizeCampaign.name()),
    contentcss(Type.contentcss.name()),
    trustedagentconfig(Type.trustedAgentConfig.name()),
    googleplaystoreconfig(Type.googlePlayStoreConfig.name()),
    itunesstoreconfig(Type.iTunesStoreConfig.name()),
    geofence(Type.geoFence.name()),
    propshtml5desktop(Type.propsHtml5Desktop.name()),
    propshtml5tablet(Type.propsHtml5Tablet.name()),
    propshtml5phone(Type.propsHtml5Phone.name()),
    propsandroidtablet(Type.propsAndroidTablet.name()),
    propsandroidphone(Type.propsAndroidPhone.name()),
    propsiostablet(Type.propsIosTablet.name()),
    propsiosphone(Type.propsIosPhone.name()),
    passwordpolicyconfig(Type.passwordPolicyConfig.name()),
    contextattribute(Type.contextAttribute.name()),
    eventprocessing(Type.eventProcessing.name()),
    eventpattern(Type.eventPattern.name()),
    remotelinks(Type.remoteLinks.name()),
    streamquery(Type.streamQuery.name()),
    actionmappinghtml5desktop(Type.actionMappingHtml5Desktop.name()),
    actionmappinghtml5tablet(Type.actionMappingHtml5Tablet.name()),
    actionmappinghtml5phone(Type.actionMappingHtml5Phone.name()),
    actionmappingandroidtablet(Type.actionMappingAndroidTablet.name()),
    actionmappingandroidphone(Type.actionMappingAndroidPhone.name()),
    actionmappingiostablet(Type.actionMappingIosTablet.name()),
    actionmappingiosphone(Type.actionMappingIosPhone.name()),
    twilioconfig(Type.twilioConfig.name()),
    sendgridconfig(Type.sendGridConfig.name()),
    mailgunconfig(Type.mailGunConfig.name()),
    adobeconfig(Type.adobeConfig.name()),
    darkskyconfig(Type.darkSkyConfig.name()),
    openweathermapconfig(Type.openWeatherMapConfig.name()),
    adobe(Type.adobe.name()),
    entity(Type.entity.name()),
    query(Type.query.name()),
    mlfeature(Type.mlFeature.name()),
    androidmethodsmappingconfig(Type.androidMethodsMappingConfig.name()),
    uploaddownloadstats(Type.uploadDownloadStats.name()),
    testidconfig(Type.testIdConfig.name()),
    cubequery(Type.cubeQuery.name());

    private String displayName;

    Subtype(String displayName)
    {
      this.displayName = displayName;
    }

    @Override
    public String toString()
    {
      return displayName;
    }

    public static Subtype parse(String str)
    {
      for (Subtype s : Subtype.values())
      {
        if (s.name().equalsIgnoreCase(str)) return s;
      }

      return unknown;
    }
  }

  /**
   * Find and Install artifact by existing id.
   * 
   * Create new if not found.
   * 
   * Update existing only if overwrite flag is true
   * 
   * @param ctx
   * @param artifacttype
   * @param artifactsubtype
   * @param map
   * @param overwritepayload
   *          Overwrite existing payload if artifact found.
   * @param nameIdMap
   * @return
   */
  public static String installArtifact(UContext ctx, ArtifactType artifacttype,
      Subtype artifactsubtype, Map<String, Object> map,
      boolean overwritepayload, Map<String, String> nameIdMap,
      Map<String, String> srcMap)
  {
    try
    {
      String id = map.containsKey("id") ? map.get("id").toString() : null;
      String payload = map.containsKey("payload")
          ? map.get("payload").toString()
          : null;

      if (payload == null || id == null) return null;

      if (ArtifactType.config == artifacttype)
      {
        JsonMarshaller jm = new JsonMarshaller();
        switch (artifactsubtype)
        {
          case eventinfo:
          {
            List<Map<String, Object>> eventData = new ArrayList<>();

            eventData = jm.readAsObject(payload, List.class);
            // Incoming events are not affected by overwrite flag.
            CustomConfig.writeDataItemList(ctx, Type.eventInfo, eventData,
                DataItemWritePolicy.replaceIfFound);

            return id;
          }
          case signal:
          {
            SignalPart s = SignalPart.load(ctx, id, true);
            if (s == null || !s.hasConfig())
            {
              s = SignalPart.create(ctx, id);
              s.setPayload(payload);
              s.save();
            }
            else if (overwritepayload)
            {
              s.setPayload(payload);
              s.save();
            }

            return s.getId();
          }
          case channel:
          {
            String name = map.containsKey("name") ? map.get("name").toString()
                : "";
            String desc = map.containsKey("description")
                ? map.get("description").toString()
                : "";

            ChannelDefWrapper cdr = ChannelDefWrapper.forceLoad(ctx, id);

            if (nameIdMap.containsKey(id))
            {
              String fileName = nameIdMap.get(id);
              String updpayload = _getPayloadFromSrcMap(Subtype.channel,
                  payload, srcMap.get(fileName));

              if (updpayload != null) payload = updpayload;
            }

            Map<String, Object> pmap = new JsonMarshaller().readAsMap(payload);

            if (pmap != null && pmap.containsKey("id"))
              id = pmap.get("id").toString();

            if (cdr == null)
            {
              cdr = ChannelDefWrapper.create(ctx, id);
              cdr.setName(name);
              cdr.setDescription(desc);
              cdr.setPayload(payload);
              cdr.setState(State.published);
              cdr.save();
            }
            else if (overwritepayload)
            {
              cdr.setName(name);
              cdr.setDescription(desc);
              cdr.setPayload(payload);
              cdr.save();
            }

            return cdr.getId();
          }
          case segment:
          {
            Segment s = Segment.load(ctx, id, true);

            if (s == null)
            {
              s = Segment.create(ctx, id);
              s.setPayload(payload);
              s.save();
            }
            else if (overwritepayload)
            {
              s.setPayload(payload);
              s.save();
            }

            return s.getId();
          }
          case journey:
          case campaign:
          case onetimecampaign:
          {
            String name = map.get("name").toString();
            String desc = map.get("description").toString();

            Journey.Type jtype = Journey.Type
                .valueOf(artifactsubtype.toString());

            Journey j = Journey.forceLoadDef(ctx, id, jtype);

            if (nameIdMap.containsKey(id))
            {
              String fileName = nameIdMap.get(id);
              String updpayload = _getPayloadFromSrcMap(Subtype.campaign,
                  payload, srcMap.get(fileName));

              if (updpayload != null) payload = updpayload;
            }

            if (j == null)
            {
              j = Journey.create(ctx, name, jtype, id);
              j.setDescription(desc);
              j.setPayload(payload);
              j.save(false);
            }
            else if (overwritepayload)
            {
              j.setDescription(desc);
              j.setPayload(payload);
              j.save(false);
            }

            return j.getId();
          }
          case contextattribute:
          case eventprocessing:
          case remotelinks:
          {
            String name = map.get("name").toString();

            if (nameIdMap.containsKey(id))
            {
              String fileName = nameIdMap.get(id);
              String updpayload = _getPayloadFromSrcMap(Subtype.eventprocessing,
                  payload, srcMap.get(fileName));

              if (updpayload != null) payload = updpayload;
            }

            Map<String, Object> item = jm.readAsMap(payload);

            Type type = Type.valueOf(artifactsubtype.toString());

            CustomConfig cc = CustomConfig.load(ctx, name, type, true);

            if (cc == null)
            {
              cc = CustomConfig.create(ctx, type, name);
              cc.setName(name);
              cc.setPayload(new JsonMarshaller().serializeMap(item));
              cc.save();
            }
            else if (overwritepayload)
            {
              cc.setName(name);
              cc.setPayload(new JsonMarshaller().serializeMap(item));
              cc.save();
            }

            return cc.getId();
          }
          case customaction:
          {
            CustomActionDef ca = CustomActionDef.load(ctx, id);

            if (nameIdMap.containsKey(id))
            {
              String fileName = nameIdMap.get(id);
              String updpayload = _getPayloadFromSrcMap(Subtype.customaction,
                  payload, srcMap.get(fileName));

              if (updpayload != null) payload = updpayload;
            }

            if (ca == null)
            {
              ca = CustomActionDef.create(ctx, id);
              ca.setPayload(payload);
              ca.save();
            }
            else if (overwritepayload)
            {
              ca.setPayload(payload);
              ca.save();
            }

            return ca.getId();
          }
          case streamquery:
          {
            // Some Stream Queries do not have a name, ex:
            // streamQuery+udc.system.core.ZineOne Processing:EpsData.
            // In this case we'll not set the name attribute.
            String name = map.containsKey("name") ? map.get("name").toString()
                : null;

            String desc = map.containsKey("description")
                ? map.get("description").toString()
                : "";

            Type type = Type.valueOf(artifactsubtype.toString());

            id = id.substring(id.indexOf("+") + 1);

            CustomConfig cc = CustomConfig.load(ctx, id, type, true);

            if (cc == null)
            {
              cc = CustomConfig.create(ctx, type, id);

              if (name != null) cc.setName(name);

              cc.setDescription(desc);
              cc.setPayload(payload);
              cc.save();
            }
            else if (overwritepayload)
            {
              if (name != null) cc.setName(name);

              cc.setDescription(desc);
              cc.setPayload(payload);
              cc.save();
            }

            return cc.getId();
          }
          case systemconfig:
          {
            Type type = Type.valueOf(artifactsubtype.toString());

            CustomConfig sysConfig = CustomConfig.load(ctx, id, type, true);

            if (sysConfig == null)
            {
              sysConfig = CustomConfig.create(ctx, type, id);
              String oldConfig = sysConfig.getPayload();
              sysConfig.setPayload(payload);
              sysConfig.save();
              SystemConfigListener.configSaved(ctx, oldConfig, payload);
            }
            else if (overwritepayload)
            {
              String oldConfig = sysConfig.getPayload();
              sysConfig.setPayload(payload);
              sysConfig.save();
              SystemConfigListener.configSaved(ctx, oldConfig, payload);
            }
            return sysConfig.getId();
          }
          case mlfeature:
          case query:
          case entity:
          {
            String name = (map.containsKey("name")) ? map.get("name").toString()
                : map.get("id").toString();

            String description = (map.containsKey("description"))
                ? map.get("description").toString()
                : "";

            Type type = Type.valueOf(artifactsubtype.toString());

            CustomConfig cc = CustomConfig.load(ctx, name, type, true);

            if (cc == null)
            {
              cc = CustomConfig.create(ctx, type, name);
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              cc.save();
            }
            else if (overwritepayload)
            {
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              cc.save();
            }

            return cc.getId();
          }
          case ipncert:
          case androidpushconfig:
          case uwppushconfig:
          case chromepushconfig:
          case eventmapping:
          case eventblacklist:
          case messagepolicy:
          case profileschema:
          case eventpostprocessing:
          case livelinks:
          case androidphonecontentcss:
          case androidtabcontentcss:
          case iosphonecontentcss:
          case iostabcontentcss:
          case prioritizecampaign:
          case trustedagentconfig:
          case googleplaystoreconfig:
          case itunesstoreconfig:
          case propsandroidphone:
          case propsandroidtablet:
          case propshtml5desktop:
          case propshtml5phone:
          case propshtml5tablet:
          case propsiosphone:
          case propsiostablet:
          case passwordpolicyconfig:
          case actionmappingandroidphone:
          case actionmappingandroidtablet:
          case actionmappinghtml5desktop:
          case actionmappinghtml5phone:
          case actionmappinghtml5tablet:
          case actionmappingiosphone:
          case actionmappingiostablet:
          case twilioconfig:
          case sendgridconfig:
          case mailgunconfig:
          case adobeconfig:
          case darkskyconfig:
          case openweathermapconfig:
          case androidmethodsmappingconfig:
          case testidconfig:
          case outcome:
          case contentcss:
          case deepLinks:
          case geofence:
          case cubequery:
          {
            Type type = Type.valueOf(artifactsubtype.toString());

            CustomConfig cc = CustomConfig.load(ctx, id, type, true);

            if (cc == null)
            {
              cc = CustomConfig.create(ctx, type, id);
              cc.setPayload(payload);
              cc.save();
            }
            else if (overwritepayload)
            {
              cc.setPayload(payload);
              cc.save();
            }

            return cc.getId();
          }

          case unknown:
          {
            if ("accountContext".equalsIgnoreCase(id))
            {
              AccountContextWrapper acw = AccountContextWrapper.create(ctx);
              acw.setPayload(payload);
              acw.save();
            }
            else
            {
              DefinitionItem ditem = DefinitionItem.getInstance(ctx,
                  ArtifactType.config, id);

              if (ditem == null)
              {
                ditem = DefinitionItem.newInstance(ctx, id,
                    ArtifactType.config);
                ditem.getValues().put(DefinitionItem.Fields.payload.name(),
                    payload);
                ditem.save();
              }
              else if (overwritepayload)
              {
                ditem.getValues().put(DefinitionItem.Fields.payload.name(),
                    payload);
                ditem.save();
              }

              return ditem.getId();

            }
          }

          default:
          {
            //// Unhandled Cases.
            // case adobe:
            // case channel:
            // case chatsubjectresponse:
            // case event:
            // case eventpattern:
            // case goal:
            // case journey:
            // case uploaddownloadstats:
            ULogger logger = ctx.getLogger(ArtifactInstaller.class);
            logger.info("Executing default case for subtype :" + artifactsubtype);
            return null;
          }
        }
      }
      else if (ArtifactType.datasheet == artifacttype
          || ArtifactType.dashboard == artifacttype)
      {
        // We already know
        // 'id' in map is of form <workspace_id>/<datasht_id or dashbrd_id>
        String wId = id.split("/")[0];
        String dId = id.split("/")[1];

        String mrgd_id = wId + "/" + dId;
        String desc = "";

        // Now serialize it as a def item
        DefinitionItem defItem = DefinitionItem.newInstance(ctx, mrgd_id,
            artifacttype);

        if (desc != null)
        {
          defItem.getValues().put(DefinitionItem.Fields.description.name(),
              desc);
        }

        // Store the owner
        User user = ctx.getUser();
        if (user != null)
        {
          defItem.getValues().put(DefinitionItem.Fields.owner.name(),
              user.getId());
        }

        defItem.getValues().put(DefinitionItem.Fields.payload.name(), payload);
        defItem.save();

        // Now store the ref to the workspace
        WorkspaceService ws = new WorkspaceService(ctx);
        DefinitionItem wObj = ws.getWorkspace(wId);

        //Create workspace collection if none found.
        if (wObj == null)
        {
          wObj = ws.createWorkspace(wId, "");
        }

        ws.saveDatasheet(wObj, dId, desc);
      }
      return null;
    }
    catch (Exception e)
    {
      return null;
    }
  }

  /**
   * Returns updated payload if artifact code like pre/post processing is
   * present in the 'src' directory.
   * 
   */
  private static String _getPayloadFromSrcMap(Subtype subtype, String payload,
      String srcpayload)
  {
    Map<String, Object> payloadmap = new JsonMarshaller().readAsMap(payload);

    if (subtype == Subtype.eventprocessing)
    {
      if (payloadmap.containsKey("codePayload"))
      {
        payloadmap.put("codePayload", srcpayload);
      }
    }
    else if (subtype == Subtype.customaction)
    {
      if (payloadmap.containsKey("payload"))
      {
        payloadmap.put("payload", srcpayload);
      }
    }
    else if (subtype == Subtype.campaign)
    {
      if (payloadmap.containsKey("params"))
      {
        List<Map<String, Object>> list = (List<Map<String, Object>>) payloadmap
            .get("params");

        if (list != null && list.size() > 0)
        {
          // Iterate over definition and check whether definition contains
          // "z1_postaction"
          for (Map<String, Object> temp : list)
          {
            if (temp.containsKey("name") && "z1_postaction"
                .equalsIgnoreCase(temp.get("name").toString()))
            {
              if (temp.containsKey("value"))
              {
                temp.put("value", srcpayload);
              }
              break;
            }
          }
        }
      }
    }
    else if (subtype == Subtype.channel)
    {
      if (payloadmap.containsKey("pre"))
      {
        Map<String, Object> premap = (Map<String, Object>) payloadmap
            .get("pre");
        if (premap.containsKey("code")) premap.put("code", srcpayload);
      }
    }

    return new JsonMarshaller().serialize(payloadmap);
  }

  /**
   * Content type artifacts require the whole payload to be set as string by
   * KBStore
   * 
   * @param ctx
   * @param artifacttype
   * @param artifactsubtype
   * @param contentpayload
   * @return
   */
  public static String installContent(UContext ctx, ArtifactType artifacttype,
      Subtype artifactsubtype, String contentpayload)
  {
    if (ArtifactType.config == artifacttype)
    {
      switch (artifactsubtype)
      {
        case contentpush:
        case contentfullscreen:
        case contentbanner:
        case contentalert:
        case contentappbox:
        case contentarticle:
        case contentimage:
        {
          KBProcessor proc = new KBProcessor();
          String id = null;
          try
          {
            id = proc.createNewContent(ctx, contentpayload);
          }
          catch (IOException e)
          {
            z1.commons.Utils.showStackTraceIfEnable(e, null);
          }
          catch (Exception ex)
          {
            z1.commons.Utils.showStackTraceIfEnable(ex, null);
          }
          return id;
        }
        default:
        {
          ULogger logger = ctx.getLogger(ArtifactInstaller.class);
          logger.info("Executing default case for subtype :" + artifactsubtype);
          return null;
        }
      }

    }
    return null;
  }

}
