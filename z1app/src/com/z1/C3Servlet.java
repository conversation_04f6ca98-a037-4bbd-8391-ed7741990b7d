/**
 * Copyright 2011 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import java.io.IOException;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import udichi.core.util.ServletUtil;
import z1.stats.JourneyContextAttrStatRecorder;


/**
 * Launches the runtime environment.
 */
@SuppressWarnings("serial")
public class C3Servlet extends HttpServlet
{  
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/html");
    try
    {
      // We'll register the cube definition handlers
      JourneyContextAttrStatRecorder.registerCubeDefinitionProvider();
      
      // Load the start page and send it.
      ServletUtil.writeResource("com/z1/resource/c3/desktop.html", resp.getWriter());
      resp.getWriter().println("");
    }
    catch (Exception e)
    {
      ServletError err = new ServletError().setReason(e);
      resp.getWriter().print(err.getMessage());
    }

  }

}
