package com.z1;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.Utils.ApiUtils;

import udichi.core.ApiKey;
import udichi.core.UContext;
import z1.users.AccessControl;
import z1.users.User;

/**
 * Checks for the authentication for the requests./
 */
public class AuthFilter implements Filter
{

  @Override
  public void destroy()
  {
    // TODO Auto-generated method stub

  }

  @Override
  public void doFilter(ServletRequest request, ServletResponse response,
      FilterChain chain) throws IOException, ServletException
  {
    HttpServletRequest req = (HttpServletRequest) request;
    UContext ctx = UContext.getInstance(req);
    User user = (User) ctx.getUser();
    
    if (user == null)
    {
      String reqURI = req.getRequestURI();

      if (com.z1.handler.ModulesHandler.GetCommand.getUris().stream().anyMatch(reqURI::contains)
              || com.z1.handler.ModulesHandler.PostCommand.getUris().stream().anyMatch(reqURI::contains)
              || com.z1.handler.SignalsHandler.GetCommand.getUris().stream().anyMatch(reqURI::contains))
      {
        if (ctx.getNamespace() == null)
          ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
        
        try
        {
          ApiKey key = ApiUtils.getApiKey(ctx, req);
          if (key == null)
          {
            setError(response, HttpServletResponse.SC_UNAUTHORIZED);
            return;            
          }
       }
        catch (Exception e)
        {
          setError(response, HttpServletResponse.SC_UNAUTHORIZED);
          return;
        }        
        
        chain.doFilter(request, response);
      }
      else
      {
        setError(response, HttpServletResponse.SC_UNAUTHORIZED);
        return;
      }
    }
    
    else
    {
      AccessControl ac = new AccessControl(user);
      if (!ac.canDo(req.getPathInfo(),req.getMethod()))
      {
        setError(response, HttpServletResponse.SC_FORBIDDEN);
        return;
      }
      chain.doFilter(request, response);
    }
  }

  private void setError(ServletResponse response, int code) throws IOException
  {
    HttpServletResponse resp = (HttpServletResponse)response;
    resp.sendError(code);
  }

  @Override
  public void init(FilterConfig arg0) throws ServletException
  {
    // TODO Auto-generated method stub

  }

  ///////////////////////////////////////////

}
