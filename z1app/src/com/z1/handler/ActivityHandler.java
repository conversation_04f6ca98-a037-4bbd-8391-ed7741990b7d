package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ActivitySerializer;
import com.z1.Utils.ResponseMessage;
import com.z1.Utils.ResponseMessage.Status;

import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.util.Bytes;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.c3.AbstractSignal;
import z1.c3.ActivityDataHandler;
import z1.c3.CustomConfig;
import z1.c3.Segment;
import z1.c3.api.Commons;
import z1.c3.mapping.EventMappingInfo;
import z1.c3.signal.TimerEventSignal;
import z1.chat.ChatSubject;
import z1.commons.Const;
import z1.commons.def.TimeSelectorDef;
import z1.core.Activity;
import z1.core.Activity.ActivityPrefix;
import z1.core.profile.ProfileService;
import z1.core.Context;
import z1.core.Profile;
import z1.core.Type;
import z1.core.utils.ActivityIterator;
import z1.core.utils.TimeUtils;

public class ActivityHandler implements CommandHandlerFactory
{
  private enum Command
  {
    // customer activity
    aggregate,
    deviceDetails,
    segments,
    customer,
    ctxparams,
    all,
    getFromActivityKey
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");
        Command type = Command.valueOf(pathParts[0]);

        switch (type)
        {
          // c3/data/activity/aggregate?profileId=<profile-id>&tunit=<tunit>&tvalue=<tvalue>
          // aggregate activity per user
          case aggregate:
          {
            String profileId = req.getParameter("profileId");
            String unit = req.getParameter("tunit");
            String value = req.getParameter("tvalue");

            TimeSelectorDef timeDef = new TimeSelectorDef();
            timeDef.setUnit(unit);
            timeDef.setValue(Integer.parseInt(value));
            timeDef.setDate(Const.PAST_TIME);

            AbstractSignal sig = new TimerEventSignal(uctx);
            List<ActivityDataHandler.MatchedItem> signals = ActivityDataHandler
                .getMatchedEvents(uctx, sig, profileId, timeDef);
            JsonMarshaller jm = new JsonMarshaller();
            resp.getWriter().print(jm.serialize(signals));
            return;
          }

          // c3/data/activity/all?profileId=<profile-id>&tunit=<tunit>&tvalue=<tvalue>
          case all:
          {
            String profileId = req.getParameter("profileId");
            String unit = req.getParameter("tunit");
            String value = req.getParameter("tvalue");

            TimeSelectorDef timeDef = new TimeSelectorDef();
            timeDef.setUnit(unit);
            timeDef.setValue(Integer.parseInt(value));
            timeDef.setDate(Const.PAST_TIME);

            ActivityIterator it = ActivityDataHandler.getDataIterator(
                Context.getInstance(uctx), timeDef, profileId,
                ActivityPrefix.event);
            String ret = ActivitySerializer.serialize(it, 300);
            resp.getWriter().print(ret);
            return;
          }

          // c3/data/activity/segments?profileId=<profile-id>
          case segments:
          {
            String profileId = req.getParameter("profileId");
            Context ctx = Context.getInstance(uctx);
            Profile p = Profile.instance(ctx, profileId, false);
            List<Map<String, Object>> segments = new ArrayList<Map<String, Object>>();
            Set<String> segs = p.segmentHandler().getMatchedSegments();
            if (segs.isEmpty())
            {
              resp.getWriter().print("{}");
              return;
            }
            for (String sId : segs)
            {

              Segment segment = Segment.load(uctx, sId, true);
              if (segment == null) continue;
              Map<String, Object> map = new HashMap<String, Object>();
              map.put("name", segment.getDef().getName());
              map.put("icon", segment.getDef().getIcon());
              segments.add(map);
            }

            JsonMarshaller jm = new JsonMarshaller();
            resp.getWriter().print(jm.serialize(segments));
            return;
          }

          // c3/data/activity/deviceDetails?profileId=<profile-id>&chatId=<chat-id>
          case deviceDetails:
          {
            String profileId = req.getParameter("profileId");
            String chatId = req.getParameter("chatId");
            Context ctx = Context.getInstance(uctx);
            String z1prof = "";
            Map<String, Object> z1ProfMap = null;
            Profile p = null;
            if (profileId != null && !profileId.isEmpty())
            {
              p = Profile.instance(ctx, profileId, false);
              z1prof = p.getProperty(Const.Z1_PROFILE);
            }
            else if (chatId != null && !chatId.isEmpty())
            {
              ChatSubject subject = ChatSubject.findChatSubject(ctx, chatId);
              if (subject == null)
              {
                ServletHandlerResponse err = new ServletHandlerResponse();
                err.setReason(
                    "Chat Subject not found for this chat id:" + chatId);
                resp.getWriter().print(err);
                return;
              }

              z1prof = subject.getValue(ChatSubject.Fields.z1_profile);

            }
            if (z1prof == null || z1prof.isEmpty())
            {
              resp.getWriter().print("{}");
              return;
            }

            z1ProfMap = new JsonMarshaller().readAsMap(z1prof);

            Map<String, Object> deviceContextMap = convertZ1ProfToDeviceContext(
                z1ProfMap);
            // Add location info
            if (p != null)
            {
              for (Commons.LocationInfo li : Commons.LocationInfo.values())
              {
                Profile.Fields f = Profile.Fields.parse(li.name());
                if (f.isDisplayable)
                {
                  Object v = p.getProperty(f.value);
                  if (v != null) deviceContextMap.put(f.value, v);
                }
              }
            }

            String deviceContext = new JsonMarshaller()
                .serialize(deviceContextMap);
            resp.getWriter().print(deviceContext);
            return;
          }
          // c3/data/activity/customer?customerId=<customer-id>
          case customer:
          {
            String id = req.getParameter("customerId");
            Context ctx = Context.getInstance(uctx);
            ProfileService ps = new ProfileService(ctx);
            Profile p = ps.findAProfile(id, Type.IndexType.CUSTOMERID);
            if (p == null)
            {
              ResponseMessage rm = new ResponseMessage(uctx, Status.fail,
                  ResponseMessage.Type.noProfileFound);
              resp.getWriter().print(rm.toString());
              return;
            }
            String profileId = p.getKeyValue();
            Map<String, String> z1ProfMap = new HashMap<String, String>();
            z1ProfMap.put("profileId", profileId);
            z1ProfMap.put("status", Status.success.name());
            String profileIdString = new JsonMarshaller().serialize(z1ProfMap);
            resp.getWriter().print(profileIdString);
            return;
          }
          // c3/data/activity/ctxparams?customerId=<customer-id>
          case ctxparams:
          {
            Map<String, Object> paramsMap = new HashMap<>();
            String id = req.getParameter("customerId");
            Context ctx = Context.getInstance(uctx);
            ProfileService ps = new ProfileService(ctx);
            Profile p = ps.findAProfile(id, Type.IndexType.CUSTOMERID);
            if (p == null)
            {
              return;
            }

            // Add name, email and phone
            String z1prof = (String) p.getProperty(Const.Z1_PROFILE);
            if (z1prof != null && !z1prof.isEmpty())
            {
              Map<String, Object> z1ProfMap = new JsonMarshaller()
                  .readAsMap(z1prof);

              String name;
              if (z1ProfMap.containsKey("name"))
              {
                name = (String) z1ProfMap.get("name");
              }
              else
              {
                name = (String) p.getProperty(Profile.Fields.name.value);
              }
              paramsMap.put("name", (name == null) ? "" : name);

              String email;
              if (z1ProfMap.containsKey("email"))
              {
                email = (String) z1ProfMap.get("email");
              }
              else
              {
                email = (String) p.getProperty(Profile.Fields.email.value);
              }
              paramsMap.put("email", (email == null) ? "" : email);

              String phone;
              if (z1ProfMap.containsKey("phone"))
              {
                phone = (String) z1ProfMap.get("phone");
              }
              else
              {
                phone = (String) p.getProperty(Profile.Fields.phone.value);
              }
              paramsMap.put("phone", (phone == null) ? "" : phone);

            }

            // Add last activity
            String lastEvent = (String) p
                .getProperty(Profile.Fields.current_event.value);
            if (lastEvent != null && !lastEvent.isEmpty())
            {
              paramsMap.put(Profile.Fields.current_event.value, lastEvent);
            }

            String lastEventTime = (String) p
                .getProperty(Profile.Fields.current_event_time.value);
            if (lastEventTime != null && !lastEventTime.isEmpty())
            {
              paramsMap.put(Profile.Fields.current_event_time.value,
                  lastEventTime);
            }

            // Add other context attribute values
            CustomConfig ccs = CustomConfig.load(uctx,
                CustomConfig.Type.profileSchema, true);
            if (ccs != null)
            {
              String payload = ccs.getPayload();
              if (payload != null)
              {
                List<Map<String, Object>> list = new JsonMarshaller()
                    .readAsObject(payload, List.class);
                for (Map<String, Object> attr : list)
                {
                  String useAs = (String) attr.get("useAs");
                  if ("system:custom".equalsIgnoreCase(useAs))
                  {
                    String name = (String) attr.get("name");
                    if ("name".equalsIgnoreCase(name)
                        || "email".equalsIgnoreCase(name)
                        || "phone".equalsIgnoreCase(name))
                    {
                      continue;
                    }

                    String val = p.getProperty(name);
                    if (val != null)
                    {
                      paramsMap.put(name, val);
                    }
                  }

                }
              }
            }

            // Add device push registration IDs
            String pushRegId = (String) p
                .getProperty(Profile.Fields.pushRegId.value);
            // Add the push registration for the device
            if (pushRegId != null)
            {
              paramsMap.put("Push tokens for devices", pushRegId);
            }

            // Is this a push target?
            String s = (String) p
                .getProperty(Profile.Fields.deviceProile.value);
            if (s == null) s = "false";
            Boolean isDeviceProfile = Boolean.valueOf(s);
            paramsMap.put("Is a push target?", !isDeviceProfile);

            // Last connected device ID
            s = (String) p.getProperty(Profile.Fields.lastDeviceId.value);
            paramsMap.put("Last Connected Device ID", s);

            JsonMarshaller jm = new JsonMarshaller();
            resp.getWriter().print(jm.serialize(paramsMap));

            return;
          }
          // c3/data/activity/getFromActivityKey?profileId=<profile id
          // value>&time=<UTC timestamp>&event=<event unique name>&field=<field
          // value to get>
          // time is in YYYYMMDDHHmmssSSS format
          case getFromActivityKey:
          {
            String profileId = req.getParameter("profileId"),
                time = req.getParameter("time"),
                eventName = req.getParameter("event"),
                attribute = req.getParameter("field");
            if (profileId == null || time == null || eventName == null)
            {
              resp.getWriter().print("{}");
              return;
            }
            String activityKey = new StringBuilder().append(profileId)
                .append(Activity.SEP).append(ActivityPrefix.event.text)
                .append(Activity.SEP).append(time).append(Activity.SEP)
                .append(eventName).toString();
            Activity activity = new Activity(Context.getInstance(uctx),
                Bytes.toBytes(activityKey));
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> res = activityStats(activity);
            if (attribute == null || attribute.isEmpty())
            {
              resp.getWriter().print(jm.serialize(res));
              return;
            }
            if (attribute.equals("eventPayload"))
            {
              Object eventPayload = getActivityEventPayload(uctx, activity);
              res.put(attribute, eventPayload);
            }
            else
            {
              res.put(attribute, activity.getValue(attribute));
            }
            resp.getWriter().print(jm.serialize(res));
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return null;
  }

  private Map<String, Object> convertZ1ProfToDeviceContext(
      Map<String, Object> z1ProfMap)
  {
    Map<String, Object> deviceContextMap = new HashMap<>();

    for (Commons.DeviceInfo di : Commons.DeviceInfo.values())
    {
      if (di.displayName == null) continue;
      Object v = z1ProfMap.get(di.name());
      if (v != null) deviceContextMap.put(di.displayName, v);
    }

    return deviceContextMap;
  }

  /**
   * Get the source event details of given activity, event params will
   * have their corresponding values in the payload.
   * 
   * @param uctx
   * @param activity
   * @return the event payload with each param value
   */
  private Object getActivityEventPayload(final UContext uctx, Activity activity)
  {
    String activityKey = activity.getKeyValue();
    String[] parts = activityKey.split("\\|");
    // For activity where we store the time, we create
    // the row key as -> pid|type|time|activity_text
    if (parts.length != 4 || !parts[1].equals(ActivityPrefix.event.text))
    {
      return null;
    }
    String eventName = parts[3];
    List<Map<String, Object>> eventList = EventMappingInfo.getEventInfo(uctx,
        true, true);
    Map<String, Object> res = new HashMap<>();
    for (Map<String, Object> event : eventList)
    {
      if (event.getOrDefault("name", "").equals(eventName))
      {
        res = event;
        break;
      }
    }
    Map<String, Object> paramVals = activity.getValues(null);
    for (String param : (List<String>) res.get("params"))
    {
      if (paramVals.containsKey(param))
      {
        continue;
      }
      Object val = activity.getValue(param);
      if (val == null)
      {
        paramVals.put(param, StringUtils.EMPTY);
        continue;
      }
      if (param.equals("time"))
      {
        val = Long.valueOf((String) val);
      }
      paramVals.put(param, val);
    }
    res.put("paramVals", paramVals);
    Map<String, Object> refs = EventMappingInfo.getEventMappingRefs(uctx,
        eventName, false);
    res.putAll(refs);
    return res;
  }

  /**
   * Generate a map as return JSON for simple activity info, like time, activity name and channel
   * @param activity
   * @return
   */
  private Map<String, Object> activityStats(Activity activity)
  {
    Map<String, Object> map = new java.util.HashMap<>();
    String time = activity.getTime();
    String act = activity.getName();
    String chnl = activity.getValue(Const.P_EVENT_SOURCE);
    String event = activity.getValue(Const.P_EVENT_NAME);
    if ((time == null) || (act == null))
    {
      return map;
    }
    TimeUtils tu = new TimeUtils();
    Long millis = tu.parseTime(time);
    map.put("time", millis);
    map.put("timestamp", time);
    map.put("activity", act);
    map.put("channel", chnl);
    map.put("eventName", event);
    return map;
  }
}
