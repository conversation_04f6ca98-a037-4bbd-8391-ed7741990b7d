package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.commons.Utils;
import z1.stats.MAUStatRecorder;
import z1.stats.ProfileCountStat;

public class SystemStatHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    mau, usercounts
    
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          return;
        }

        switch (command)
        {  
          // c3/data/systemstat/mau
          case mau:
          {
            Map<String,Object> map = new HashMap<String, Object>();
            map.put("mau", new MAUStatRecorder().getMAUStat(uctx));
            map.put("lastMonthMau", new MAUStatRecorder().getLastMonthsMAUStat(uctx));
            JsonMarshaller jm = new JsonMarshaller();
            resp.getWriter().print(jm.serialize(map));
            return;
          }
          
          // c3/data/systemstat/usercounts?month=<month>&year=<year>
          case usercounts:
          {
            String year = req.getParameter("year");
            String month = req.getParameter("month");
            if (year != null && month != null)
            {
              List<Map<String, Object>> list = ProfileCountStat.getTotalCountsForMonth(uctx, 
                  Integer.valueOf(year), Integer.valueOf(month));
              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(jm.serialize(list));
            }
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return null;
  }

}
