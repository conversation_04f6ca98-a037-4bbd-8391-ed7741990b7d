package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.core.ListOfProperties;

/**
 * This class handles list of properties requests.
 * 
 *
 */
public class LOPHandler implements CommandHandlerFactory
{
  /**
   * The lop get all call will not return any lop with the id prefixed with this value.
   */
  protected static final String SYSTEM_LOPS_PREFIX = "udc.system.core";
  
  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  // Supported get commands
  private enum GetCommand
  {
    all,
    id
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/lop/all => Returns all lists of properties metadata
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx, Type.listOfProperties);
            JsonMarshaller jm = new JsonMarshaller();
            for (CustomConfig cc : ccList)
            {
              String id = cc.getId();
              if (id.startsWith(SYSTEM_LOPS_PREFIX)) continue;

              Map<String, Object> map = new HashMap<>();
              map.put("id", id);
              map.put("name", cc.getName());
              map.put("description", cc.getDescription());
              map.put("count", 0);
              
              String pl2 = cc.getPayload2();
              if (pl2 != null && (!pl2.isEmpty()))
              {
                Map<String, Object> pl2Map = jm.readAsMap(pl2);
                if (pl2Map != null && (!pl2Map.isEmpty())) 
                  map.put("count", pl2Map.get("count"));
              }
              ret.add(map);
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
          // c3/data/lop/id?id=<id> => Returns the list of properties that matches the given id
          case id:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;

            CustomConfig cc = CustomConfig.load(uctx, id, Type.listOfProperties, true);
            if (cc == null)
            {
              resp.getWriter().print("[]");
              return;
            }
            
            Map<String, Object> ret = new HashMap<>();
            ret.put("id", id);
            ret.put("name", cc.getName());
            ret.put("description", cc.getDescription());
            ret.put("payload", new JsonMarshaller().readAsMap(cc.getPayload()));
            
            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
          default:
          {
            return;
          }

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/lop/create => Creates a LOP out of the payload
          case create:
          {
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty()) return;

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(pl);
            String name = (String) map.get("name");
            String description = (String) map.get("description");
            Map<String, Object> payload = (Map<String, Object>) map.get("payload");
            
            ListOfProperties lop = new ListOfProperties(uctx, name, description, payload);
            lop.save(true);
            Map<String, Object> m = new java.util.HashMap<>();
            m.put("id", name);
            resp.getWriter().print(jm.serializeMap(m));
            
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(Type.listOfProperties.name()), name, name,
                Operations.create).save();
            break;
          }
          // c3/data/lop/update?id=<id> => Updates the LOP with the given id
          case update:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty()) return;
                        
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(pl);
            Map<String, Object> payload = (Map<String, Object>) map.get("payload");
            String description = (String) map.get("description");
            ListOfProperties lop = new ListOfProperties(uctx, id, description, payload);
            lop.save(false);

            Map<String, Object> m = new java.util.HashMap<>();
            m.put("id", id);
            resp.getWriter().print(jm.serializeMap(m));
            
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(Type.listOfProperties.name()), id, id,
                Operations.edit).save();
            break;
          }
          // c3/data/lop/delete?id=<id> => Deletes the LOP with the given id
          case delete:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;
            
            req.setAttribute("type", Type.listOfProperties.name());
            String[] subParts = new String[2];
            subParts[0] = Type.listOfProperties.name();
            subParts[1] = cStr;

            new BXHandler().post().handle(uctx, subParts, req, resp);
            
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(Type.listOfProperties.name()), id, id,
                Operations.delete).save();
            
            break;
          }
          default:
          {
            break;
          }          
        }
        App.notifyClearCache(uctx.getNamespace(), Type.listOfProperties.name());
      }
    };
  }

}
