/**
 * 
 */
package com.z1.handler;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.Collections;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.data.Result;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import udichi.gateway.AppServiceFacade;
import udichi.gateway.appservice.AppRegItem;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.actions.ActionUtils;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.Journey;
import z1.c3.Journey.JourneySummaryInfo;
import z1.c3.Journey.Type;
import z1.c3.Segment;
import z1.c3.SignalPart;
import z1.c3.def.ActivitySelectorDef;
import z1.c3.def.ParamsType;
import z1.c3.def.StateDef;
import z1.c3.def.TimerDef;
import z1.c3.mapping.EventMappingInfo;
import z1.channel.ChannelDefWrapper;
import z1.channel.ChannelType;
import z1.commons.Const;
import z1.commons.def.ActionDef;
import z1.commons.def.ParamDef;
import z1.core.ListOfProperties;
import z1.domains.DomainEventInfo;
import z1.domains.DomainHandler;
import z1.processing.JourneyRuntimeUtils;
import z1.template.JourneyDesignUtils;
import z1.template.ModuleFilter;
import z1.template.ModuleFilter.FilterTag;
import z1.template.ModuleReaderFacade;
import z1.template.ModuleVisitor;
import z1.template.ModuleVisitor.VisitingOperation;
import z1.template.ModuleWriterFacade;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateConst.ActionTemplateTags;
import z1.template.TemplateConst.ActionType;
import z1.template.TemplateConst.AnchoredContent;
import z1.template.TemplateConst.ExperienceTags;
import z1.template.TemplateConst.FilterOption;
import z1.template.TemplateConst.ModuleType;
import z1.template.TemplateConst.NSApp;
import z1.template.TemplateConst.PublishingScope;
import z1.template.TemplateConst.SegmentTags;
import z1.template.TemplateConst.TriggerTags;
import z1.template.TemplateUtils;
import z1.template.TemplateUtils.VersionComparator;
import z1.users.User;

/**
 * 
 *
 */
public class ModulesHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    category("/c3/data/modules/category"),
    id("/c3/data/modules/id"),
    loadArtifacts("/c3/data/modules/loadArtifacts"),
    loadDefinition("/c3/data/modules/loadDefinition"),
    filter("/c3/data/modules/filter"),
    versionInfo("/c3/data/modules/versionInfo"),
    checkUniqueName("/c3/data/modules/checkUniqueName"),
    actionTypes("/c3/data/modules/actionTypes"),
    getDomainEvents("c3/data/modules/getDomainEvents"),
    actiontemplateRefs("c3/data/modules/actiontemplateRefs"),
    moduleDomainEvents("c3/data/modules/domainEvents"),
    guidedEventSources("c3/data/modules/getGuidedEventSources"),  
    checkUpgradeAll("c3/data/modules/checkUpgradeAll"),
    instancesByActionTemplate("c3/data/modules/instancesByActionTemplate");
    
    String uri;

    GetCommand(String uri)
    {
      this.uri = uri;
    }

    public static Set<String> getUris()
    {
      return Arrays.stream(GetCommand.values()).map(cmd -> {
        return cmd.uri;
      }).collect(Collectors.toSet());
    }
  }

  public enum PostCommand
  {
    create("/c3/data/modules/create"), // create instance from module
    delete("/c3/data/modules/delete"), // delete instance created from module
    publishModule("/c3/data/modules/publishModule"),
    unpublishModule("/c3/data/modules/unpublishModule"),
    all("/c3/data/modules/all"),
    allInstances("/c3/data/modules/allInstances"),
    createNSModule("/c3/data/modules/createNSModule"), 
    prioritizeCampaign("/c3/data/modules/prioritizeCampaign"),
    prioritizeCampaignInfo("/c3/data/modules/prioritizeCampaignInfo"),
    mappingReady("c3/data/modules/mappingReady"),
    upgradeActionTemplate("c3/data/modules/upgradeActionTemplate"), 
    checkUpgrade("c3/data/modules/checkUpgrade"),
    upgrade("c3/data/modules/upgrade");

    String uri;

    PostCommand(String uri)
    {
      this.uri = uri;
    }

    public static Set<String> getUris()
    {
      return Arrays.stream(PostCommand.values()).map(cmd -> {
        return cmd.uri;
      }).collect(Collectors.toSet());
    }
  }

  /**
   * 
   */
  public ModulesHandler()
  {
    // TODO Auto-generated constructor stub
  }

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandlerFactory#get()
   */
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          command = GetCommand.id;
        }
        
        ULogger logger = ULogger.instance(ctx);
        String BXMPNS = z1.commons.Utils.getBXMPNS(ctx);
        String BXMPApikey = z1.commons.Utils.getBXMPApikey(ctx);
        switch (command)
        {
          //c3/data/modules/actionTypes?type=<c1|campaign> where type is optional
          case actionTypes:
          {
            String type = req.getParameter("type");
            resp.getWriter().print(
                new JsonMarshaller().serialize(ActionType.getActionTypes(type)));
            break;
          }
          // c3/data/modules/versionInfo?id=<versionless_modId>
          // e.g, c3/data/modules/versionInfo?id=udc.system.core.ExperienceSampleModule:TI_One
          case versionInfo:
          {
            String modId = req.getParameter("id");
            ResponseMessage msg = null;
            String[] parts = modId.split(":");
            if (modId == null || modId.isEmpty() || parts.length < 2)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Missing or invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String pkgId = parts[0];

            // Adding 1 to parts.length compensating for 'version' since
            // TemplateUtils.isActionTemplate() expects length of pkgid:group:appname:version
            boolean isActionTemplate = TemplateUtils.isActionTemplate(ctx, pkgId, parts.length+1);
            String name = (isActionTemplate) ? parts[2] : parts[1];
            if (name.contains(" "))
            {
              name = URLEncoder.encode(name, "UTF-8");
              modId = (isActionTemplate) ? pkgId + ":" + parts[1] + ":" + name
                  : pkgId + ":" + name;
            }

            List<Map<String, Object>> versionList = new ArrayList<>();
            JsonMarshaller jm = new JsonMarshaller();
            if (z1.commons.Utils.isMarketPlace)
            {
              String mpNS = req.getParameter("ns");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }

              versionList = ModuleFilter.getVersionHistory(ctx, modId, true);
              String payload = jm.serialize(versionList);
              resp.getWriter().print(payload);
            }
            else
            {

              String response = null;
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                response = TemplateUtils.callMP(
                    "/c3/data/modules/versionInfo?id=" + modId, null, BXMPNS,
                    "GET", BXMPApikey);
              }
              // check local too
              versionList = ModuleFilter.getVersionHistory(ctx, modId, false);
              if (response == null || response.isEmpty())
              {
                resp.getWriter().print(jm.serialize(versionList));
              }
              else
              {
                if (versionList == null || versionList.isEmpty())
                {
                  resp.getWriter().print(response);
                }
                else
                {
                  // combine local and MP results
                  List<Map<String, Object>> mpVersionList = jm
                      .readAsObject(response, ArrayList.class);
                  versionList.addAll(mpVersionList);
                  versionList = ModuleFilter.sortVersions(versionList);
                  resp.getWriter().print(jm.serialize(versionList));
                }
              }

            }
            break;
          }
          // get category list
          case category:
          {
            String type = req.getParameter("type");
            
            if (type == null)
            {
              logger
                  .warning("Get module's categories: missing parameter: type");
              return;
            }
            
            if (ModuleType.getBySubtype(type).equals(ModuleType.none))
            {
              logger.warning(
                    "Get module's categories: invalid parameter -- module's type!");
              return;
            }
 
            Set<String> categories = new HashSet<String>(10);
            if (z1.commons.Utils.isMarketPlace)
            {
              String mpNS = req.getParameter("ns");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }
              
              AppServiceFacade asf = new AppServiceFacade(ctx);
              List<AppRegItem> subApps = asf.getSubscribedApps();

              // Go through the list of templates that had been subscribed
              for (AppRegItem subApp : subApps)
              {
                Map<String, Object> vals = subApp.getValues();
                String appType = (String) vals.get("type");
                String category = (String) vals.get("category");
                if (category == null || category.isEmpty() || appType == null
                    || appType.isEmpty()
                    || ModuleType.getBySubtype(appType).equals(ModuleType.none)
                    || ModuleType.valueOf(appType) == null
                    || (type != null && !ModuleType.getBySubtype(type).name()
                        .equals(appType)))
                {
                  continue;
                }

                // Parse comma separated string to a set and filter out "any"
                String[] strArr = category.trim().split(",");
                Set<String> set = Arrays.stream(strArr).filter(s -> {
                  return !s.equals("any");
                }).collect(Collectors.toSet());

                categories.addAll(set);
              }
              String payload = new JsonMarshaller().serialize(categories);
              resp.getWriter().print(payload);
            }
            else
            {
              String response = null;
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                response = TemplateUtils.callMP(
                    "/c3/data/modules/category?type=" + type, null, BXMPNS,
                    "GET", BXMPApikey);
              }
              AppServiceFacade asf = new AppServiceFacade(ctx);
              List<AppRegItem> subApps = asf.getSubscribedApps();
              
              // Go through the list of templates that had been subscribed
              for (AppRegItem subApp : subApps)
              {
                Map<String, Object> vals = subApp.getValues();
                String appType = (String) vals.get("type");
                String category = (String) vals.get("category");
                if (category == null || category.isEmpty() || appType == null
                    || appType.isEmpty()
                    || ModuleType.getBySubtype(appType).equals(ModuleType.none)
                    || ModuleType.valueOf(appType) == null
                    || (type != null && !ModuleType.getBySubtype(type).name()
                        .equals(appType)))
                {
                  continue;
                }
                
                // Parse comma separated string to a set and filter out "any"
                String[] strArr = category.trim().split(",");
                Set<String> set = Arrays.stream(strArr).filter(s -> {
                  return !s.equals("any");
                }).collect(Collectors.toSet());

                categories.addAll(set);
              }
              
              // now merge local result with result from MP 
              Set<String> allCategories = new HashSet<>();
              
              if (response != null && !response.isEmpty())
              {
                try
                {
                  allCategories = new JsonMarshaller().readAsObject(response, HashSet.class);
                }
                catch (Exception ex)
                {
                  // response failed to parse, probably because it's value is
                  // {"status": "fail", "reason": "Server side error."}
                }
              }

              if (!categories.isEmpty())
              {
                allCategories.addAll(categories);
              }
              String payload = new JsonMarshaller().serialize(allCategories);
              resp.getWriter().print(payload);
            }
            break;
          }
          // c3/data/modules/id?id=udc.system.core.ExperienceSampleModule:TI_One:2.0
          case id:
          {
            String modId = req.getParameter("id");
            ResponseMessage msg = null;

            if (modId == null || modId.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String[] parts = modId.split(":");

            if (parts.length < 3)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String pkgName = parts[0];

            boolean isActionTemplate = TemplateUtils.isActionTemplate(ctx, pkgName, parts.length);

            if (isActionTemplate && parts.length < 4)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Invalid module id for type actiontemplate.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String group = (isActionTemplate) ? parts[1] : null;
            String modName = (isActionTemplate) ? parts[2] : parts[1];
            if (modName.contains(" "))
            {
              modName = URLEncoder.encode(modName, "UTF-8");
            }
            String version = (isActionTemplate) ? parts[3] : parts[2];

            String mpNS = null;

            if (z1.commons.Utils.isMarketPlace)
            {
              mpNS = req.getParameter("ns");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }
            }
            else
            {
              mpNS = z1.commons.Utils.getBXMPNS(ctx);
            }

            Map<String, Object> module = TemplateUtils.getModuleDefinition(ctx,
                pkgName, group, modName, version);

            if (module == null || module.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "The id given is not recognizable as any published module.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String payload = new JsonMarshaller().serialize(module);
            resp.getWriter().print(payload);

            break;
          }
          // c3/data/modules/loadArtifacts?id=udc.system.core.ExperienceSampleModule:TI_One:2.0
          case loadArtifacts:
          {
            String searchModId = req.getParameter("id");
            String[] parts = searchModId.split(":");

            String pkgId = parts[0];
            boolean isActionTemplate = TemplateUtils.isActionTemplate(ctx, pkgId, parts.length);
            String name = (isActionTemplate) ? parts[2] : parts[1];
            if (name.contains(" "))
            {
              name = URLEncoder.encode(name, "UTF-8");
              String version = (isActionTemplate) ? parts[3] : parts[2];
              searchModId = (isActionTemplate)
                  ? pkgId + ":" + parts[1] + ":" + name + ":" + version
                  : pkgId + ":" + name + ":" + version;
            }

            String msg = "";
            String mpNS = (z1.commons.Utils.isMarketPlace)
                ? req.getParameter("ns")
                : z1.commons.Utils.getBXMPNS(ctx);

            String artifacts = TemplateUtils.getArtifacts(ctx, searchModId, msg,
                false, mpNS);

            if (artifacts == null)
            {
              ResponseMessage resMsg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleLoadArtifactFailed, msg);
              resp.getWriter().print(resMsg.toString());
            }
            else
            {
              resp.getWriter().print(artifacts);
            }

            break;
          }
          // c3/data/modules/loadDefinition?id=<appId> --> Load module's def
          // after it has been published to MP
          // e.g, c3/data/modules/loadDefinition?id=udc.system.core.ExperienceSampleModule:1.0
          case loadDefinition:
          {
            String appId = req.getParameter("id");
            ResponseMessage msg = null;
            if (appId == null || appId.isEmpty() || appId.indexOf(":") < 0)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Missing or invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String[] parts = appId.split(":");
            String appName = parts[0];
            String version = parts[1];

            if (z1.commons.Utils.isMarketPlace)
            {
              String mpNS = req.getParameter("ns");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }

              ModuleReaderFacade mrf = new ModuleReaderFacade(ctx, appName,
                  version);

              // Load definition from cache or Mongo.
              String appDef = mrf.loadDefinition(appId);

              if (appDef == null)
              {
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.moduleAppGetDefinitionFailed,
                    "The id given is not recognizable as any published module.");
                resp.getWriter().print(msg.toString());
                return;
              }

              resp.getWriter().print(appDef);
            }
            else
            {
              // template is OOTBS then first read from namespace , if not
              // present in namespace then read it from MP
              String appDef = null;
              if (appName.equals(TemplateConst.TRIGGER_OOTB)
                  || appName.equals(TemplateConst.ACTIONTEMPLATE_OOTB))
              {
                ModuleReaderFacade mrf = new ModuleReaderFacade(ctx, appName,
                    version);
                appDef = mrf.loadDefinition(appName);
                if (appDef != null)
                {
                  resp.getWriter().print(appDef);
                  return;
                }
              }
              String response = null;
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                response = TemplateUtils.callMP(
                    "/c3/data/modules/loadDefinition?id=" + appId, null, BXMPNS,
                    "GET", BXMPApikey);
              }

              if (response == null)
              {
                // try local
                response = Utils.loadDefinition(ctx, appId,
                    ArtifactType.application);

                if (response == null)
                {
                  msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.moduleAppGetDefinitionFailed,
                      "The id given is not recognizable as any module in Market Place or local.");
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
              resp.getWriter().print(response);
            }
            break;
          }
          // c3/data/modules/filter
          case filter:
          {
            String modType = req.getParameter("type");
            String managePage = req.getParameter("page");            
            
            ModuleType mtype = ModuleType.getBySubtype(modType);            
            Map<String, Object> qTag = null;   
            switch (mtype)
            {
              case actiontemplate:
              {
                qTag = ActionTemplateTags.getActionTemplateTags();
                break;
              }
              case template:
              {
                if (managePage != null && managePage.equalsIgnoreCase("manage"))
                  qTag = ExperienceTags.getExperienceInstanceTags();
                else
                  qTag = ExperienceTags.getExperienceTags();
                break;
              }
              case trigger:
              {
                qTag = TriggerTags.getTriggerTags();
                break;
              }
              case segment:
              {
                if (managePage != null && managePage.equalsIgnoreCase("manage"))
                {
                  qTag = SegmentTags.getSegmentTags(true);
                }
                else
                {
                  qTag = SegmentTags.getSegmentTags(false);                 
                }
                break;
              }
              case none:
              {
                return;
              }
            }
            
            // ZMOB-9465 LinkedHashMap guarantees order for filtermap.putAll()
            // Insertion order will be order of tags present at com/z1/resource/module/*Tags.json
            Map<String, Object> filtermap = new LinkedHashMap<>();

            if (mtype.equals(ModuleType.actiontemplate))
            {
              // type 'action' does not have system tags. Rather, we return action types
              Set<String> groups = ActionType.getActionTypeNames(Collections.singletonList(ActionType.article));
              filtermap.put(FilterTag.group.name(), groups);
            }
            
            // ZMOB-9531 : Do not display Target Logic filter on UI
            if (mtype.equals(ModuleType.trigger))
            {
              qTag.remove(TriggerTags.targetlogic.displayName);
            }

            filtermap.putAll(qTag);
    
            String payload = "";

            if (z1.commons.Utils.isMarketPlace
                || mtype.equals(ModuleType.actiontemplate)
                || mtype.equals(ModuleType.trigger))
            {
              String mpNS = req.getParameter("ns");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }

              ModuleFilter filter = new ModuleFilter(ctx, modType, null,
                  null, true);

              boolean requestIsValid = filter.validateInputs();

              if (!requestIsValid) return;
        
              // custom tag map is returned for all types.
              Set<String> customtags = filter.getCustomFilters(ctx);
              filtermap.put("Add custom tag", customtags);

              payload = new JsonMarshaller().serialize(filtermap);
              resp.getWriter().print(payload);
            }
            else
            {
              if (modType == null)
              {
                if (logger.canLog()) logger.log(
                    "c3/data/modules/filter: missing mandatory parameter 'type'!");
                return;
              }

              String url = "/c3/data/modules/filter?type=" + modType;
              if (managePage != null && managePage.equalsIgnoreCase("manage"))
              {
                url = url + "&page="+managePage;
              }
              
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                payload = TemplateUtils.callMP(url, null, BXMPNS, "GET",
                    BXMPApikey);
              }
              else
              {
                // Since MP is unavailable, use filtermap created on namespace.
                payload = new JsonMarshaller().serialize(filtermap);
              }
              
              ModuleFilter filter = new ModuleFilter(ctx, modType, null,
                  null, false);

              boolean requestIsValid = filter.validateInputs();

              if (!requestIsValid)
              {
                resp.getWriter().print(payload);
                return;
              }

              // custom tag map is returned for all types.
              Set<String> customtags = filter.getCustomFilters(ctx);
  
              if (customtags == null || customtags.isEmpty())
              {
                resp.getWriter().print(payload);
                return;
              }
              
              if (payload == null || payload.isEmpty()
                  || payload.contains("<title>Error 401 Unauthorized</title>"))
              {
                Map<String, Object> ns_customtagmap = new HashMap<>();
                ns_customtagmap.put("Add custom tag", customtags);
                payload = new JsonMarshaller().serialize(ns_customtagmap);
              }
              else
              {
                Map<String, Object> mpMap = new JsonMarshaller()
                    .readAsMap(payload);
       
                Set<String> customVals = mpMap.containsKey("Add custom tag")
                    ? new HashSet<>((List<String>) mpMap.get("Add custom tag"))
                    : new HashSet<>();
                
                customVals.addAll(customtags);
                
                mpMap.put("Add custom tag", customVals);
                payload = new JsonMarshaller().serialize(mpMap);
              }
              resp.getWriter().print(payload);
            }
            break;
          }
          // c3/data/modules/checkUniqueName?subtype=<c1|campaign|signal|segment|actiontemplate>
          // &instance=<true|false>&name=<name>&version=<version>&group=<banner|fs|..>
          //
          // version is mandatory for templates (instance=false)
          // instance is for non actiontemplate only
          // group is for actiontemplate only
          //
          // Instances are to be checked by name since instance id=<type>+UUID
          case checkUniqueName:
          {
            ResponseMessage msg = null;

            String subtype = req.getParameter("subtype");
            String encName = req.getParameter("name"); // URLEncoded
            String isInstStr = req.getParameter("instance");
            String version = req.getParameter("version");
            String group = req.getParameter("group");

            boolean isInstance = (isInstStr == null) ? false
                : Boolean.valueOf(isInstStr);

            if (subtype == null || subtype.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Missing or invalid subtype");
              resp.getWriter().print(msg.toString());
              return;
            }

            NSApp stype = NSApp.parseByName(subtype);

            if (stype.equals(NSApp.none) || encName == null || encName.isEmpty()
                || (!isInstance && (version == null || version.isEmpty()))
                || (stype.equals(NSApp.actiontemplate)
                    && (isInstance || group == null || group.isEmpty())))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Missing or invalid parameters in the request");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            // ZMOB-9734 Return name uniqueness check for segment and signal template modules.
            if (!isInstance && stype.equals(NSApp.signal))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Can't check name uniqueness for segment and signal template modules");
              resp.getWriter().print(msg.toString());
              return;
            }

            // spaces in 'id' are replaced with '+' in mongo
            encName = URLEncoder.encode(encName, "UTF-8");

            // URL decoded name.
            String decName = URLDecoder.decode(encName, "UTF-8");

            // Build 'id' for templates.
            String id = "";
            
            if (!isInstance)
            {
              if (stype.equals(NSApp.actiontemplate))
              {
                id = stype + "+" + stype.appId + ":"
                    + group + ":" + encName + ":" + version;
              }
              else
              {
                // id structure for config wrappers:
                //    c1,campaign --> <appid>:<name>:<version>
                // signal,segment --> <type>+<appid>:<name>:<version>
                
                id = stype.appId + ":" + encName + ":" + version;
                if (stype.equals(NSApp.signal))
                {
                  id = stype + "+" + id;
                }
              }
            }

            boolean artifactPresent = false;

            switch (stype)
            {
              case signal:
              {
                if (isInstance)
                {
                  List<CustomConfig> ccs = CustomConfig.loadAll(ctx,
                      CustomConfig.Type.signal);

                  if (ccs != null && !ccs.isEmpty())
                  {
                    for (CustomConfig cc : ccs)
                    {
                      // Look up 'name' in payload
                      Map<String, Object> pmap = new JsonMarshaller()
                          .readAsMap(cc.getPayload());
                      String instnname = (String) pmap.get("name");
                      if (instnname != null && instnname.equals(decName))
                      {
                        artifactPresent = true;
                        break;
                      }
                    }
                  }
                }
                break;
              }
              case segment:
              {
                if (isInstance)
                {
                  List<Segment> seglist = Segment.loadAll(ctx, true);
                  if (seglist != null && !seglist.isEmpty())
                  {
                    for (Segment seg : seglist)
                    {
                      // Look up 'name' in payload
                      Map<String, Object> pmap = new JsonMarshaller()
                          .readAsMap(seg.getPayload());
                      String instnname = (String) pmap.get("name");
                      if (instnname != null && instnname.equals(decName))
                      {
                        artifactPresent = true;
                        break;
                      }
                    }
                  }
                }
                else
                {
                  artifactPresent = Segment.load(ctx, Segment.PREFIX + "+" + id) != null;
                }
                break;
              }
              case campaign:
              case c1:
              {
                Journey.Type jtype = Journey.Type.valueOf(stype.name());

                if (isInstance)
                {
                  List<Journey> jlist = Journey.forceLoadAll(ctx, jtype);
                  if (jlist != null && !jlist.isEmpty())
                  {
                    for (Journey j : jlist)
                    {
                      if (j.getName() != null && j.getName().equals(decName))
                      {
                        artifactPresent = true;
                        break;
                      }
                    }
                  }
                  if (!artifactPresent)
                  {
                    jlist = jtype.equals(Journey.Type.c1)
                        ? Journey.forceLoadAll(ctx, Journey.Type.campaign)
                        : Journey.forceLoadAll(ctx, Journey.Type.c1);
                    if (jlist != null && !jlist.isEmpty())
                    {
                      for (Journey j : jlist)
                      {
                        if (j.getName() != null && j.getName().equals(decName))
                        {
                          artifactPresent = true;
                          break;
                        }
                      }
                    }
                  }
                }
                else
                {
                  artifactPresent = Journey.forceLoadDef(ctx, id,
                      Journey.Type.c1) != null
                      || Journey.forceLoadDef(ctx, id,
                          Journey.Type.campaign) != null;
                }
                break;
              }
              case actiontemplate:
              {
                List<CustomConfig> atemplates = CustomConfig.loadAll(ctx,
                    CustomConfig.Type.actiontemplate);

                // Regex for exact name match
                // udc.system.core.[ActionTemplateOOTB|NamespaceAction]:<group>:<name>:<version>
                String regex = "^udc\\.system\\.core\\.\\w+:\\w+:" + encName + ":.+$";

                artifactPresent = atemplates.stream().anyMatch(t -> {
                
                  String tid = t.getId();

                  if (tid.matches(regex))
                  {
                    // If regex matched check for 'group' and 'version' : 
                    // ===============================================================
                    // Case 1 : 'group' different --> Disallow
                    // Ex. if banner:simple:1.0 exists then disallow alert:simple:x.x
                    // ===============================================================
                    //
                    // ===============================================================
                    // Case 2.1 : 'group' && 'version' same --> Disallow
                    // Ex. if banner:simple:1.0 exists then disallow banner:simple:1.0
                    // ===============================================================
                    //
                    // ===============================================================
                    // Case 2.2 : 'group' same && 'version' different --> Allow
                    // Ex. if banner:simple:1.0 exists then allow banner:simple:1.1
                    // ===============================================================

                    String[] parts = tid.split(":");
                    return !group.equals(parts[1]) || version.equals(parts[3]);
                  }

                  return false;
                });

                break;
              }
            }
            
            if (!z1.commons.Utils.isMarketPlace && (!artifactPresent))
            {
              String response = null;
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                response = TemplateUtils.callMP(
                    "/c3/data/modules/checkUniqueName?subtype=" + subtype
                        + "&instance=" + isInstStr + "&name=" + encName
                        + "&version=" + version + "&group=" + group,
                    null, BXMPNS, "GET", BXMPApikey);
              }
              if (response != null && !response
                  .contains("<title>Error 401 Unauthorized</title>"))
              {
                resp.getWriter().print(response);
              }
              else
              {
                msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.uniqueCheckSuccess,
                    "The name '" + decName + "' is unique.");
                resp.getWriter().print(msg.toString());
              }
            }
            else
            {
              msg = artifactPresent
                  ? new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.uniqueCheckFailed,
                      "The name '" + decName + "' is already present.")
                  : new ResponseMessage(ctx, ResponseMessage.Status.success,
                      ResponseMessage.Type.uniqueCheckSuccess,
                      "The name '" + decName + "' is unique.");
              resp.getWriter().print(msg.toString());
            }
            break;
          }
          //c3/data/modules/getDomainEvents
          case getDomainEvents:
          {
            if (z1.commons.Utils.isMarketPlace)
            {
              return;
            }
            resp.getWriter().print(DomainHandler.instance(ctx).serializedDomainEvents());

            break;

          }
          // c3/data/modules/actiontemplateRefs?id=<actiontemplateID>
          // sample id: 'udc.system.core.NamespaceAction:push:NS+Push+Template:2.0'
          case actiontemplateRefs:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            
            String modId = req.getParameter("id");
            ResponseMessage msg = null;

            if (modId == null || modId.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String[] parts = modId.split(":");

            // ActionTemplate id must be 4 parts exact.
            if (parts.length != 4)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            if (!TemplateUtils.isActionTemplate(ctx, parts[0], parts.length))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "'id' must be actiontemplate id only.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String pkgId = parts[0];
            String acttype = parts[1];
            String name = parts[2];
            String version = parts[3];

            // Encode space to '+'.
            if (name.contains(" "))
            {
              name = URLEncoder.encode(name, "UTF-8");
              modId = pkgId + ":" + acttype + ":" + name + ":" + version;
            }

            List<String> refids = JourneyRuntimeUtils.getActionTemplateRef(ctx, modId);
            
            Map<String, Object> res = new HashMap<>();
            res.put("refs", refids);
            res.put("type", acttype);

            resp.getWriter().print(new JsonMarshaller().serialize(res));
            break;
          }
          //c3/data/modules/moduleDomainEvents?id=<moduleId>
          case moduleDomainEvents:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            
            String modId = req.getParameter("id");
            ResponseMessage msg = null;
            if (modId == null || modId.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            resp.getWriter().print(new JsonMarshaller()
                .serialize(TemplateUtils.getExperienceDomainEvent(ctx, modId)));
            break;
          }
          //c3/data/modules/guidedEventSources?id=<moduleId>
          case guidedEventSources:
          {
            if (z1.commons.Utils.isMarketPlace);
            
            String modId = req.getParameter("id");
            ResponseMessage msg = null;
            if (modId == null || modId.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            String[] parts = modId.split(":"); 
            if (parts.length < 3)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id is illformatted.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            List<ChannelType> eventSources = EventMappingInfo.getEventSourcesForModuleInstance(ctx, modId);
            
            if (eventSources.isEmpty())
            {
              resp.getWriter().print(new JsonMarshaller()
                  .serialize(eventSources));
              return;
            }
            
            List<Map<String, String>> esList = eventSources
                .stream().map(es -> {
                  Map<String, String> esMap = new HashMap<>(3);
                  esMap.put("id", es.name());
                  esMap.put("displayName", es.getDisplayName());
                  esMap.put("description", es.getDescription());
                  return esMap;
                }).collect(Collectors.toList());
    
            resp.getWriter().print(new JsonMarshaller()
                .serialize(esList));
            break;
          }
 
          case checkUpgradeAll:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            String type = req.getParameter("type");
            ResponseMessage msg = null;
            if (type == null || type.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing type in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            if (type.equals("experience"))
            {
              List<Map<String, Object>> resultList = new ArrayList<>();
              List<Map<String, Object>> cpgnList = _checkUpgradeforAllExperienceTemplate(ctx, Journey.Type.campaign);
              List<Map<String, Object>> c1List = _checkUpgradeforAllExperienceTemplate(ctx, Journey.Type.c1);
              resultList.addAll(cpgnList);
              resultList.addAll(c1List);
              resp.getWriter().print(new JsonMarshaller()
                  .serialize(resultList));
              return;
            }
            TemplateArtifact.Subtype stype = TemplateArtifact.Subtype.valueOf(type);
            switch(stype)
            {
              case c1:
              case campaign:
              {
                resp.getWriter()
                    .print(new JsonMarshaller()
                        .serialize(_checkUpgradeforAllExperienceTemplate(ctx,
                            Journey.Type.parse(type))));
                break;
              }
              case action:
              {
                resp.getWriter().print(new JsonMarshaller()
                    .serialize(_checkUpgradeforAllActionTemplate(ctx)));
                break;
              }
              case segment:
              {
                resp.getWriter().print(new JsonMarshaller()
                    .serialize(_checkUpgradeforAllSegmentTemplate(ctx)));
                break;
              }
              case signal:
              {
                resp.getWriter().print(new JsonMarshaller()
                    .serialize(_checkUpgradeforAllTriggerTemplate(ctx)));
                break;
              }
              default:
                break;
            }
            break;
          }
          //c3/data/modules/instancesByActionTemplate?id=<id>&version=<1.0> where id will be without version and version is optional.
          //ex. instancesByActionTemplate?id=udc.system.core.NamespaceAction:push:Push+with+deeplink&version=1.0
          case instancesByActionTemplate:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            String modId = req.getParameter("id");
            String version = req.getParameter("version");
            String[] parts = modId.split(":");

            ResponseMessage msg = null;
            if (modId == null || modId.isEmpty() || parts.length < 1)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Missing or invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String pkgId = parts[0];

            // Adding 1 to parts.length compensating for 'version' since
            // TemplateUtils.isActionTemplate() expects length of pkgid:group:appname:version
            boolean isActionTemplate = TemplateUtils.isActionTemplate(ctx, pkgId, parts.length+1);
            if (!isActionTemplate)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "'id' must be actiontemplate id only.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String acttype = parts[1];
            String name = parts[2];

            // Encode space to '+'.
            if (name.contains(" "))
            {
              name = URLEncoder.encode(name, "UTF-8");
            }

            modId = pkgId + ":" + acttype + ":" + name;
            resp.getWriter().print(new JsonMarshaller().serialize(
                _getActionTemplateReferencesInExperience(ctx, modId, version)));
            return;

          }
          default:
          {
            break;
          }
        }
      }

      private List<Map<String, Object>> dummyCheckUpdateResponse(int len)
      {

        List<Map<String, Object>> res = new ArrayList<>();

        for (int i = 0; i < len; i++)
        {
          Map<String, Object> om = new HashMap<>();
          Map<String, Object> im = new HashMap<>();

          String priArtid = "campaign:udc.system.core.TestModule:TI_One:1.0";

          im.put(priArtid, new ArrayList<>(Arrays.asList("latestTI",
              "other_instance", "another_instnce_too")));

          im.put("segment:udc.system.core.TestModule:Segment_X:1.0",
              new ArrayList<>(Arrays.asList("Segment_X (TI_One)")));

          im.put("segment:udc.system.core.TestModule:FrequentUser:1.0",
              new ArrayList<>(Arrays.asList("FrequentUser (TI_One)")));

          im.put("signal:udc.system.core.TestModule:Trigger_One:1.0",
              new ArrayList<>(Arrays.asList("Trigger_One (latestTI)",
                  "Trigger_One (other_instance)",
                  "Trigger_One (another_instnce_too)")));

          om.put(priArtid, im);

          res.add(om);
        }
        return res;
      }
    };
  }

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandlerFactory#post()
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings({ "unchecked", "incomplete-switch", "unused" })
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/modules/mappingReady?type=<experience|trigger|segment>
          // payload example:
          // [{
          // "appId": "udc.system.core.TestEventMapping3:1.0",
          // "id": "udc.system.core.TestEventMapping3:TI_tz1_e1_3:1.0",
          // "class": ["campaign"]
          // },...]
          case mappingReady:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            
            String type = req.getParameter("type");

            ResponseMessage msg = null;
            if (type == null || type.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'type'");
              resp.getWriter().print(msg.toString());
              return;
            }
            // Detect modules missing event mapping
            ModuleType moduleType = TemplateConst.ModuleType.getBySubtype(type);
            if (!moduleType.equals(ModuleType.template)
                && !moduleType.equals(ModuleType.trigger)
                && !moduleType.equals(ModuleType.segment))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Parameter 'type' must be either 'experience' or 'trigger' or 'segment'");
              resp.getWriter().print(msg.toString());
              return;
            }

            String payload = null;
            try
            {
              payload = ServletUtil.getPayload(req);
            }
            catch (Exception e)
            {
              ULogger logger = ULogger.instance(ctx);
              if (logger.canLog()) logger.log(
                  "c3/data/modules/mapReady: payload is ill formatted or null.");
            }

            if (payload == null || payload.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Request payload is missing. Expecting list of module Ids.");
              resp.getWriter().print(msg.toString());
              return;
            }
            List<Map<String, Object>> modules = new JsonMarshaller()
                .readAsObject(payload, List.class);

            List<Map<String, Object>> mapReadyInfo = _checkModuleMappingReady(
                ctx, modules, moduleType);

            resp.getWriter()
                .print(new JsonMarshaller().serialize(mapReadyInfo));
            break;
          }
          // Get all published modules based on:
          // - type: mandatory. Value can be: experience, segment, trigger,
          // content, model, or goal
          // - category: optional. Value can be: retail, bank, etc
          // - group: optional. Currently used only for content
          // - POST payload: optional, holds filters
          case all:
          {
            String type = req.getParameter("type");
            String payload = null;
            try
            {
              payload = ServletUtil.getPayload(req);
            }
            catch (Exception e)
            {
              ULogger logger = ULogger.instance(ctx);
              if (logger.canLog()) logger.log(
                  "c3/data/modules/all: tag payload is ill formatted or null. Ignore this filter as it is optional.");
            }
            Map<String, Object> qTag = (payload != null && !payload.isEmpty())
                ? new JsonMarshaller().readAsMap(payload)
                : null;

            if (z1.commons.Utils.isMarketPlace)
            {
              String mpNS = req.getParameter("ns");
              if (mpNS == null)
              {
                if (null == ctx.getNamespace())
                {
                  ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
                }
              }
              else
              {
                ctx.setNamespace(mpNS);
              }

              // Pick non-mp's industry.
              String category = req.getParameter("category");
              String skipOOTB = req.getParameter("skipOOTB");

              ModuleFilter filter = new ModuleFilter(ctx, type, category, qTag,
                  true);
              boolean requestIsValid = filter.validateInputs();

              if (!requestIsValid) return;

              List<Map<String, Object>> filteredRes = filter.filterModules(ctx,
                  true);
              String response = null;
              if (skipOOTB != null && skipOOTB.equalsIgnoreCase("true")
                  && (type.equals("trigger") || type.equals("actiontemplate") || type.equals("successmetric")))
              {
                List<Map<String, Object>> filteredResMP = filteredRes.stream()
                    .filter(match -> _isExistOOTB(match))
                    .collect(Collectors.toList());
                response = new JsonMarshaller().serialize(filteredResMP);
              }
              else
              {
                response = new JsonMarshaller().serialize(filteredRes);
              }
              resp.getWriter().print(response);
            }
            else
            {
              // check user for non mp
              User user = (User) ctx.getUser();
              if (user == null)
              {
                return;
              }
              if (type == null)
              {
                ULogger logger = ULogger.instance(ctx);
                if (logger.canLog()) logger.log(
                    "c3/data/modules/all: missing mandatory parameter 'type'!");
                return;
              }

              // Current namespace's industry.
              String category = TemplateUtils.getIndustry(ctx);

              // Now check local for any modules in test.
              ModuleFilter filter = new ModuleFilter(ctx, type, category, qTag,
                  false);
              boolean requestIsValid = filter.validateInputs();

              if (!requestIsValid)
              {
                return;
              }

              // read modules from local

              List<Map<String, Object>> filteredRes = filter.filterModules(ctx,
                  true);

              // combine local result with result from MP
              List<Map<String, Object>> matches = new ArrayList<>();

              List<Map<String, Object>> OOTBs = new ArrayList<>();
              List<Map<String, Object>> nonOOTBs = new ArrayList<>();
              if (filteredRes != null && !filteredRes.isEmpty())
              {
                // actionTemplate and trigger should be at start in list
                // to display first on UI
                if (type.equals("actiontemplate") || type.equals("trigger")  || type.equals("successmetric"))
                {

                  for (Map<String, Object> ootb : filteredRes)
                  {
                    String appId = ootb.containsKey("appId")
                        ? ootb.get("appId").toString().split(":")[0]
                        : null;
                    if (appId != null
                        && (appId.equals(TemplateConst.ACTIONTEMPLATE_OOTB)
                            || appId.equals(TemplateConst.TRIGGER_OOTB)))
                    {
                      OOTBs.add(ootb);
                    }
                    else
                    {
                      nonOOTBs.add(ootb);
                    }
                  }

                }
                else
                {
                  nonOOTBs.addAll(filteredRes);
                }
              }

              // read from MP
              String BXMPNS = z1.commons.Utils.getBXMPNS(ctx);
              String BXMPApikey = z1.commons.Utils.getBXMPApikey(ctx);
              String response = null;
              if (BXMPNS != null && !BXMPNS.isEmpty() && BXMPApikey != null
                  && !BXMPApikey.isEmpty())
              {
                String endpoint = "/c3/data/modules/all?type=" + type;
                if (category != null && !category.isEmpty())
                {
                  endpoint += "&category=" + category;
                  if (!OOTBs.isEmpty())
                  {
                    endpoint += "&skipOOTB=true";
                  }
                }
                response = TemplateUtils.callMP(endpoint, qTag, BXMPNS, "POST",
                    BXMPApikey);
                if (response == null || response.isEmpty())
                {
                  response = "[]";
                }
                if (response != null
                    && !response
                        .contains("<title>Error 401 Unauthorized</title>")
                    && !response.contains("\"status\": \"fail\""))
                {
                  List<Map<String, Object>> matchesMP = new JsonMarshaller()
                      .readAsObject(response, ArrayList.class);
                  if (OOTBs.isEmpty())
                  {
                    matches = matchesMP;
                  }
                  else
                  {
                    // older version does not evaluates skipOOTB parameter , if
                    // MP has older version then MP return OOTB template
                    // filter out OOTB templates, if already loaded from
                    // namespace
                    List<Map<String, Object>> filterModulesMP = matchesMP
                        .stream().filter(match -> _isExistOOTB(match))
                        .collect(Collectors.toList());
                    matches = filterModulesMP;
                  }
                }
              }

              // add OOTB templates at start of list so during rendering
              // these will get display at start
              if (!OOTBs.isEmpty())
              {
                matches.addAll(0, OOTBs);
              }
              if (!nonOOTBs.isEmpty())
              {
                matches.addAll(nonOOTBs);
              }
              // Filter hidden modules
              String role = (String) user.getValues().get(User.ROLE);

              // Filter out based on 'publishingScope':
              // BU : 'invisible' OR 'internal'
              // AllUsers: 'invisible'
              matches = matches.stream().filter(e -> {
                if (e.get("publishingScope") == null) return true;

                String ps = e.get("publishingScope").toString();
                PublishingScope pubScope = PublishingScope.parseByName(ps);
                return role.equals(User.Role.analyst.name())
                    ? !pubScope.equals(PublishingScope.invisible)
                        && !pubScope.equals(PublishingScope.internal)
                    : !pubScope.equals(PublishingScope.invisible);

              }).collect(Collectors.toList());

              resp.getWriter().print(new JsonMarshaller().serialize(matches));
            }
            break;
          }
          case publishModule:
          {
            if (!z1.commons.Utils.isMarketPlace) return;
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            Map<String, Object> m = j.readAsMap(payload);
            if (m == null) return;
            ResponseMessage respMsg = null;
            String pkgId = (String) m.get("id");
            String appJar = (String) m.get("jar");
            String appVer = (String) m.get("version");
            String imageName = (String) m.get("img");
            String multiModule = (String) m.get("multiModule");
            
            if (pkgId == null || appJar == null || appVer == null
                || imageName == null)
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppPublishingFailed,
                  "Missing module pkgId or appjar or version or images in the request.");
              resp.getWriter().print(respMsg.toString());
              return;
            }

            if (null == ctx.getNamespace())
            {
              ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
            }
            
            boolean isMultiMod = Boolean.parseBoolean(multiModule);
          
            // Single module. Publish using existing way
            if (!isMultiMod)
            {
              ResponseMessage versionCheckResponse = _checkVersion(ctx, pkgId,
                  appVer);

              // If result is not null then version conflict detected
              if (versionCheckResponse != null)
              {
                respMsg = versionCheckResponse;
                resp.getWriter().print(respMsg.toString());
                return;
              }

              ModuleReaderFacade mrf = new ModuleReaderFacade(ctx, pkgId,
                  appJar, appVer, imageName);

              List<String> artsInstalled = _installModule(appVer, pkgId, mrf);

              String res = new JsonMarshaller().serialize(artsInstalled);
              resp.getWriter().print(res);
            }
            else
            {
              // Handle MultiModule JAR

              // All modules in the MultiModule jar share the same JAR path and
              // images.zip path
              String jarpath = ModuleReaderFacade.getRepoPath(pkgId, appVer,
                  appJar);
              String imagepath = ModuleReaderFacade.getRepoPath(pkgId, appVer,
                  imageName);

              // Fetch list of all the apps from the app_reg.xml
              List<String> pkgidList = ModuleReaderFacade
                  .getAppRegItemIds(jarpath, ctx);

              // Check version of all modules before publishing
              for (String pid : pkgidList)
              {
                ResponseMessage versionCheckResponse = _checkVersion(ctx, pid,
                    appVer);

                // If result is not null then version conflict detected.
                if (versionCheckResponse != null)
                {
                  respMsg = versionCheckResponse;
                  resp.getWriter().print(respMsg.toString());
                  return;
                }
              }

              List<String> allArtIds = new ArrayList<>();

              // Install all the apps found in app_reg.xml
              for (String pid : pkgidList)
              {
                ModuleReaderFacade mrf = new ModuleReaderFacade(ctx, pid,
                    appJar, appVer, imageName, jarpath, imagepath);

                List<String> artIds = _installModule(appVer, pid, mrf);

                allArtIds.addAll(artIds);
              }

              String res = new JsonMarshaller().serialize(allArtIds);
              resp.getWriter().print(res);
            }
            break;
          }
          // c3/data/modules/unpublishModule?id=<appId>&force=<true>
          case unpublishModule:
          {
            String modId = req.getParameter("id");
            ResponseMessage msg = null;

            if (modId == null || modId.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String[] parts = modId.split(":");

            if (parts.length < 3)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String pkgId = parts[0];

            boolean isActionTemplate = TemplateUtils.isActionTemplate(ctx, pkgId, parts.length);

            if (isActionTemplate && parts.length < 4)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppGetDefinitionFailed,
                  "Invalid module id for type actiontemplate.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String version = (isActionTemplate) ? parts[3] : parts[2];

            boolean isNSApp = NSApp.getAppIds().contains(pkgId);

            if (z1.commons.Utils.isMarketPlace
                && (isNSApp || null == ctx.getNamespace()))
            {
              return;
            }

            ResponseMessage respMsg = null;

            if (pkgId == null || version == null)
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppUnpublishingFailed,
                  "Missing module id or version in the request.");
              resp.getWriter().print(respMsg.toString());
              return;
            }

            if (null == ctx.getNamespace())
            {
              ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
            }

            String name = (isActionTemplate) ? parts[2] : parts[1];
            if (name.contains(" "))
            {
              name = URLEncoder.encode(name, "UTF-8");
              modId = (isActionTemplate)
                  ? pkgId + ":" + parts[1] + ":" + name + ":" + version
                  : pkgId + ":" + name + ":" + version;
            }
            
            String force = req.getParameter("force");
            boolean forceDelete = false;
            if (force != null && force.equalsIgnoreCase("true"))
              forceDelete = true;

            ModuleWriterFacade mwf = new ModuleWriterFacade(ctx);
            String res = mwf.unpublishModule(ctx, modId, isNSApp, isActionTemplate, forceDelete);
            if (res == null || res.isEmpty())
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.moduleAppUnpublishingSuccess,
                  "Unpublish Module " + modId);
            }
            else
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppUnpublishingFailed, res);
            }
            resp.getWriter().print(respMsg.toString());
            break;
          }
          // c3/data/modules/delete?id=<instanceID>
          case delete:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            // Set the flag to skip all the inline notify clear cache
            // Call clear cache at the end of the processing
            ctx.put(Const.SKIP_INLINE_NOTIFY_CLEAR_CACHE,
                    Boolean.TRUE.toString());

            String instanceId = req.getParameter("id");
            String type = req.getParameter("type");
            
            // Route from segments/signals/journey handler for delete
            if (instanceId == null)
            {
              instanceId = req.getAttribute("id").toString();
            }
            if (type == null)
            {
              type = req.getAttribute("type").toString();
            }
            if (type.equals("campaignonce")) type = "c1";

            ResponseMessage respMsg = null;
            if (instanceId == null)
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.instanceDeletionFailed,
                  "Missing module id in the request.");
              resp.getWriter().print(respMsg.toString());
              return;
            }
            String subtype = null;
            if (instanceId.contains("+"))
            {
              String[] idType = instanceId.split("\\+");
              subtype = idType[0];
            }
            if (type != null)
            {
              subtype = type;
            }
            if (subtype == null)
            {
              respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.instanceDeletionFailed,
                  "Missing config type parameter in request");
              resp.getWriter().print(respMsg.toString());
              return;
            }

            // Disable deletion of OOTB segments.
            if (type.equals("segment") && instanceId != null)
            {
              Segment s = Segment.load(ctx, instanceId);
              if (s != null && s.getDef() != null && Segment.OOTB_SEGMENTS.contains(
                  s.getDef().getName()))
              {
                respMsg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.segmentCreateOrUpdateFailed,
                    String.format("OOTB segment '%s' cannot be deleted.",
                        s.getDef().getName()));
                resp.getWriter().print(respMsg);
                return;
              }
            }

            Artifact art = new Artifact();
            art.setSubtype(subtype);
            art.setId(instanceId);
            TemplateArtifact ta = new TemplateArtifact(ctx, null, art, null,
                null, subtype, null);

            ModuleVisitor mv = new ModuleVisitor(ctx, null,
                VisitingOperation.retrieveDefItem);
            mv.visit(ta);
            if (ta.getDefItem() != null)
            {
              Object payload2 = ta.getDefItem().getValues().get("payload2");
              String pl2Str = (payload2 != null
                  && !payload2.toString().isEmpty()) ? payload2.toString()
                      : null;

              Map<String, Object> ret = TemplateUtils
                  .getModuleConfigInstanceReferences(ctx, pl2Str);
              if (ret == null || !ret.containsKey("type")) return;
              TemplateConst.InstanceConfigType ict = TemplateConst.InstanceConfigType
                  .valueOf((String) ret.get("type"));
              Set<String> refs = (Set<String>) ret.get("refs");

              ResponseMessage msg = null;
              if (ict.equals(TemplateConst.InstanceConfigType.supportive))
              {
                if (refs != null && !refs.isEmpty())
                {
                  String references = refs.stream()
                      .collect(Collectors.joining(", "));
                  msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "This " + subtype
                          + " is supportive component of a module instance and still being referenced in: "
                          + references);
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
            }
            
            // Delete list of properties containing the exp instance mutable params
            if (type != null && type.equalsIgnoreCase(Type.campaign.name()))
            {
              Journey j = Journey.loadDef(ctx, instanceId, Type.campaign);
              JourneyDesignUtils.removeRefJidFromAudience(ctx, j);
              String instanceLopName = String.format("%s.%s",
                  LOPHandler.SYSTEM_LOPS_PREFIX, j.getName());
              CustomConfig cc = CustomConfig.load(ctx, instanceLopName,
                  CustomConfig.Type.listOfProperties, false);
              if (cc != null)
              {
                cc.delete();
                ArtifactAudit
                    .newInstance(ctx, ItemTypes.listOfProperties,
                        instanceLopName, instanceLopName, Operations.delete)
                    .save();
              }
            }

            ta.setDeletable(true);
            Map<String, String> primaryRef = new HashMap<>();
            primaryRef.put("configId", instanceId);
            ta.setPrimary(true);
            ta.setPrimaryArtRef(primaryRef);
            mv = new ModuleVisitor(ctx, null,
                ModuleVisitor.VisitingOperation.delete);
            mv.visit(ta);

            App.notifyClearCache(ctx.getNamespace(), CustomConfig.Type.listOfProperties.name());
            App.notifyClearCache(ctx.getNamespace(), CustomConfig.Type.action.name());
            respMsg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.instanceDeletionSuccessful,
                "Instance Id " + instanceId);
            resp.getWriter().print(respMsg.toString());
            break;
          }
          // c3/data/modules/create?id=<moduleId>
          case create:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            // Set the flag to skip all the inline notify clear cache
            // Call clear cache at the end of the processing
            ctx.put(Const.SKIP_INLINE_NOTIFY_CLEAR_CACHE,
                Boolean.TRUE.toString());

            Set<String> subtypeList = new HashSet<>();

            ResponseMessage msg = null;
            JsonMarshaller jm = new JsonMarshaller();

            User user = (User) ctx.getUser();
            if (user == null)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleCreateFailed,
                  "Fatal Error: Unable to validate user in request!");
              resp.getWriter().print(msg.toString());
              return;
            }
            String role = (String) user.getValues().get(User.ROLE);
            
            String moduleId = req.getParameter("id");
            String[] moduleIdParts = moduleId.split(":");
            if (moduleIdParts.length < 3)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleCreateFailed,
                  "Module 'id' is ill formatted: " + moduleId);
              resp.getWriter().print(msg.toString());
              return;
            }
            String moduleVersion = moduleIdParts[2];
            String appId = moduleId.substring(0, moduleId.indexOf(":"));
            String reqPLStr = ServletUtil.getPayload(req);
            if (reqPLStr == null || reqPLStr.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleCreateFailed,
                  "Request payload is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> reqPLMap = jm.readAsMap(reqPLStr);

            String instanceName = reqPLMap.get("name").toString();
            String instanceDesc = reqPLMap.get("description").toString();
            List<String> eventSource = Arrays.asList("any");
            Object dayRange = null;
            if (reqPLMap.containsKey("payload2"))
            {
              Map<String, Object> primaryPl2 = (Map<String, Object>) reqPLMap
                  .get("payload2");
              if (primaryPl2.containsKey("eventSource"))
              {
                eventSource = (List<String>) primaryPl2.get("eventSource");
              }
              if (primaryPl2.containsKey("dayRange"))
              {
                dayRange = primaryPl2.get("dayRange");
              }
            }

            if (moduleId == null || moduleId.isEmpty() || instanceName == null
                || instanceName.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleCreateFailed,
                  "Missing module id or instance name or payload containing artifact params in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            boolean isNSApp = NSApp.getAppIds().contains(appId);
            String failMsg = "";
            String mpNS = z1.commons.Utils.getBXMPNS(ctx);
            String defs = TemplateUtils.getArtifacts(ctx, moduleId, failMsg,
                false, mpNS);
            if (defs == null)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleCreateFailed,
                  "Unable to load artifacts defs for module - " + moduleId);
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> arts = jm.readAsMap(defs);
            Map<String, Object> artPayloads = new HashMap<>();
            List<String> channels = new ArrayList<>();
            List<String> types = new ArrayList<>(arts.keySet());
            String primaryId = moduleId.substring(0, moduleId.lastIndexOf(":"));
            // Get primary artifact payload from the request
            Map<String, Object> primaryPl = (Map<String, Object>) reqPLMap
                .get("primary");
            types.forEach(type -> {
              List<Map<String, Object>> typePLs = (List<Map<String, Object>>) arts
                  .get(type);
              typePLs.forEach(art -> {
                String id = (String) art.get("id");
                String subtype = (String) art.get("subtype");
                String payload = (String) art.get("payload");
                if (id.equals(primaryId) && primaryPl != null)
                {
                  payload = jm.serialize(primaryPl);
                }
                artPayloads.put(subtype + "+" + id, payload);
              });
            });

            // Get the module info payload from request
            Map<String, Object> reqModPayload = (Map<String, Object>) reqPLMap
                .get("payload");
            // Get the artifact list from module info in request
            List<Map<String, Object>> modArtList = (List<Map<String, Object>>) reqModPayload
                .get("artifacts");
            String moduleName = (String) reqModPayload.get("name"); // display
                                                                    // name
                                                                    // given by
                                                                    // BAE for
                                                                    // primary
                                                                    // artifact

            List<Map<String, Object>> respArts = new ArrayList<>();
            ModuleVisitor createVisitor = new ModuleVisitor(ctx, null,
                ModuleVisitor.VisitingOperation.create);

            TemplateArtifact primaryTA = null;
            Set<TemplateArtifact> supportTAs = new HashSet<>();

            // idSubs map holds Id for substitution for "irregular" config types
            Map<String, Map<String, String>> idSubs = new HashMap<>();
            
            Map<String, Object> lopMapForInstance = new HashMap<>();
            
            List<Map<String, Object>> gParamList = (List<Map<String, Object>>) reqModPayload
                .get("params");
            List<ParamsType> globalParamsTypes = TemplateUtils
                .convertCatalogParamToParmsType(gParamList);
            TemplateUtils.addMutableParamsToMap(appId, globalParamsTypes,
                lopMapForInstance);

            boolean isC1 = false;
            // Loop through and create appropriate config instances
            for (Map<String, Object> art : modArtList)
            {
              String version = (String) art.get("version");
              String artId = (String) art.get("id");
              String[] artIdParts = artId.split(":");
              String shortId = artIdParts[1];
              String subtype = (String) art.get("subtype");
              subtypeList.add(subtype);
              Map<String, Object> property = (Map<String, Object>) art
                  .get("property");
              boolean isPrimary = false;
              if (property != null && !property.isEmpty())
              {
                String isPrimaryStr = (String) property.get("isPrimary");
                if (isPrimaryStr != null)
                  isPrimary = Boolean.parseBoolean(isPrimaryStr);
              }

              List<Map<String, Object>> params = (List<Map<String, Object>>) art
                  .get("params");
              List<ParamsType> paramsTypes = TemplateUtils
                  .convertCatalogParamToParmsType(params);
              TemplateUtils.addMutableParamsToMap(artId, paramsTypes,
                  lopMapForInstance);

              String payload = (String) artPayloads.get(subtype + "+" + artId);

              Map<String, Object> payload2Map = (Map<String, Object>) art
                  .get("payload2");
              if (payload2Map != null && !payload2Map.isEmpty())
              {
                String payload2 = jm.serialize(payload2Map);
                art.put("payload2", payload2);
              }

              String artifactId = artId + ":" + version;

              art.put("instanceName", instanceName);
              art.put("moduleName", moduleName);
              art.put("role", role);
              art.put("moduleVersion", moduleVersion);
              if (reqModPayload.get("publishingScope") != null)
                art.put("publishingScope",
                    reqModPayload.get("publishingScope"));
              else
                art.put("publishingScope", "external");

              if (eventSource != null && !eventSource.isEmpty())
                art.put("eventSource", eventSource);

              if (isPrimary)
              {
                if (instanceDesc != null) art.put("instanceDesc", instanceDesc);
                if (reqModPayload.get("appTag") != null)
                {
                  Map<String, Object> appTag = (Map<String, Object>) reqModPayload
                      .get("appTag");
                  art.put("appTag", appTag);
                }
                if (dayRange != null) art.put("dayRange", dayRange);

                Map<String, Object> plm = null;
                if (subtype.equalsIgnoreCase("c1"))
                {
                  isC1 = true;
                }

                // Set name and description for primary config
                if (primaryPl.containsKey("name"))
                  primaryPl.put("name", instanceName);
                if (primaryPl.containsKey("description"))
                  primaryPl.put("description", instanceDesc);
                payload = jm.serialize(primaryPl);
              }
              else
              {
                Map<String, Object> pl2 = new HashMap<>();
                pl2.put("artifactId", artifactId);
                pl2.put("params", paramsTypes);
                String desc = (String) art.get("description");
                if (desc != null) art.put("instanceDesc", desc);
              }

              // Create primary TemplateArtifact now but don't instantiate it
              // until after all supportive art configs had been created
              if (isPrimary)
              {
                primaryTA = new TemplateArtifact(ctx, appId, art, paramsTypes,
                    globalParamsTypes, subtype, payload, false, version);
                if (AnchoredContent.isNameAsIdConfig(subtype))
                {
                  Map<String, String> ids = Optional
                      .ofNullable(idSubs.get(subtype)).orElse(new HashMap<>());
                  ids.put(shortId, primaryTA.getInstanceConfigName());
                  idSubs.put(subtype, ids);
                }
                continue;
              }
              // if NSApp then no need to create other than primary artifact
              // Signal reference is already namespace instance
              if (!isNSApp)
              {
                // Save supportive artifacts to be instantiated later to make
                // sure
                // we got the primary artifact version which is the module
                // version
                TemplateArtifact ta = new TemplateArtifact(ctx, appId, art,
                    paramsTypes, globalParamsTypes, subtype, payload, false,
                    version);
                if (AnchoredContent.isNameAsIdConfig(subtype))
                {
                  Map<String, String> ids = Optional
                      .ofNullable(idSubs.get(subtype)).orElse(new HashMap<>());
                  ids.put(shortId, ta.getInstanceConfigName());
                  idSubs.put(subtype, ids);
                }
                supportTAs.add(ta);
              }
            }

            // ----------------------------------------------------------
            // Instantiate supportive artifact configs first
            if (!isNSApp && !supportTAs.isEmpty())
            {
              for (TemplateArtifact ta : supportTAs)
              {
                ta.accept(createVisitor);
                String subtype = ta.getSubtype();

                Map<String, Object> supportArtPl = ta.getPayload();
                String supportArtConfigId = (supportArtPl != null
                    && supportArtPl.containsKey("configId"))
                        ? (String) supportArtPl.get("configId")
                        : null;

                if (subtype.equals("channel") && supportArtConfigId != null)
                {
                  channels.add((String) supportArtConfigId);
                }
              }
            }

            // ----------------------------------------------------------
            // Now create primary artifact config and its payload2
            primaryTA.accept(createVisitor);

            // fetch supportive artifact configs info
            Map<String, Object> createVisitorPL = createVisitor.getPayload();
            List<Map<String, Object>> visitedArtsInfo = null;
            if (createVisitorPL != null && !createVisitorPL.isEmpty())
            {
              visitedArtsInfo = (List<Map<String, Object>>) createVisitorPL
                  .get("artifacts");
              primaryTA.setVisitedArts(visitedArtsInfo);
            }
            // handle post create for primary art
            ModuleVisitor postCreateVisitor = new ModuleVisitor(ctx, null,
                ModuleVisitor.VisitingOperation.postCreate);
            primaryTA.setConfigIdSubs(idSubs);
            primaryTA.accept(postCreateVisitor);

            respArts = (List<Map<String, Object>>) primaryTA.getPayload2()
                .get("artifacts");
            Map<String, Object> visitedPrimary = createVisitor
                .getVisitPayload();
            Map<String, Object> primaryArt = new HashMap<>();
            String primaryArtSubtype = (String) visitedPrimary.get("subtype");
            String primaryArtConfigId = (String) visitedPrimary.get("configId");
            String primaryArtName = (String) visitedPrimary.get("name");
            Map<String, String> primaryRef = new HashMap<>();
            primaryRef.put("configId",
                primaryArtSubtype + "+" + primaryArtConfigId);
            primaryRef.put("name", primaryArtName);
            // create domain url
            String scheme = z1.core.utils.Utils.getReqScheme(req);
            int port = req.getServerPort();
            if (scheme.equalsIgnoreCase("https")) port = -1;
            String domainUrl = scheme + "://" + req.getServerName();
            if (port != -1)
            {
              domainUrl += ":" + port;
            }
            List<ParamsType> sysParams = new ArrayList<>(10);
            ParamsType paramsType = new ParamsType();
            paramsType.setName("z1_domainUrl");
            paramsType.setValue(domainUrl);
            sysParams.add(paramsType);

            if (primaryArtSubtype.equals("campaign")
                || primaryArtSubtype.equals("c1"))
            {
              paramsType = new ParamsType();
              paramsType.setName("z1_instanceName");
              paramsType.setValue(primaryArtName);
              sysParams.add(paramsType);
              paramsType = new ParamsType();
              paramsType.setName("z1_experienceType");
              paramsType.setValue(primaryTA.getSubtype());
              sysParams.add(paramsType);
              paramsType = new ParamsType();
              paramsType.setName("z1_instanceId");
              paramsType.setValue(primaryArtConfigId);
              sysParams.add(paramsType);
            }
            // handle post create for supportive arts
            if (!supportTAs.isEmpty())
            {
              for (TemplateArtifact ta : supportTAs)
              {
                // update param z1_instanceName,z1_experienceType,
                // z1_instanceId only for deletable supportive config
                // primary config is campaign or c1
                if ((primaryArtSubtype.equals("campaign")
                    || primaryArtSubtype.equals("c1")) && ta.isDeletable())
                {
                  ta.setParams(sysParams);
                }
                ta.setPrimaryArtRef(primaryRef);
                // special art config ID/name to find [[anchor:...]] for
                // substitution
                ta.setConfigIdSubs(idSubs);
                // set all module's artifacts to find/replace trigger and
                // segment references can be substituted.
                ta.setVisitedArts(visitedArtsInfo); // set all module's
                                                    // artifacts to find/replace
                                                    // trigger and segment
                                                    // references can be
                ModuleVisitor taPostCreateVisitor = new ModuleVisitor(ctx, null,
                    ModuleVisitor.VisitingOperation.postCreate);
                ta.accept(taPostCreateVisitor);
              }
            }
            
            // create list of properties containing mutable params
            if (!lopMapForInstance.isEmpty())
            {
              String lopName = String.format("%s.%s",
                  LOPHandler.SYSTEM_LOPS_PREFIX, instanceName);
              String description = String
                  .format("Config for experience instance %s", instanceName);
              ListOfProperties lop = new ListOfProperties(ctx, lopName,
                  description, lopMapForInstance);
              boolean createLop = true;
              Operations lopOperation = Operations.create;
              // check for some reason if lopName already existed then overwrite.
              CustomConfig cc = CustomConfig.load(ctx, lopName, CustomConfig.Type.listOfProperties, true);
              if (cc != null)
              {
                createLop = false;
                lopOperation = Operations.edit;
              }
              lop.save(createLop);

              ArtifactAudit.newInstance(ctx, ItemTypes.listOfProperties,
                  lopName, lopName, lopOperation).save();
              subtypeList.add(CustomConfig.Type.listOfProperties.name());
            }

            // Switch the channel if any to published mode
            channels.forEach(channelId -> {
              ChannelDefWrapper cdr = ChannelDefWrapper.forceLoad(ctx,
                      channelId);
              cdr.setState(State.published);
              cdr.save();
            });

            // Call Notify clear cache for all sub-type before executing SI.
            // RunOneCampaign once will also call one last notify clear cache
            subtypeList.add(CustomConfig.Type.action.name());
            subtypeList.forEach(subtype -> {
              App.notifyClearCache(ctx.getNamespace(), subtype);
            });

            App.notifyClearCache(ctx.getNamespace(), primaryArtSubtype);
            if (isC1)
            {
              JourneyHandler.runCampaignOnce(ctx, primaryArtConfigId,
                  Journey.Type.c1.name());
              // A schedule channel custom config is created by runCampaignOnce. So channel need to clear cache
              App.notifyClearCache(ctx.getNamespace(), ChannelDefWrapper.PREFIX);
            }

            // Prepare response for request
            primaryArt.put("configId", primaryArtConfigId);
            primaryArt.put("artifactId", visitedPrimary.get("artifactId"));
            primaryArt.put("subtype", primaryArtSubtype);
            primaryArt.put("isPrimary", visitedPrimary.get("isPrimary"));
            primaryArt.put("deletable", visitedPrimary.get("deletable"));
            respArts.add(primaryArt);

            String res = jm.serialize(respArts);
            resp.getWriter().print(res);
            break;
            
          }
          case upgrade:
          {

            if (z1.commons.Utils.isMarketPlace) return;

            ResponseMessage msg = null;
            JsonMarshaller jm = new JsonMarshaller();

            User user = (User) ctx.getUser();
            if (user == null)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleUpgradeFailed,
                  "Fatal Error: Unable to validate user in request!");
              resp.getWriter().print(msg.toString());
              return;
            }
            String reqPLStr = ServletUtil.getPayload(req);
            if (reqPLStr == null || reqPLStr.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleUpgradeFailed,
                  "Request payload is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }
            List<Map<String, Object>> reqPLList = jm.readAsObject(reqPLStr,
                ArrayList.class);
            if (reqPLList == null || reqPLList.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleUpgradeFailed,
                  "Request payload is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }

            List<ResponseMessage> responces = new ArrayList<>();
            reqPLList.forEach(instance -> {

              String moduleId = (String) instance.get("moduleId");
              String[] moduleIdParts = moduleId.split(":");
              String failMsg = "";
              if (moduleIdParts.length < 3)
              {
                responces
                    .add(new ResponseMessage(ctx, ResponseMessage.Status.fail,
                        ResponseMessage.Type.moduleUpgradeFailed,
                        "Module 'id' is ill formatted: " + moduleId));

              }
              else
              {
                String moduleVersion = moduleIdParts[2];
                String appId = moduleId.substring(0, moduleId.indexOf(":"));

                String mpNS = z1.commons.Utils.getBXMPNS(ctx);

                String latestVersion = null;
                boolean isNSApp = NSApp.getAppIds().contains(appId);
                if (isNSApp)
                {
                  latestVersion = _getLatestversionFromLocal(ctx,
                      moduleId.substring(0, moduleId.lastIndexOf(":")),
                      moduleVersion);
                }
                else
                {
                  latestVersion = _getLatestVersionFromMP(ctx,
                      moduleId.substring(0, moduleId.lastIndexOf(":")),
                      moduleVersion);
                }
                String version = latestVersion;
                String defs = TemplateUtils.getArtifacts(ctx,
                    moduleId.substring(0, moduleId.lastIndexOf(":") + 1)
                        + latestVersion,
                    failMsg, false, mpNS);
                if (defs == null)
                {
                  responces
                      .add(new ResponseMessage(ctx, ResponseMessage.Status.fail,
                          ResponseMessage.Type.moduleUpgradeFailed,
                          "Unable to load artifacts defs for module - "
                              + moduleId));
                }
                else
                {

                  Map<String, Object> arts = jm.readAsMap(defs);
                  if (arts.keySet().size() != 3)
                  {
                    Map<String, Object> module = TemplateUtils
                        .getModuleDefinition(ctx, appId, null, moduleIdParts[1],
                            version);
                    List<String> instanceIds = (List<String>) instance
                        .get("instanceIds");
                    String instanceType = (String) instance.get("type");
                    instanceIds.forEach(instanceId -> {
                      _upgrade(ctx, req, instanceType, instanceId, moduleId,
                          appId, arts, module);
                    });
                    responces.add(
                        new ResponseMessage(ctx, ResponseMessage.Status.success,
                            ResponseMessage.Type.moduleUpgradeDone,
                            "Module instances upgraded successfully for "
                                + moduleId));
                  }
                }
              }
            });
            resp.getWriter().print(responces.toString());
            break;

          }
          // c3/data/modules/createNSModule?subtype=<campaign|c1|actiontemplate>
          case createNSModule:
          {
            // No Namespace module should be created on Market Place
            if (z1.commons.Utils.isMarketPlace) return;
            
            String subtype = req.getParameter("subtype");
            String payLoad = ServletUtil.getPayload(req);
            ResponseMessage msg = null;
            if (subtype == null || subtype.isEmpty() || !NSApp.isValid(subtype)
                || payLoad == null || payLoad.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppPublishingFailed,
                  "Missing or invalid subtype or payload in request.");
              resp.getWriter().print(msg.toString());
              return;
            }

            NSApp nsApp = NSApp.valueOf(subtype);
            if (!nsApp.equals(NSApp.c1) && !nsApp.equals(NSApp.campaign)
                && !nsApp.equals(NSApp.actiontemplate)
                && !nsApp.equals(NSApp.segment))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppPublishingFailed,
                  "Invalide subtype. Namespace module can only be c1, campaign, segment or actiontemplate.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String appId = nsApp.appId;
            JsonMarshaller jm = new JsonMarshaller();
            List<Map<String, Object>> artifactList = jm.readAsObject(payLoad,
                List.class);

            // Validate tag names and values.
            if (!TemplateUtils.validateTags(artifactList, nsApp))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.moduleAppPublishingFailed,
                  "Invalid tag names.");
              resp.getWriter().print(msg.toString());
              return;
            }

            ModuleWriterFacade mwf = new ModuleWriterFacade(ctx);
            Map<String, Object> resMap = mwf.createNSModule(ctx, subtype, appId,
                artifactList, false);
            if (!resMap.isEmpty())
            {
              String res = new JsonMarshaller().serialize(resMap);
              resp.getWriter().print(res);
              return;
            }
            ModuleReaderFacade mrf = new ModuleReaderFacade(ctx, appId, "");
            List<Artifact> arts = mrf.getArtifacts(appId);

            // notify clear cache
            Set<String> subtypes = arts.stream().map(art -> {
              return art.getSubtype();
            }).collect(Collectors.toSet());
            TemplateUtils.clearModuleCache(ctx, subtypes);

            String res = new JsonMarshaller().serialize(arts);
            resp.getWriter().print(res);

            break;
          }
          // _/modules/allInstances?type=experience|segment|signal&category=<retail|..etc>
          // Additional params:
          //
          // expernc -> filteropt=<active|inactive|draft>
          // -> days=<>
          //
          // tags sent as map in req payload. Ex:
          // {
          // "goal": ["Revenue","Retention"],
          // "custom": ["RTO"]
          // }
          case allInstances:
          {
            if (z1.commons.Utils.isMarketPlace)
            {
              return;
            }
            
            String instanceType = req.getParameter("type"); // Mandatory
            if(instanceType == null)  // Route from segments/signals/journey handler /all
            {
              instanceType = req.getAttribute("type").toString();
            }
            
            String hasStatus = req.getAttribute("hasStatus") != null ? req.getAttribute("hasStatus").toString() : null;
            boolean includeStatus = true;
            if (hasStatus != null && hasStatus.equalsIgnoreCase("false")) includeStatus = false;

            //filter from SD /campaignonce/all?type=<c1|campaignonce>&filter=<active|inactive|last5days|draft>
            String sdFilter = req.getParameter("filter"); 
            String payload = null;
            Map<String, Object> tags = null;

            try
            {
              payload = ServletUtil.getPayload(req);
              tags = (payload != null && !payload.isEmpty())
                  ? new JsonMarshaller().readAsMap(payload)
                  : null;
            }
            catch (Exception e)
            {
              ULogger logger = ULogger.instance(ctx);
              if (logger.canLog())
              {
                logger.log(
                    "c3/data/modules/allInstances: tag payload is ill formatted or null."
                        + " Ignore this filter as it is optional.");
              }
            }

            if (instanceType == null || instanceType.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing or invalid instance type");
              resp.getWriter().print(msg.toString());
              return;
            }

            if ("campaignonce".equals(instanceType)) instanceType = "c1";

            TemplateConst.ModuleType moduleType = TemplateConst.ModuleType
                .getBySubtype(instanceType);
            if (moduleType.equals(ModuleType.none))
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, "Invalid instance type");
              resp.getWriter().print(msg.toString());
              return;
            }

            resp.getWriter()
                .print(new JsonMarshaller().serialize(getAllInstances(ctx,
                    instanceType, TemplateUtils.getIndustry(ctx), tags,
                    moduleType, sdFilter, includeStatus)));
            break;
          }
          // c3/data/modules/prioritizeCampaign
          case prioritizeCampaign:
          {
            List<Map<String, Object>> prioritizedGlobal = new ArrayList<>();
            if (z1.commons.Utils.isMarketPlace)
            {
              resp.getWriter()
                  .print(new JsonMarshaller().serialize(prioritizedGlobal));
              return;
            }

            String reqPL = ServletUtil.getPayload(req);
            Map<String, Object> filters = (reqPL != null && !reqPL.isEmpty())
                ? new JsonMarshaller().readAsMap(reqPL)
                : null;

            ResponseMessage msg = null;
            String page = null;
            List<String> positions = new ArrayList<>();
            if (filters != null && !filters.isEmpty())
            {
              if (!filters
                  .containsKey(JourneyRuntimeUtils.ActionLabelType.page.name()))
              {
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Specifying filtering criteria is optional, but if used, must include a 'page'.");
                resp.getWriter().print(msg.toString());
                return;
              }

              page = (String) filters
                  .get(JourneyRuntimeUtils.ActionLabelType.page.name());

              if (page.trim().isEmpty())
              {
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Specifying filtering criteria is optional, but if used, must include a 'page'.");
                resp.getWriter().print(msg.toString());
                return;
              }

              // position is optional
              if (filters
                  .containsKey(JourneyRuntimeUtils.ActionLabelType.position.name()))
              {
                positions = (List<String>) filters
                    .get(JourneyRuntimeUtils.ActionLabelType.position.name());
              }
            }

            prioritizedGlobal = JourneyRuntimeUtils.filterCampaignsByActionLabels(ctx,
                page, positions);

            resp.getWriter()
                .print(new JsonMarshaller().serialize(prioritizedGlobal));
            break;
          }
          // c3/data/modules/prioritizeCampaignInfo
          case prioritizeCampaignInfo:
          {
            Map<String, Object> info = new HashMap<>();
            if (z1.commons.Utils.isMarketPlace)
            {
              resp.getWriter().print(new JsonMarshaller().serialize(info));
              return;
            }

            ResponseMessage msg = null;
            String id = req.getParameter("id");
            if (id == null || id.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'id'.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String reqPL = ServletUtil.getPayload(req);
            Map<String, Object> filters = (reqPL != null && !reqPL.isEmpty())
                ? new JsonMarshaller().readAsMap(reqPL)
                : null;

            String page = null;
            List<String> positions = new ArrayList<>();
            if (filters != null && !filters.isEmpty())
            {
              if (!filters
                  .containsKey(JourneyRuntimeUtils.ActionLabelType.page.name()))
              {
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Specifying filtering criteria is optional, but if used, must include a 'page'.");
                resp.getWriter().print(msg.toString());
                return;
              }

              page = (String) filters
                  .get(JourneyRuntimeUtils.ActionLabelType.page.name());

              // position is optional
              if (filters
                  .containsKey(JourneyRuntimeUtils.ActionLabelType.position.name()))
              {
                positions = (List<String>) filters
                    .get(JourneyRuntimeUtils.ActionLabelType.position.name());
              }
            }

            info = JourneyRuntimeUtils.getPrioritizedCampaignInfo(ctx, id, page,
                positions);

            resp.getWriter().print(new JsonMarshaller().serialize(info));
            break;
          }

          //c3/data/modules/upgradeActionTemplate?id=<id>
          case upgradeActionTemplate:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            String atId = req.getParameter("id");
            String reqPL = ServletUtil.getPayload(req);
            ResponseMessage msg = null;
            if (reqPL == null || reqPL.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            JsonMarshaller jm = new JsonMarshaller();
            List<Map<String, Object>> reqPLList = jm.readAsObject(reqPL,
                ArrayList.class);
            if (atId == null || atId.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            String[] parts = atId.split(":");

            // ActionTemplate id must be 4 parts exact.
            if (parts.length != 4)
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Invalid module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            if (!TemplateUtils.isActionTemplate(ctx, parts[0], parts.length))
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "'id' must be actiontemplate id only.");
              resp.getWriter().print(msg.toString());
              return;
            }
            String pkgId = parts[0];
            String acttype = parts[1];
            String name = parts[2];
            String version = parts[3];

            // Encode space to '+'.
            if (name.contains(" "))
            {
              name = URLEncoder.encode(name, "UTF-8");
            }
            atId = pkgId + ":" + acttype + ":" + name;
            String latestVersion = null;
            if (NSApp.actiontemplate.appId.equals(pkgId))
            {
              latestVersion = _getLatestversionFromLocal(ctx, atId, version);
            }
            else
            {
              latestVersion = _getLatestVersionFromMP(ctx, atId, version);
            }
            if (latestVersion != null && !latestVersion.isEmpty())
            {

              String oldTemplateId = atId + ":" + version;
              String newTemplateId = atId + ":" + latestVersion;
              if (reqPLList == null || reqPLList.isEmpty())
              {
                upgradeActionTemplate(ctx, oldTemplateId, newTemplateId, null,
                    null);
              }
              else
              {
                reqPLList.forEach(l -> {
                  Object instanceType = l.get("type");
                  List<String> instanceIds = (List<String>) (l)
                      .get("instanceIds");
                  if (instanceType != null
                      && !Journey.Type.parse(instanceType.toString())
                          .equals(Journey.Type.unknown)
                      && instanceIds != null && !instanceIds.isEmpty())
                  {
                    upgradeActionTemplate(ctx, oldTemplateId, newTemplateId,
                        instanceType.toString(), instanceIds);
                  }
                });
              }
              msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.actionTemplateUpgraded, "");
            }
            else
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Invalid module id in request.");
            }
            resp.getWriter().print(msg.toString());
            return;

          }
          case checkUpgrade:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            String reqPL = ServletUtil.getPayload(req);

            ResponseMessage msg = null;
            if (reqPL == null || reqPL.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            JsonMarshaller jm = new JsonMarshaller();
            List<String> ids = jm.readAsObject(reqPL, ArrayList.class);
            if (ids.isEmpty())
            {
              msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing module ids list in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            List<Map<String, Object>> resultList = new ArrayList<>(10);
            ids.stream().forEach(modId -> {
              String[] parts = modId.split(":");
              ResponseMessage msgLocal = null;
              String pkgName = parts[0];

              boolean isActionTemplate = TemplateUtils
                  .isActionTemplate(ctx, pkgName, parts.length);

              String pkgId = null;
              String acttype = null;
              String name = null;
              String version = null;
              if (!isActionTemplate && parts.length >= 3)
              {
                pkgId = parts[0];
                name = parts[1];
                version = parts[2];
                // Encode space to '+'.
                if (name.contains(" "))
                {
                  try
                  {
                    name = URLEncoder.encode(name, "UTF-8");
                  }
                  catch (UnsupportedEncodingException e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                  }
                }
                modId = pkgId + ":" + name;
              }
              else if (parts.length == 4)
              {
                pkgId = parts[0];
                acttype = parts[1];
                name = parts[2];
                version = parts[3];
                // Encode space to '+'.
                if (name.contains(" "))
                {
                  try
                  {
                    name = URLEncoder.encode(name, "UTF-8");
                  }
                  catch (UnsupportedEncodingException e)
                  {
                    z1.commons.Utils.showStackTraceIfEnable(e, null);
                  }
                }
                modId = pkgId + ":" + acttype + ":" + name;
              }
              String latestVersion = null;
              if (NSApp.getAppIds().contains(pkgId)
                  || pkgId.equals(TemplateConst.ACTIONTEMPLATE_OOTB)
                  || pkgId.equals(TemplateConst.TRIGGER_OOTB))
              {

                latestVersion = _getLatestversionFromLocal(ctx, modId, version);

                if (latestVersion == null
                    && (pkgId.equals(TemplateConst.ACTIONTEMPLATE_OOTB)
                        || pkgId.equals(TemplateConst.TRIGGER_OOTB)))
                {
                  latestVersion = _getLatestVersionFromMP(ctx, modId, version);
                }
              }
              else
              {
                latestVersion = _getLatestVersionFromMP(ctx, modId, version);
              }

              if (latestVersion != null)
              {
                Map<String, Object> map = new HashMap<>(1);
                map.put("isUpgradeAvailable", true);
                map.put("moduleId", modId + ":" + version);
                map.put("latestVersion", latestVersion);
                resultList.add(map);
              }
            });
            resp.getWriter().print(new JsonMarshaller().serialize(resultList));
            return;
          }
        }

      }

      private boolean _isExistOOTB(Map<String, Object> match)
      {
        String appId = match.get("appId").toString();
        String pkgId = appId.split(":")[0];
        return !pkgId.equals(TemplateConst.ACTIONTEMPLATE_OOTB)
            && !pkgId.equals(TemplateConst.TRIGGER_OOTB);
      }
    };
  }

  // ////////////////////////////////////////////////////////////////////////////
  // -- PRIVATE IMPLEMENTATION --
  
  /**
  * Returns list of instance info based on instance type and matching tag
  * filter.
  * 
  * @param ctx
  * @param category
  * @param qTag
  * @param containsTagCategory
  * @param journeyType
  * @return
  */
 @SuppressWarnings("unchecked")
  private List<Map<String, Object>> _getJourneyInfo(final UContext ctx,
      String category, Map<String, Object> qTag, boolean containsTagCategory,
      Type journeyType, List<String> filterOptions, String ns, boolean includeStatus)
 {
   List<Map<String, Object>> ret = new ArrayList<>();
   List<String> instIdsFilterTagCategory = null;
   
   if (qTag != null && (!qTag.isEmpty()) && containsTagCategory)
   {
      Map<String, List<String>> idmap = Journey.getModIdMap(ctx, journeyType);
      if (idmap.size() > 0)
        instIdsFilterTagCategory = _getIdsFilterByTagCategory(ctx, "experience",
            category, qTag, idmap, ns);
    }

    List<Journey> journeys = Journey.forceLoadAll(ctx, journeyType);
    
    User user = (User) ctx.getUser();
    String role = (String) user.getValues().get(User.ROLE);
    boolean isBU = role.equals(User.Role.analyst.name());
    
    Result<User> userRes = User.loadAll(ctx);
    Map<String, Map<String,String>> agentInfoById = new HashMap<>();
    for (User agent : userRes.getData())
    {
      Map<String, String> info = new HashMap<>(2);
      String fName = Optional
          .ofNullable((String) agent.getValues().get(User.Fields.fname.name()))
          .orElse("Unknown");
      String lName = Optional
          .ofNullable((String) agent.getValues().get(User.Fields.lname.name()))
          .orElse("");
      String agentId = (String) agent.getId();
      String email = Optional
          .ofNullable((String) agent.getValues().get(User.Fields.email.name()))
          .orElse(agentId);      
      info.put("fnln",  (lName.isEmpty()) ? fName : fName + " " + lName);
      info.put("email", email);
      agentInfoById.put(agentId, info);      
    }
    
    Map<String, Object> channelMap = journeyType == Type.c1
        ? Journey.getChannels(ctx)
        : null;
    for (Journey journey : journeys)
    {
      if (TemplateUtils.isTemplateConfig(journey.getId(), journeyType.name()))
        continue;

      DefinitionItem def = journey.getDefItem();
      String payload2 = (String) def.getValues()
          .get(DefinitionItem.Fields.payload2.name());
      Map<String, Object> appTag = null;
      Map<String, Object> artTag = null;
      if (payload2 != null && (!payload2.isEmpty()))
      {
        Map<String, Object> pl2 = new JsonMarshaller().readAsMap(payload2);
        // Hiding artifact instances in internal module
        if (pl2 != null && (!pl2.isEmpty())
            && pl2.get("publishingScope") != null
            && isBU)
        {
          String publishingScope = pl2.get("publishingScope").toString();
          if (PublishingScope.internal.name().equals(publishingScope)) continue;
        }

        if (pl2 != null && (!pl2.isEmpty()) && pl2.get("appTag") != null)
        {
          appTag = (Map<String, Object>) pl2.get("appTag");
        }
        if (pl2 != null && (!pl2.isEmpty()) && pl2.get("artTag") != null)
        {
          artTag = (Map<String, Object>) pl2.get("artTag");
        }
      }
      Map<String, Set<Object>> combinedTag = TemplateUtils
          .combineAppAndArtLevelTags(appTag, artTag);
      Map<String, Object> info = null;
      if (qTag == null || qTag.isEmpty())
      {
        info = TemplateUtils.getJourneyInstanceInfo(ctx,
            journey, journeyType, channelMap, filterOptions, null, includeStatus);
        
      }
      else
      {
        if (ModuleFilter.isBelongsToTag(ctx, combinedTag, qTag))
        {
          if (containsTagCategory
              && (!instIdsFilterTagCategory.contains(journey.getId())))
            continue;
          info = TemplateUtils.getJourneyInstanceInfo(ctx,
              journey, journeyType, channelMap, filterOptions, null, includeStatus);
        }
      }
      
      if (info != null && info.get("defitem") != null)
      {
        Map<String, Object> defItemInfo = (Map<String, Object>) info.get("defitem");
        String createdBy = Optional.ofNullable((String) defItemInfo.get(JourneySummaryInfo.createdBy.name())).orElse("Unknown");
        String createdByName = (agentInfoById.get(createdBy) != null) ? agentInfoById.get(createdBy).get("fnln") : "Unknown";
        String createdByEmail = (agentInfoById.get(createdBy) != null) ? agentInfoById.get(createdBy).get("email") : createdBy;
        String lastUpdatedBy = Optional.ofNullable((String) defItemInfo.get(JourneySummaryInfo.lastUpdatedBy.name())).orElse("Unknown");        
        String lastUpdatedByName = (agentInfoById.get(lastUpdatedBy) != null) ? agentInfoById.get(lastUpdatedBy).get("fnln") : "Unknown";
        String lastUpdatedByEmail = (agentInfoById.get(lastUpdatedBy) != null) ? agentInfoById.get(lastUpdatedBy).get("email") : lastUpdatedBy;
        defItemInfo.put(JourneySummaryInfo.createdByName.name(), createdByName);
        defItemInfo.put(JourneySummaryInfo.createdBy.name(), createdByEmail);
        defItemInfo.put(JourneySummaryInfo.lastUpdatedByName.name(), lastUpdatedByName);
        defItemInfo.put(JourneySummaryInfo.lastUpdatedBy.name(), lastUpdatedByEmail);
        List<Map<String, Object>> targetedSegments = _getSegmentInfoForJourney(ctx, journey);
        defItemInfo.put(JourneySummaryInfo.segments.name(), targetedSegments);
        ret.add(info);
      }            
    }
    return ret;
 }

  /**
   *  ZMOB-19234: Get Segment(s) info for campaign and campaignonce
   * @param j
   * @return
   * the targeted segment for this campaign
   */
 private List<Map<String, Object>> _getSegmentInfoForJourney(final UContext ctx, Journey j)
 {
   DefinitionItem item = j.getDefItem();
   List<Map<String, Object>> res = new ArrayList<>();
   if (item == null) return res;
   Set<String> segments = new HashSet<>();
   for(StateDef stateDef: j.getDef().getStates()){
     for (String segmentId: stateDef.getRef().split(",")){
       segments.add(segmentId);
     }
   }
   if (segments.contains(Const.ALL_USERS)){
     Map<String, Object> segmentInfo = new LinkedHashMap<>();
     segmentInfo.put("segmentId", Const.ALL_USERS);
     segmentInfo.put("segmentName", "All Users");
     res.add(segmentInfo);
     return res;
   }
   for (String segmentId: segments) {
     Map<String, Object> segmentInfo = new LinkedHashMap<>();
     Segment targetedSegment = Segment.load(ctx, segmentId, true);
     if (targetedSegment != null){
       segmentInfo.put("segmentId", targetedSegment.getId());
       segmentInfo.put("segmentName", targetedSegment.getDef().getName());
       res.add(segmentInfo);
     }
   }
   return res;
 }
  /**
   * Returns list of instance info based on instance type and matching tag
   * filter.
   * 
   * @param ctx
   * @param instype
   * @param category
   * @param qTag
   * @param containsTagCategory
   * @return
   */
  @SuppressWarnings("unchecked")
  private List<Map<String, Object>> _getSegmentInfo(final UContext ctx, String instype,
      String category, Map<String, Object> qTag, boolean containsTagCategory,
      Segment.Type segType, String ns)
  {
    List<Map<String, Object>> ret = new ArrayList<>();
    List<String> instIdsFilterTagCategory = null;
    if (containsTagCategory)
    {
      Map<String, List<String>> idmap = Segment.getModIdMap(ctx);
      if (!idmap.isEmpty())
        instIdsFilterTagCategory = _getIdsFilterByTagCategory(ctx, instype,
            category, qTag, idmap, ns);
    }

    for (Segment s : Segment.loadAll(ctx, true))
    {
      if (TemplateUtils.isTemplateConfig(s.getId(), Segment.PREFIX)) continue;
      // skip inline segments
      if (s.isInline()) continue;
      
      Map<String, Object> appTag = null;
      Map<String, Object> artTag = null;
      Map<String, Object> pl2 = new HashMap<>();

      String payload2 = s.getPayload2();
      if (payload2 != null && (!payload2.isEmpty())) 
      {
        pl2 = new JsonMarshaller().readAsMap(payload2);
        
        // Hiding artifact instances in internal module
        User user = (User) ctx.getUser();
        String role = (String) user.getValues().get(User.ROLE);
        if (pl2 != null && (!pl2.isEmpty()) && pl2.get("publishingScope") != null &&
            role.equals(User.Role.analyst.name()))
        {
          String publishingScope = pl2.get("publishingScope").toString();
          if (PublishingScope.internal.name().equals(publishingScope)) continue;
        }
      }
      if (pl2 != null && pl2.get("appTag") != null)
      {
        appTag = (Map<String, Object>) pl2.get("appTag");
      }
      if (pl2 != null && pl2.get("artTag") != null)
      {
        artTag = (Map<String, Object>) pl2.get("artTag");
      }   

      Map<String, Set<Object>> combinedTag = TemplateUtils
          .combineAppAndArtLevelTags(appTag, artTag);

      if (qTag == null || qTag.isEmpty())
      {
        Map<String, Object> info = s.getInstanceInfo(segType);
        if (info != null) ret.add(info);
      }
      else
      {
        if ((appTag == null || appTag.isEmpty())
            && (artTag == null || artTag.isEmpty()))
          continue;
        if (ModuleFilter.isBelongsToTag(ctx, combinedTag, qTag))
        {
          if (containsTagCategory
              && (!instIdsFilterTagCategory.contains(s.getId())))
            continue;
          Map<String, Object> info = s.getInstanceInfo(segType);
          if (info != null) ret.add(info);
        }
      }
    }
    return ret;
  }

  /**
   * Returns list of instance info based on instance type and matching tag
   * filter.
   * 
   * @param ctx
   * @param instype
   * @param category
   * @param qTag
   * @param containsTagCategory
   * @param ns
   * @return
   */
  @SuppressWarnings("unchecked")
  private List<Map<String, Object>> _getTriggerInfo(final UContext ctx, String instype,
      String category, Map<String, Object> qTag, boolean containsTagCategory, String ns)
  {
    List<Map<String, Object>> ret = new ArrayList<>();
    List<String> instIdsFilterTagCategory = null;
    if (containsTagCategory)
    {
      Map<String, List<String>> idmap = SignalPart.getModIdMap(ctx);
      if (idmap.size() > 0)
        instIdsFilterTagCategory = _getIdsFilterByTagCategory(ctx, instype, category, qTag, idmap, ns);
    }

    for (SignalPart s : SignalPart.forceLoadAll(ctx))
    {
      if (TemplateUtils.isTemplateConfig(s.getId(),
          SignalPart.PREFIX))
        continue;
            
      Map<String, Object> appTag = null;
      Map<String, Object> artTag = null;
      Map<String, Object> pl2 = new HashMap<>();

      String payload2 = s.getPayload2();
      if (payload2 != null && (!payload2.isEmpty())) 
      {
        pl2 = new JsonMarshaller().readAsMap(payload2);

        // Hiding artifact instances in internal module
        User user = (User) ctx.getUser();
        String role = (String) user.getValues().get(User.ROLE);
        if (pl2 != null && (!pl2.isEmpty()) && pl2.get("publishingScope") != null &&
            role.equals(User.Role.analyst.name()))
        {
          String publishingScope = pl2.get("publishingScope").toString();
          if (PublishingScope.internal.name().equals(publishingScope)) continue;
        }
      }
      if (pl2 != null && pl2.get("appTag") != null)
      {
        appTag = (Map<String, Object>) pl2.get("appTag");
      }
      if (pl2 != null && pl2.get("artTag") != null)
      {
        artTag = (Map<String, Object>) pl2.get("artTag");
      }

      Map<String, Set<Object>> combinedTag = TemplateUtils.combineAppAndArtLevelTags(appTag, artTag);
      Map<String, Object> info = null;

      if (qTag == null || qTag.isEmpty())
      {
        info = s.getInstanceInfo();
        if (info != null) ret.add(info);
      }
      else
      {
        // For trigger type, only 'Target Logic' tags are applied in 'tag'.
        // 'Trigger Type' is evaluated using 'group' in the
        // ModuleFilter.filterModulesByTypeCategoryAndGroup() method
        List<Object> qtagType = (List<Object>) qTag.get(FilterTag.targetlogic.displayName);

        // If 'qtagType' is null, it means no 'targetlogic' applied to match 'tag'. 
        // But these ModulePart's could have been filtered using 'group' in
        // ModuleFilter.filterModulesByTypeCategoryAndGroup() method. Hence we'll allow them
        // to be added to the matched list. Otherwise check if targetlogic matches.
        boolean tagMatched = (qtagType == null) || ModuleFilter.isBelongsToTag(ctx, combinedTag, qTag);
        
        if (tagMatched)
        {
          if (containsTagCategory && (!instIdsFilterTagCategory.contains(s.getId()))) continue;
          info = s.getInstanceInfo();
          if (info != null) ret.add(info);
        }
      }
    }
    return ret;
  }
  
  /**
   * @param ctx
   * @param instype
   * @param category
   * @param qTag
   * @param idmap
   * @param ns
   * @return
   */
  @SuppressWarnings("unchecked")
  private List<String> _getIdsFilterByTagCategory(UContext ctx, String instype, 
      String category, Map<String, Object> qTag, Map<String, List<String>> idmap, String ns)
  {
    List<Map<String, Object>> filteredRes = null;
    if (z1.commons.Utils.isMarketPlace)
    {
      if (ns == null)
      {
        if (null == ctx.getNamespace())
        {
          ctx.setNamespace(z1.commons.Utils.getBXMPNS(ctx));
        }
      }
      else
      {
        ctx.setNamespace(ns);
      }

      ModuleFilter filter = new ModuleFilter(ctx, instype, category, qTag,
          true);
      boolean requestIsValid = filter.validateInputs();

      if (requestIsValid) filteredRes = filter.filterModules(ctx, true);
    }
    else
    {
      String endpoint = "/c3/data/modules/all?type=" + instype;
      if (category != null && !category.isEmpty())
      {
        endpoint += "&category=" + category;
      }

      String response = TemplateUtils.callMP(endpoint, qTag, z1.commons.Utils.getBXMPNS(ctx),
          "POST", z1.commons.Utils.getBXMPApikey(ctx));

      if (response != null && !response.isEmpty())
      {
        try
        {
          filteredRes = new JsonMarshaller().readAsObject(response,
              ArrayList.class);
        }
        catch (Exception ex)
        {
          // response probably returned
          // {"status": "fail", "reason": "Server side error."}
        }
      }

      // If TemplateUtils.callMP(...) returns null or empty response,
      // filteredRes will be null here. Hence instantiate to empty arralist.
      if (filteredRes == null)
      {
        filteredRes = new ArrayList<>();
      }

      // Now check local for any modules in test.
      ModuleFilter filter = new ModuleFilter(ctx, instype, category, qTag,
          false);

      if (filter.validateInputs())
      {
        List<Map<String, Object>> localfiltered = filter.filterModules(ctx,
            true);

        // combine local result with result from MP
        if (localfiltered != null && !localfiltered.isEmpty())
        {
          filteredRes.addAll(localfiltered);
        }
      }
    }
    
    List<String> instIds = new ArrayList<>();
    // matches with modid of the instance.
    for (Map<String, Object> art : filteredRes)
    {
      String mid = art.get("id").toString();
      if (idmap.containsKey(mid))
      {
        // Found match
        for (String instid : idmap.get(mid))
        {  
          instIds.add(instid);
        }
      }
    }
    
    return instIds;
  }
  
  /**
   * Returns all instances of a given instance type, category, tags, and moduleType
   * @param ctx the context of the request
   * @param instanceType the type of instances to filter on
   * @param category the category to filter on
   * @param tags the tags to filter on
   * @param moduleType the module type as derived from the instance type
   * @return a list of instances meeting the criteria
   */
  @SuppressWarnings("unchecked")
  private List<Map<String, Object>> getAllInstances(
      final UContext ctx, final String instanceType, final String category, 
      Map<String, Object> tags, TemplateConst.ModuleType moduleType, String sdFilter, boolean includeStatus)
  {
    // Filtering by tag and category
    boolean containsTagCategory = tags != null && tags.size() != 0
        || category != null && !category.isEmpty(); 
    
    List<Map<String,Object>> ret = new ArrayList<>();
    switch (moduleType)
    {
      case template: // experience
      {                
        List<Object> experienceType = null;
        if (tags != null && tags.get(FilterTag.experienceType.displayName) != null)
        {
          experienceType = (List<Object>) tags.get(FilterTag.experienceType.displayName);
          tags.remove(FilterTag.experienceType.displayName);
        }
        
        List<String> filterOptions = Arrays.asList(FilterOption.days.name());
        if (tags != null && tags.get(FilterTag.experienceStatus.displayName) != null)
        {
          filterOptions = (List<String>) tags.get(FilterTag.experienceStatus.displayName);
          if (filterOptions.contains("Created in last 5 days"))
          {
            filterOptions.remove("Created in last 5 days");
            filterOptions.add(FilterOption.last5days.name());
          }
          tags.remove(FilterTag.experienceStatus.displayName);
        }
        
        // Request /campaign/all routing from SD view
        if (instanceType.equals("campaign"))
        {
          if (experienceType == null) experienceType = new ArrayList<>(1);
          if (!experienceType.contains("Triggered")) experienceType.add("Triggered");
        }
        // Request /[campaignonce|c1]/all routing from SD view
        else if (instanceType.equals("c1"))
        {
          if (experienceType == null) experienceType = new ArrayList<>(1);
          if (!experienceType.contains("Scheduled")) experienceType.add("Scheduled");
          if (sdFilter != null && (!sdFilter.isEmpty()))
          {
            filterOptions = Arrays.asList(sdFilter);
          }
        }


        // Filter experienceType
        String mpNS = z1.commons.Utils.getBXMPNS(ctx);
        if (experienceType == null || experienceType.contains("Triggered"))
        {
          List<Map<String, Object>> campaignMap = _getJourneyInfo(
              ctx, category, tags, containsTagCategory, Type.campaign, filterOptions, mpNS, includeStatus);
          if (campaignMap != null)
          {
            ret.addAll(campaignMap);
          }
        }
        if (experienceType == null || experienceType.contains("Scheduled"))
        {
          List<Map<String, Object>> c1Map = _getJourneyInfo(
              ctx, category, tags, containsTagCategory, Type.c1, filterOptions, mpNS, includeStatus);
          if (c1Map != null)
          {
            ret.addAll(c1Map);
          }
        }

        break;
      }
      
      case segment:
      {
        Segment.Type segType = Segment.Type.unknown;

        if (tags != null && tags.get(FilterTag.targetlogic.displayName) != null)
        {
          List<String> targetlogic = (List<String>) tags.get(
              FilterTag.targetlogic.displayName);

          segType = Segment.getSegmentTypeFromFilter(targetlogic);

          // Keep only custom tags in filterlist
          targetlogic = targetlogic.stream().filter(
                  tl -> Segment.Type.unknown.equals(
                      Segment.Type.parseFromTag(tl)))
              .collect(Collectors.toList());

          if (targetlogic.isEmpty())
          {
            tags.remove(FilterTag.targetlogic.displayName);
          }
        }
        
        String mpNS = z1.commons.Utils.getBXMPNS(ctx);

        List<Map<String, Object>> segmentMap = _getSegmentInfo(
            ctx, instanceType, category, tags, containsTagCategory, segType, mpNS);

        if (Segment.Type.dynamic.equals(segType))
        {
          Map<String, Object> users = new HashMap<>();
          users.put("name", "All Users");
          users.put("icon", "users");
          users.put("id", "system:allUsers");
          segmentMap.add(users);
        }

        ret.addAll(segmentMap);
        break;
      }

      case trigger:
      {

        String mpNS = z1.commons.Utils.getBXMPNS(ctx);
        List<Map<String, Object>> trigerMap = _getTriggerInfo(ctx, instanceType,
            category, tags, containsTagCategory, mpNS);
        if (trigerMap != null)
        {
          ret.addAll(trigerMap);
        }
        break;
      }

      default:
        break;
    }

    return ret;
  }

  /**
   * Gets recent version available for module
   * 
   * @param ctx
   * @param modId
   * @param version
   * @return
   */
  private String _getLatestVersionFromMP(UContext ctx, String modId,
      String version)
  {
    // combine local and MP results
    List<Map<String, Object>> mpVersionList = _getVersionHistoryFromMP(ctx,
        modId);
    Double l = Double.parseDouble(version);
    Map<String, Object> latestVersion = mpVersionList.stream()
        .filter(v -> Double.parseDouble(v.get("version").toString().trim()) > l)
        .findFirst().orElse(null);
    if (latestVersion == null)
    {
      return null;
    }
    return (String) latestVersion.get("version");
  }
  
  /**
   * Checks upgrade available for action templates used in experience instances
   * 
   * @param ctx
   * @return
   */
  private List<Map<String, Object>> _checkUpgradeforAllActionTemplate(
      UContext ctx)
  {
    List<Map<String, Object>> resultList = new ArrayList<>();
    List<CustomConfig> localTemplateList = CustomConfig.loadAll(ctx,
        z1.c3.CustomConfig.Type.action);
    localTemplateList.forEach(cc -> {
      String templateId = cc.getId();
      String[] parts = templateId.split(":");
      String pkgId = parts[0];
      String acttype = parts[1];
      String name = parts[2];
      String version = parts[3];

      String modId = pkgId + ":" + acttype + ":" + name;
      String latestVersion = null;
      if (!NSApp.actiontemplate.appId.equals(pkgId))
      {
        latestVersion = _getLatestVersionFromMP(ctx, modId, version);
      }
      else
      {
        latestVersion = _getLatestversionFromLocal(ctx, modId, version);
      }
      if (latestVersion != null)
      {
        Map<String, Object> map = new HashMap<>(1);
        map.put("isUpgradeAvailable", true);
        map.put("moduleId", pkgId + ":" + acttype + ":" + name + ":" + version);
        map.put("version", version);
        map.put("latestVersion", latestVersion);
        resultList.add(map);
      }
    });
    return resultList;
  }
  
  /**
   * Check upgrades for all modules templates for which instance get created
   * 
   * @param ctx
   * @param type
   * @return
   */
  private List<Map<String, Object>> _checkUpgradeforAllExperienceTemplate(
      UContext ctx, Journey.Type type)
  {
    List<Map<String, Object>> resultList = new ArrayList<>();
    Set<String> instanesList = new HashSet<>();
    List<Journey> journeyList = null;
    if (type.equals(Type.campaign))
    {
      journeyList = Journey.loadAll(ctx, type);
    }
    else if (type.equals(Type.c1))
    {
      journeyList = Journey.loadJourneysForExecution(ctx, type);
    }
    journeyList.stream().forEach(journey -> {
      String templateId = journey.getModId();
      if (templateId != null && !instanesList.contains(templateId))
      {
        instanesList.add(templateId);
        String[] parts = templateId.split(":");
        String pkgId = parts[0];
        String name = parts[1];
        String version = parts[2];
        String modId = pkgId + ":" + name;
        String latestVersion = null;
        if (!NSApp.campaign.appId.equals(pkgId)
            && !NSApp.c1.appId.equals(pkgId))
        {
          latestVersion = _getLatestVersionFromMP(ctx, modId, version);
        }
        else
        {
          latestVersion = _getLatestversionFromLocal(ctx, modId, version);
        }
        if (latestVersion != null)
        {
          Map<String, Object> map = new HashMap<>(1);
          map.put("isUpgradeAvailable", true);
          map.put("moduleId", pkgId + ":" + name + ":" + version);
          map.put("version", version);
          map.put("latestVersion", latestVersion);
          resultList.add(map);
        }
      }
    });

    return resultList;
  }
  
  
  /**
   * Check upgrades for all segments templates for which instance get created
   * 
   * @param ctx
   * @return
   */
  private List<Map<String, Object>> _checkUpgradeforAllSegmentTemplate(
      UContext ctx)
  {
    List<Map<String, Object>> resultList = new ArrayList<>();
    Set<String> instanesList = new HashSet<>();
    List<Segment> segmentList = Segment.loadAll(ctx, false);

    segmentList.stream().forEach(segment -> {
      String templateId = segment.getModId();
      if (templateId != null && !instanesList.contains(templateId))
      {
        instanesList.add(templateId);
        String[] parts = templateId.split(":");
        String pkgId = parts[0];
        String name = parts[1];
        String version = parts[2];
        String modId = pkgId + ":" + name;
        String latestVersion = null;
        if (!NSApp.segment.appId.equals(pkgId))
        {
          latestVersion = _getLatestVersionFromMP(ctx, modId, version);
        }
        else
        {
          latestVersion = _getLatestversionFromLocal(ctx, modId, version);
        }
        if (latestVersion != null)
        {
          Map<String, Object> map = new HashMap<>(1);
          map.put("isUpgradeAvailable", true);
          map.put("moduleId", pkgId + ":" + name + ":" + version);
          map.put("version", version);
          map.put("latestVersion", latestVersion);
          resultList.add(map);
        }
      }
    });
    return resultList;
  }
  
  /**
   * Check upgrades for all trigger templates for which instance get created
   * 
   * @param ctx
   * @return
   */
  private List<Map<String, Object>> _checkUpgradeforAllTriggerTemplate(
      UContext ctx)
  {
    List<Map<String, Object>> resultList = new ArrayList<>();
    Set<String> instanesList = new HashSet<>();
    List<SignalPart> signalList = SignalPart.loadAll(ctx);

    signalList.stream().forEach(signal -> {
      String templateId = signal.getModId();
      if (templateId != null && !instanesList.contains(templateId))
      {
        instanesList.add(templateId);
        String[] parts = templateId.split(":");
        String pkgId = parts[0];
        String name = parts[1];
        String version = parts[2];
        String modId = pkgId + ":" + name;
        String latestVersion = null;
        if (!NSApp.signal.appId.equals(pkgId))
        {
          latestVersion = _getLatestVersionFromMP(ctx, modId, version);
        }
        else
        {
          latestVersion = _getLatestversionFromLocal(ctx, modId, version);
        }
        if (latestVersion != null)
        {
          Map<String, Object> map = new HashMap<>(1);
          map.put("isUpgradeAvailable", true);
          map.put("moduleId", pkgId + ":" + name + ":" + version);
          map.put("version", version);
          map.put("latestVersion", latestVersion);
          resultList.add(map);
        }
      }
    });
    return resultList;
  }
  /**
   * Gets version history from mp
   * 
   * @param ctx
   * @param modId
   * @return
   */
  private List<Map<String,Object>> _getVersionHistoryFromMP(UContext ctx, String modId )
  {
    JsonMarshaller jm = new JsonMarshaller();
    String response = TemplateUtils.callMP(
        "/c3/data/modules/versionInfo?id=" + modId, null,
        z1.commons.Utils.getBXMPNS(ctx), "GET",
        z1.commons.Utils.getBXMPApikey(ctx));

    List<Map<String, Object>> mpVersionList = null;

    if (response != null)
    {
      try
      {
        mpVersionList = jm.readAsObject(response, ArrayList.class);
      }
      catch (Exception e)
      {
        mpVersionList = new ArrayList<>();
      }
      mpVersionList = ModuleFilter.sortVersions(mpVersionList);
    }
    else
    {
      mpVersionList = new ArrayList<>();
    }
    return mpVersionList;
  }

  /**
   * Gets latest version of a
   * 
   * @param ctx
   * @param modId
   * @param version
   * @return
   */
  private String _getLatestversionFromLocal(UContext ctx, String modId,
      String version)
  {
    Double l = Double.parseDouble(version);
    List<Map<String, Object>> versionList = ModuleFilter.getVersionHistory(ctx,modId, true);
    Map<String, Object> latestVersion = versionList.stream()
        .filter(v -> Double.parseDouble(v.get("version").toString().trim()) > l)
        .findFirst().orElse(null);
    if (latestVersion == null)
    {
      return null;
    }
    return latestVersion.get("version").toString();
  }
  
  /**
   * get list of experience instances where given action template is used 
   * 
   * @param ctx
   * @param templateId
   * @param version
   * @return
   */
  private List<Map<String, Object>> _getActionTemplateReferencesInExperience(
      UContext ctx, String templateId, String version)
  {
    List<Map<String, Object>> instanceList = new ArrayList<Map<String, Object>>();
    List<Journey> journeyList = new ArrayList<Journey>();
    journeyList.addAll(Journey.loadAll(ctx, Journey.Type.campaign));
    Map<String, Object> channelMap = Journey.getChannels(ctx);
    journeyList.addAll(Journey.loadAll(ctx, Journey.Type.c1));
    journeyList.stream().forEach(journey -> {
      Journey.Type journeyType = Journey.Type
          .parse(journey.getDefItem().getValues().get("custom").toString());
      
      boolean jAddToList = false;
      for (StateDef state : journey.getDef().getStates())
      {
        if (journeyType.equals(Journey.Type.c1))
        {
          for (TimerDef timerDef : state.getTimers())
          {
            for (ActionDef action : timerDef.getActions())
            {
              ParamDef paramDef = action.getParams().stream()
                  .filter(param -> param.getName().equals("z1_template_ref")
                      && ((version != null && param.getValue()
                          .equals(templateId + ":" + version))
                          || (version == null
                              && param.getValue().contains(templateId))))
                  .findFirst().orElse(null);
              if (paramDef != null)
              {
                String[] parts = paramDef.getValue().split(":");
                String actionArtifactId = templateId + ":" + parts[3];
                List<String> fop = new ArrayList<String>();
                fop.add(FilterOption.days.name());
                Map<String, Object> info = TemplateUtils.getJourneyInstanceInfo(
                    ctx, journey, journeyType, channelMap, fop,
                    actionArtifactId, false);
                if (info != null && (!info.isEmpty())) 
                  {
                    instanceList.add(info);
                    jAddToList = true;
                    break;
                  }
              }
            }
            if (jAddToList) break;
          }
        }
        else
        {
          for (ActivitySelectorDef activity : state.getActivities())
          {
            for (ActionDef action : activity.getActions())
            {
              ParamDef paramDef = action.getParams().stream()
                  .filter(param -> param.getName().equals("z1_template_ref")
                      && ((version != null && param.getValue()
                          .equals(templateId + ":" + version))
                          || (version == null
                              && param.getValue().contains(templateId))))
                  .findFirst().orElse(null);
              if (paramDef != null)
              {
                String[] parts = paramDef.getValue().split(":");
                String actionArtifactId = templateId + ":" + parts[3];
                List<String> fop = new ArrayList<String>();
                fop.add(FilterOption.days.name());
                Map<String, Object> info = TemplateUtils.getJourneyInstanceInfo(
                    ctx, journey, journeyType, channelMap, fop,
                    actionArtifactId, false);
                if (info != null && (!info.isEmpty()))
                {
                  instanceList.add(info);
                  jAddToList = true;
                  break;
                }
              }

            }
            if (jAddToList) break;
          }
        }
        if (jAddToList) break;
      }
    });
    return instanceList;
  }
  
  /**
   * Upgrades actionTemplate 
   *
   * @param ctx
   * @param oldModId
   * @param modId
   * @param type
   * @param instanceIds
   */
  private void upgradeActionTemplate(UContext ctx,String oldModId,String  modId,String type, List<String> instanceIds)
  {
    List<Journey> journeyList = new ArrayList<>();
    if (type == null && instanceIds == null)
    {
      journeyList.addAll(Journey.loadAll(ctx, Type.campaign));
      journeyList.addAll(Journey.loadAll(ctx, Type.c1));
    }
    else
    {
      Type instanceType = Type.parse(type);
      journeyList.addAll(Journey.loadAll(ctx, instanceType));
    }
    journeyList.stream().forEach(journey -> {
      if (instanceIds == null || instanceIds.contains(journey.getId()))
      {
        // Upgrade action template reference from activities property
        journey.getDef().getStates().forEach(state -> {
          state.getActivities().stream().forEach(activity -> {
            activity.getActions().stream().forEach(action -> {
              ParamDef paramDef = action.getParams().stream()
                  .filter(param -> param.getName().equals("z1_template_ref")
                      && param.getValue().equals(oldModId))
                  .findFirst().orElse(null);
              if (paramDef != null)
              {
                upgradeZ1TemplateRef(ctx, paramDef, modId);
              }
            });
          });
        });
        
        // Upgrade action template reference from timers property
        journey.getDef().getStates().forEach(state -> {
          state.getTimers().stream().forEach(timerDef -> {
            timerDef.getActions().stream().forEach(action -> {
              ParamDef paramDef = action.getParams().stream()
                  .filter(param -> param.getName().equals("z1_template_ref")
                      && param.getValue().equals(oldModId))
                  .findFirst().orElse(null);
              if (paramDef != null)
              {
                upgradeZ1TemplateRef(ctx, paramDef, modId);
              }
            });
          });
        });

        ActionUtils.invalidateScriptCache(ctx, journey.getId());
        journey.save();
      }
    });
  }
  
  /**
   * Upgrades actionTemplate reference
   *
   * @param ctx
   * @param modId
   * @param paramDef
   */
  @SuppressWarnings("unchecked")
  private void upgradeZ1TemplateRef(UContext ctx, ParamDef paramDef, String  modId)
  {
    paramDef.setValue(modId);
    // Check if an "action" CustomConfig of the same ID exists in
    // local mongo
    CustomConfig.Type cctype = CustomConfig.Type.action;
    CustomConfig cc = CustomConfig.load(ctx, modId, cctype);
    if (cc == null)
    {
      String payloads = TemplateUtils.getArtifacts(ctx, modId, "",
          false, null);

      if (payloads != null && !payloads.isEmpty())
      {
        Map<String, Object> plMap = new JsonMarshaller()
            .readAsMap(payloads);
        List<Map<String, Object>> configs = (List<Map<String, Object>>) plMap
            .get("config");
        if (configs != null && !configs.isEmpty())
        {
          String templatePL = null;
          for (Map<String, Object> config : configs)
          {
            if (!((String) config.get("subtype"))
                .equals(TemplateConst.NSApp.actiontemplate.name())
                && !modId.startsWith(config.get("id") + ":"))
              continue;
            String name = (config.containsKey("name"))
                ? (String) config.get("name")
                : null;
            templatePL = new JsonMarshaller().serialize(config);
            cc = CustomConfig.create(ctx, cctype, modId);
            cc.setPayload(templatePL);
            if (name != null) cc.setName(name);
            cc.save();
            break;
          }
        }
      }
      App.notifyClearCache(ctx.getNamespace(), cctype.name());
    }
  }

  /**
   * Marked any TI, Trigger, or Segment module whose event mapping has not been
   * carried out by SD for the namespace.
   * 
   * @param uctx
   * @param modules
   * @param moduleType
   * @return
   */
  @SuppressWarnings("unchecked")
  private List<Map<String,Object>> _checkModuleMappingReady(UContext uctx,
      List<Map<String, Object>> modules, ModuleType moduleType)
  {
    List<Map<String,Object>> mappingInfos = new ArrayList<>();
    for (Map<String,Object> module : modules)
    {
      Map<String,Object> mappingInfo = new HashMap<>(2);
      String id = (String) module.get("id");
      mappingInfo.put("id", id);
      if (moduleType.equals(ModuleType.template))
      {
        String appId = (String) module.get("appId");
        List<String> classList = (List<String>) module.get("class");
        if (appId.startsWith("udc.system.core.NamespaceExperience")
            || !classList.contains("campaign"))
        {
          mappingInfo.put("isEventMapReady", Boolean.FALSE);
          mappingInfos.add(mappingInfo);
          continue;
        }
      }
      List<ChannelType> mappedSources = EventMappingInfo.getEventSourcesForModuleInstance(uctx, id);
      if (mappedSources == null || mappedSources.isEmpty())
      {
        mappingInfo.put("isEventMapReady", Boolean.FALSE);
      }
      else
      {
        mappingInfo.put("isEventMapReady", Boolean.TRUE);
      }
      mappingInfos.add(mappingInfo);
    }
    return mappingInfos;
  }

  /**
   * Upgrades experience module
   * 
   * @param ctx
   * @param req
   * @param instanceType
   * @param instanceId
   * @param moduleId
   * @param appId
   * @param arts
   * @param module
   */
  private void _upgrade(UContext ctx, HttpServletRequest req,
      String instanceType, String instanceId, String moduleId, String appId,
      Map<String, Object> arts, Map<String, Object> module)
  {

    //// {
    // "subtype": "signal",
    // "isPrimary": "false",
    // "configId": "signal+c18a38df-a366-44ad-8b72-d3616cb4efa7",
    // "deletable": "true",
    // "artifactId": "udc.system.core.ExperienceSampleModule1:Trigger_One:2.0",
    // "params": [
    // {
    // "name": "z1_fireonce",
    // "type": "z1_fireonce",
    // "display": "Fire trigger only once per session",
    // "description": "Toggle to enable or disable fire once",
    // "value": "false",
    // "control": "{\"control\": \"system:boolean\",\"controlOption\":
    // \"{\\\"configtype\\\":\\\"params\\\"}\",\"isRequired\": \"false\"}"
    // },
    // {
    // "name": "event",
    // "type": "",
    // "display": "Watch for specific activity",
    // "description": "Select one or more activities that activate the trigger
    // evaluation",
    // "value": "z1_viewed_product",
    // "control": "{\"control\": \"system:event\",\"controlOption\":
    // {\"readonly\": \"true\"},\"isRequired\": \"true\"}"
    // },
    // {
    // "name": "name",
    // "description": "A sample experience module for a few TIs and its
    // supportive artifacts.",
    // "value": "TI_One_TESTING 11418"
    // }
    // ]
    // }

    // Set the flag to skip all the inline notify clear cache
    // Call clear cache at the end of the processing
    ctx.put(Const.SKIP_INLINE_NOTIFY_CLEAR_CACHE,
            Boolean.TRUE.toString());
    Set<String> subtypeList = new HashSet<>();

    String instanceName = null;
    String instanceDesc = null;
    Map<String, Object> primaryPl2 = null;
    Map<String, Object> primaryPlLocal = null;
    JsonMarshaller jm = new JsonMarshaller();
    boolean isNSApp = NSApp.getAppIds().contains(appId);
    List<Map<String, Object>> params = new ArrayList<>();
    List<ParamsType> paramsTypes = null;
    switch (instanceType)
    {
      case "campaign":
      case "c1":
      {
        Journey journey = Journey.loadDef(ctx, instanceId,
            Type.parse(instanceType));

        instanceName = journey.getName();
        instanceDesc = journey.getDescription();
        primaryPl2 = (Map<String, Object>) journey.getPayload2();
        primaryPlLocal = (Map<String, Object>) jm
            .readAsMap((String) journey.getDefItem().getValues()
                .get(DefinitionItem.Fields.payload.name()));
        if (isNSApp)
        {
          for (ParamDef pd : journey.getDef().getParams())
          {
            Map<String, Object> param = new HashMap<>();
            param.put("name", pd.getName());
            param.put("value", pd.getValue());
            params.add(param);
          }
        }
        break;
      }
      case "segment":
      {
        instanceId = "segment+" + instanceId;
        Segment segment = Segment.load(ctx, instanceId, true);
        instanceName = segment.getDef().getName();
        instanceDesc = segment.getDef().getDescription();
        primaryPl2 = jm.readAsMap(segment.getPayload2());
        primaryPlLocal = segment.getDefAsMap();
        if (isNSApp)
        {
          paramsTypes = segment.getParams();
        }

        break;
      }
      case "signal":
      {
        SignalPart signalPart = SignalPart.load(ctx, instanceId, true);
        instanceName = signalPart.getDef().getName();
        instanceDesc = signalPart.getDef().getDescription();
        primaryPl2 = jm.readAsMap(signalPart.getPayload2());
        primaryPlLocal = signalPart.getDefAsMap();
        if (isNSApp)
        {
          paramsTypes = signalPart.getDef().getParams();
        }
        break;
      }
    }
    Map<String, Object> primaryPl = primaryPlLocal;
    List<String> eventSource = Arrays.asList("any");
    Object dayRange = null;

    if (primaryPl2.containsKey("eventSource"))
    {
      eventSource = (List<String>) primaryPl2.get("eventSource");
    }
    if (primaryPl2.containsKey("dayRange"))
    {
      dayRange = primaryPl2.get("dayRange");
    }
    List<Map<String, Object>> existingArtifacts = (List<Map<String, Object>>) primaryPl2
        .get("artifacts");
    List<Map<String, Object>> existingGlobalParamsList = (List<Map<String, Object>>) primaryPl2
        .get("globalParams");

    if (arts == null || arts.isEmpty())
    {

      return;
    }

    // Map<String, Object> arts = jm.readAsMap(defs);
    Map<String, Object> artPayloads = new HashMap<>();
    List<String> channels = new ArrayList<>();
    List<String> types = new ArrayList<>(arts.keySet());
    String primaryId = moduleId.substring(0, moduleId.lastIndexOf(":"));
    // Get primary artifact payload from the request
    types.forEach(type -> {
      List<Map<String, Object>> typePLs = (List<Map<String, Object>>) arts
          .get(type);
      typePLs.forEach(art -> {
        String id = (String) art.get("id");
        String subtype = (String) art.get("subtype");
        String payload = (String) art.get("payload");

        artPayloads.put(subtype + "+" + id, payload);
      });
    });
    primaryPl = jm
        .readAsMap((String) artPayloads.get(instanceType + "+" + primaryId));

    List<Map<String, Object>> paramOld = primaryPlLocal.containsKey("params")
        ? (List<Map<String, Object>>) primaryPlLocal.get("params")
        : null;

    List<Map<String, Object>> paramNew = primaryPl.containsKey("params")
        ? (List<Map<String, Object>>) primaryPl.get("params")
        : null;

    // Update params with oldparams
    _setParamsValue(paramOld, paramNew);

    // Get the module info payload from request
    // Map<String, Object> reqModPayload = (Map<String, Object>) module
    // .get("payload");
    // Get the artifact list from module info in request
    List<Map<String, Object>> modArtList = (List<Map<String, Object>>) module
        .get("artifacts");
    String moduleName = (String) module.get("name"); // display name given by
                                                     // BAE for primary artifact

    List<Map<String, Object>> respArts = new ArrayList<>();
    ModuleVisitor createVisitor = new ModuleVisitor(ctx, null,
        ModuleVisitor.VisitingOperation.upgrade);

    TemplateArtifact primaryTA = null;
    Set<TemplateArtifact> supportTAs = new HashSet<>();

    // idSubs map holds Id for substitution for "irregular" config types
    Map<String, Map<String, String>> idSubs = new HashMap<>();

    boolean isC1 = false;
    // Loop through and create appropriate config instances
    for (Map<String, Object> art : modArtList)
    {
      String version = (String) art.get("version");
      String artId = (String) art.get("id");
      String[] artIdParts = artId.split(":");
      String shortId = artIdParts[1];
      String subtype = (String) art.get("subtype");
      subtypeList.add(subtype);
      Map<String, Object> property = (Map<String, Object>) art.get("property");
      boolean isPrimary = false;
      if (property != null && !property.isEmpty())
      {
        String isPrimaryStr = (String) property.get("isPrimary");
        if (isPrimaryStr != null)
          isPrimary = Boolean.parseBoolean(isPrimaryStr);
      }
      // get old config and merge params for artifact

      List<Map<String, Object>> oldParams = null;
      if (!isPrimary)
      {
        Map<String, Object> oldArtifactConfig = _getExistingArtifact(
            existingArtifacts, art);

        if (oldArtifactConfig != null)
        {
          oldParams = (List<Map<String, Object>>) oldArtifactConfig
              .get("params");
        }
      }
      // in namespace module params values are part of payload
      if (!isNSApp)
      {
        params = (List<Map<String, Object>>) art.get("params");
      }
      else
      {
        params = paramNew;
      }
      if (oldParams != null && !oldParams.isEmpty())
      {
        _setParamsValue(oldParams, params);
      }

      art.put("params", params);

      paramsTypes = TemplateUtils.convertCatalogParamToParmsType(params);
      

      String payload = (String) artPayloads.get(subtype + "+" + artId);

      Map<String, Object> payload2Map = (Map<String, Object>) art
          .get("payload2");
      if (payload2Map != null && !payload2Map.isEmpty())
      {
        String payload2 = jm.serialize(payload2Map);
        art.put("payload2", payload2);
      }

      String artifactId = artId + ":" + version;

      art.put("instanceName", instanceName);
      art.put("moduleName", moduleName);
      art.put("moduleVersion", version);
      if (module.get("publishingScope") != null)
        art.put("publishingScope", module.get("publishingScope"));
      else
        art.put("publishingScope", "external");

      if (eventSource != null && !eventSource.isEmpty())
        art.put("eventSource", eventSource);

      if (isPrimary)
      {
        if (instanceDesc != null) art.put("instanceDesc", instanceDesc);
        if (module.get("appTag") != null)
        {
          Map<String, Object> appTag = (Map<String, Object>) module
              .get("appTag");
          art.put("appTag", appTag);
        }
        if (dayRange != null) art.put("dayRange", dayRange);

        Map<String, Object> plm = null;
        if (subtype.equalsIgnoreCase("c1"))
        {
          isC1 = true;
        }

        // Set name and description for primary config
        if (primaryPl.containsKey("name")) primaryPl.put("name", instanceName);
        if (primaryPl.containsKey("description"))
          primaryPl.put("description", instanceDesc);
        payload = jm.serialize(primaryPl);
      }
      else
      {
        Map<String, Object> pl2 = new HashMap<>();
        pl2.put("artifactId", artifactId);
        pl2.put("params", paramsTypes);
        String desc = (String) art.get("description");
        if (desc != null) art.put("instanceDesc", desc);
      }

      // Create primary TemplateArtifact now but don't instantiate it
      // until after all supportive art configs had been created
      // update global params
      List<Map<String, Object>> gParamList = (List<Map<String, Object>>) module
          .get("params");
      if (existingGlobalParamsList != null
          && !existingGlobalParamsList.isEmpty())
      {
        _setParamsValue(existingGlobalParamsList, gParamList);
      }
      module.put("params", gParamList);
      List<ParamsType> globalParamsTypes = TemplateUtils
          .convertCatalogParamToParmsType(gParamList);

      if (isPrimary)
      {
        primaryTA = new TemplateArtifact(ctx, appId, art, paramsTypes,
            globalParamsTypes, subtype, payload, false, version);
        if (AnchoredContent.isNameAsIdConfig(subtype))
        {
          Map<String, String> ids = Optional.ofNullable(idSubs.get(subtype))
              .orElse(new HashMap<>());
          ids.put(shortId, primaryTA.getInstanceConfigName());
          idSubs.put(subtype, ids);
        }
        continue;
      }
      // if NSApp then no need to create other than primary artifact
      // Signal reference is already namespace instance
      if (!isNSApp)
      {
        // Save supportive artifacts to be instantiated later to make sure
        // we got the primary artifact version which is the module version
        TemplateArtifact ta = new TemplateArtifact(ctx, appId, art, paramsTypes,
            globalParamsTypes, subtype, payload, false, version);
        if (AnchoredContent.isNameAsIdConfig(subtype))
        {
          Map<String, String> ids = Optional.ofNullable(idSubs.get(subtype))
              .orElse(new HashMap<>());
          ids.put(shortId, ta.getInstanceConfigName());
          idSubs.put(subtype, ids);
        }
        supportTAs.add(ta);
      }
    }

    // ----------------------------------------------------------
    // Instantiate supportive artifact configs first
    if (!isNSApp && !supportTAs.isEmpty())
    {
      for (TemplateArtifact ta : supportTAs)
      {
        ta.accept(createVisitor);
        String subtype = ta.getSubtype();

        Map<String, Object> supportArtPl = ta.getPayload();
        String supportArtConfigId = (supportArtPl != null
            && supportArtPl.containsKey("configId"))
                ? (String) supportArtPl.get("configId")
                : null;

        if (subtype.equals("channel") && supportArtConfigId != null)
        {
          channels.add((String) supportArtConfigId);
        }
      }
    }

    // ----------------------------------------------------------
    // Now create primary artifact config and its payload2
    primaryTA.accept(createVisitor);

    // fetch supportive artifact configs info
    Map<String, Object> createVisitorPL = createVisitor.getPayload();
    List<Map<String, Object>> visitedArtsInfo = null;
    if (createVisitorPL != null && !createVisitorPL.isEmpty())
    {
      visitedArtsInfo = (List<Map<String, Object>>) createVisitorPL
          .get("artifacts");
      primaryTA.setVisitedArts(visitedArtsInfo);
    }
    // handle post create for primary art
    ModuleVisitor postCreateVisitor = new ModuleVisitor(ctx, null,
        ModuleVisitor.VisitingOperation.postUpgrade);
    primaryTA.setConfigIdSubs(idSubs);
    primaryTA.accept(postCreateVisitor);

    respArts = (List<Map<String, Object>>) primaryTA.getPayload2()
        .get("artifacts");
    Map<String, Object> visitedPrimary = createVisitor.getVisitPayload();
    Map<String, Object> primaryArt = new HashMap<>();
    String primaryArtSubtype = (String) visitedPrimary.get("subtype");
    String primaryArtConfigId = (String) visitedPrimary.get("configId");
    String primaryArtName = (String) visitedPrimary.get("name");
    Map<String, String> primaryRef = new HashMap<>();
    primaryRef.put("configId", primaryArtSubtype + "+" + primaryArtConfigId);
    primaryRef.put("name", primaryArtName);
    // create domain url
    String scheme = z1.core.utils.Utils.getReqScheme(req);
    int port = req.getServerPort();
    if (scheme.equalsIgnoreCase("https")) port = -1;
    String domainUrl = scheme + "://" + req.getServerName();
    if (port != -1)
    {
      domainUrl += ":" + port;
    }
    List<ParamsType> sysParams = new ArrayList<>(10);
    ParamsType paramsType = new ParamsType();
    paramsType.setName("z1_domainUrl");
    paramsType.setValue(domainUrl);
    sysParams.add(paramsType);

    if (primaryArtSubtype.equals("campaign") || primaryArtSubtype.equals("c1"))
    {
      paramsType = new ParamsType();
      paramsType.setName("z1_instanceName");
      paramsType.setValue(primaryArtName);
      sysParams.add(paramsType);
      paramsType = new ParamsType();
      paramsType.setName("z1_experienceType");
      paramsType.setValue(primaryTA.getSubtype());
      sysParams.add(paramsType);
      paramsType = new ParamsType();
      paramsType.setName("z1_instanceId");
      paramsType.setValue(primaryArtConfigId);
      sysParams.add(paramsType);
    }
    // handle post create for supportive arts
    if (!supportTAs.isEmpty())
    {
      for (TemplateArtifact ta : supportTAs)
      {
        // update param z1_instanceName,z1_experienceType,
        // z1_instanceId only for deletable supportive config
        // primary config is campaign or c1
        if ((primaryArtSubtype.equals("campaign")
            || primaryArtSubtype.equals("c1")) && ta.isDeletable())
        {
          ta.setParams(sysParams);
        }
        ta.setPrimaryArtRef(primaryRef);
        // special art config ID/name to find [[anchor:...]] for
        // substitution
        ta.setConfigIdSubs(idSubs);
        // set all module's artifacts to find/replace trigger and
        // segment references can be substituted.
        ta.setVisitedArts(visitedArtsInfo); // set all module's
                                            // artifacts to find/replace
                                            // trigger and segment
                                            // references can be
        ModuleVisitor taPostCreateVisitor = new ModuleVisitor(ctx, null,
            ModuleVisitor.VisitingOperation.postUpgrade);
        ta.accept(taPostCreateVisitor);
      }
    }

    // Switch the channel if any to published mode
    channels.forEach(channelId -> {
      ChannelDefWrapper cdr = ChannelDefWrapper.load(ctx, channelId);
      cdr.setState(State.published);
      cdr.save();
    });

    subtypeList.add(CustomConfig.Type.action.name());
    subtypeList.forEach(subtype -> {
      App.notifyClearCache(ctx.getNamespace(), subtype);
    });

    if (isC1)
    {
      JourneyHandler.runCampaignOnce(ctx, primaryArtConfigId,
          Journey.Type.c1.name());
    }

    // Prepare response for request
    primaryArt.put("configId", primaryArtConfigId);
    primaryArt.put("artifactId", visitedPrimary.get("artifactId"));
    primaryArt.put("subtype", primaryArtSubtype);
    primaryArt.put("isPrimary", visitedPrimary.get("isPrimary"));
    primaryArt.put("deletable", visitedPrimary.get("deletable"));
    respArts.add(primaryArt);

  }

  private void _setParamsValue(List<Map<String, Object>> oldParams,
      List<Map<String, Object>> params)
  {
    if (oldParams != null && !oldParams.isEmpty() && params != null
        && !params.isEmpty())
    {
      oldParams.forEach(oldParam -> {
        String paramName = (String) oldParam.get("name");
        // if param is script param then no need to set old param value
        // upgraded instace will have updated value for script params
        if (!Journey.ScriptType.isScriptParam(paramName))
        {
          Map<String, Object> paramTypeNew = params.stream()
              .filter(p -> p.get("name").equals(paramName)).findFirst()
              .orElse(null);
          if (paramTypeNew != null)
          {
            paramTypeNew.put("value", oldParam.get("value"));
          }
          else if (paramName.equals("z1_target") || paramName.equals("z1_isAB")
              || paramName.equals("z1_execsilent"))
          {
            Map<String, Object> newParam = new HashMap<>(2);
            newParam.put("name", paramName);
            newParam.put("value", oldParam.get("value"));
            params.add(newParam);
          }

        }
      });
    }
  }

  private Map<String, Object> _getExistingArtifact(
      List<Map<String, Object>> existingArtifacts, Map<String, Object> art)
  {

    String id = (String) art.get("id");
    for (int i = 0; i < existingArtifacts.size(); i++)
    {
      String oldConfigId = existingArtifacts.get(i).get("artifactId")
          .toString();
      if (oldConfigId.substring(0, oldConfigId.lastIndexOf(":")).equals(id))
      {
        return existingArtifacts.get(i);
      }
    }
    return null;
  }
  
  /**
   * Returns ResponseMessage with status fail and the reason if module is already
   * installed with same or higher version number. Returns null if version check
   * passed.
   * 
   * @param ctx
   * @param pkgId
   * @param appVer
   * @return
   */
  private ResponseMessage _checkVersion(UContext ctx, String pkgId, String appVer)
  {
    // Only allow to publish a newer version module
    String latestVer = "";
    VersionComparator vc = new VersionComparator();
    AppServiceFacade asf = new AppServiceFacade(ctx);
    List<AppRegItem> aris = asf.getSubscribedApps();

    if (aris != null && (!aris.isEmpty()))
    {
      for (AppRegItem ari : aris)
      {
        Map<String, Object> vals = ari.getValues();
        String appId = (String) vals.get("id");
        String[] appParts = appId.split(":");

        // Skip if different pkgId OR non-bx module since they don't have
        // version
        if (!pkgId.equals(appParts[0]) || appParts.length < 2) continue;

        String version = appParts[1];
        int versioncomp = vc.compare(version, appVer);
        if (versioncomp == 0)
        {
          return new ResponseMessage(ctx, ResponseMessage.Status.fail,
              ResponseMessage.Type.moduleAppPublishingFailed, "App id '" + pkgId
                  + "' version '" + version + "' already exists.");

        }
        else if (versioncomp < 0)
        {
          latestVer = version;
        }
      }
    }

    if (latestVer != null && (!latestVer.isEmpty()))
    {
      return new ResponseMessage(ctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.moduleAppPublishingFailed,
          "Module version must be newer than " + latestVer);

    }

    // return null if no conflict detected
    return null;
  }

  /**
   * Returns list of artifact ids installed for the package.
   * 
   * @param appVer
   * @param pid
   * @param mrf
   * @return
   */
  private List<String> _installModule(String appVer, String pid,
      ModuleReaderFacade mrf)
  {
    // Register and Subcribe
    mrf.registerModuleApp(true);
    mrf.subscribeModuleApp(true);

    List<Artifact> arts = mrf.getArtifacts(pid + ":" + appVer);

    // return list of ids of artifact installed.
    return arts.stream().map(Artifact::getId).collect(Collectors.toList());
  }
}

