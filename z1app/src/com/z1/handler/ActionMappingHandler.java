package com.z1.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.SystemConfig;
import z1.commons.Utils;

/**
 * This class handles action mapping requests for tags.
 *
 */
public class ActionMappingHandler implements CommandHandlerFactory
{
  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete,
    enable,
    disable,
    publish
  }

  // Supported get commands
  private enum GetCommand
  {
    all,
    id,
    state
  }

  // Supported action mapping types
  private List<String> types = Arrays.asList("actionMappingHtml5Desktop",
      "actionMappingHtml5Tablet", "actionMappingHtml5Phone",
      "actionMappingAndroidTablet", "actionMappingAndroidPhone",
      "actionMappingIosTablet", "actionMappingIosPhone");

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/actionMapping/all?type=actionMappingAndroidPhone => Returns
          // all action mapping requests for tags of a certain type
          case all:
          {
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;

            // Load old config for backwards compatibility
            List<CustomConfig> ccList = CustomConfig
                .forceLoadAll(uctx, CustomConfig.Type.valueOf(type)).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
            if (ccList == null)
            {
              resp.getWriter().print("[]");
              return;
            }
            Map<String, Object> output = new HashMap<String, Object>();
            List<Map<String, Object>> actionMap = new ArrayList<>();
            JsonMarshaller jm = new JsonMarshaller();

            for (CustomConfig cc : ccList)
            {
              String payload2 = cc.getPayload2();
              if (payload2 != null && !payload2.isEmpty())
              {
                Map<String, Object> plMap = jm.readAsMap(payload2);
                plMap.put("state", cc.getState());
                plMap.put("lastUpdatedBy", cc.getLastUpdatedBy());
                plMap.put("lastUpdatedTime", cc.getLastUpdatedTime());
                actionMap.add(plMap);
              }
            }
            output.put("actionMap", actionMap);

            if (!type.contains("actionMappingHtml5Desktop")
                && !type.contains("actionMappingHtml5Tablet")
                && !type.contains("actionMappingHtml5Phone"))
            {

              String ttlKey = null;
              if (type.equals("actionMappingAndroidTablet"))
                ttlKey = "android.tablet.ttl.value";
              else if (type.equals("actionMappingAndroidPhone"))
                ttlKey = "android.phone.ttl.value";
              else if (type.equals("actionMappingIosTablet"))
                ttlKey = "ios.tablet.ttl.value";
              else if (type.equals("actionMappingIosPhone"))
                ttlKey = "ios.phone.ttl.value";
              if (ttlKey != null)
              {
                output.put("ttl",
                    SystemConfig.getStringValue(uctx, ttlKey, ""));
              }

            }

            String ttsKey = null;
            if (type.equals("actionMappingHtml5Desktop"))
              ttsKey = "html5.desktop.tts.value";
            else if (type.equals("actionMappingHtml5Tablet"))
              ttsKey = "html5.tablet.tts.value";
            else if (type.equals("actionMappingHtml5Phone"))
              ttsKey = "html5.phone.tts.value";
            else if (type.equals("actionMappingAndroidTablet"))
              ttsKey = "android.tablet.tts.value";
            else if (type.equals("actionMappingAndroidPhone"))
              ttsKey = "android.phone.tts.value";
            else if (type.equals("actionMappingIosTablet"))
              ttsKey = "ios.tablet.tts.value";
            else if (type.equals("actionMappingIosPhone"))
              ttsKey = "ios.phone.tts.value";

            if (ttsKey != null)
            {
              @SuppressWarnings("unchecked")
              Map<String, Object> map = (Map<String, Object>) SystemConfig
                  .getValue(uctx, ttsKey);
              if (map != null && map.containsKey("tags"))
              {
                output.put("tts", map.get("tags"));
              }
            }

            resp.getWriter().print(jm.serialize(output));
            return;
          }
          // c3/data/actionMapping/id/<id>?type=actionMappingAndroidPhone =>
          // Returns the action mapping that matches the given id
          case id:
          {
            if (pathParts.length != 2) return;
            String id = pathParts[1].split("\\?")[0];
            if (id == null || id.isEmpty()) return;
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;

            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.valueOf(type), true);
            if (cc == null)
            {
              resp.getWriter().print("[]");
              return;
            }
            String payload2 = cc.getPayload2();
            if (payload2 == null || payload2.isEmpty())
            {
              payload2 = "[]";
            }
            resp.getWriter().print(payload2);
            return;
          }
          // c3/data/actionMapping/state/<id>?type=actionMappingAndroidPhone =>
          // Returns the state ("published" or "draft") of the tag matching the
          // given id
          case state:
          {
            if (pathParts.length != 2) return;
            String id = pathParts[1].split("\\?")[0];
            if (id == null || id.isEmpty()) return;
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.valueOf(type), true);
            if (cc == null)
            {
              return;
            }
            Map<String, Object> m = new java.util.HashMap<>();
            m.put("state", cc.getState());
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/actionMapping/create?type=actionMappingAndroidPhone
          case create:
          {
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty()) return;

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(pl);
            String tag = (String) map.get("tagName");
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;
            CustomConfig cc = CustomConfig.load(uctx, tag,
                CustomConfig.Type.valueOf(type), true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, CustomConfig.Type.valueOf(type),
                  tag);
              cc.setPayload2(pl);
              cc.save(true);
              Map<String, Object> m = new java.util.HashMap<>();
              m.put("id", tag);
              m.put("lastUpdatedBy", cc.getLastUpdatedBy());
              m.put("lastUpdatedTime", cc.getLastUpdatedTime());
              resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            }
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(type), tag, tag,
                Operations.create).save();
            break;
          }
          // c3/data/actionMapping/update?id=<id>&type=actionMappingAndroidPhone
          case update:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty()) return;
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.valueOf(type), true);
            if (cc == null) return;
            cc.setPayload2(pl);
            String state = cc.getState();
            if (state.equals(DefinitionItem.State.draft.name()))
            {
              cc.save();
            }
            else if (!state.equals(DefinitionItem.State.suspended.name()))
            {
              cc.save(true);
            }
            Map<String, Object> m = new java.util.HashMap<>();
            m.put("id", id);
            m.put("lastUpdatedBy", cc.getLastUpdatedBy());
            m.put("lastUpdatedTime", cc.getLastUpdatedTime());
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(type), id, id,
                Operations.edit).save();
            break;
          }
          // c3/data/actionMapping/delete?id=<id>&type=actionMappingAndroidPhone
          case delete:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;

            CustomConfig.Type cctype = CustomConfig.Type.valueOf(type);

            CustomConfig cc = CustomConfig.load(uctx, id, cctype, true);
            if (cc != null)
            {
              
              cc.updateState(DefinitionItem.State.suspended);
              
              Map<String, Object> m = new java.util.HashMap<>();
              m.put("id", id);
              resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            }
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(type), id, id,
                Operations.delete).save();
            break;
          }
          // c3/data/actionMapping/enable?id=<id>&type=actionMappingAndroidPhone
          case enable:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.valueOf(type), true);
            if (cc != null)
            {
              cc.save(true);
              Map<String, Object> m = new java.util.HashMap<>();
              m.put("id", id);
              m.put("lastUpdatedBy", cc.getLastUpdatedBy());
              m.put("lastUpdatedTime", cc.getLastUpdatedTime());
              resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            }
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(type), id, id,
                Operations.enable).save();
            break;
          }
          // c3/data/actionMapping/disable?id=<id>&type=actionMappingAndroidPhone
          case disable:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty()) return;
            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.valueOf(type), true);
            if (cc != null)
            {
              cc.save(State.draft);
              cc.setState(DefinitionItem.State.draft.name());
              cc.save();
              Map<String, Object> m = new java.util.HashMap<>();
              m.put("id", id);
              m.put("lastUpdatedBy", cc.getLastUpdatedBy());
              m.put("lastUpdatedTime", cc.getLastUpdatedTime());
              resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            }
            ArtifactAudit.newInstance(uctx, ItemTypes.parse(type), id, id,
                Operations.disable).save();
            break;
          }
          // c3/data/actionMapping/publish?type=<actionMappingAndroidPhone>&devicetype=<type>&os=<os>
          case publish:
          {
            // Move all the updated/created item from payload2 to payload.

            String type = req.getParameter("type");
            if (type == null || type.isEmpty() || !types.contains(type)) return;
            CustomConfig.Type cctype = CustomConfig.Type.valueOf(type);

            List<CustomConfig> ccList = CustomConfig
                .forceLoadAll(uctx, cctype).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
            
            if (ccList == null)
            {
              resp.getWriter().print("[]");
              return;
            }
            JsonMarshaller jm = new JsonMarshaller();

            for (CustomConfig cc : ccList)
            {
              // Delete tag in suspended state.
              if (cc.getState().equalsIgnoreCase(DefinitionItem.State.suspended.name()))
              {
                CustomConfig.delete(uctx, cc.getId(), cctype);
                continue;
              }
              
              if (cc.getPayload2() != null && !cc.getPayload2().isEmpty()
                  && !cc.getPayload2().equalsIgnoreCase("{}")
                  && !cc.getPayload2().equalsIgnoreCase(cc.getPayload()))
              {
                cc.setPayload(cc.getPayload2());
                if (cc.getState().equalsIgnoreCase(DefinitionItem.State.draft.name()))
                {
                  cc.save();
                }
                else
                {
                  cc.save(true);
                }
              }
            }

            // Get the deviceType and os from the QS param
            String deviceType = req.getParameter("devicetype");
            String os = req.getParameter("os");

            SystemConfig.updateTTS(uctx, os, deviceType, "tags");

            String key = os.toLowerCase() + "." + deviceType.toLowerCase()
                + ".tts.value";
            Map<String, Object> map = new HashMap<>();
            @SuppressWarnings("unchecked")
            Map<String, Object> ttsValue = (Map<String, Object>) SystemConfig
                .getValue(uctx, key);

            if (ttsValue != null && !ttsValue.isEmpty())
            {
              map.putAll(ttsValue);
            }

            Map<String, Object> res = new HashMap<>();
            res.put(key, map);
            resp.getWriter().print(jm.serialize(res));

            ArtifactAudit.newInstance(uctx, ItemTypes.parse(type),
                "All Updated Tags", "All Updated Tags", Operations.publish)
                .save();
            break;
          }
          default:
            break;
        }
      }
    };
  }

  private boolean _isTagUnPublished(CustomConfig cc)
  {

    if (cc.getPayload2() != null && !cc.getPayload2().isEmpty()
        && !cc.getPayload2().equalsIgnoreCase("{}")
        && !cc.getPayload2().equalsIgnoreCase(cc.getPayload()))
    {
      return true;
    }

    return false;

  }

}