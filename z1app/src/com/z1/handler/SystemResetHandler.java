package com.z1.handler;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.handler.api.Const;

import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.apiservice.PipelineExecutor;
import udichi.gateway.defservice.DefinitionItem;
import z1.actions.sysrst.RSTStatus;
import z1.actions.sysrst.SysResetAction.SysResetActionType;
import z1.commons.Utils;
import z1.core.utils.FileLoaderStats;
import z1.core.utils.SystemResetUtils;
import z1.template.TemplateConst;

public class SystemResetHandler implements CommandHandlerFactory
{
  private enum PostCommand
  {
    deleteUnused
  }

  private enum GetCommand
  {
    unused,
    deleteAllUnused,
    deleteUploadDownloadStats
  }

  public static final String RST_LAST_SUCCESS_DATETIME_FORMAT = "MMM dd, yyyy hh:mm:ss a zzz";

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String ns = req.getParameter("ns");
        String cStr = pathParts[0];
        SystemResetHandler.GetCommand command;
        command = SystemResetHandler.GetCommand.valueOf(cStr);
        switch (command)
        {
          // c3/data/systemreset/unused?type=<campaign|trigger|segment|action|actiontemplate>
          case unused:
          {
            String type = req.getParameter(z1.commons.Const.TYPE);
            TemplateConst.ModuleType moduleType = TemplateConst.ModuleType
                .getBySubtype(type);
            resp.getWriter().print(new JsonMarshaller()
                .serialize(SystemResetUtils.getUnUsedModules(ctx, moduleType)));
            break;
          }
          // c3/data/systemreset/deleteAllUnused?type=<campaign|trigger|segment|action|actiontemplate>
          case deleteAllUnused:
          {
            ResponseMessage responseMessage = new ResponseMessage(ctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation successful.");
            String type = req.getParameter(z1.commons.Const.TYPE);
            TemplateConst.ModuleType moduleType = TemplateConst.ModuleType
                .getBySubtype(type);
            List<String> failedIds = SystemResetUtils.deleteAllUnused(ctx,
                moduleType);
            if (!failedIds.isEmpty())
            {
              responseMessage = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Operation failed. Failed to delete : "
                      + String.join(", ", failedIds));
            }
            resp.getWriter().print(responseMessage);
            break;
          }
          // c3/data/systemreset/deleteUploadDownloadStats?beforeDays=7
          case deleteUploadDownloadStats:
          {
            long timeToDeleteBefore = Utils.uploadDownloadStatsTimetoliveDays;
            try
            {
              int before = Integer.parseInt(req.getParameter("beforeDays"));
              if (before > 0)
              {
                timeToDeleteBefore = before;
              }
            }
            catch (Exception e)
            {
              // NO OP
            }
            // convert days to miliseconds
            timeToDeleteBefore *= 24 * 60 * 60 * 1000;
            FileLoaderStats.deleteOldStats(ctx, timeToDeleteBefore);
            ResponseMessage responseMessage = new ResponseMessage(ctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.resourseDeletedSuccessfully);
            resp.getWriter().print(responseMessage);
            break;
          }
          // c3/data/systemreset?ns=<ns>
          default:
          {
            Map<String, Object> map = new HashMap<String, Object>();

            DefinitionItem ditem = DefinitionItem.getInstance(ctx,
                ArtifactType.instancedata, "_rst_" + ns);
            if (ditem != null)
            {
              String json = (String) ditem.getValues()
                  .get(DefinitionItem.Fields.payload.name());
              map = new JsonMarshaller().readAsMap(json);
              formatLastSuccessDateTime(map);
            }
            else
            {
              map.put("rst_status", RSTStatus.unavailable.name());
            }

            String json = new JsonMarshaller().serializeMap(map);
            resp.getWriter().print(json);
          }
        }
      }
    };
  }

  static long getTime()
  {
    return System.currentTimeMillis();
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts != null && pathParts.length > 0)
        {
          try
          {
            String cStr = pathParts[0];
            PostCommand command = PostCommand.valueOf(cStr);
            switch (command)
            {
              // c3/data/systemreset/deleteUnused?type=<campaign|trigger|segment|action|actiontemplate>
              case deleteUnused:
              {
                String type = req.getParameter(z1.commons.Const.TYPE);
                TemplateConst.ModuleType moduleType = TemplateConst.ModuleType
                    .getBySubtype(type);
                ResponseMessage msg = new ResponseMessage(ctx,
                    ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "Operation successful.");
                String payload = ServletUtil.getPayload(req);
                if (payload.isEmpty())
                {
                  msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Missing request payload.");
                  resp.getWriter().print(msg.toString());
                  return;
                }
                List<String> listToDel = z1.commons.Const.jsonMarshaller
                    .readAsList(payload);
                if (listToDel.isEmpty())
                {
                  msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      String.format("No %s ids in payload.", type));
                  resp.getWriter().print(msg.toString());
                  return;
                }
                List<String> failedIds = SystemResetUtils
                    .deleteUnUsedModules(ctx, moduleType, listToDel);
                if (!failedIds.isEmpty())
                {
                  msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "Operation failed. Failed to delete : "
                          + String.join(", ", failedIds));
                }
                resp.getWriter().print(msg.toString());
                return;
              }
            }
          }
          catch (IllegalArgumentException ex)
          {
            ctx.getLogger(getClass()).info(
                "[SystemResetHandler] Unrecognized command. Falling back to default system reset flow.");
          }
        }

        // default case:
        // c3/data/systemreset?ns=<ns>
        String ns = req.getParameter("ns");
        ctx.getLogger(getClass()).info(
            "[SystemResetHandler] Received system reset request for namespace: "
                + ns);
        if (ns != null && !ns.isEmpty())
        {
          Map<String, Object> map = new HashMap<>();
          DefinitionItem ditem = null;
          try
          {
            ditem = DefinitionItem.getInstance(ctx, ArtifactType.instancedata,
                "_rst_" + ns);
            if (ditem == null)
            {
              ctx.getLogger(getClass()).info(
                  "[SystemResetHandler] No existing DefinitionItem found. Creating new one.");
              ditem = DefinitionItem.newInstance(ctx, "_rst_" + ns,
                  ArtifactType.instancedata);
            }
            else
            {
              ctx.getLogger(getClass()).info(
                  "[SystemResetHandler] Existing DefinitionItem found for: _rst_"
                      + ns);
              String json = (String) ditem.getValues()
                  .get(DefinitionItem.Fields.payload.name());
              map = new JsonMarshaller().readAsMap(json);

              String status = String.valueOf(map.get("rst_status"));
              ctx.getLogger(getClass())
                  .info("[SystemResetHandler] Current rst_status: " + status);

              if (RSTStatus.submitted.name().equals(status))
              {
                ctx.getLogger(getClass()).info(
                    "[SystemResetHandler] Reset already submitted. Returning response.");
                map.put("rst_status", RSTStatus.already_submitted.name());
                formatLastSuccessDateTime(map);
                resp.getWriter().write(new JsonMarshaller().serialize(map));
                return;
              }
            }
          }
          catch (Exception e)
          {
            ctx.getLogger(getClass()).error(
                "[SystemResetHandler] Error handling DefinitionItem for namespace: "
                    + ns,
                e);
            resp.getWriter().write("{\"status\": \"fail\"}");
            return;
          }

          try
          {
            String rstStart = String.valueOf(getTime());
            map.put("rst_start", rstStart);
            map.put("rst_status", RSTStatus.submitted.name());

            String json = new JsonMarshaller().serializeMap(map);
            ditem.getValues().put(DefinitionItem.Fields.payload.name(), json);
            ditem.save();
            ctx.getLogger(getClass()).info(
                "[SystemResetHandler] Payload saved successfully for namespace: "
                    + ns);
          }
          catch (Exception e)
          {
            ctx.getLogger(getClass()).error(
                "[SystemResetHandler] Error saving DefinitionItem for namespace: "
                    + ns,
                e);
            resp.getWriter().write("{\"status\": \"fail\"}");
            return;
          }

          try
          {
            Map<String, Object> item = new HashMap<>();
            item.put("ns", ns);
            item.put("rst_start", map.get("rst_start"));
            item.put("type", SysResetActionType.system_reset.name());

            PipelineExecutor pe = PipelineExecutor
                .createActionExecutor(Const.SYSRESET_ACTION_NAME, ctx);
            pe.setParams(item);
            pe.setAsyncExec(true);
            pe.run();

            ctx.getLogger(getClass()).info(
                "[SystemResetHandler] Async pipeline execution started for namespace: "
                    + ns);
          }
          catch (Exception e)
          {
            ctx.getLogger(getClass()).error(
                "[SystemResetHandler] Error starting pipeline executor for namespace: "
                    + ns,
                e);
            resp.getWriter().write("{\"status\": \"fail\"}");
            return;
          }

          try
          {
            formatLastSuccessDateTime(map);
            resp.getWriter().write(new JsonMarshaller().serialize(map));
            ctx.getLogger(getClass()).info(
                "[SystemResetHandler] Final response returned for namespace: "
                    + ns);
          }
          catch (Exception e)
          {
            ctx.getLogger(getClass()).error(
                "[SystemResetHandler] Error serializing final response for namespace: "
                    + ns,
                e);
            resp.getWriter().write("{\"status\": \"fail\"}");
          }
          return;
        }

        else
        {
          ctx.getLogger(getClass()).warning(
              "[SystemResetHandler] No namespace provided in request."); 
          resp.getWriter().write("{\"status\": \"fail\"}");
        }
      }
    };
  }

  // Takes the long value of rst_last_success from map and formats it to the desired output
  // and puts it in rst_last_success_datetime attribute
  static void formatLastSuccessDateTime(Map<String, Object> map)
  {
    String lastSuccessTime = (String) map.get("rst_last_success");
    if (lastSuccessTime != null)
    {
      map.put("rst_last_success_datetime",
          new SimpleDateFormat(RST_LAST_SUCCESS_DATETIME_FORMAT)
              .format(new Date(Long.parseLong(lastSuccessTime))));
    }
  }
}