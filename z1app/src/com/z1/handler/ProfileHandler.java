package com.z1.handler;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.Utils.ResponseMessage.Status;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.SystemConfig;
import z1.c3.api.Commons;
import z1.commons.Const;
import z1.commons.FileLoader;
import z1.commons.FileLoader.ObjType;
import z1.commons.FileLoader.OpType;
import z1.commons.Normalizer;
import z1.commons.Utils;
import z1.core.Context;
import z1.core.Profile;
import z1.core.Profile.Fields;
import z1.core.Type;
import z1.core.Type.IndexType;
import z1.core.profile.ProfileService;
import z1.core.utils.FileLoaderStats;

public class ProfileHandler implements CommandHandlerFactory
{
  private enum Command
  {
    id,
    detail,
    stats,
    locHistory
  }

  private enum PostCommand
  {
    upload,
    deleteOneStats,
    deleteStats,
    download
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");
        Command type = Command.valueOf(pathParts[0]);

        switch (type)
        {
          // c3/data/profile/id[?profileId=<>&customerId=<>&deviceId<>&email=<>&phone=<>&key=<>
          case id:
          {
            Profile p = null;
            Context ctx = Context.getInstance(uctx);
            ProfileService ps = new ProfileService(ctx);
            boolean customerIdOnlyKey = SystemConfig.getBooleanValue(uctx,
                "z1.customerIdOnlyKey", false);

            final String KEY_PREFIX = "key:";
            Map<String, String[]> map = req.getParameterMap();
            for (String s : map.keySet())
            {
              String val = req.getParameter(s);
              if (val == null) continue;
              if ("profileId".equalsIgnoreCase(s))
              {
                if (Profile.isExistProfile(ctx, val))
                {
                  p = Profile.instance(ctx, val, false);
                  // if profile has optedOut then ignore.
                  String isOptIn = p.getProperty(Fields.isOptIn.value);
                  if (isOptIn != null && isOptIn.equals("false"))
                  {
                    p = null;
                  }
                }
                ArtifactAudit.newInstance(uctx, ItemTypes.customerSearch,
                    "Profile Id = " + val, "Profile Id = " + val,
                    Operations.search).save();
              }
              else if ("customerId".equalsIgnoreCase(s))
              {
                p = ps.findAProfile(val, Type.IndexType.CUSTOMERID);
                ArtifactAudit.newInstance(uctx, ItemTypes.customerSearch,
                    "Customer Id = " + val, "Customer Id = " + val,
                    Operations.search).save();
              }
              else if ("deviceId".equalsIgnoreCase(s))
              {
                p = ps.findAProfile(val, Type.IndexType.DEVICE);
                ArtifactAudit.newInstance(uctx, ItemTypes.customerSearch,
                    "Device Id = " + val, "Device Id = " + val,
                    Operations.search).save();
              }
              else if ("email".equalsIgnoreCase(s) && !customerIdOnlyKey)
              {
                p = ps.findAProfile(val, Type.IndexType.EMAIL);
                ArtifactAudit
                    .newInstance(uctx, ItemTypes.customerSearch,
                        "Email = " + val, "Email = " + val, Operations.search)
                    .save();
              }
              else if ("phone".equalsIgnoreCase(s) && !customerIdOnlyKey)
              {
                String phone = Normalizer.normalizePhoneNumber(val);
                p = ps.findAProfile(phone, Type.IndexType.PHONE);
              }
              else if (s.startsWith(KEY_PREFIX))
              {
                final String defaultVal = "true";

                String ignoreSecondaryKey = SystemConfig
                    .getStringValue(uctx, "z1.ignoreSecondaryKey", defaultVal)
                    .trim();

                // Set default value, if value is null or empty.
                if (ignoreSecondaryKey == null || ignoreSecondaryKey.isEmpty())
                {
                  ignoreSecondaryKey = defaultVal;
                }

                // Identity resolution using secondary keys are only allowed
                // when z1.ignoreSecondaryKey = false
                if ("false".equalsIgnoreCase(ignoreSecondaryKey))
                {
                  String secondaryKey = s.substring(KEY_PREFIX.length());
                  String keyValue = req.getParameter(s);
                  if (secondaryKey != null && keyValue != null)
                  {
                    String key = secondaryKey + "|" + keyValue;
                    p = ps.findAProfile(key, IndexType.CUSTOMKEY);
                    ArtifactAudit
                        .newInstance(uctx, ItemTypes.customerSearch,
                            secondaryKey + " = " + keyValue,
                            secondaryKey + " = " + keyValue, Operations.search)
                        .save();
                  }
                }
              }

              if (p != null) break;
            }

            if (p == null)
            {
              ResponseMessage rm = new ResponseMessage(uctx, Status.fail,
                  ResponseMessage.Type.noProfileFound);
              resp.getWriter().print(rm.toString());
              return;
            }

            String profileId = p.getKeyValue();
            Map<String, String> z1ProfMap = new HashMap<>();
            z1ProfMap.put("profileId", profileId);
            z1ProfMap.put("status", Status.success.name());
            String profileIdString = new JsonMarshaller().serialize(z1ProfMap);
            resp.getWriter().print(profileIdString);
            return;
          }

          // c3/data/profile/detail?pid=<pid>
          case detail:
          {
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> paramsMap = new HashMap<>();

            String pid = req.getParameter("pid");
            Context ctx = Context.getInstance(uctx);
            Profile p = Profile.instance(ctx, pid, false);
            if (p == null)
            {
              return;
            }

            // Adding profile displayable fields
            for (Profile.Fields f : Profile.Fields.getDisplayables())
            {
              String v = p.getProperty(f.value);
              if (v != null)
              {
                paramsMap.put(f.value, v);
              }
            }

            // Adding device info
            Map<String, Object> z1DevMap;
            String z1Device = p.getProperty(Profile.Fields.device.value);
            z1DevMap = (z1Device != null) ? jm.readAsMap(z1Device)
                : java.util.Collections.emptyMap();

            if (!z1DevMap.isEmpty())
            {
              // put the device information to the map to be sent back
              for (Commons.DeviceInfo di : Commons.DeviceInfo.values())
              {
                if (!di.displayable) continue;
                Object v = z1DevMap.get(di.name());
                if (v != null) paramsMap.put(di.name(), v);
              }

              String dId = (String) z1DevMap
                  .get(Commons.DeviceInfo.device_id.name());
              if (dId == null) dId = "No device found";
              paramsMap.put(Profile.Fields.lastDeviceId.value, dId);
            }
            else
            {
              // If no device info data found, we'll check if we get the last
              // device ID stored in the profile
              // Last connected device ID
              String s = p.getProperty(Profile.Fields.lastDeviceId.value);
              if (s == null || s.startsWith("{"))
              {
                paramsMap.put(Profile.Fields.lastDeviceId.value,
                    "No device found");
              }
            }

            // Add device battery settings
            Map<String, Object> z1DevSettings;
            String sDevSettings = p
                .getProperty(Profile.Fields.deviceSettings.value);
            z1DevSettings = (sDevSettings != null) ? jm.readAsMap(sDevSettings)
                : java.util.Collections.emptyMap();
            if (!z1DevSettings.isEmpty())
            {
              paramsMap.putAll(z1DevSettings);
            }

            // Add other context attribute values
            List<CustomConfig> ccsList = CustomConfig.forceLoadAll(uctx,
                CustomConfig.Type.contextAttribute);
            for (CustomConfig ccs : ccsList)
            {
              if (ccs == null) continue;
              String payload = ccs.getPayload();
              if (payload != null)
              {
                Map<String, Object> attr = jm.readAsMap(payload);
                String useAs = (String) attr.get("useAs");
                if ("system:custom".equalsIgnoreCase(useAs))
                {
                  String name = (String) attr.get("name");

                  if (Profile.Fields.parse(name) != Profile.Fields.none)
                    continue;

                  String val = p.getProperty(name);
                  if (val != null)
                  {
                    paramsMap.put(name, val);
                  }
                }
              }
            }

            resp.getWriter().print(jm.serialize(paramsMap));

            return;
          }
          // c3/data/profile/stats[?opType=<opType>] => Get all stat records for
          // user upload
          // activity
          case stats:
          {
            OpType opType = null;
            if (req.getParameter("opType") != null)
            {
              opType = OpType.valueOf(req.getParameter("opType"));

            }

            List<Map<String, Object>> ret = FileLoaderStats.getStats(uctx,
                FileLoader.USER_OBJNAME, opType);

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;
          }
          // c3/data/profile/locHistory?profileId=<profile-id>
          case locHistory:
          {
            String profileId = req.getParameter("profileId");
            if ((profileId == null) || (profileId.length() == 0)) return;

            Context ctx = Context.getInstance(uctx);
            Profile p = Profile.instance(ctx, profileId, false);
            if (p == null) return;

            String locHistValue = p
                .getProperty(Profile.Fields.locHistory.value);
            if ((locHistValue == null) || (locHistValue.length() == 0)) return;

            JsonMarshaller jm = new JsonMarshaller();
            String[] locHistory = jm.readAsObject(locHistValue, String[].class);
            resp.getWriter().print(jm.serialize(locHistory));
            break;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/profile/upload[?secKeys=<secKeys>]
          case upload:
          {
            @SuppressWarnings("unchecked")
            List<FileItem> items = new ServletFileUpload(
                new DiskFileItemFactory()).parseRequest(req);
            String secKeys = null;

            if (req.getParameter("secKeys") != null)
            {
              secKeys = req.getParameter("secKeys");
            }

            byte[] fileContent = null;
            String fileName = null;

            for (FileItem item : items)
            {
              String fldName = item.getFieldName();
              if ("file".equals(fldName))
              {
                fileContent = item.get();
              }
              else if ("name".equals(fldName))
              {
                fileName = new String(item.get());
              }
            }

            if (!Utils.getFileExtension(fileName).equals("csv"))
            {
              resp.getWriter()
                  .print(new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidPayload,
                      "Only file type csv is valid."));

              break;
            }

            InputStream is = new ByteArrayInputStream(fileContent);
            Map<String, Object> otherInfo = new HashMap<String, Object>();

            if (secKeys != null && !secKeys.isEmpty())
            {
              String[] keys = secKeys.split(",");

              if (keys.length > 0)
              {
                otherInfo.put(Const.SECONDARY_KEYS, Arrays.asList(keys));
              }
            }

            Thread uploadT = new Thread(new FileLoader(uctx, OpType.upload,
                ObjType.user, null, is, fileName, otherInfo));
            uploadT.setDaemon(true);
            uploadT.start();

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.processing,
                ResponseMessage.Type.uploadProcessing,
                "Processing upload. Monitor progress by selecting 'View Upload Status'");
            resp.getWriter().print(msg.toString());

            break;
          }
          // c3/data/profile/deleteOneStats?id=<id>
          case deleteOneStats: // delete one stats record, unique ID is given
          {
            String id = req.getParameter("id");

            // String id = pathParts[1];
            FileLoaderStats.deleteOneStats(uctx, id);
            return;
          }

          // c3/data/profile/deleteStats[?opType=<opType>]
          case deleteStats: // delete all stat records for user upload activity
          {
            OpType opType = null;

            if (req.getParameter("opType") != null)
            {
              opType = OpType.valueOf(req.getParameter("opType"));

            }

            FileLoaderStats.deleteStats(uctx, FileLoader.USER_OBJNAME, opType);

            return;
          }
          // c3/data/profile/download
          case download:
          {
            Thread downloadT = new Thread(new FileLoader(uctx, OpType.download,
                ObjType.user, null, null, null));
            downloadT.setDaemon(true);
            downloadT.start();

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.processing,
                ResponseMessage.Type.downloadProcessing,
                "Processing download. Monitor progress by selecting 'Download Status'");
            resp.getWriter().print(msg.toString());

            break;
          }
        }

      }
    };
  }

  private List<String> _getReserveAttrNamesfromUpload(final UContext uctx,
      String[] attrNames)
  {
    List<String> ret = new java.util.ArrayList<>(10);
    List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
        CustomConfig.Type.contextAttribute);
    JsonMarshaller jm = new JsonMarshaller();
    for (CustomConfig cc : ccList)
    {
      if (cc == null) continue;
      String pl = cc.getPayload();

      Map<String, Object> localMap = jm.readAsMap(pl);

      String useAs = localMap.get("useAs").toString();
      if (useAs.equals("system:computed") || useAs.equals("system:remote")
          || useAs.equals("system:learned"))
      {
        ret.add(localMap.get("name").toString());
      }
    }
    List<String> foundReservedAttrNames = new ArrayList<>();
    if (!ret.isEmpty())
    {
      for (int i = 0; i < attrNames.length; i++)
      {
        if (ret.contains(attrNames[i]))
        {
          foundReservedAttrNames.add(attrNames[i]);
        }
      }
    }
    return foundReservedAttrNames;
  }
}
