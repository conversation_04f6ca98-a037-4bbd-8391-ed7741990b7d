package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.analytics.commons.SessionDimensions;
import z1.analytics.explorerops.ExplorerSubsystem.ExplorerCubeAxis;
import z1.c3.CustomConfig.Type;
import z1.c3.mapping.PropertyMapping;
import z1.c3.mapping.PropertyMappingInfo;
import z1.c3.mapping.PropertyMappingInfo.Property;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;

@SuppressWarnings("unchecked")
public class PropertyMappingHandler implements CommandHandlerFactory
{
  
  public enum GetCommand
  {
    all,
    allMapped
  }

  public enum PostCommand
  {
    add,
    delete,
    update
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        JsonMarshaller jm = new JsonMarshaller();
        switch(command) {
          // c3/data/propertymapping/all
          case all:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            PropertyMappingInfo pm = PropertyMapping.getInstance().getNSMappingInfo(uctx);
            if (!pm.doesConfigExist())
            {
              resp.getWriter().print("{}");
            }
            else
            {
              String payload = pm.getPayload();
              if (payload != null && !payload.isEmpty())
              {
                List<Map<String, Object>> ccMapList = jm.readAsObject(payload, List.class);
                resp.getWriter().print(jm.serialize(ccMapList));
              }
              else
              {
                resp.getWriter().print("{}");
              }
            }

            break;
          }
          // c3/data/propertymapping/allMapped
          case allMapped:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            PropertyMappingInfo pm = PropertyMapping.getInstance()
                .getNSMappingInfo(uctx);
            Map<String, String> propMap = pm.getAllMappedNameFromAxis();
            LinkedHashMap<String, String> sortedPropMap = new LinkedHashMap<>();
            List<String> propKeys = new LinkedList<>(propMap.keySet());
            Collections.sort(propKeys, String.CASE_INSENSITIVE_ORDER);
            for (String key: propKeys) {
              sortedPropMap.put(key, propMap.get(key));
            }
            resp.getWriter().print(jm.serialize(sortedPropMap));
            break;

          }
          default:
            break;
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        JsonMarshaller jm = new JsonMarshaller();
        switch(command){

          // c3/data/propertymapping/add
          case add:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            ResponseMessage msg = null;
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Missing or ill formatted request payload. Expecting property mapping payload to add.");
              resp.getWriter().print(msg.toString());
              return;
            }

            List<Map<String,Object>> eList = jm.readAsObject(pl, List.class);
            if (eList == null || eList.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Request payload is missing or empty!");
              resp.getWriter().print(msg.toString());
              return;
            }

            
            PropertyMappingInfo pmi = PropertyMapping.getInstance().getNSMappingInfo(uctx);
            if (!pmi.doesConfigExist())
            {
              pmi = pmi.create();
            }
            
            List<Map<String, Object>> filteredEList = new LinkedList<>();
            for (Map<String, Object> eMap : eList){

              if (!eMap.containsKey("name") || !eMap.containsKey("id"))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                        ResponseMessage.Type.invalidParams,
                        "Invalid structured request payload! "
                                + "At least one mapping missing required 'name' or 'id' field.");
                resp.getWriter().print(msg.toString());
                return;
              }   
              String name = (String) eMap.get("name");
              String attr = (String) eMap.get("id");
              //Check if property name has already axisMapped in the config.
              //If not, then put it in the ready list and
              // assign the available axis out of the 10 axis later
              if ((pmi.getMappedAxisFromName(name) != null
                  && !pmi.getMappedAxisFromName(name).isEmpty())
                  || (pmi.getMappedAxisFromAttr(attr) != null
                      && !pmi.getMappedAxisFromAttr(attr).isEmpty()))
                continue; // property name or attr is already mapped
              filteredEList.add(eMap);
            }
            eList = filteredEList;
            
            List<String> availableMappedAxis = pmi.getAvailableMappedAxis();
            int availableCount = availableMappedAxis.size();
            
            if (availableCount < eList.size()) {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "There are not enough mapped axises or explorer cube axises!" 
                      +" Only "+availableCount+" axises are available right now");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            Map<String, Property> propertyMapping = pmi.getPropertyMapping();
            
            for (Map<String, Object> eMap : eList){
              // there's no more available axis for mapping
              if (availableMappedAxis.isEmpty()) 
                break;

              String name = (String) eMap.get("name");
              String attr = (String) eMap.get("id");
              String desc = eMap.get("description") != null ? (String) eMap.get("description") : "";
                          
              if (propertyMapping.containsKey(name))
              {
                Property prop = propertyMapping.get(name);
                if (prop.getId().equals(attr))
                {
                  prop.setPropMapped(availableMappedAxis.get(0));
                  // set prop extra configs : showAttr...
                  HashMap<String, Object> propConfig = prop.getProp() != null?
                      (HashMap<String, Object>)prop.getProp() : new HashMap<String, Object>(1);
                  propConfig.put("showAttr", attr.startsWith("z1_") ? false : true);
                  prop.setProp(propConfig);
                  availableMappedAxis.remove(0);
                  propertyMapping.put(name, prop);
                }
              }
              else
              {
                HashMap<String, Object> propConfig = new HashMap<String, Object>(1);
                propConfig.put("showAttr", attr.startsWith("z1_") ? false : true);
                Property prop = new Property(name, availableMappedAxis.get(0), attr, desc, propConfig);
                availableMappedAxis.remove(0);
                propertyMapping.put(name, prop);
              }
            }
            
            pmi.setPayload(jm.serialize(propertyMapping.values()));
            pmi.save(true);
            
            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Done processing propertymapping/" + command.name() + " api");
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/propertymapping/delete //delete by name or propMapped
          case delete:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            ResponseMessage msg = null;
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Missing or ill formatted request payload. Expecting property mapping payload to delete.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> map = jm.readAsMap(pl);
            String name = map.get("name") == null ? null : map.get("name").toString();
            String propMapped = map.get("propMapped") == null ? null : map.get("propMapped").toString();
            if((name == null || name.isEmpty()) && (propMapped == null || propMapped.isEmpty()))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Missing name or propMapped to delete.");
              resp.getWriter().print(msg.toString());
              return;
            }
                        
            PropertyMappingInfo pmi = PropertyMapping.getInstance().getNSMappingInfo(uctx);
            if(pmi == null)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingDone,
                      "Not found name or propMapped to delete.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Property> propertyMapping = pmi.getPropertyMapping();
            if (name != null && !name.isEmpty())
            {
              if (!propertyMapping.containsKey(name))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingDone,
                    "Not found name to delete.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              Property p = propertyMapping.get(name);
              propMapped = p.getPropMapped();
              if(isSystemReservedProp(propMapped))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Not allow to delete a system reserved properties or invalid propMapped.");
                resp.getWriter().print(msg.toString());
                return;
              }
                  
              List<String> availableMappedAxis = pmi.getAvailableMappedAxis();
              availableMappedAxis.add(p.getPropMapped());
              
              propertyMapping.remove(name);
              pmi.setPayload(jm.serialize(propertyMapping.values()));
              pmi.setAvailableMappedAxis(availableMappedAxis);
              pmi.save(true);
              
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done deleting propMapped name " + name);
            }
            else // delete a given propMapped
            {
              Map<String, Property> propertyAxisMapping = pmi.getAllMappedPropertyFromAxis();
              if (!propertyAxisMapping.containsKey(propMapped))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingDone,
                    "Not found propMapped to delete.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              if(isSystemReservedProp(propMapped))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Not allow to delete a system reserved properties.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              List<String> availableMappedAxis = pmi.getAvailableMappedAxis();
              availableMappedAxis.add(propMapped);
              pmi.setAvailableMappedAxis(availableMappedAxis);

              Property p = propertyAxisMapping.get(propMapped);
              propertyMapping.remove(p.getName());
              pmi.setPayload(jm.serialize(propertyMapping.values()));
              pmi.save(true);
              
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done deleting propMapped " + propMapped);                           
            }

            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/propertymapping/update
          // Update name and/or prop/description config by propMapped
          case update:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            ResponseMessage msg = null;
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Missing or ill formatted request payload. Expecting property mapping payload to update.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> map = jm.readAsMap(pl);
            String propMapped = map.get("propMapped") != null ? (String) map.get("propMapped") : null;
            if(propMapped == null || propMapped.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Not allow to update a system reserved properties or invalid given propMapped.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            String name = map.get("name") != null ? (String) map.get("name") : null;
            String desc = map.get("description") != null ? (String) map.get("description") : null;
            Object prop = map.get("prop");
            
            if ((name == null || name.isEmpty())
                    && prop == null
                    && (desc == null || desc.isEmpty()))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams,
                      "Missing or ill formatted request payload. Expecting propMapped and (name or prop or description) to aupdate.");
              resp.getWriter().print(msg.toString());
              return;
            }


            PropertyMappingInfo pmi = PropertyMapping.getInstance().getNSMappingInfo(uctx);
            if(pmi == null)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingDone,
                      "Not found propMapped to update.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            Map<String, Property> propertyAxisMapping = pmi.getAllMappedPropertyFromAxis();
            if (!propertyAxisMapping.containsKey(propMapped))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingDone,
                  "Not found propMapped to update.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            Property p = propertyAxisMapping.get(propMapped);
            String oldPropMapName = p.getName();
            // update name
            if (name != null && !name.isEmpty()) p.setName(name);
            // update desc
            if (desc != null && !desc.isEmpty()) p.setDescription(desc);
            // update prop config
            if (prop != null) p.setProp(prop);
            
            Map<String, Property> propertyMapping = pmi.getPropertyMapping();
            propertyMapping.remove(oldPropMapName);
            propertyMapping.put(p.getName(), p);
            pmi.setPayload(jm.serialize(propertyMapping.values()));
            pmi.save(true);

            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Done update propMapped " + propMapped);                           
            resp.getWriter().print(msg.toString());
            break;
          }
          default:
            break;
        }
        
        App.notifyClearCache(uctx.getNamespace(), Type.propertyMapping.name());
      }

    };
  }
  
  /**
   * Check proMapped is preset for Home Dashboard.
   * 
   * @param proMapped
   * 
   * @return
   */
  private Boolean isSystemReservedProp(String proMapped)
  {
    // system reserved properties
    if (proMapped == null || proMapped.isEmpty()
        || proMapped.equals(ExplorerCubeAxis.deviceContext.name())
        || proMapped.equals(ExplorerCubeAxis.visitIden.name())
        || proMapped.equals(ExplorerCubeAxis.visitRec.name())
        || proMapped.equals(ExplorerCubeAxis.propA.name())  //reserved for Cart Contents
        || proMapped.equals(ExplorerCubeAxis.propB.name())) //reserved for Epp Label
      return true;
    return false;
  }

}
