package com.z1.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import z1.commons.Utils;
import z1.core.Task;

public class TaskHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    id
  }

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);        

        switch (command)
        {
        // c3/data/task/all => Sends all goals
          case all:
          {
            
            return;
          }

          // c3/data/task/id?taskId=<task-id>
          case id:
          {
            // This must be a request for a specific goal payload.
            String taskId = req.getParameter("taskId");
            // get the goal payload
            Task t = Task.instance(ctx, taskId.getBytes());
            String payload = (String)t.getProperty(Task.Fields.interaction);
            resp.getWriter().print(payload);

            return;
          }

          default:
          {
            return;
          }
        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return null;
  }
}
