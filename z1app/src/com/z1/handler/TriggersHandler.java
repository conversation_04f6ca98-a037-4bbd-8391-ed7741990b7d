package com.z1.handler;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.kb.ContentStore;
import z1.kb.KBProcessor;
import z1.kb.KBResponse;
import z1.template.TemplateUtils;

public class TriggersHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all
  }
  
  
  private enum PostCommand
  {
    create,
    update,
    delete
  }
  
  /**
   *  TODO
   *
   */
  public static class KbStoreAdapter
  {

    public static String convert(String payload)
    {

      JsonMarshaller jm = new JsonMarshaller();
      Map<String,Object> triggerMap = jm.readAsMap(payload);
      String title = (String)triggerMap.get("name");
      
      List<String> tagsList = new ArrayList<>();
      tagsList.add("_z1_trigger");
      List<Map<String,Object>> params = (List<Map<String,Object>>)triggerMap.get("params");
      for(Map<String,Object> param:params)
      {
        String name = (String)param.get("name");
        if(name.equalsIgnoreCase("z1_tags"))
        {
          String tagStr = (String)param.get("value");
          String[] tags = tagStr.split(",");
          for(String tag:tags)
          {
            tagsList.add(tag); 
          }
        }
      }
      
      Map<String,Object> kbStoreMap = new HashMap<>();
      
      kbStoreMap.put("title", title);
      kbStoreMap.put("contentType", KBResponse.ContentType.text.name());
      kbStoreMap.put("contents", payload);
      kbStoreMap.put("tags", tagsList); 
      
      String result = jm.serialize(kbStoreMap);
      
      return result;
    }
    
  }
  
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
          String cStr = pathParts[0];
          GetCommand command = null;
          try
          {
            command = GetCommand.valueOf(cStr);
          }
          catch (Throwable e)
          {
            z1.commons.Utils.showStackTraceIfEnable(e, null);
            resp.getWriter().print("fail"); //TODO: write the correct https return code
          }
           

        switch (command)
        {
          // c3/data/triggers/all
          case all:
          {      
            try
            {
            String payload = getAllTriggers(ctx);
            resp.getWriter().print(payload);
            }
            catch (Throwable e)
            {
              z1.commons.Utils.showStackTraceIfEnable(e, null);
              resp.getWriter().print("fail"); //TODO: write the correct https return code
            }
            return;
          }
        }

      }

      /**
       * Get all data from kbstore of type triggers
       * @param uctx
       * @throws IOException
       */
      private String getAllTriggers(UContext uctx) throws IOException
      {        
        List<Map<String,Object>> results = new ArrayList<>();
        Result<ContentStore> res = ContentStore.getAllContent(uctx,ContentStore.EntityType.trigger.name());
        Iterator<ContentStore> itr = res.getData().iterator();
        JsonMarshaller jm = new JsonMarshaller();
        
        while(itr.hasNext())
        {
          ContentStore cs = itr.next();
          String id = (String)cs.getValues().get("id");
          if (TemplateUtils.isTemplateConfig(id, ContentStore.EntityType.trigger.name()))  
            continue;
          
          String payload = (String)cs.getValues().get("contents");
         
          Map<String, Object> result = jm.readAsMap(payload);  
          result.put("id", id);
          results.add(result);
          
        }
        String payload = new JsonMarshaller().serialize(results);
        return payload;
      }

        


    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/triggers/create => payload has the definition
          // returns <trigger id>
          case create:
          {
            String payload = ServletUtil.getPayload(req);

            //Transform Trigger payload to kb_store payload
            String kbStorePayload = KbStoreAdapter.convert(payload);
            
            KBProcessor proc = new KBProcessor();
            String id = proc.createNewContent(ctx, kbStorePayload);
              
            String ret = "{ \"id\": \""+ id +"\" }";
            resp.getWriter().print(ret);
            
            break;
          }
          // c3/data/triggers/update?triggerId=<trigger-id> => payload has the definition          
          case update:
          {
            String payload = ServletUtil.getPayload(req);
            
            //Transform Trigger payload to kb_store payload
            String kbStorePayload = KbStoreAdapter.convert(payload);
            
            String triggerId = req.getParameter("triggerId"); 
            
            KBProcessor proc = new KBProcessor();
            proc.editContent(ctx, triggerId, kbStorePayload);

            break;
          }
          // c3/data/triggers/delete?triggerId=<trigger-id>
          case delete:
          {
            try
            {
              String triggerId = req.getParameter("triggerId");
              
              KBProcessor proc = new KBProcessor();
              proc.deleteContent(ctx, triggerId);
              
              resp.getWriter().print(""); // TODO
            }
            catch(Throwable e)
            {
              z1.commons.Utils.showStackTraceIfEnable(e, null);
              resp.getWriter().print("fail"); // TODO
            }

            break;
          }
        }
        
        // TODO: Invalidate Cache ZMOB-5431
        //App.notifyClearCache(ctx.getNamespace(), SignalPart.PREFIX);

      }
    };


  }

  // /////////////////////////////////////////////////////////////////


}
