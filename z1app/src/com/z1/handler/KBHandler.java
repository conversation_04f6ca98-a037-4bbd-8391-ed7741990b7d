package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.api.Commons.ReqFields;
import z1.imagescanner.ImageUploader;
import z1.kb.ContentStore;
import z1.kb.ContentStore.EntityType;
import z1.kb.ContentStoreStatsRecorder;
import z1.kb.FetchTags;
import z1.kb.KBConfigStore;
import z1.kb.KBDocStatus;
import z1.kb.KBDocumentRetreiver;
import z1.kb.KBProcessor;
import z1.kb.KBResponse;
import z1.kb.KBResponse.ContentType;
import z1.processing.JourneyRuntimeUtils;
import z1.kb.KBSearchFederator;
import z1.kb.KBStore;
import z1.kb.ZineoneStoreHandler;

public class KBHandler implements CommandHandlerFactory
{
  private enum Command
  {
    config, // create KBConfiguration
    create,
    contentCount,
    edit,
    update,
    deleteTag,
    deleteContent,
    tags,
    fetchTags,
    content,
    publishAllLanguages,
    unpublishAllLanguages
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        Command command = Command.valueOf(cStr);

        // Create an array to pass for the relevant commands
        String[] subParts = new String[pathParts.length - 1];
        int j = 0;
        for (int i = 1; i < pathParts.length; i++, j++)
        {
          subParts[j] = pathParts[i];
        }

        switch (command)
        {
          // c3/data/kb/tags?entype=<entity-type>
          case tags: // use previously fetched tags if available in the z1 store
          {
            // Get all tags
            ContentStore.EntityType entype = ContentStore.EntityType.article;
            String entypeStr = req.getParameter("entype");
            if (entypeStr != null && !entypeStr.isEmpty())
            {
              entype = ContentStore.EntityType.valueOf(entypeStr);
            }
            Set<String> tags = null;
            switch (entype)
            {
              case article:
                KBSearchFederator fed = new KBSearchFederator(uctx);
                tags = fed.getAllTags(uctx);
                break;
              case faq:
              case push:
              case fullscreen:
              case banner:
              case alert:
              case appbox:
              case trigger:
                ZineoneStoreHandler handler = new ZineoneStoreHandler();
                tags = handler.getAllTagsForEntity(uctx, entype);
                break;
            }
            // Exclude the internal tags from showing
            Set<String> finalTags = new java.util.HashSet<>(20);
            for (String t : tags)
            {
              if (t.startsWith("_")) continue;
              finalTags.add(t);
            }

            String payload = new JsonMarshaller().serialize(finalTags);
            resp.getWriter().print(payload);
            break;
          }

          // c3/data/kb/fetchTags?companyName=<company-name>&storeName=<store-name>
          case fetchTags: // load all new tags from the 3rd party system store
          {
            try
            {
              // fetch all tags
              String companyName = req.getParameter("companyName");
              String storeName = req.getParameter("storeName");

              KBConfigStore store = KBConfigStore.getByCompanyAndStoreName(uctx,
                  companyName, storeName);
              // for certain stores get all the tags from the store and store in
              // Z1 Store
              Map<String, Object> storeMap = new HashMap<>();
              storeMap.put(KBConfigStore.Fields.handler.name(),
                  store.getValues().get(KBConfigStore.Fields.handler.name()));
              storeMap.put(KBConfigStore.Fields.companyName.name(), store
                  .getValues().get(KBConfigStore.Fields.companyName.name()));
              storeMap.put(KBConfigStore.Fields.storeName.name(),
                  store.getValues().get(KBConfigStore.Fields.storeName.name()));
              storeMap.put(KBConfigStore.Fields.properties.name(), store
                  .getValues().get(KBConfigStore.Fields.properties.name()));

              FetchTags ft = new FetchTags();
              String tags = ft.fetchTags(uctx, storeMap);

              // store all tags
              store.getValues().put(KBConfigStore.Fields.tags.name(), tags);
              store.save();

              // ServletUtil.printSuccess(resp.getWriter());
              String ret = "{\"tags\": \"" + tags + "\"}";
              resp.getWriter().print(ret);
            }
            catch (Exception e)
            {
              ServletUtil.printFailure(resp.getWriter(),
                  "Unable to fetch document tags for this store");
            }

            break;
          }

          // c3/data/kb/config?filter=<all|company>[&companyName=<company-name>]
          case config:
          {
            // TODO add req.getParameter("op") == null
            // case when filter=company
            String filter = req.getParameter("filter");
            if (filter.equalsIgnoreCase("company"))
            {
              String companyName = req.getParameter(KBConfigStore.Fields.companyName.name());
              Map<String, Object> result = new HashMap<String, Object>();

              Set<KBConfigStore> stores = KBConfigStore.getByCompanyName(uctx,
                  companyName);
              for (KBConfigStore store : stores)
              {
                String storeName = (String) store.getValues()
                    .get(KBConfigStore.Fields.storeName.name());
                String properties = (String) store.getValues()
                    .get(KBConfigStore.Fields.properties.name());

                Map<String, Object> propsMap = new JsonMarshaller()
                    .readAsMap(properties);

                result.put(KBConfigStore.Fields.storeName.name(), storeName);
                result.put(KBConfigStore.Fields.properties.name(), propsMap);

                break; // only one store entry should exist
              }

              String payload = new JsonMarshaller().serialize(result);
              resp.getWriter().print(payload);
            }

            else if (filter.equalsIgnoreCase("all"))
            {

              List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
              Set<KBConfigStore> stores = KBConfigStore.getAllHandlers(uctx);
              for (KBConfigStore store : stores)
              {
                String storeName = (String) store.getValues()
                    .get(KBConfigStore.Fields.storeName.name());
                String companyName = (String) store.getValues()
                    .get(KBConfigStore.Fields.companyName.name());
                String properties = (String) store.getValues()
                    .get(KBConfigStore.Fields.properties.name());

                Map<String, Object> propsMap = new JsonMarshaller()
                    .readAsMap(properties);
                propsMap.put("password", ""); // dont send the password back

                Map<String, Object> m = new HashMap<String, Object>();
                m.put(KBConfigStore.Fields.storeName.name(), storeName);
                m.put(KBConfigStore.Fields.companyName.name(), companyName);
                m.put(KBConfigStore.Fields.properties.name(), propsMap);

                result.add(m);
              }

              String payload = new JsonMarshaller().serialize(result);
              resp.getWriter().print(payload);

            }

            break;
          }
          // c3/data/kb/contentCount
          case contentCount:
          {

            ContentStoreStatsRecorder stats = new ContentStoreStatsRecorder(
                uctx);
            long count = stats.getTotalMessages();

            String payload = String.valueOf(count);
            resp.getWriter().print(payload);
            break;

          }
          /*
           * c3/data/kb/content?entype=<entypeStr>[&status=<status>]
           * c3/data/kb/content?contentId=<content-id>[&language=<lang>]
           * c3/data/kb/content?tags=<tag-values>[&entype=<entypeStr>]
           * c3/data/kb/content?topK=<n>[&entype=<entypeStr>&repo=<repo>]
           */
          case content:
          {
            // c3/data/kb/content?contentId=contentId
            String contentId = req.getParameter(ReqFields.contentId.name());
            String tagsCsv = req.getParameter("tags");

            KBDocStatus docStatus = KBDocStatus.published;
            String status = req.getParameter("status");
            if (status != null && !status.isEmpty())
            {
              docStatus = KBDocStatus.valueOf(status);
            }
            ContentStore.EntityType entype = ContentStore.EntityType.article;
            String entypeStr = req.getParameter("entype");
            if (entypeStr != null && !entypeStr.isEmpty())
            {
              try
              {
                entype = ContentStore.EntityType.valueOf(entypeStr);
              }
              catch (Exception e)
              {
                // Failed to understand the type
                return;
              }
            }

            if (contentId != null && !contentId.isEmpty())
            {
              Set<KBResponse> contents = new HashSet<KBResponse>();

              // full JSON of the content document stored
              KBResponse response = KBDocumentRetreiver.getContent(uctx,
                  contentId);
              if (response != null) contents.add(response);
              String payload = new JsonMarshaller().serialize(contents);
              resp.getWriter().print(payload);
            }

            // c3/data/kb/content?tags="tag1,tag2,..."
            else if (tagsCsv != null && !tagsCsv.isEmpty())
            {
              Set<String> tags = new HashSet<String>();
              String[] splits = tagsCsv.split(",");
              for (int i = 0; i < splits.length; i++)
              {
                tags.add((splits[i].trim()));
              }

              List<KBResponse> contents = null;
              switch (entype)
              {
                case article:
                  KBSearchFederator fed = new KBSearchFederator(uctx);
                  contents = fed.getContent(uctx, tags, null);
                  break;
                case faq:
                case push:
                case fullscreen:
                case banner:
                case alert:
                  ZineoneStoreHandler handler = new ZineoneStoreHandler();
                  contents = handler.getContent(uctx, tags, null, entype);
                  break;
              }

              /*
               * If a document status was specified then filter on it. Else
               * return all documents
               */
              if (status != null && !status.isEmpty())
              {
                contents = z1.kb.ContentUtil
                    .filterContentListOnDocStatus(contents, docStatus);
              }

              String payload = new JsonMarshaller().serialize(contents);
              resp.getWriter().print(payload);
            }

            // c3/data/kb/content?topK=<n>"
            else
            {
              int topK = -1;
              String topK_s = req.getParameter("topK");
              if (topK_s != null && !topK_s.isEmpty())
              {
                topK = Integer.parseInt(topK_s);
              }

              ZineoneStoreHandler handler = new ZineoneStoreHandler();
              // no content just meta data such as title, etc for K documents

              KBStore.RepoType repoType = KBStore.RepoType.GENERIC;
              String repo = req.getParameter("repo");
              if (repo != null && !repo.isEmpty())
                repoType = KBStore.RepoType.EVENT_IMAGES;

              List<KBResponse> contents = handler.getMetaContent(uctx, topK,
                  entype, repoType);
              if (entype.equals(EntityType.image))
              {
                String reqURL = req.getRequestURL().toString();

                // ZMOB-3223
                // If it's not a localhost, we will set the scheme to HTTPS
                // always
                int schemeNdx = reqURL.indexOf("://");
                boolean isLocalHost = (reqURL.indexOf("://localhost") > 0);
                if (!isLocalHost && (schemeNdx >= 0))
                {
                  reqURL = "https" + reqURL.substring(schemeNdx);
                }

                int index = reqURL.lastIndexOf("/c3/data/kb/content");
                if (index > 0)
                {
                  String host = reqURL.substring(0, index);
                  List<KBResponse> rl = new ArrayList<>();
                  for (KBResponse rsp : contents)
                  {
                    if ("_z1_pushlogo".equals(rsp.getContentId())
                        || "_z1_nslogo".equals(rsp.getContentId()))
                    {
                      rl.add(rsp);
                    }
                    else
                    {
                      String imgURL = rsp.getImageURL();
                      if (imgURL == null || imgURL.isEmpty())
                      {
                        rsp.setImageURL(host + "/c3/api/v1/images/"
                            + rsp.getContentId() + "?apikey=${apikey}");
                      }
                    }
                  }
                  contents.removeAll(rl);
                }
              }

              String payload = new JsonMarshaller().serialize(contents);
              resp.getWriter().print(payload);
            }
            break;
          }
          default:
          {
            return;
          }
        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        Command command = Command.valueOf(cStr);

        // Create an array to pass for the relevant commands
        String[] subParts = new String[pathParts.length - 1];
        int j = 0;
        for (int i = 1; i < pathParts.length; i++, j++)
        {
          subParts[j] = pathParts[i];
        }

        switch (command)
        {
          // c3/data/kb/config?op=<create|delete>&companyName=<company-name>
          case config:
          {
            String op = req.getParameter("op");

            String companyName = req.getParameter(KBConfigStore.Fields.companyName.name());

            if (op.equalsIgnoreCase("create"))
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller m = new JsonMarshaller();
              Map<String, Object> map = m.readAsMap(payload);
              String storeName = (String) map
                  .get(KBConfigStore.Fields.storeName.name());
              Map<String, Object> properties = (Map<String, Object>) map
                  .get("properties");

              String handlerClassName = "";
              // Need to do this better. Create handlerClassName from company
              // and storename
              if (companyName.equalsIgnoreCase("zendesk"))
              {
                if (storeName.equalsIgnoreCase("Web Portal"))
                {
                  handlerClassName = "com.z1social.kb.zendesk.ZenDeskWebPortalKBStoreHandler";
                }
                else
                // help center
                {
                  handlerClassName = "com.z1social.kb.zendesk.ZenDeskHelpCenterKBStoreHandler";
                }
              }

              // for certain stores get all the tags from the store and store in
              // Z1 Store
              Map<String, Object> storeMap = new HashMap<>();
              storeMap.put(KBConfigStore.Fields.handler.name(),
                  handlerClassName);
              storeMap.put(KBConfigStore.Fields.companyName.name(),
                  companyName);
              storeMap.put(KBConfigStore.Fields.storeName.name(), storeName);

              String propPayload = new JsonMarshaller()
                  .serializeMap(properties);

              storeMap.put(KBConfigStore.Fields.properties.name(), propPayload);
              FetchTags ft = new FetchTags();
              String tags = ft.fetchTags(uctx, storeMap);

              KBConfigStore store = KBConfigStore.getByCompanyAndStoreName(uctx,
                  companyName, storeName);
              if (store == null)
              {
                store = KBConfigStore.newInstance(uctx);
              }

              String props = m.serialize(properties);
              store.getValues().put(KBConfigStore.Fields.companyName.name(),
                  companyName);
              store.getValues().put(KBConfigStore.Fields.storeName.name(),
                  storeName);
              store.getValues().put(KBConfigStore.Fields.handler.name(),
                  handlerClassName);
              store.getValues().put(KBConfigStore.Fields.properties.name(),
                  props);
              store.getValues().put(KBConfigStore.Fields.tags.name(), tags);
              store.save();
            }

            else if (op.equalsIgnoreCase("delete"))
            {
              Set<KBConfigStore> stores = KBConfigStore.getByCompanyName(uctx,
                  companyName);
              for (KBConfigStore store : stores)
              {
                store.delete();
              }
            }
            break;

          }
          // c3/data/kb/create/
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            KBProcessor proc = new KBProcessor();
            String id = proc.createNewContent(uctx, payload);

            if (id == null)
            {
              JsonMarshaller m = new JsonMarshaller();
              Map<String, Object> map = m.readAsMap(payload);
              String title = (String) map.get("title");
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.contentCreateOrUpdateFailed,
                  "Name '" + title + "' already exists. Enter unique name.");
              resp.getWriter().print(msg.toString());
            }
            else
            {
              String ret = "{\"id\":\"" + id + "\"}";
              resp.getWriter().print(ret);
            }
            break;
          }

          // c3/data/kb/edit?contentId=<contentId>
          case edit:
          {
            String payload = ServletUtil.getPayload(req);
            String contentId = req.getParameter("contentId");

            KBProcessor proc = new KBProcessor();
            String id = proc.editContent(uctx, contentId, payload);

            if (id == null)
            {
              JsonMarshaller m = new JsonMarshaller();
              Map<String, Object> map = m.readAsMap(payload);
              String title = (String) map.get("title");
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.contentCreateOrUpdateFailed,
                  "Name '" + title + "' already exists. Enter unique name.");
              resp.getWriter().print(msg.toString());
            }
            else
            {
              String ret = "{\"id\":\"" + id + "\"}";
              resp.getWriter().print(ret);
            }
            break;
          }

          // c3/data/kb/deleteTag?tag=<tagId>
          case deleteTag:
          {
            String tag = req.getParameter("tag");

            KBProcessor proc = new KBProcessor();
            proc.deleteContentTagEntry(uctx, tag.toLowerCase());
            break;
          }

          // c3/data/kb/deleteContent?contentId=<contentId>&force=<true>
          case deleteContent:
          {
            String contentId = req.getParameter("contentId");
            boolean isForceDelete = Boolean
                .parseBoolean(req.getParameter("force"));
            KBProcessor proc = new KBProcessor();
            Map<String, Object> refs = JourneyRuntimeUtils.getContenRef(uctx,
                contentId);
            Boolean isContentInUse = false;
            for (Object refList : refs.values())
            {
              if (!((List<?>) refList).isEmpty()) isContentInUse = true;
            }
            if (!isContentInUse || isForceDelete)
            {
              KBResponse document = proc.getContent(uctx, contentId);
              if (document != null)
              {
                ContentType type = document.getContentType();
                if (type == ContentType.image) {
                  ImageUploader imageUploader = new ImageUploader();
                  imageUploader.imageOperation(uctx, contentId, uctx.getNamespace(),
                          "delete", null);
                }

              }
              proc.deleteContent(uctx, contentId);

              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success, null,
                  "Content deleted successfully");
              resp.getWriter().print(msg);
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.contentDeletionFailed,
                  new JsonMarshaller().serializeMap(refs));
              resp.getWriter().print(msg);
            }

            break;
          }

          // c3/data/kb/publishAllLanguages?contentId=<contentId>
          case publishAllLanguages:
          {
            String contentId = req.getParameter("contentId");

            KBProcessor proc = new KBProcessor();
            proc.publishAllContent(uctx, contentId);
            break;
          }
          // c3/data/kb/unpublishAllLanguages?contentId=<contentId>
          case unpublishAllLanguages:
          {
            String contentId = req.getParameter("contentId");

            KBProcessor proc = new KBProcessor();
            proc.unpublishAllContent(uctx, contentId);
            break;
          }
          default:
          {
            return;
          }
        }
      }

    };
  }

}
