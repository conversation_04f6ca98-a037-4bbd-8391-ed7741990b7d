package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.USessionStore;
import udichi.core.log.ULogger;
import udichi.core.system.User;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.SIPUtils;
import udichi.core.util.ServletUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * Handles the /c3/system/context endpoint, providing the same functionality as
 * the original /system/context endpoint.
 */
public class SystemHandler implements CommandHandlerFactory
{

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        ULogger logger = ctx.getLogger(getClass());
        // Check if the namespace is passed to this query
        String sentNS = req.getParameter("namespace");

        if (logger.canLog()) logger.log(
            String.format("[c3/system/context] sent namespace: %s", sentNS));

        // get the user data and serialize the info
        JsonMarshaller jm = new JsonMarshaller();

        String installationType = App.getInstance()
            .getProperty("installation.type");
        if (installationType == null) installationType = "cloud";

        // If no NS is passed, there is no guarantee that the request has
        // reached
        // the right sub cloud. In that case we won't try to find a user
        // associated
        // with the session.
        User user = null;
        if (sentNS != null)
        {
          user = ctx.setNamespace(sentNS).getUser();
        }

        if (user == null)
        {
          String token = null;
          if (sentNS == null)
          {
            token = new SIPUtils().generateSIPToken();
          }
          else
          {
            token = Optional.ofNullable(ctx.sessionStore(false))
                .map(s -> (String) s.get("token"))
                .orElseGet(() -> new SIPUtils().generateSIPToken());
          }

          // We need to remember the token we generated now. The UI will use
          // this token to encrypt the message. We will generate a unique key to
          // store the token and send the key along with the token as well. The
          // UI will store the key and will return the key with all calls.

          // Store the token for the given key
          String key = ServletUtil.putTokenToCache(token);

          if (logger.canLog()) logger.log(String.format(
              "[c3/system/context] No user in the context. Created the encrypted "
                  + "token to send. The token key: %s",
              key));

          resp.getWriter().print("{");
          resp.getWriter().print(
              String.format("\"installation\":\"%s\",", installationType));
          resp.getWriter().print(String.format("\"_udc_tkey\":\"%s\",", key));
          ServletUtil.printFailure(resp.getWriter(), "NOT_AUTHENTICATED");
          resp.getWriter().print(String.format(", \"token\":\"%s\"", token));
          resp.getWriter().print("}");
        }
        else
        {
          // Check if the user can use workbench
          boolean canUse = true;
          if (App.loginHandler != null
              && req.getRequestURI().endsWith("workbench"))
          {
            canUse = App.loginHandler.canAccessWorkbench(user);
          }

          if (!canUse)
          {
            resp.getWriter().print("{");
            resp.getWriter().print(
                String.format("\"installation\":\"%s\",", installationType));
            ServletUtil.printFailure(resp.getWriter(), "NOT_AUTHORIZED");
            resp.getWriter().print("}");
            return;
          }

          String token = null;
          USessionStore.Store sessStore = ctx.setNamespace(sentNS)
              .sessionStore(false);
          if (sessStore != null)
          {
            token = (String) sessStore.get("token");
            if (token == null)
            {
              token = new SIPUtils().generateSIPToken();
              sessStore.put("token", token);
            }
          }

          // let's gather the info first and check if we get all of them now
          String ns = ctx.getNamespace();
          String userPayload = jm.serialize(ctx.getUser());
          resp.getWriter().println("{");
          resp.getWriter().print(
              String.format("\"installation\":\"%s\"", installationType));
          resp.getWriter().print(String.format(", \"user\": %s", userPayload));
          resp.getWriter().print(String.format(", \"namespace\":\"%s\"", ns));
          resp.getWriter().print(String.format(", \"token\":\"%s\", ", token));
          resp.getWriter().print(String.format("\"_udc_tkey\":\"%s\",",
              ServletUtil.createTokenKeyFrom(token)));
          ServletUtil.printSuccess(resp.getWriter());
          resp.getWriter().println("}");

          if (logger.canLog()) logger.log(String.format(
              "[c3/system/context] Found user in the context. Info: %s",
              userPayload));
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    // We don't need to implement POST for the context endpoint
    return null;
  }
}
