package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.JobQueue;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.FileDownloadWorker;
import z1.commons.FileLoader;
import z1.commons.Utils;
import z1.core.query.def.QueryDef;
import z1.core.query.def.WindowDef;
import z1.core.query.exec.Query;
import z1.core.utils.FileLoaderStats;
import z1.stream.EventStream.WindowType;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;

/**
 * This class handles the query request for different objects
 *
 */
public class QueryDataHandler implements CommandHandlerFactory
{
  private static final String ATTR_ID = "id";
  private static final String ATTR_NAME = "name";
  private static final String ATTR_PAYLOAD = "payload";
  private static final String ATTR_DESCRIPTION = "description";
  private static final String ATTR_LAST_UPDATED_BY = "lastUpdatedBy";
  private static final String ATTR_LAST_UPDATED_TIME = "lastUpdatedTime";
  private static final String ATTR_TYPE = "type";
  private static final String ATTR_VAL_UNKNOWN = "unknown";
  private static final String OBJECT = "object";
  private static final String ATTR_INTERACTIONID = "interactionId";

  // Supported post commands
  private enum PostCommand
  {
    query,
    execute,
    save,
    delete,
    download,
    deleteOneStats,
    deleteStats
  }

  // Supported get commands
  private enum GetCommand
  {
    all,
    id,
    exec,
    stats
  }

  private enum QueryType
  {
    userCount,
    actSpecificUserCount,
    eventCount,
    dataOverTime,
    dataAggregation,
    interactionDetail
  }
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        ULogger logger = uctx.getLogger(getClass());
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/query/all => Sends all query
          case all:
          {
            String typeParam = req.getParameter("type");
            resp.getWriter().print(_getQueryList(uctx, typeParam));
            
            return;
          }
          // c3/data/query/id?id=<id> => Sends a query matches id
          case id:
          {
            // This must be a request for a specific query type.            
            String eId = req.getParameter("id");

            CustomConfig cc = CustomConfig.load(uctx, eId, Type.query, true);
            String pl = cc.getPayload();
            Map<String, Object> map = new HashMap<>();
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> plMap = jm.readAsMap(pl);
            map.put(ATTR_ID, cc.getId());
            map.put(ATTR_NAME, cc.getName());
            map.put(ATTR_DESCRIPTION, cc.getDescription());
            map.put(ATTR_PAYLOAD, plMap);

            resp.getWriter().print(jm.serialize(map));
            break;
          }
          
          // c3/data/query/exec?qId=<query-id>
          case exec:
          {
            // This must be a request for a specific query type.
            String qId = req.getParameter("qId");
            if (qId == null) return;

            CustomConfig cc = CustomConfig.load(uctx, qId, Type.query, true);
            if (cc == null)
            {
              if (logger.canLog())
                logger.log(String.format("Query \"%s\" is not found.", qId));
              return;
            }

            String payload = cc.getPayload();
            if (payload == null || payload.length() == 0)
            {
              if (logger.canLog())
                logger.log(String.format("Query \"%s\" has empty payload. "
                    + "Skipping the query request from C3.", qId));
              return;
            }

            payload = payload.replace("\n", "");

            JsonMarshaller jm = new JsonMarshaller();

            QueryDef queryDef = jm.readAsObject(payload, QueryDef.class);
            
            // If the query has a custom date range, we will use that if makes sense
            String toDate = req.getParameter("t");
            String fromDate = req.getParameter("f");
            if (toDate != null && fromDate != null)
            {
              WindowDef wd = queryDef.getTimeWindow();
              if (wd != null)
              {
                // As we received the to and from date, we will set the window def accordingly.
                wd.setType(WindowType.fixed.name());
                wd.setVal(fromDate + "|" + toDate);                
              }
            }

            List<Map<String, Object>> retList = 
                Query.getExecutor(uctx, queryDef).execute();

            String res = new JsonMarshaller().serialize(retList);
            StringBuilder s = new StringBuilder(500);
            s.append("{\"identifier\": \"id\", \"items\": ");
            s.append(res);
            s.append("}");
            resp.getWriter().print(s.toString());
            break;
          }
          // c3/data/query/stats?queryName=<query-name> => Get all historical stats for a specific query
          case stats:
          {
            String queryName = req.getParameter("queryName");
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                Type.uploadDownloadStats);
            for (CustomConfig cc : ccList)
            {
              if (cc == null) continue;
              String name = cc.getName();
              if (name.equals(queryName))
              {
                String id = cc.getId();
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("id", id);
                map.put("name", cc.getName());
                map.put("payload", cc.getPayload());
                ret.add(map);
              }             
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;
          }
        }
      }
    };
  }

  /**
   * This method responds to post request and sends the result of query back to
   * the caller.
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        ULogger logger = uctx.getLogger(getClass());
        switch (command)
        {
          // c3/data/query/query => Submit the query
          case query:
          {
            String payload = ServletUtil.getPayload(req);
            payload = payload.replace("\n", "");

            JsonMarshaller jm = new JsonMarshaller();
            QueryDef queryDef = jm.readAsObject(payload, QueryDef.class);

            List<Map<String, Object>> retList = 
                Query.getExecutor(uctx, queryDef).execute();
            
            resp.getWriter().print(new JsonMarshaller().serialize(retList));
            
            Map<String, Object> mPl = jm.readAsMap(payload);
            String name = (String) mPl.get(ATTR_NAME);
            if (name == null || name.isEmpty())
            {
              name = "query";
            }
            ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, name,
                name, Operations.query).save();
            break;
          }
          
          // c3/data/query/execute?id=<id>
          case execute:
          {
            // get the query ID
            String id = req.getParameter("id");
            // get the metadata
            CustomConfig cc = CustomConfig.load(uctx, id, Type.query, true);
            
            String payload = cc.getPayload();
            
            // If no payload is defined, we will simply return
            if (payload == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.queryRunFailed, "Cannot execute this query.");
              resp.getWriter().print(msg.toString());
              return;
            }

            JsonMarshaller jm = new JsonMarshaller();
            QueryDef queryDef = jm.readAsObject(payload, QueryDef.class);

            List<Map<String, Object>> retList = 
                Query.getExecutor(uctx, queryDef).execute();
            
            Map<String, Object> map = new java.util.HashMap<>();
            map.put("result", retList);
            map.put("query", queryDef);

            resp.getWriter().print(new JsonMarshaller().serialize(map));
            ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, id,
                id, Operations.run).save();
            break;
          }
          
          // c3/data/query/save => save the query in CustomConfig
          case save:
          {
            String queryPayload = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (queryPayload == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.querySaveFailed, "No query to save.");
              resp.getWriter().print(msg.toString());
              return;
            }

            // Get the name from the payload
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(queryPayload);
            String payload = jm.serialize(mPl.get(ATTR_PAYLOAD));
            String name = (String) mPl.get(ATTR_NAME);
            if (name == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.querySaveFailed,
                  "Missing query's name.");
              resp.getWriter().print(msg.toString());
              return;
            }
            String description = (String) mPl.get(ATTR_DESCRIPTION);

            CustomConfig cc = CustomConfig.load(uctx, name, Type.query, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, Type.query, name);
            }
            cc = CustomConfig.create(uctx, Type.query, name);
            cc.setPayload(payload);
            cc.setName(name);
            cc.setDescription(description);
            cc.save();
            id = cc.getId();

            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", id);
            resp.getWriter().print(jm.serializeMap(map));
            ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, id,
                id, Operations.save).save();
            break;
          }
          // c3/data/query/delete?id=<id>
          case delete:
          {
            String id = req.getParameter("id");

            req.setAttribute("id", id);
            req.setAttribute("type", CustomConfig.Type.query.name());
            String[] subParts = new String[pathParts.length + 1];
            subParts[0] = CustomConfig.Type.query.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);            
            ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, id, id,
                Operations.delete).save();
            break;
          }
          // c3/data/query/download?queryName=<query-name>
          case download:
          {
            String queryName = req.getParameter("queryName");
            try
            {
              String payload = ServletUtil.getPayload(req);
              payload = payload.replace("\n", "");
              // Prepare the params to be passed
              Map<String, Object> params = new java.util.HashMap<>(10);
              params.put(FileDownloadWorker.OBJ_NAME, queryName);
              params.put(FileDownloadWorker.OBJ_TYPE, FileLoader.ObjType.query.name());
              params.put(FileDownloadWorker.PAYLOAD, payload);
              JobQueue jQ = JobQueue.getInstance("QueryDownloadWorker",
                  FileDownloadWorker.class);
              Job job = jQ.createJob("QueryDownloadWorker", uctx);
              job.setPriority(Job.Priority.counter);
              job.setPayload(new JsonMarshaller().serializeMap(params));
              jQ.submit(job);
            }
            catch (Throwable ex)
            {
             if(logger.canLog())
              logger.severe("Download not completed successfully" + ex);
            }
           
            
            ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.processing,
                ResponseMessage.Type.downloadProcessing, 
                "Processing download. Monitor progress by selecting 'Download Status'");
            resp.getWriter().print(msg.toString());
            ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, queryName,
                queryName, Operations.download).save();
            
            break;
          }         
          // c3/data/query/deleteOneStats
          case deleteOneStats: // delete one stats record, unique ID is given
          {
            String id = pathParts[1];
            CustomConfig cc = CustomConfig.load(uctx, id, Type.uploadDownloadStats, true);
            if (cc != null)
            {
              CustomConfig.delete(uctx, id, Type.uploadDownloadStats);
              ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, id,
                  id, Operations.deleteOneStat).save();
            }
            return;
          }
          // c3/data/query/deleteStats?queryName=<query-name>
          case deleteStats: // delete all stats records for a specific entity
          {
            String queryName = req.getParameter("queryName");
            List<CustomConfig> list = CustomConfig.forceLoadAll(uctx,
                Type.uploadDownloadStats);

            for (CustomConfig cc : list)
            {
              if (cc == null || !cc.getName().equals(queryName)) continue;

              CustomConfig.delete(uctx, cc.getId(), Type.uploadDownloadStats);
              ArtifactAudit.newInstance(uctx, ItemTypes.adHocQuery, queryName,
                  queryName, Operations.deleteStats).save();
            }
            return;
          }
        }
      }
    };
  }
  
  /**
   * @param uctx
   * @param typeParam
   * @return
   */
  private String _getQueryList(UContext uctx, String typeParam)
  {
    QueryType type;
    JsonMarshaller jm = new JsonMarshaller();
    if (null != typeParam)
    {
      try
      {
        type = QueryType.valueOf(typeParam);
      }
      catch (Exception e)
      {
        ResponseMessage msg = new ResponseMessage(uctx,
            ResponseMessage.Status.fail, null, typeParam
                + " is inavlid value of request parameter type. Valid values are userCount, actSpecificUserCount, eventCount, dataOverTime, dataAggregation, interactionDetail");

        return msg.toString();
      }
    }
    List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
    List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx, Type.query);

    Map<String, Object> plMap = null;
    for (CustomConfig cc : ccList)
    {
      if (cc == null) continue;
      Map<String, Object> map = new HashMap<String, Object>();
      plMap = jm.readAsObject(cc.getPayload(), Map.class);
      List<Map<String, Object>> propList = (List<Map<String, Object>>) plMap
          .get("properties");

      if (null == typeParam && propList == null)
      {
        map.put(ATTR_TYPE, ATTR_VAL_UNKNOWN);
        map.put(ATTR_ID, cc.getId());
        map.put(ATTR_NAME, cc.getName());
        map.put(ATTR_DESCRIPTION, cc.getDescription());
        map.put(ATTR_LAST_UPDATED_BY, cc.getLastUpdatedBy());
        map.put(ATTR_LAST_UPDATED_TIME, cc.getLastUpdatedTime());
        if (plMap.containsKey(OBJECT))
        {
          map.put(ATTR_INTERACTIONID, plMap.get(OBJECT));
        }

      }
      else
      {
        for (Map<String, Object> item : propList)
        {
          if (item.get("name").toString().equalsIgnoreCase("uiWidgetType"))
          {
            if (null != typeParam)
            {
              if (((String) item.get("value")).equals(typeParam))
              {
                map.put(ATTR_TYPE, (String) item.get("value"));
                map.put(ATTR_ID, cc.getId());
                map.put(ATTR_NAME, cc.getName());
                map.put(ATTR_DESCRIPTION, cc.getDescription());
                map.put(ATTR_LAST_UPDATED_BY, cc.getLastUpdatedBy());
                map.put(ATTR_LAST_UPDATED_TIME, cc.getLastUpdatedTime());
                if (plMap.containsKey(OBJECT))
                {
                  map.put(ATTR_INTERACTIONID, plMap.get(OBJECT));
                }
                break;
              }
            }
            else
            {
              map.put(ATTR_TYPE, (String) item.get("value"));
              map.put(ATTR_ID, cc.getId());
              map.put(ATTR_NAME, cc.getName());
              map.put(ATTR_DESCRIPTION, cc.getDescription());
              map.put(ATTR_LAST_UPDATED_BY, cc.getLastUpdatedBy());
              map.put(ATTR_LAST_UPDATED_TIME, cc.getLastUpdatedTime());
              if (plMap.containsKey(OBJECT))
              {
                map.put(ATTR_INTERACTIONID, plMap.get(OBJECT));
              }
              break;
            }
          }
        }
      }

      if (!map.isEmpty()) ret.add(map);
    }
    return jm.serialize(ret);

  }
}
