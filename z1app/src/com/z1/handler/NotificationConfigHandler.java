package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import com.z1.Utils.ResponseMessage;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import z1.c3.CustomConfig;
import z1.commons.def.ParamDef;
import z1.datamonitor.def.MonitorDef;

public class NotificationConfigHandler implements CommandHandlerFactory
{
  private final static JsonMarshaller jm = new JsonMarshaller();

  private final static String NOTIFICATIONS_SCHEMA_PATH = "z1/datamonitor/notifications.json";
  private final static String MONITORS_SCHEMA_PATH = "z1/datamonitor/c3_notification_monitors.json";

  public enum GetCommand
  {
    all
  }

  public enum PostCommand
  {
    update
  }

  /**
   * Returns a GET request handler.
   */
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        switch (command)
        {
          // c3/data/notifications/all
          case all:
          {
            CustomConfig cc = CustomConfig.load(ctx,
                CustomConfig.Type.notifications, true);

            if (cc == null)
            {
              cc = CustomConfig.create(ctx, CustomConfig.Type.notifications,
                  CustomConfig.Type.notifications.name());
              cc.setName(CustomConfig.Type.notifications.name());
              String schema = Utils.loadFileResource(NOTIFICATIONS_SCHEMA_PATH);
              cc.setPayload(schema);
              cc.save(true);
            }

            Map<String, Object> res = jm.readAsMap(cc.getPayload());
            resp.getWriter().print(jm.serialize(res));
            return;
          }
          default:
            break;
        }
      }
    };
  }

  /**
   * Returns a POST request handler.
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        switch (command)
        {
          case update:
          {
            try
            {
              String payload = ServletUtil.getPayload(req);

              if (payload.isEmpty())
              {
                ResponseMessage msg = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidPayload);
                resp.getWriter().print(msg);
                return;
              }

              CustomConfig cc = CustomConfig.load(ctx,
                  CustomConfig.Type.notifications, true);

              // if cc is not defined create new cc from notifications schema
              if (cc == null)
              {
                cc = CustomConfig.create(ctx, CustomConfig.Type.notifications,
                    CustomConfig.Type.notifications.name());
                cc.setName(CustomConfig.Type.notifications.name());
                String schema = Utils
                    .loadFileResource(NOTIFICATIONS_SCHEMA_PATH);
                cc.setPayload(schema);
                cc.save(true);
              }
              else
              {
                // else update the req payload to the mongo
                cc.setPayload(payload);
                cc.save(true);
              }

              Map<String, Object> plMap = jm.readAsMap(payload);

              for (Map.Entry<String, Object> entry : plMap.entrySet())
              {
                String id = entry.getKey();
                CustomConfig customConfig = CustomConfig.load(ctx, id,
                    CustomConfig.Type.datamonitor, true);

                if (customConfig == null)
                {
                  List<Map<String, Object>> monitorSchemas = jm.readAsList(
                      Utils.loadFileResource(MONITORS_SCHEMA_PATH));

                  Map<String, Object> monitor = Optional
                      .ofNullable(monitorSchemas.stream()
                          .filter(m -> m.get("id").equals(id)).findAny())
                      .get().orElse(new HashMap<>());

                  if (monitor.isEmpty())
                  {
                    ResponseMessage msg = new ResponseMessage(ctx,
                        ResponseMessage.Status.fail,
                        ResponseMessage.Type.invalidPayload);
                    resp.getWriter().print(msg);
                    return;
                  }

                  MonitorDef monitorDef = jm.readAsObject(jm.serialize(monitor),
                      MonitorDef.class);
                  customConfig = CustomConfig.create(ctx,
                      CustomConfig.Type.datamonitor, id);
                  customConfig.setName(monitorDef.getId());
                  customConfig.setPayload(jm.serialize(monitorDef));
                }

                MonitorDef monitorDef = jm
                        .readAsObject(customConfig.getPayload(), MonitorDef.class);

                List<ParamDef> paramDefs = monitorDef.getParams();
                Map<String, Object> val = (Map<String, Object>) entry
                    .getValue();

                for (Map.Entry<String, Object> p : val.entrySet())
                {
                  for (ParamDef paramDef : paramDefs)
                  {
                    if (paramDef.getName().equals(p.getKey()))
                    {
                      paramDef.setValue(String.valueOf(p.getValue()));
                    }
                  }
                }

                customConfig.setPayload(jm.serialize(monitorDef));
                customConfig.save(true);
              }

              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg);
              return;
            }
            catch (Exception e)
            {
              ULogger logger = ULogger.instance(ctx);
              if (logger.canLog()) logger.log(
                  String.format("c3/data/notifications/update: Exception: %s",
                      e.getMessage()));
            }

            return;
          }
          default:
            break;
        }
      }
    };
  }
}