package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.c3.CustomConfig;
import z1.commons.Const;
import z1.commons.FileLoader;
import z1.commons.def.ParamDef;
import z1.core.Context;
import z1.core.Entity;
import z1.core.utils.FileLoaderStats;
import z1.core.utils.OfferUtils;
import z1.datamonitor.def.MonitorDef;
import z1.datamonitor.monitors.MonitorFactory;
import z1.datamonitor.monitors.offermonitors.CouponInventoryMonitorImpl;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class OfferHandler implements CommandHandlerFactory
{

  public static final String ALL = "all";

  public enum GetCommand
  {
    all,
    id,
    verifyname,
    uploadstats,
    couponinfo,
    matchjourneys
  }

  public enum PostCommand
  {
    create,
    update,
    delete,
    uploadcsv
  }

  final static JsonMarshaller jm = Const.jsonMarshaller;

  @Override
  public CommandHandler get()
  {
    return (uctx, pathParts, req, resp) -> {

      if (z1.commons.Utils.isMarketPlace)
      {
        return;
      }

      resp.setContentType("application/json");
      resp.setCharacterEncoding("UTF-8");

      GetCommand command = GetCommand.valueOf(pathParts[0]);

      String subpart = null;
      if (pathParts.length >= 2)
      {
        subpart = pathParts[1];
      }

      switch (command)
      {
        // c3/data/offers/all
        case all:
        {
          List<CustomConfig> ccList = CustomConfig.loadAll(uctx,
              CustomConfig.Type.offer);
          if (ccList == null || ccList.isEmpty())
          {
            resp.getWriter().print(jm.serialize(Collections.emptyList()));
          }
          else
          {
            List<Map<String, Object>> offers = ccList.stream().map(offer -> {
              Map<String, Object> res = jm.readAsMap(offer.getPayload());
              res.put("id", offer.getId());
              // Only send published jouney info
              res.put("matchedJourneys", OfferUtils.matchJourneysForOfferId(uctx, offer.getId(), false));
              return res;
            }).collect(Collectors.toList());
            resp.getWriter().print(jm.serialize(offers));
          }
          break;
        }
        // c3/data/offers/id?id=<ID>
        case id:
        {
          String id = req.getParameter("id");

          if (id == null || id.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, "'id' missing.");
            resp.getWriter().print(msg);
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, id,
              CustomConfig.Type.offer);

          if (cc == null)
          {
            resp.getWriter().print(jm.serialize(Collections.emptyMap()));
            return;
          }

          Map<String, Object> res = jm.readAsMap(cc.getPayload());
          res.put("id", cc.getId());
          // Only send published jouney info
          res.put("matchedJourneys", OfferUtils.matchJourneysForOfferId(uctx, cc.getId(), false));
          resp.getWriter().print(jm.serialize(res));

          break;
        }
        // c3/data/offers/verifyname?name=<name>
        case verifyname:
        {
          String name = req.getParameter("name");

          if (name == null || name.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "'name' param missing.");
            resp.getWriter().print(msg);
            return;
          }

          ResponseMessage msg = new ResponseMessage(uctx,
              nameAlreadyExists(uctx, name) ?
                  ResponseMessage.Status.success :
                  ResponseMessage.Status.fail);

          resp.getWriter().print(msg);
          break;
        }

        // c3/data/offers/couponinfo/all
        // c3/data/offers/couponinfo?id=<id>
        case couponinfo:
        {
          String id = ALL.equalsIgnoreCase(subpart) ? ALL : req.getParameter("id");

          if (id == null || id.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, "'id' missing.");
            resp.getWriter().print(msg);
            return;
          }

          try
          {
            if (ALL.equals(id))
            {
              resp.getWriter()
                  .print(jm.serialize(OfferUtils.getOfferCouponsInfoAll(uctx)));
            }
            else
            {
              resp.getWriter().print(
                  jm.serialize(OfferUtils.getOfferCouponsInfo(uctx, id)));
            }
          }
          catch (Exception ex)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed);
            resp.getWriter().print(msg);
            return;
          }
          break;
        }
        // c3/data/offers/matchjourneys/all[?ignorejnystate=true/false]
        // c3/data/offers/matchjourneys?id=<id>[&ignorejnystate=true/false]
        case matchjourneys:
        {
          String id = ALL.equalsIgnoreCase(subpart) ? ALL : req.getParameter("id");
          String ignJs = req.getParameter("ignorejnystate");

          boolean ignoreJnyState = ignJs == null || Boolean.parseBoolean(ignJs);

          if (id == null || id.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, "'id' missing.");
            resp.getWriter().print(msg);
            return;
          }

          try
          {
            if (ALL.equals(id))
            {
              resp.getWriter()
                  .print(jm.serialize(OfferUtils.matchJourneysForAllOffers(uctx, ignoreJnyState)));
            }
            else
            {
              resp.getWriter().print(jm.serialize(OfferUtils.matchJourneysForOfferId(uctx, id, ignoreJnyState)));
            }
          }
          catch (Exception ex)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed);
            resp.getWriter().print(msg);
            return;
          }
          break;
        }
        default:
        {
          ResponseMessage msg = new ResponseMessage(uctx,
              ResponseMessage.Status.fail,
              ResponseMessage.Type.requestProcessingFailed,
              "Unkown command: " + command.name());
          resp.getWriter().print(msg);
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return (uctx, pathParts, req, resp) -> {
      if (z1.commons.Utils.isMarketPlace)
      {
        return;
      }

      resp.setContentType("application/json");
      resp.setCharacterEncoding("UTF-8");

      PostCommand command = PostCommand.valueOf(pathParts[0]);

      switch (command)
      {
        // c3/data/offers/create
        case create:
        {
          String pl = ServletUtil.getPayload(req);
          Map<String, Object> plm = jm.readAsMap(pl);

          String name = (String) plm.get("name");

          boolean multiuse = plm.containsKey(
              "multiuse") && Boolean.parseBoolean(
              String.valueOf(plm.get("multiuse")));

          if (name == null || name.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "'name' missing.");
            resp.getWriter().print(msg);
            return;
          }

          if (nameAlreadyExists(uctx, name))
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "Offer with same name already present");
            resp.getWriter().print(msg);
            return;
          }

          CustomConfig cc = CustomConfig.create(uctx, CustomConfig.Type.offer);
          cc.setName(name);
          cc.setPayload(pl);
          cc.save();

          // Multiuse coupons dont have an assocaited entity.
          if (!multiuse)
          {
            OfferUtils.createOfferEntity(uctx, cc.getId(), name);
          }

          resp.getWriter().print(
              new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone, cc.getId()));

          ArtifactAudit.newInstance(uctx, ArtifactAudit.ItemTypes.offers,
              cc.getId(), name, ArtifactAudit.Operations.create).save();

          break;
        }
        // c3/data/offers/update?id=<ID>
        case update:
        {
          String id = req.getParameter("id");

          if (id == null || id.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, "'id' missing.");
            resp.getWriter().print(msg);
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, id, CustomConfig.Type.offer);
          if (cc == null)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "No config found with id '" + id + "'");
            resp.getWriter().print(msg);
            return;
          }

          String pl = ServletUtil.getPayload(req);
          Map<String, Object> plm = jm.readAsMap(pl);
          String name = (String) plm.get("name");
          boolean multiuse = plm.containsKey(
              "multiuse") && Boolean.parseBoolean(
              String.valueOf(plm.get("multiuse")));

          if (!nameAlreadyExists(uctx, name))
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "Offer with same id already present");
            resp.getWriter().print(msg);
            return;
          }

          cc.setName(name);
          cc.setPayload(pl);
          cc.save();

          // Multiuse coupons dont have an assocaited entity.
          if (!multiuse)
          {
            OfferUtils.createOfferEntity(uctx, cc.getId(), name);
          }
          resp.getWriter()
              .print(new ResponseMessage(uctx, ResponseMessage.Status.success));

          ArtifactAudit.newInstance(uctx, ArtifactAudit.ItemTypes.offers,
              cc.getId(), name, ArtifactAudit.Operations.updateconfig).save();

          break;
        }
        // c3/data/offers/delete?id=<ID>
        case delete:
        {
          String id = req.getParameter("id");
          if (id == null || id.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, "'id' missing.");
            resp.getWriter().print(msg);
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, id,
              CustomConfig.Type.offer);
          if (cc == null)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "No config found with id '" + id + "'");
            resp.getWriter().print(msg);
            return;
          }

          // Validate if offer is being used in some TE,SE
          List<Map<String, Object>> jnyList = OfferUtils.matchJourneysForOfferId(uctx, id, true);
          if (!jnyList.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.resourceDeletionFailed,
                jm.serialize(jnyList));
            resp.getWriter().print(msg);
            return;
          }

          String name = cc.getName();
          cc.delete();

          // Delete associated entity and all of its data if any
          Entity.deleteAll(Context.getInstance(uctx), id);
          CustomConfig.delete(uctx, id, CustomConfig.Type.entity);
          FileLoaderStats.deleteStats(uctx, id, FileLoader.OpType.download);
          FileLoaderStats.deleteStats(uctx, id, FileLoader.OpType.upload);
          ArtifactAudit.newInstance(uctx, ArtifactAudit.ItemTypes.entity, id, id,
              ArtifactAudit.Operations.delete).save();

          resp.getWriter()
              .print(new ResponseMessage(uctx, ResponseMessage.Status.success));

          ArtifactAudit.newInstance(uctx, ArtifactAudit.ItemTypes.offers,
              cc.getId(), name, ArtifactAudit.Operations.delete).save();

          break;
        }
        case uploadcsv:
        {
          String offerId = req.getParameter("id");

          if (offerId != null && offerId.startsWith(OfferUtils.OFFER_ENT_PFX))
          {
            offerId = offerId.substring(OfferUtils.OFFER_ENT_PFX.length());
          }

          if (offerId == null || offerId.trim().isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, "'id' missing.");
            resp.getWriter().print(msg);
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, offerId,
              CustomConfig.Type.offer);
          if (cc == null)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "No config found with id '" + offerId + "'");
            resp.getWriter().print(msg);
            return;
          }

          @SuppressWarnings("unchecked") List<FileItem> items =
              new ServletFileUpload(
              new DiskFileItemFactory()).parseRequest(req);

          byte[] fileContent = null;

          String fileName = "";
          for (FileItem item : items)
          {
            String fldName = item.getFieldName();
            if ("file".equals(fldName))
            {
              fileContent = item.get();
            }
            if ("name".equals(fldName))
            {
              fileName = new String(item.get());
            }
          }

          if (fileName.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail, ResponseMessage.Type.uploadFailed,
                "name attribute is missing");
            resp.getWriter().print(msg);
            return;
          }

          if (fileContent == null)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail, ResponseMessage.Type.uploadFailed,
                "File content is null");
            resp.getWriter().print(msg);
            return;
          }

          InputStream is = new ByteArrayInputStream(fileContent);

          // Offer entities are to be prefixed with spl name
          String offerEntId = OfferUtils.OFFER_ENT_PFX + offerId;

          //launch new thread to handle work
          Thread uploadT = new Thread(
              new FileLoader(uctx, FileLoader.OpType.upload,
                  FileLoader.ObjType.entity, offerEntId, is, fileName, null));
          uploadT.setDaemon(true);
          uploadT.start();

          // reset the last_sent_threshold param from coupon_inventory_monitor
          CustomConfig customConfig = CustomConfig.load(uctx,
              MonitorFactory.COUPON_INVENTORY_MON_ID,
              CustomConfig.Type.datamonitor, true);
          if (customConfig != null)
          {
            MonitorDef monitorDef = jm.readAsObject(customConfig.getPayload(),
                MonitorDef.class);
            List<ParamDef> paramDefs = monitorDef.getParams();
            paramDefs.removeIf(paramDef -> paramDef.getName()
                .equals(CouponInventoryMonitorImpl.LAST_SENT_THRESHOLD));
            customConfig.setPayload(jm.serialize(monitorDef));
            customConfig.save(true);
          }

          ResponseMessage msg = new ResponseMessage(uctx,
              ResponseMessage.Status.processing,
              ResponseMessage.Type.uploadProcessing,
              "Processing upload. Monitor progress by selecting 'Upload " +
                  "Status'");
          resp.getWriter().print(msg);

          ArtifactAudit.newInstance(uctx, ArtifactAudit.ItemTypes.offers,
              offerId, cc.getName(), ArtifactAudit.Operations.upload).save();

          break;
        }
        default:
        {
          ResponseMessage msg = new ResponseMessage(uctx,
              ResponseMessage.Status.fail,
              ResponseMessage.Type.requestProcessingFailed,
              "Unkown command: " + command.name());
          resp.getWriter().print(msg);
        }
      }
    };
  }

  private static boolean nameAlreadyExists(UContext uctx, String name)
  {
    if (name == null || name.trim().isEmpty()) return false;

    List<CustomConfig> ccList = CustomConfig.loadAll(uctx,
        CustomConfig.Type.offer);

    if (ccList == null) return false;

    return ccList.stream().anyMatch(cc -> name.equals(cc.getName()));
  }
}
