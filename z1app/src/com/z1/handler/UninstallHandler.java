package com.z1.handler;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobQueue;
import udichi.core.queue.UnsupportedJobException;
import udichi.core.util.JsonMarshaller;
import com.udichi.extension.Config;

import z1.commons.Utils;
import z1.uninstall.UninstallTracker;

/**
 * Handles the uninstall tracker.
 * 
 * <AUTHOR>
 * 
 */
public class UninstallHandler implements CommandHandlerFactory
{
  @Override
  public CommandHandler get()
  {

    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        List<String> results = new ArrayList<>();
        ULogger logger = ctx.getLogger(UninstallHandler.class);

        // Uninstall job worker
        try
        {
          JobQueue jQ = JobQueue.getInstance("UninstallTrackHandler",
              z1.uninstall.UninstallTrackHandler.class);
          Job ujob = jQ.createJob("UninstallTrackHandler", ctx);
          ujob.setPriority(Priority.counter);
          jQ.submit(ujob);
        }
        catch (UnsupportedJobException e)
        {
          if (logger.canLog())
          {
            logger.log("Exception: Uninstall Handler failed for "
                + ctx.getNamespace() + "." + e.toString());
          }

        }

        results.add("Job Schedular for tracking uninstall Created...");
        String json = new JsonMarshaller().serialize(results);
        resp.getWriter().print(json);
        return;
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // Remove all the entries from relation table based on the type
        List<String> results = new ArrayList<>();
        String type = req.getParameter("deleteType");
        if (type != null && !type.isEmpty())
        {
          UninstallTracker handler = new UninstallTracker();
          Config cfg = new Config();
          cfg.setNamespace(ctx.getNamespace());
          handler.deleteRegisteredRows(cfg, type);
          results.add("Success");
        }
        else
        {
          results
              .add("activityName not specified. eg. uninstall or registration");
        }
        String json = new JsonMarshaller().serialize(results);
        resp.getWriter().print(json);
        return;
      }
    };
  }
}