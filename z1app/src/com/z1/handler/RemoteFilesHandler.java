package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import udichi.core.util.JsonMarshaller;
import z1.commons.Const;
import z1.commons.EntityRemoteUploadWorker;

public class RemoteFilesHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    all
  }

  final static JsonMarshaller jm = Const.jsonMarshaller;

  @Override
  public CommandHandler get()
  {
    return (uctx, pathParts, req, resp) -> {
      String cStr = pathParts[0];
      RemoteFilesHandler.GetCommand command;
      command = RemoteFilesHandler.GetCommand.valueOf(cStr);

      switch (command)
      {
        // c3/data/remotefiles/all
        case all:
        {
          resp.getWriter().print(EntityRemoteUploadWorker.getRemoteFileList(uctx));
          return;
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return (uctx, pathParts, req, resp) -> {
    };
  }
}
