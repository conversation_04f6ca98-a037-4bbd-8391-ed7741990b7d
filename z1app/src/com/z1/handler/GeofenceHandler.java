package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.SystemConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.core.utils.FileLoaderStats;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;

public class GeofenceHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    id
  }

  private enum PostCommand
  {
    create,
    suspend,
    activate,
    update,
    delete,
    updateconfig,
    loadconfig,
    publish,
    unpublish,
    invalidateCache
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        
        switch (command)
        {
          // c3/data/geofence/all
          case all:
          {
            List<CustomConfig> ccList = CustomConfig
                .forceLoadAll(ctx, CustomConfig.Type.geoFence).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());

            if (ccList.isEmpty())
            {
              resp.getWriter().print("[]");
            }
            else
            {
              Map<String, Object> output = new HashMap<>();
              List<Map<String, Object>> geofenceList = GeofenceHandler.getGeoFences(ccList);
              output.put("locMap", geofenceList);  
              output.put("proxRadius", SystemConfig.getStringValue(ctx, "z1.geofence.proxradius", "5"));
            
              if (output.size() > 0)
              {
                resp.getWriter()
                    .print(new JsonMarshaller().serialize(output));
              }
              else
              {
                resp.getWriter().print("[]");
              }
            }
            return;
          }

          // c3/data/geofence/id?geofenceName=<geofence-name>
          case id:
          {            
            // This must be a request for a specific item payload.            
            String geofenceName = req.getParameter("geofenceName");
            CustomConfig cc = CustomConfig.load(ctx, geofenceName,
                CustomConfig.Type.geoFence, true);
            if (cc != null)
            {
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> map = jm.readAsMap(cc.getPayload());
              map.put(DefinitionItem.Fields.state.name(),cc.getState());
              cc.updateGeneralStat(map);
          
              resp.getWriter().print(new JsonMarshaller().serialize(map));

              return;
            }
          }
        }
      }
    };
  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/geofence/create
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            String id = java.util.UUID.randomUUID().toString();

            CustomConfig cc = CustomConfig.create(ctx, CustomConfig.Type.geoFence, id);

            if (cc == null) return;

            JsonMarshaller jm = new JsonMarshaller();

            Map<String, Object> map = jm.readAsMap(payload);
            map.put(DefinitionItem.ID, id);
        
            cc.setPayload(jm.serialize(map));
            
            //Set newly created geofence as unpublished.
            cc.save();

            resp.getWriter().print("{ \"id\": \"" + id + "\" }");

            break;
          }
          // c3/data/geofence/update/<name>
          case update:
          {
            String id = req.getParameter("id");
            if (id == null) {
              return;
            }                        

            String payload = ServletUtil.getPayload(req);

            if ((payload == null) || (payload.length() == 0)) return;

            CustomConfig cc = CustomConfig.load(ctx, id,
                CustomConfig.Type.geoFence, true);

            if (cc == null) return;

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> newDef = jm.readAsMap(payload);

            cc.setPayload(jm.serialize(newDef));
            
            cc.getDefItem().getValues().get("state");
            cc.save();
            break;
          }
          // c3/data/geofence/delete/<geofence-id>
          case delete:
          {            
            String id = req.getParameter("id");
            
            req.setAttribute("id", id);
            req.setAttribute("type", CustomConfig.Type.geoFence.name());
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = CustomConfig.Type.geoFence.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(ctx, subParts, req, resp);
            break;
            
          }
          // c3/data/geofence/activate/<geofence-id>
          // c3/data/geofence/publish/<geofence-id>
          case activate:
          case publish:
          {
            String id = req.getParameter("id");            

            CustomConfig cc = CustomConfig.load(ctx, id,
                CustomConfig.Type.geoFence);

            if (cc == null) return;

            cc.save(true);

            resp.getWriter().print("{ \"id\": \"" + id + "\" }");

            break;
          }
          // c3/data/geofence/suspend/<geofence-id>
          // c3/data/geofence/unpublish/<geofence-id>
          case suspend:
          case unpublish:
          {
            String id = req.getParameter("id");

            CustomConfig cc = CustomConfig.load(ctx, id,
                CustomConfig.Type.geoFence, true);

            if (cc == null) return;

            cc.save(false);

            resp.getWriter().print("{ \"id\": \"" + id + "\" }");

            break;
          }
          
          case invalidateCache:
          {
            App.notifyClearCache(ctx.getNamespace(), CustomConfig.Type.geoFence.name());
          }
          
        }
        
        App.notifyClearCache(ctx.getNamespace(), CustomConfig.Type.geoFence.name());

      }

      // //////////////////////////////////////////////////////////////////

    };
  }

  public static ArrayList<Map<String, Object>> getGeoFences(
      List<CustomConfig> ccList)
  {
    if (ccList == null || ccList.size() <= 0)
    {
      return new ArrayList<Map<String, Object>>();
    }

    ArrayList<Map<String, Object>> geofenceList = new ArrayList<Map<String, Object>>();
    JsonMarshaller jm = new JsonMarshaller();
    
    for (CustomConfig c : ccList)
    {
      Map<String, Object> map = jm.readAsMap(c.getPayload());
      map.put(DefinitionItem.Fields.state.name(),c.getState());
      c.updateGeneralStat(map);
      
      geofenceList.add(map);
    }

    return geofenceList;

  }
}
