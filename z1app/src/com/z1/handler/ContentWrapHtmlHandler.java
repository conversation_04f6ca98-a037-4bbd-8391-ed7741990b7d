package com.z1.handler;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.actions.AbstractMobileAction;
import z1.commons.Utils;
import z1.mobile.Formatter;

public class ContentWrapHtmlHandler implements CommandHandlerFactory
{
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts, final HttpServletRequest req,
          final HttpServletResponse resp) throws Exception
      {
       
      }
    };
  }


  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts, final HttpServletRequest req,
          final HttpServletResponse resp) throws Exception
      {
        String payload = ServletUtil.getPayload(req);
        //String module=pathParts[1];
        //String deviceType=pathParts[2];
        //String os=pathParts[3];
        
        String module = req.getParameter("module");
        String deviceType = req.getParameter("deviceType");
        String os = req.getParameter("os");
        
        
        String subCategory="main";
        
        String cssStyle = AbstractMobileAction.getCssStyle(ctx, module, os, deviceType, subCategory); 
        
        String text = Formatter.wrapHtmlForPreview(payload, cssStyle);
        
        String json = new JsonMarshaller().serialize(text);
        resp.getWriter().print(json);
        return;
      }
    };
  }
}