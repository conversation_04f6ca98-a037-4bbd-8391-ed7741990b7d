package com.z1.handler.api;

public final class Const
{
  public static final String SYSRESET_ACTION_NAME = "udc.system.core.ZineOne Processing:SysResetAction";
  public static final String TIMER_EVENT_EXEC_NAME = "udc.system.core.ZineOne Processing:TimerEventExec";
  public static final String API_KEY = "apiKey";
  
  /**
   * Supported GET commands
   */
  public enum GetCommand
  {
    accesstoken,
    chat,
    connect, 
    connectwebsocket,
    kbdoc, // Gets a kb document
    faq,
    kbdoctitle,
    beacons,
    images,
    pncontentchrome,
    pnwebconnect,
    schema
  }

  /**
   * Supported POST commands
   */
  public enum PostCommand
  {
    chat,
    event,
    profile,
    msgRating,
    actionResponse,
    faq,
    pnregister,
    originId,
    location,
    connect,
    endSession
  }

  
}
