package com.z1.handler.api;

import com.z1.Utils.ApiUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.SystemConfig;
import z1.commons.Const;
import z1.commons.Const.DeviceProfileInfo;
import z1.core.Context;
import z1.core.Profile;
import z1.core.profile.session.CommonDataExtractor;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static javax.ws.rs.HttpMethod.GET;
import static javax.ws.rs.HttpMethod.POST;

/**
 * Handles device connections. It handles the connect call from devices to
 * create the payload by constructing the profile as well as other configuration
 * parameters that need to be sent to the devices.
 */
public class ConnectResponseBuilder
{
  private static final String K_LOAD_CONFIG = "loadConfig";
  private static final String K_TTS_CONFIG = "tts";
  private static final String CONFIG = "config";
  private static final String ACTION_MAPPING = "actionMapping";
  private static final String TTL = "TTL";
  private static final String SESSION_TIME = "sessionTime";

  private static final String ACTION_MAPPING_HTML5 = "actionMappingHtml5";
  private static final String Z1_CODE_LIBRARY_TAG = "z1_codeLibraryTag";
  private static final String TAG_NAME = "tagName";

  private static final String ACTIONMAPPING_CHANGED = "actionMappingChanged";
  private static final String SDKPROPS_CHANGED = "sdkPropsChanged";

  private static final String Z1_SDK_VERSION = "z1SDKVersion";

  //

  /**
   * Handles a device connect call.
   * 
   * @param ctx
   *          Current runtime context.
   * @param custId
   *          Target customer ID.
   * @param req
   *          Incoming http request.
   * @return The constructed payload to be sent to the device.
   * @throws Exception
   *           for invalid api keys for the current channel.
   */
  public String build(UContext ctx, String custId, final HttpServletRequest req)
      throws Exception
  {
    return build(ctx, custId, req, null);
  }

  // ...............................................................
  /**
   * Handles a device connect call.
   *
   * @param ctx
   *          Current runtime context.
   * @param custId
   *          Target customer ID.
   * @param req
   *          Incoming http request.
   * @param payload
   *          Incoming payload.
   * @return The constructed payload to be sent to the device.
   * @throws Exception
   *           for invalid api keys for the current channel.
   */
  public String build(UContext ctx, String custId, final HttpServletRequest req,
      Map<String, Object> payload) throws Exception
  {
    final String method = req.getMethod();
    switch (method)
    {
      // The get method is for backward compatibility
      // of old version connection build
      case GET:
      {
        Map<String, Object> connectParams = _getConnectBuild(ctx, req);
        return _connectWithParams(ctx, custId, connectParams);
      }
      case POST:
      {
        Map<String, Object> connectParams = _postConnectBuild(ctx, req,
            payload);
        return _connectWithParams(ctx, custId, connectParams);
      }
      default:
        throw new UException(
            "unsupported http method for connect call: " + method);
    }
  }

  /**
   * Handles a device connect get call.
   *
   * @param ctx
   *          Current runtime context.
   * @param req
   *          Incoming http request.
   * @return The constructed payload to be sent to the device.
   */
  private Map<String, Object> _getConnectBuild(UContext ctx,
      final HttpServletRequest req)
  {
    ULogger logger = ctx.getLogger(getClass());
    String msg = String.format(
        "[CONNECT] GET device connection called. URI: %s, Query: %s",
        req.getRequestURL(), req.getQueryString());
    if (logger.canLog())
    {
      logger.log(msg);
    }
    String queryString = req.getQueryString();
    String[] queryParts = queryString.split("&");
    Map<String, Object> connectParams = new java.util.HashMap<>();
    // Are we pull in the configuration as well?
    connectParams.put(K_LOAD_CONFIG, false);
    // get other keys
    Map<String, String> otherKeys = new java.util.HashMap<>(10);
    Enumeration en = req.getParameterNames();
    while (en.hasMoreElements())
    {
      String key = (String) en.nextElement();
      String val = req.getParameter(key);

      if (key.equalsIgnoreCase(z1.commons.Const.P_CONNECT_DEVICE_ID)
          || key.equalsIgnoreCase(DeviceProfileInfo.os.name())
          || key.equalsIgnoreCase(DeviceProfileInfo.devicetype.name())
          || key.equalsIgnoreCase(K_LOAD_CONFIG)
          || key.equalsIgnoreCase(K_TTS_CONFIG)
          || key.equalsIgnoreCase(Z1_SDK_VERSION))
      {
        _processConnectKeyValue(key, val, connectParams, otherKeys);
      }
      else
      {
        final String EQ = "=";
        for (int i = 0; i < queryParts.length; i++)
        {
          if (!queryParts[i].startsWith(key + "=")) continue;
          int index = queryParts[i].indexOf(EQ);
          if (index >= 0)
          {
            val = queryParts[i].substring(index + 1);
          }
        }
        if (logger.canLog())
        {
          logger.log(String.format(
              "[CONNECT] Other Keys . Key: %s, Value Encoded: %s", key, val));
        }
        _processConnectKeyValue(key, val, connectParams, otherKeys);
      }
    }
    connectParams.put("otherKeys", otherKeys);

    // fetch user-agent
    final String userAgent = req.getHeader(HttpHeaders.USER_AGENT);
    connectParams.put(Const.USER_AGENT, userAgent);

    return connectParams;
  }

  /**
   * Handles a device connect post call.
   *
   * @param ctx
   *          Current runtime context.
   * @param req
   *          Incoming http request.
   * @param payload
   *          the request payload body
   * @return The constructed payload to be sent to the device.
   */
  private Map<String, Object> _postConnectBuild(UContext ctx,
      final HttpServletRequest req, Map<String, Object> payload)
  {
    ULogger logger = ctx.getLogger(getClass());
    logger.log(String.format(
        "[CONNECT] POST device connection called. URI=%s, Query=%s, Payload=%s",
        req.getRequestURL(), req.getQueryString(), payload));

    // get the device id and other keys
    Map<String, String> otherKeys = new java.util.HashMap<>(10);
    Map<String, Object> connectParams = new java.util.HashMap<>();
    connectParams.put(K_LOAD_CONFIG, false);

    for (Map.Entry<String, Object> entry : payload.entrySet())
    {
      String key = entry.getKey();
      Object objVal = entry.getValue();
      String val = (objVal != null) ? objVal.toString() : "";

      // Do not add customerId coming from the payload with otherKeys
      _processConnectKeyValue(key, val, connectParams, otherKeys);
    }
    connectParams.put("otherKeys", otherKeys);

    // fetch user-agent
    final String userAgent = req.getHeader(HttpHeaders.USER_AGENT);
    connectParams.put(Const.USER_AGENT, userAgent);

    return connectParams;
  }

  /**
   * Process the key value for connect call params
   * 
   * @param key
   * @param val
   * @param connectParams
   * @param otherKeys
   */
  private void _processConnectKeyValue(String key, String val,
      Map<String, Object> connectParams, Map<String, String> otherKeys)
  {

    if (key.equalsIgnoreCase(z1.commons.Const.P_CONNECT_DEVICE_ID))
    {
      connectParams.put(z1.commons.Const.P_CONNECT_DEVICE_ID, val);
    }
    else if (key.equalsIgnoreCase(DeviceProfileInfo.os.name()))
    {
      connectParams.put(DeviceProfileInfo.os.name(), val);
    }
    else if (key.equalsIgnoreCase(DeviceProfileInfo.devicetype.name()))
    {
      connectParams.put(DeviceProfileInfo.devicetype.name(), val);
    }
    else if (key.equalsIgnoreCase(K_LOAD_CONFIG))
    {
      connectParams.put(K_LOAD_CONFIG, true);
    }
    else if (key.equalsIgnoreCase(K_TTS_CONFIG))
    {
      if (StringUtils.isNotEmpty(val))
      {
        connectParams.put(K_TTS_CONFIG, val);
      }
    }
    else if (key.equalsIgnoreCase(Z1_SDK_VERSION))
    {
      if (StringUtils.isNotEmpty(val))
      {
        connectParams.put(Z1_SDK_VERSION, val);
      }
    }
    else
    {
      otherKeys.put(key, val);
    }
  }

  /**
   * Shared method for get call and post call, for creating a connection with
   * given parameters
   * 
   * @param ctx
   * @param custId
   * @param connectParams
   * @return
   * @throws Exception
   */
  private String _connectWithParams(UContext ctx, String custId,
      Map<String, Object> connectParams) throws Exception
  {
    ULogger logger = ctx.getLogger(getClass());
    boolean isLoadConfig = (boolean) connectParams.getOrDefault(K_LOAD_CONFIG,
        false);
    String utcTime = (String) connectParams.getOrDefault(K_TTS_CONFIG, "");
    String deviceId = (String) connectParams
        .getOrDefault(z1.commons.Const.P_CONNECT_DEVICE_ID, "");
    String os = (String) connectParams.getOrDefault(DeviceProfileInfo.os.name(),
        "");
    String deviceType = (String) connectParams
        .getOrDefault(DeviceProfileInfo.devicetype.name(), "");
    String sdkVersion = (String) connectParams.getOrDefault(Z1_SDK_VERSION, "");
    final String userAgent = (String) connectParams.get(Const.USER_AGENT);

    Map<String, String> otherKeys = (Map<String, String>) connectParams
        .getOrDefault("otherKeys", new java.util.HashMap<>(10));
    // Check if we have the valid api key for this channel
    if (!ApiUtils.isValidContext(ctx, deviceType, os))
    {
      throw new Exception("Invalid api key.");
    }
    Map<String, Object> ret = new java.util.HashMap<>(2);
    // Before findorCreateProfile check if optOut pIndex entry for this deviceId
    // or customerId
    String pId = null;
    boolean isNewProfile = false;
    // Only check new sdks which send sdkVersion in connect
    if (sdkVersion != null && !sdkVersion.isEmpty()
        && !ApiUtils.isSDKSupportedByPlatform(os, sdkVersion))
    {
      // Send response back with Profile.OPTOUT_PROFILE_ID
      pId = Profile.OPTOUT_PROFILE_ID;
      if (logger.canLog())
      {
        logger.warning("SDK version not supported by platform: " + sdkVersion);
      }
    }
    else
    {
      if (logger.canLog())
      {
        logger
            .log("Device connection processing for SDK version: " + sdkVersion);
      }

      Profile.CreateProfileResult cpRes = Profile
          .identityResolver(Context.getInstance(ctx)).withCustomerId(custId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile().thenUpdateCountStat(ctx);

      if (cpRes.isNull()) return null;

      pId = cpRes.profile().getKeyValue();
      isNewProfile = cpRes.isNewProfile();

      // Fetch the active session information if the cross-subdomain flag is
      // enabled for a given namespace
      Boolean isCrossSubDomainSessEnabled = SystemConfig.getBooleanValue(ctx,
          "z1.enableCrossSubdomainSession", false);
      if (isCrossSubDomainSessEnabled)
      {
        Map<String, Object> activeSessionMap = cpRes.profile().session()
            .getBasicSessionInfo(ctx, pId);
        Map<String, Object> minimalSessionMap = new HashMap<>();

        String userAgentAtSessionStart = (String) activeSessionMap
            .get(Const.USER_AGENT);
        if (userAgent.equals(userAgentAtSessionStart))
        {
          minimalSessionMap.put("sessionTrackerId", activeSessionMap
              .get(CommonDataExtractor.Field.sessionTrackerId.colName));
        }
        ret.put("activeSession", minimalSessionMap);
      }

    }

    logger.log(String.format(
        "[CONNECT] CustomerId=%s SDKVersion=%s SecondaryKey=%s returned ProfileId=%s]",
        custId, sdkVersion, otherKeys, pId));

    if (pId == null) return null;

    ret.put(Profile.ID, custId);
    ret.put(z1.c3.api.Commons.ReqFields.profileId.name(), pId);
    ret.put(z1.c3.api.Commons.ReqFields.firstTimeUser.name(), isNewProfile);
    ret.put(SESSION_TIME,
        SystemConfig.getStringValue(ctx, "z1.sessionTimeGap", "30"));

    if (isLoadConfig)
    { // Checks and sends config data only if the loadconfig requested
      String key = os.toLowerCase() + "." + deviceType.toLowerCase()
          + ".tts.value";
      ret.put(ACTIONMAPPING_CHANGED,
          checkIfTTSExpired(ctx, key, "tags", utcTime));
      ret.put(SDKPROPS_CHANGED,
          checkIfTTSExpired(ctx, key, "sdkprops", utcTime));
      // We will load various configs to send along with the response
      _loadConfigs(ctx, ret, os, deviceType, utcTime, sdkVersion);
    }

    return new JsonMarshaller().serialize(ret);
  }

  // ////////////////////////////////////////////////////////

  // ................................................................
  // Constructs a "config" item to be sent on connect call.
  private void _loadConfigs(UContext ctx, Map<String, Object> data, String os,
      String deviceType, String utcTime, String sdkVersion)
  {
    Map<String, Object> cfg = new java.util.HashMap<>(10);

    String key = os.toLowerCase() + "." + deviceType.toLowerCase()
        + ".tts.value";

    _loadActionMappings(ctx, cfg, os, deviceType, key, utcTime, sdkVersion);

    if (checkIfTTSExpired(ctx, key, "props", utcTime))
    {
      _loadProps(ctx, cfg, os, deviceType);
    }

    _loadSDKProps(ctx, cfg, os, deviceType, key, utcTime, sdkVersion);

    if (!cfg.isEmpty())
    {
      data.put(CONFIG, cfg);
    }
  }

  // ...............................................................
  private void _loadActionMappings(UContext ctx, Map<String, Object> cfg,
      String os, String deviceType, String key, String utcTime,
      String sdkVersion)
  {
    // we will check if BE needs to serve config based on client provided
    // utcTime
    CustomConfig.Type t = null;

    String ttlKey = null;

    if (z1.commons.Const.DeviceOS.html5.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.desktop.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingHtml5Desktop;
      }
      else if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingHtml5Tablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingHtml5Phone;
      }
    }
    else if (z1.commons.Const.DeviceOS.android.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingAndroidTablet;
        ttlKey = "android.tablet.ttl.value";
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingAndroidPhone;
        ttlKey = "android.phone.ttl.value";
      }
    }
    else if (z1.commons.Const.DeviceOS.ios.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingIosTablet;
        ttlKey = "ios.tablet.ttl.value";
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.actionMappingIosPhone;
        ttlKey = "ios.phone.ttl.value";
      }
    }

    Map<String, Object> map = new HashMap<>();
    List<Map<String, Object>> actionMap = new ArrayList<>();
    if (t != null && checkIfTTSExpired(ctx, key, "tags", utcTime))
    {
      List<CustomConfig> ccList = CustomConfig.loadAll(ctx, t);

      if (ccList != null)
      {

        JsonMarshaller jm = new JsonMarshaller();

        // Send only active tag whose payload is non null and non empty.
        // 'suspended' tags are deleted from mongo when user clicks 'Publish
        // Tags' button on C3.
        // Hence, on connect request, also send 'suspended' tags since user must
        // have not published the tags after having deleted them.
        List<CustomConfig> validCCs = ccList.stream().filter(Objects::nonNull)
            .filter(cc -> {
              String pl = cc.getPayload();
              return ((cc.getState().equals("published")
                  || cc.getState().equals("suspended")) && pl != null
                  && !pl.isEmpty());
            }).collect(Collectors.toList());

        // Sort the ActionMapping config list for HTML5 list so that
        // z1_codeLibraryTag
        // is listed first.
        if (t.name().toLowerCase().contains(ACTION_MAPPING_HTML5.toLowerCase()))
        {
          Collections.sort(validCCs, new ActionMappingHtml5Comparator());
        }

        final List<String> notReqKeys = Arrays.asList("state","lastUpdatedBy","lastUpdatedTime");
        validCCs.forEach(cc -> {
          Map<String, Object> payloadMap = jm.readAsMap(cc.getPayload());
          notReqKeys.forEach(payloadMap::remove);
          actionMap.add(payloadMap);
        });
      }

      map.put("actionMap", actionMap);

      obfuscatedMethodNames(ctx, map);

    }
    else
    {
      map.put("actionMap", actionMap);
    }

    String ttl = SystemConfig.getStringValue(ctx, ttlKey, "1440");
    cfg.put(TTL, ttl);

    // Backward compatibility support for HTML5 phase1 TTS which doesn't send
    // sdkVersion
    if (z1.commons.Const.DeviceOS.html5.name().equalsIgnoreCase(os)
        && sdkVersion == null && (actionMap == null || actionMap.isEmpty()))
    {
      return;
    }

    cfg.put(ACTION_MAPPING, map.get("actionMap"));
  }

  private void _loadProps(UContext ctx, Map<String, Object> cfg, String os,
      String deviceType)
  {
    CustomConfig.Type t = null;
    final String PROPS = "props";

    if (z1.commons.Const.DeviceOS.html5.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.desktop.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsHtml5Desktop;
      }
      else if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsHtml5Tablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsHtml5Phone;
      }
    }
    else if (z1.commons.Const.DeviceOS.android.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsAndroidTablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsAndroidPhone;
      }
    }
    else if (z1.commons.Const.DeviceOS.ios.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsIosTablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.propsIosPhone;
      }
    }

    if (t == null) return;

    CustomConfig cc = CustomConfig.load(ctx, t);
    if (cc == null) return;

    String payload = cc.getPayload();
    if (payload == null || payload.isEmpty()) return;

    @SuppressWarnings("unchecked")
    Map<String, Object> map = new JsonMarshaller().readAsObject(payload,
        Map.class);

    cfg.put(PROPS, map);
  }

  @SuppressWarnings("unchecked")
  private void _loadSDKProps(UContext ctx, Map<String, Object> cfg, String os,
      String deviceType, String key, String utcTime, String sdkVersion)
  {

    CustomConfig.Type t = null;
    final String SDKPROPS = "sdkprops";

    if (z1.commons.Const.DeviceOS.html5.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.desktop.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsHtml5Desktop;
      }
      else if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsHtml5Tablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsHtml5Phone;
      }
    }
    else if (z1.commons.Const.DeviceOS.android.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsAndroidTablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsAndroidPhone;
      }
    }
    else if (z1.commons.Const.DeviceOS.ios.name().equalsIgnoreCase(os))
    {
      if (z1.commons.Const.DeviceType.tablet.name()
          .equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsIosTablet;
      }
      if (z1.commons.Const.DeviceType.phone.name().equalsIgnoreCase(deviceType))
      {
        t = CustomConfig.Type.sdkpropsIosPhone;
      }
    }

    Map<String, Object> map = null;
    if (t != null && checkIfTTSExpired(ctx, key, "sdkprops", utcTime))
    {

      CustomConfig cc = CustomConfig.load(ctx, t);
      if (cc != null)
      {
        String payload = cc.getPayload();
        if (payload != null && !payload.isEmpty())
        {
          map = new JsonMarshaller().readAsObject(payload, Map.class);
        }
      }
    }
    // Backward compatibility support for HTML5 SDK with TTS which doesn't send
    // sdkVersion
    if (z1.commons.Const.DeviceOS.html5.name().equalsIgnoreCase(os)
        && sdkVersion == null && (map == null || map.isEmpty()))
    {
      return;
    }

    if (map == null) map = new HashMap<>();
    cfg.put(SDKPROPS, map);
  }

  @SuppressWarnings("unchecked")
  private void obfuscatedMethodNames(UContext ctx, Map<String, Object> map)
  {
    CustomConfig ccs = CustomConfig.load(ctx, Type.androidMethodsMappingConfig);
    if (ccs != null)
    {
      String payload = ccs.getPayload();
      Map<String, Object> methodObsMap = new JsonMarshaller()
          .readAsObject(payload, Map.class);
      List<Map<String, Object>> list = (List<Map<String, Object>>) map
          .get("actionMap");
      for (Map<String, Object> m : list)
      {
        Map<String, Object> v = (Map<String, Object>) m.get("domSelector");
        if (v != null)
        {
          String type = (String) v.get("type");
          if (type.equals("method"))
          {
            String methodToObfuscate = (String) v.get("val");
            String obfuscatedName = (String) methodObsMap
                .get(methodToObfuscate);
            if (obfuscatedName != null)
            {
              v.put("val", obfuscatedName);
            }
          }
        }
      }
    }
  }

  /**
   * Check if Time to serve config is expired.
   * 
   * @param uctx
   * @param key
   * @param stateType
   * @param provUtcTime
   * @return
   */
  private boolean checkIfTTSExpired(UContext uctx, String key, String stateType,
      String provUtcTime)
  {

    if (provUtcTime == null || provUtcTime.isEmpty()) return true;
    @SuppressWarnings("unchecked")
    Map<String, Object> map = (Map<String, Object>) SystemConfig.getValue(uctx,
        key);
    if (map != null && map.containsKey(stateType))
    {
      @SuppressWarnings("unchecked")
      Map<String, Object> map2 = (Map<String, Object>) map.get(stateType);
      long utcTime = (long) map2.get("utcTime");
      if (Long.parseLong(provUtcTime) < utcTime)
      {
        return true;
      }
    }
    return false;
  }

  /**
   * Compare ActionMapping config, so that when sorted, z1_codeLibraryTag config
   * will be on the top of list.
   *
   */
  public static class ActionMappingHtml5Comparator
      implements Comparator<CustomConfig>
  {
    private static JsonMarshaller jm = new JsonMarshaller();

    @Override
    public int compare(CustomConfig o1, CustomConfig o2)
    {
      String o1Pl = o1.getPayload();
      String o2Pl = o2.getPayload();
      if (o1Pl == null || o1Pl.isEmpty() || o2Pl == null || o2Pl.isEmpty())
        return 0;
      Map<String, Object> o1PlMap = jm.readAsMap(o1Pl);
      String o1TagName = (o1PlMap != null) ? (String) o1PlMap.get(TAG_NAME)
          : null;
      if (o1TagName != null && o1TagName.equals(Z1_CODE_LIBRARY_TAG)) return -1;
      Map<String, Object> o2PlMap = jm.readAsMap(o2Pl);
      String o2TagName = (o2PlMap != null) ? (String) o2PlMap.get(TAG_NAME)
          : null;
      if (o2PlMap != null && o2TagName.equals(Z1_CODE_LIBRARY_TAG)) return 1;
      return 0;
    }
  }

}
