package com.z1.handler.api;

import com.z1.C3ApiServlet;
import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.ServletError;
import com.z1.Utils.ApiUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHeaders;
import org.json.JSONArray;
import org.json.JSONObject;
import udichi.core.ApiKey;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobQueue;
import udichi.core.queue.UnsupportedJobException;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.EventHandler;
import z1.actions.AbstractMobileAction;
import z1.actions.ActionUtils;
import z1.c3.AbstractSignal;
import z1.c3.CustomConfig;
import z1.c3.EventInfo;
import z1.c3.FAQ;
import z1.c3.SystemConfig;
import z1.c3.api.Commons;
import z1.c3.api.Commons.DeviceInfo;
import z1.c3.api.Commons.ReqFields;
import z1.commons.Const.DeviceProfileInfo;
import z1.commons.HandlerUtils;
import z1.commons.Normalizer;
import z1.commons.Utils;
import z1.core.ActionIndex;
import z1.core.ActionLog;
import z1.core.Context;
import z1.core.Profile;
import z1.core.Relation;
import z1.core.Type.IndexType;
import z1.core.profile.ProfileCacheNotifier;
import z1.core.profile.ProfileService;
import z1.core.profile.session.ProfileSession;
import z1.core.profile.session.termination.SessionEndRequestHandler;
import z1.core.profile.session.termination.SessionEntityManager;
import z1.core.utils.SessionUtils;
import z1.core.utils.TimeUtils;
import z1.domains.DomainHandler;
import z1.domains.ecom.EcomDomainHandler;
import z1.kb.ContentUtil;
import z1.kb.KBDeviceInfo;
import z1.kb.KBDocStatus;
import z1.kb.KBDocumentRetreiver;
import z1.kb.KBResponse;
import z1.kb.KBResponse.ContentTuple;
import z1.kb.KBStoreFactory;
import z1.push.PushRegistrationRelationHandler.PushRegActionType;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class V1Handler implements CommandHandlerFactory
{
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/api/v1/xxx
        String cStr = pathParts[0];
        Const.GetCommand command;
        try
        {
          command = Const.GetCommand.valueOf(cStr.toLowerCase());
        }
        catch (Throwable e)
        {
          return;
        }

        ULogger logger = ctx.getLogger(getClass());
        switch (command)
        {
          // c3/api/v1/connect/<unique_id>?deviceId=<device_id>[&wstoken=<token_id>&devicetype=<device
          // type>&os=<os>&loadConfig]
          case connect:
          {
            String custId = pathParts[1];
            _handleConnect(ctx, custId, req, resp, null);

            return;
          }

          // c3/api/v1/connectwebsocket/<device_id>
          case connectwebsocket:
          {
            String ret = HandlerUtils.createWebsocket(ctx, pathParts[1]);
            if (ret != null) resp.getWriter().print(ret);
            return;
          }
          // c3/api/v1/accesstoken?expires=<secs>&id=<name>
          case accesstoken:
          {
            ctx.getLogger(z1.commons.Const.TRACE_CONTEXT)
                .log("Access Token Requested");
            String result = "{}";
            int secs = Integer.parseInt(req.getParameter("expires"));
            String clientid = req.getParameter("clientid");
            if (clientid == null) clientid = " ";
            String apiKey = req.getHeader(C3ApiServlet.HEADER_APIKEY);

            if (apiKey != null)
            {
              ApiKey key = ApiKey.get(ctx, apiKey);
              if (key != null && key.isActive())
              {
                String expiryTime = new TimeUtils().getNextTime(secs,
                    TimeUtils.TimeUnit.second);
                String newtoken = expiryTime + "|" + apiKey + "|" + clientid;

                String encryptedToken = new z1.commons.encryption.Encryption()
                    .encryptAccessToken(newtoken);

                // URL encode the token
                String encodedToken = URLEncoder.encode(encryptedToken,
                    "UTF-8");

                // Append the hostname and apply base64 encoding
                String hostname = req.getServerName();
                String tokenWithHost = new z1.commons.encryption.Encryption()
                    .encodeBase64(
                        String.format("%s|%s", hostname, encodedToken));

                result = "{\"accesstoken\"" + ":" + "\"" + tokenWithHost
                    + "\"}";

              }
              else
              {
                result = "{\"status\": \"fail\",\"reason\": \"API key not found or active.\"}";
              }
            }
            else
            {
              result = "{\"status\": \"fail\",\"reason\": \"API key not provided in the header.\"}";
            }
            resp.getWriter().print(result);
            return;
          }
          case chat:
          {
            // Create an array to pass for the relevant commands
            String[] subParts = new String[pathParts.length - 1];
            int j = 0;
            for (int i = 1; i < pathParts.length; i++, j++)
            {
              subParts[j] = pathParts[i];
            }
            new com.z1.handler.ChatHandler().get().handle(ctx, subParts, req,
                resp);
            return;
          }
          // c3/api/v1/kbdoc/<docid>?os=<ios|android>&type=<tablet|phone>
          case kbdoc:
          {
            String docId = pathParts[1];
            String language = req.getParameter("language");
            String status = req.getParameter("status");
            String osStr = req.getParameter(Commons.DeviceInfo.os.name());
            String typeStr = req
                .getParameter(Commons.DeviceInfo.devicetype.name());

            KBDocStatus docStatus = KBDocStatus.published;
            if (status != null) docStatus = KBDocStatus.valueOf(status);

            KBResponse res = KBDocumentRetreiver.getContent(ctx, docId);
            if (res == null) return;

            switch (res.getContentType())
            {
              case text:
              case url:
              case unknown:
              case imageurl:
              case videourl:
              {
                resp.setContentType("text/html");
                break;
              }
              default:
                break;
            }

            String payload = "Error occurred when retrieving document.";
            if (res.getContent() == null) // for documents with different
                                          // language versions and status per
                                          // language
            {
              ContentTuple ct = res.getContentTuple(language);
              if (ct == null || ct.getStatus() != docStatus)
              {
                ct = res.getContentTuple(KBResponse.DEFAULT_LANGUAGE);
              }

              if (ct != null)
              {
                // populate KBResponse content details
                res.populateContentDetails(ct);
                payload = res.getContent();
              }

            }
            else
            {
              payload = res.getContent();
            }

            if (req.getParameter("raw") != null)
            {
              if (res.getContentType().isUrlType())
              {
                res.setURL(res.getContent());
              }
              Map<String, Object> map = new HashMap<>();
              res.populate(map);
              resp.getWriter().print(new JsonMarshaller().serializeMap(map));
            }
            else
            {
              payload = _createFullHtml(ctx, payload, osStr, typeStr);
              resp.getWriter().print(payload);
            }

            return;

          }
          case images:
          {
            // Create an array to pass for the relevant commands
            String[] subParts = new String[pathParts.length - 1];
            int j = 0;
            for (int i = 1; i < pathParts.length; i++, j++)
            {
              subParts[j] = pathParts[i];
            }
            new com.z1.handler.ImageDataHandler().get().handle(ctx, subParts,
                req, resp);
            return;
          }
          case kbdoctitle:
          {
            String keyword = req.getParameter("keyword");
            Map<String, Object> input = new HashMap<>();
            input.put("status", KBDocStatus.published);

            KBDeviceInfo devInfo = new KBDeviceInfo(); // we need to filter so
                                                       // need to send this as
                                                       // non-null
            String deviceOS = req.getParameter(DeviceProfileInfo.os.name());
            if (deviceOS != null)
            {
              devInfo.setDeviceOS(KBDeviceInfo.OS.parse(deviceOS));
            }
            input.put("device", devInfo);

            Map<String, String> searchRes = KBStoreFactory.getDefaultKBStore()
                .searchByDocTitle(ctx, keyword, input);
            if (keyword != null)
            {
              String payload = new JsonMarshaller().serialize(searchRes);
              resp.getWriter().print(payload);
            }
            else
            {
              // only titles are sent if there is no keyword, no associated
              // docid sent
              for (Map.Entry<String, String> entry : searchRes.entrySet())
              {
                resp.getWriter().print(entry.getValue() + "\n");
              }
            }
            return;
          }
          case faq:
          {
            // language
            String language = req.getParameter("language");
            if (language == null) language = KBResponse.DEFAULT_LANGUAGE;

            // devInfo
            KBDeviceInfo devInfo = null;
            String deviceOS = req.getParameter(DeviceProfileInfo.os.name());
            if (deviceOS != null)
            {
              devInfo = new KBDeviceInfo();
              devInfo.setDeviceOS(KBDeviceInfo.OS.valueOf(deviceOS));
            }

            Long tsDevice = null;
            String tsDeviceStr = req.getParameter("ts_device");
            if (tsDeviceStr != null)
            {
              tsDevice = Long.parseLong(tsDeviceStr);
            }

            FAQ f = FAQ.load(ctx);

            String payload = null;
            if (devInfo != null)
            {
              if (f == null)
              {
                resp.getWriter().print("{}");
                return;
              }
              Boolean showEnglishAsDefault = SystemConfig.getBooleanValue(ctx,
                  "z1.content.showEnglishAsDefault", true);
              payload = ContentUtil.getFAQForDevice(ctx, f, language, devInfo,
                  tsDevice, showEnglishAsDefault);
            }
            else
            {
              if (f == null)
              {
                resp.getWriter().print("[]");
                return;
              }
              ContentUtil.checkAndUpdateFAQPayload(ctx, f);
              payload = ContentUtil.getFAQForAdmin(ctx, f);
            }

            resp.getWriter().print(payload);
            return;
          }
          case beacons:
          {
            String beaconList = SystemConfig
                .getStringValue(ctx, "z1.beacons", "").trim();
            List<String> bl = new ArrayList<>();
            if (!beaconList.isEmpty())
            {
              String[] bAll = beaconList.split(",");
              for (String b : bAll)
              {
                bl.add(b.trim());
              }
            }
            resp.getWriter().print(new JsonMarshaller().serialize(bl));
            // resp.getWriter().print("["+beaconList+"]");
            return;
          }
          // c3/api/v1/pnwebconnect/ - Implemented 8400, Deprecated 21281
          case pncontentchrome:
          case pnwebconnect:
          {
            resp.getWriter().print("{}");
            return;
          }
          // c3/api/v1/schema/eventschema?apikey={{apikey}}
          // c3/api/v1/schema/datatypes?apikey={{apikey}}
          // c3/api/v1/schema/datatypes?id={{complextype}}&apikey={{apikey}}
          case schema:
          {
            // drop "schema" from path parts.
            String[] subParts = Arrays.copyOfRange(pathParts, 1, pathParts.length);
            new com.z1.handler.SchemaHandler().get().handle(ctx, subParts, req, resp);
            return;
          }
          default:
            break;
        }

      }
    };

  }

  // ............................................................
  // POST Implementation

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length == 0) return;
        String cStr = pathParts[0];
        Const.PostCommand command = Const.PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/api/v1/connect with
          // {
          // "customerId": <customerId>,
          // "deviceId" : <device_id>, "wstoken"=<token_id>,
          // "devicetype": <device_type>, "os"=<os>, loadConfig=<true/false>...
          // } in the payload
          case connect:
          {

            String payload = ServletUtil.getPayload(req);
            Map<String, Object> pMap = z1.commons.Const.jsonMarshaller
                .readAsMap(payload);
            // Do not pass customerId as part of the payload system will
            // consider as a secondary key
            String custId = (String) pMap.remove("customerId");
            _handleConnect(ctx, custId, req, resp, pMap);
            return;
          }

          // c3/api/v1/event
          // -----------------------------
          case event:
          {
            // The payload will have a list of maps encoded in JSON.
            // Each map must have a mandatory entry called "event" that holds
            // the event name. All other entries are event parameters.
            ULogger logger = ctx.getLogger(V1Handler.class);

            if (logger.canLog())
            {
              String apiKey = (String) ctx.get(Commons.ReqFields.apiKey.name());
              logger.log("Event received for ApiKey = " + apiKey);
            }

            String payload = ServletUtil.getPayload(req);
            if (payload != null)
            {
              logger.info(
                  String.format("Event payload length = %d", payload.length()));
              if (logger.canLog())
              {
                logger.log("SDK event payload: " + payload);
              }
            }

            // check event payload size
            if (!HandlerUtils.isValidPayloadSize(payload))
            {
              logger.warning("Event payload size limit exceeded.");
              return;
            }

            // Load the list of events from the payload
            List<Map<String, Object>> inputEList = new JsonMarshaller()
                .readAsObject(payload, List.class);
            if ((inputEList == null) || (inputEList.isEmpty()))
            {
              logger.log(
                  "Event end point called with no payload for events. Ignoring.");
              return;
            }

            // Create a filtered list - taking out blacklisted events, non
            // suppported browser events etc
            List<Map<String, Object>> eList = inputEList.stream()
                .filter(e -> new EventInfo(ctx)
                    .isKnownEvent((String) e.get(AbstractSignal.EVENT)))
                .filter(e -> !AbstractSignal.isEventBlacklisted(ctx,
                    (String) e.get(AbstractSignal.EVENT)))
                .filter(e -> {
                  boolean browserSupported = isBrowserSupported(ctx, e);
                  if (!browserSupported && logger.canLog())
                  {
                    logger.log(
                        "The following event is blocked as it fails browser support check: "
                            + e.get("event"));
                  }
                  return browserSupported;
                }).collect(java.util.stream.Collectors.toList());

            if (eList == null || eList.isEmpty()) return;

            String profileId = (String) eList.get(0).get("profileId");
            // Check if profile is unique optOut profile
            if (profileId != null
                && profileId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
            {
              if (logger.canLog())
              {
                logger.log("Event is coming from unique optout profileId: "
                    + Profile.OPTOUT_PROFILE_ID + ". Ignoring.");
              }
              return;
            }

            // -:NOTE:- After all supported SDKs start to send 'sdkVersion' in
            // connect call, we can safely remove isSdkBlackListedOrObsolete()
            // check on every event call. Any obsolete or blacklisted sdks
            // will be rejected at connect request only.
            Map<String, Object> profileMap = ((Map<String, Object>) eList.get(0)
                .get("z1_profile"));
            if (profileMap.containsKey("os")
                && eList.get(0).containsKey("z1SDKVersion")
                && ApiUtils.isSdkBlackListedOrObsolete(
                    profileMap.get("os").toString(),
                    eList.get(0).get("z1SDKVersion").toString()))
            {
              if (logger.canLog())
              {
                logger.log(
                    "SDK version " + eList.get(0).get("z1SDKVersion").toString()
                        + " is not supported  ");
              }

              resp.sendError(HttpServletResponse.SC_PRECONDITION_FAILED,
                  "SDK version " + eList.get(0).get("z1SDKVersion").toString()
                      + " is not supported.");
              return;
            }

            boolean blockEventIP = SystemConfig.getBooleanValue(ctx, SystemConfig.Z1_BLOCK_EVENT_IP, true);

            String ip = blockEventIP ? null : ApiUtils.getCustomerIpAddress(req);

            // Add user-agent as part of the event payload. Similar to ip
            // address it won't persist
            final String userAgent = req.getHeader(HttpHeaders.USER_AGENT);

            for (Map<String, Object> e : eList)
            {
              if (ip != null && !ip.trim().isEmpty())
              {
                e.put(z1.commons.Const.IP_ADDRESS, ip);
              }
              if (userAgent != null && !userAgent.trim().isEmpty())
              {
                e.put(z1.commons.Const.USER_AGENT, userAgent);
              }
            }

            new z1.EventHandler(ctx).setProfileId(profileId).handle(eList, logger);

            return;
          }
          // c3/api/v1/chat
          case chat:
          {
            // Create an array to pass for the relevant commands
            String[] subParts = new String[pathParts.length - 1];
            int j = 0;
            for (int i = 1; i < pathParts.length; i++, j++)
            {
              subParts[j] = pathParts[i];
            }
            new com.z1.handler.ChatHandler().post().handle(ctx, subParts, req,
                resp);
            return;
          }

          // rating for an action (message) served
          // c3/api/v1/msgRating
          // c3/api/v1/actionResponse
          case msgRating:
          case actionResponse:
          {
            try
            {
              ctx.getLogger(z1.commons.Const.TRACE_CONTEXT)
                  .log("Action response received from device.");
              String payload = ServletUtil.getPayload(req);

              ULogger logger = ctx.getLogger(V1Handler.class);
              if (logger.canLog()) logger.log(payload);

              logActionResponse(ctx, logger, payload);

              JobQueue jQ = JobQueue.getInstance("ActionResponseProcessor",
                  z1.actions.ActionResponseProcessor.class);
              Job job = jQ.createJob("ActionResponseProcessor", ctx);
              job.setPayload(payload);

              jQ.submit(job);
            }
            catch (Exception e)
            {
              ServletError err = new ServletError().setReason(e);
              resp.getWriter().print(err.getMessage());
            }
            return;
          }
          case profile:
          {
            return;
          }
          case faq:
          {

            String payload = ServletUtil.getPayload(req);
            JSONArray jaIn = new JSONArray(payload);

            FAQ f = FAQ.load(ctx);
            if (f == null)
            {
              f = FAQ.create(ctx);
            }

            JSONArray ja = new JSONArray(f.getPayload());

            if (jaIn.length() == ja.length())
            {
              Set<String> contentIds = new LinkedHashSet<>();

              for (int i = 0; i < jaIn.length(); i++)
              {
                // contentIds set is filled with Input Payload faq ids
                JSONObject j = (JSONObject) jaIn.get(i);
                contentIds.add(j.getString(ReqFields.contentId.name()));
              }

              boolean save = true;
              // check if its a reorder by iterating through the
              // existing faq ids of FAQ
              for (int i = 0; i < ja.length(); i++)
              {
                String existingFaqId = ja.getString(i);
                if (!contentIds.contains(existingFaqId))
                {
                  save = false;
                  break;
                }
              }
              if (save)
              {
                f.setPayload(new JsonMarshaller().serialize(contentIds));
                f.save();
              }
            }
            /*
             * ResponseMessage rm = new
             * ResponseMessage(ResponseMessage.Status.success,
             * "FAQ Saved successfully."); resp.getWriter().print(new
             * JsonMarshaller().serialize(rm));
             */
            return;
          }

          case pnregister:
          {
            String payload = ServletUtil.getPayload(req);
            ULogger logger = ctx.getLogger(this.getClass());
            logger.log("Device registering for PN:" + payload);

            Map<String, Object> eMap = new JsonMarshaller().readAsMap(payload);

            String dId = (String) eMap.get("deviceId");
            String pnRegId = (String) eMap.get("pnRegId");

            Object oMap = eMap.get("keys");
            String keys = null;
            if (oMap instanceof Map)
            {
              Map<String, Object> keysMap = (Map<String, Object>) oMap;
              if (keysMap != null)
                keys = new JsonMarshaller().serializeMap(keysMap);
            }

            Context zCtx = Context.getInstance(ctx);
            ProfileService ps = new ProfileService(zCtx);
            Profile p = Profile.identityResolver(zCtx).withDeviceId(dId)
                .includeOptOutProfile().findAProfile();
            if (p == null)
            {
              // This is a 1st time user, we will create a profile
              p = Profile.instance(zCtx);
              p.addHandle(dId, IndexType.DEVICE);
              p.add2Namespace(Profile.SubscriptionType.asDevice);
            }
            else
            {
              if (p.getKeyValue().equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
              {
                if (logger.canLog())
                {
                  logger.log(
                      "pnregister call is coming from unique optout profileId: "
                          + Profile.OPTOUT_PROFILE_ID + ". Ignoring.");
                }
                return;
              }
            }

            String deviceOS = (String) eMap.get("deviceOS");
            KBDeviceInfo.OS knownOS = KBDeviceInfo.OS.parse(deviceOS);

            // Storing keys for Web Push
            if (keys != null && !keys.isEmpty())
            {
              p.addProperty(Profile.Fields.pnWebKeys.value, keys);
              if (logger.canLog())
              {
                logger.log(String.format(
                    "DeviceId:%s with profileId:%s "
                        + "PNregisters pnWebKeys:%s",
                    dId, p.getKeyValue(), keys));
              }

            }

            String pushRegId = p.getProperty("pushRegId");
            Map<String, Map<String, Object>> pushRegIdMap = null;
            // If pushReg is large and beyond the base size limit then push only
            // current device token.
            // Old device tokens will be gone.
            if (pushRegId != null && !pushRegId.isEmpty()
                && pushRegId.length() < z1.commons.Utils.maxPushRegByte)
            {
              pushRegIdMap = new JsonMarshaller().readAsObject(pushRegId,
                  Map.class);
            }
            else
            {
              pushRegIdMap = new HashMap<>();
            }
            String os = knownOS.name();
            if (KBDeviceInfo.OS.html5.equals(knownOS))
            {
              String osName = KBDeviceInfo.formatOSNameForHtml5(
                  (String) eMap.get(DeviceInfo.browser.name()));
              if (osName != null)
              {
                os = osName;
              }
              else
              {
                logger.log(
                    "Invalid/Absent browser property for html5 platform. Device not registered for PN:"
                        + dId + " ");
                return;
              }
            }

            LinkedHashMap<String, Object> entryMap = (LinkedHashMap<String, Object>) pushRegIdMap
                .get(os);

            if (entryMap == null)
            {
              entryMap = new LinkedHashMap<>();
              pushRegIdMap.put(os, entryMap);
            }

            // If DeviceId already exist and push token is same do not proccess.
            if (entryMap.containsKey(dId) && entryMap.get(dId).equals(pnRegId))
            {
              return;
            }
            entryMap.put(dId, pnRegId);

            // Check the PushReg limit
            p.pushRegHandler().checkAndRemoveRegIdsExceedingLimit(entryMap, os);

            p.addProperty("pushRegId",
                new JsonMarshaller().serialize(pushRegIdMap));

            logger.log(String.format(
                "Device registered for PN deviceId:%s profileId:%s", dId,
                p.getKeyValue()));
            p.addProperty(DeviceProfileInfo.os.name(), os);
            p.flush();
            // call ProfileCacheNotifier to update profile cache
            try
            {
              Map<String, Object> ret = new HashMap<>();
              ret.put(z1.commons.Const.P_PROFILE_ID, p.getKeyValue());
              JobQueue jQ = JobQueue.getInstance("ProfileCacheNotifierJob",
                  ProfileCacheNotifier.class);
              Job job = jQ.createJob("ProfileCacheNotifierJob", ctx)
                  .withKey(p.getKeyValue());
              job.setPriority(Job.Priority.high);
              job.setPayload(new JsonMarshaller().serializeMap(ret));
              jQ.submit(job);
            }
            catch (UnsupportedJobException e)
            {
              if (logger.canLog())
              {
                logger.log(
                    "Exception: Failed to clear profile cache for profile id : "
                        + p.getKeyValue() + ". Reason: " + e.toString());
              }
            }
            Boolean allowPushRelationEntries = SystemConfig.getBooleanValue(ctx,
                SystemConfig.Z1_PUSH_SI, false);
            if (!allowPushRelationEntries)
            {
              return;
            }
            // Creating a job for p4 to create for push registration entry in
            // relation
            JobQueue jQ = JobQueue.getInstance(
                "PushRegistrationRelationHandler",
                z1.push.PushRegistrationRelationHandler.class);

            Job job = jQ.createJob("PushRegistrationRelationHandler", ctx);
            Map<String, Object> map = new HashMap<>();
            map.put(z1.commons.Const.P_CONNECT_DEVICE_ID, dId);
            map.put(DeviceInfo.os.name(), deviceOS);
            map.put(Relation.Fields.deviceToken.name(), pnRegId);
            map.put("type", PushRegActionType.update_entry.name());
            String pay = new JsonMarshaller().serialize(map);
            job.setPayload(pay);
            job.setPriority(Priority.counter);
            try
            {
              jQ.submit(job);
            }
            catch (Exception e)
            {
              z1.commons.Utils.showStackTraceIfEnable(e, null);
            }

            return;
          }
          // c3/api/v1/originId?originId=<>&profileId=<>
          case originId:
          {
            // originId is used to correlate an action from the backend with the
            // page on which it is to be viewed.
            // fetch the originId and profileId to save along with the profile
            String oId = req.getParameter("originId");
            String pId = req.getParameter("profileId");
            ULogger logger = ctx.getLogger(this.getClass());

            if (pId != null && pId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
            {
              if (logger.canLog())
              {
                logger.log(
                    "originId call is coming from unique optout profileId: "
                        + Profile.OPTOUT_PROFILE_ID + ". Ignoring.");
              }
              return;
            }

            if (logger.canLog())
            {
              String payload = ServletUtil.getPayload(req);
              logger.log("Origin Id received " + payload);
            }

            Context zCtx = Context.getInstance(ctx);

            Profile p = Profile.instance(zCtx, pId, false);
            if (p == null)
            {
              if (logger.canLog())
              {
                logger.log("Profile Id is null");
              }
              return;
            }

            // save the profileId to be retrieved later when an action response
            // is being created
            p.addProperty(Profile.Fields.originId.value, oId);
            if (logger.canLog())
            {
              logger.log(
                  String.format("Origin Id %s added to profile %s", oId, pId));
            }

            p.flush();
            return;
          }
          // c3/api/v1/location
          case location:
          {

            String ret = new LocationResponseBuilder().build(ctx, req);
            if (ret != null)
            {
              resp.getWriter().print(ret);
            }
            return;
          }

          // c3/api/v1/endSession?offline=<true|false>
          case endSession:
          {
            UContext uCtx = UContext.getInstance();
            ULogger logger = uCtx.getLogger(V1Handler.class);
            String payload = ServletUtil.getPayload(req);
            boolean isOfflineMode = Boolean
                .parseBoolean(req.getParameter("offline"));
            String ns = req.getParameter("namespace");
            if (!Utils.isNotNullEmptyObject(ns))
            {
              if (logger.canLog())
              {
                logger.error(
                    "[EndSession]: Missing namespace for end session request.");
              }
              return;
            }
            uCtx.setNamespace(ns);
            Map<String, Object> eventParams = z1.commons.Const.jsonMarshaller
                .readAsMap(payload);
            String sessionTrackerId = (String) eventParams
                .get(z1.commons.Const.SESSION_TRACKING_ID);
            if (sessionTrackerId != null)
            {
              Profile.setSessionTrackingId(uCtx, sessionTrackerId);
            }
            // check for profile id
            if (!eventParams.containsKey(z1.commons.Const.P_PROFILE_ID))
            {
              if (logger.canLog())
              {
                logger.error(
                    "[EndSession]: Missing profileId for end session request.");
              }
              return;
            }

            if (isOfflineMode)
            {
              eventParams.put(SessionEntityManager.SEM_CACHE_STATE,
                  ProfileSession.CacheState.EVICTED);
              eventParams.put(SessionUtils.OFFLINE_MODE, true);
             uCtx.put(SessionUtils.OFFLINE_MODE, true, true);
              // Distribute end session request to p15 nodes to reduce load on p1
              try
              {
                JobQueue jQ = JobQueue.getInstance("SessionEndRequest",
                    SessionEndRequestHandler.class);
                Job job = jQ.createJob("SessionEndRequest", uCtx);
                job.setPriority(Job.Priority.session);
                job.setPayload(
                    z1.commons.Const.jsonMarshaller.serializeMap(eventParams));
                jQ.submit(job);
              }
              catch (UnsupportedJobException e)
              {
                logger.error(String.format(
                    "[EndSession] Exception: Failed to send end session request for profileId: %s",
                    eventParams.get(z1.commons.Const.P_PROFILE_ID)), e);
              }
              return;
            }

            eventParams.put(z1.c3.api.Commons.ReqFields.event.name(),
                z1.commons.Const.P_END_SESSION_REQUEST);
            eventParams.put(z1.commons.Const.TRANSIENT_EVENT, true);
            eventParams.put("__z1_qa_flag","true");
            List<Map<String, Object>> eList = new java.util.ArrayList<>(1);
            eList.add(eventParams);
            EventHandler eh = new EventHandler(uCtx).setProfileId(
                (String) eventParams.get(z1.commons.Const.P_PROFILE_ID));
            // for debug mode, handle this in the same process without sending a job
            if (uCtx.isUnitTestMode()) eh.handleInProcess();
            eh.handle(eList, logger);
            return;
          }
        }
      }

      /**
       * Inspects if event comes from a supported browser version for html5.
       * 
       * @param event
       * @return
       */
      @SuppressWarnings("unchecked")
      private boolean isBrowserSupported(UContext ctx,
          Map<String, Object> event)
      {
        Map<String, Object> p = (Map<String, Object>) event
            .get(z1.commons.Const.Z1_PROFILE);
        if (p == null) return false;

        try
        {
          // Fetch and inspect "os" profile attribute.
          Object os = p.get("os");
          // os attribute is necessary. Return false if it's missing from event
          if (os == null) return false;
          // SDK mobile events do not have “browser” and “browserversion”
          // defined.
          // --> returns true if it's is not “html5”
          if (!("html5".equalsIgnoreCase((String) os))) return true;

          Object browser = p.get("browser");
          Object browserversion = p.get("browserversion");
          Object devicetype = p.get("devicetype");

          // return false if any of these necessary profile attributes missing
          // from event
          if (browser == null || devicetype == null || browserversion == null)
          {
            return false;
          }

          // inspect browserversion string pattern, should starts with
          // "<browser>:<majorvervion>"
          String bvStr = ((String) browserversion).toLowerCase();
          if (!z1.commons.Utils.browserVersionPattern.matcher(bvStr).find())
            return false;

          // Get major version from something like 'Chrome:69.0.3497.100'
          // Expected evtBrowserVersion = 69
          String[] parts = bvStr.split(":");
          String eBrowser = parts[0];
          String eBrowserMajorVersion = parts[1].split("\\.")[0];

          int evtBrowserVersion = 0;
          if (eBrowserMajorVersion.matches("\\d+"))
          {
            // major version string contains numeric
            evtBrowserVersion = Integer.parseInt(eBrowserMajorVersion);
          }
          else
          {
            // major version string contains alphanumeric.
            // case: os=html5, browserversion=Mobile:16C101
            return eBrowser.equalsIgnoreCase("mobile");
          }

          // fetch supported browser config info. We'd rather return true
          // (supported) if config is missing.
          Map<String, Object> dtMap = (Map<String, Object>) z1.commons.Utils.supportedBrowsers
              .get(((String) devicetype).toLowerCase());

          if (dtMap == null || dtMap.isEmpty()) return true;

          Map<String, Object> osMap = (Map<String, Object>) dtMap
              .get(((String) os).toLowerCase());

          if (osMap == null || osMap.isEmpty()) return true;

          Object supportedVersion = osMap.get(((String) browser).toLowerCase());

          if (supportedVersion == null) return true;

          return evtBrowserVersion >= (int) supportedVersion;
        }
        catch (Exception e)
        {
          ctx.getLogger(V1Handler.class).warning(
              "Exception received while inspecting browser version from event payload:"
                  + e.getMessage());
          System.out.println(
              "Exception received while inspecting browser compatibility for event: "
                  + event);
          z1.commons.Utils.showStackTraceIfEnable(e, null);
        }

        return false;
      }

      /**
       * ZMOB-7063 - until we find the issue print journeyId by looking up
       * actionIdIndex,actionId from cache or mongo it will only print on
       * masters where we will set action.printresponse flag to true in udichi
       * properties
       * 
       * @param ctx
       * @param logger
       * @param payload
       */
      private void logActionResponse(UContext ctx, ULogger logger,
          String payload)
      {
        if (!App.getInstance().isSupportedProperty("action.printresponse"))
          return; // do nothing

        Map<String, Object> map = new JsonMarshaller().readAsMap(payload);
        if (map.containsKey(AbstractMobileAction.MsgAttr.actionId.name()))
        {
          String actionIdIndex = (String) map
              .get(AbstractMobileAction.MsgAttr.actionId.name());

          String actionId = ActionIndex.lookupActionId(ctx,
              Context.getInstance(ctx), actionIdIndex);

          if (actionId == null)
          {
            ctx.getLogger(ActionUtils.class).severe(
                "ActionResponse: unable to get actionId for " + actionIdIndex);
          }
          else
          {
            StringBuilder delimiter = new StringBuilder().append("\\")
                .append(ActionLog.SEP);
            String[] splits = actionId.split(delimiter.toString());
            String actionType = splits[6];

            if (actionType != null && actionType.isEmpty()
                && actionType.equalsIgnoreCase("Push"))
            {
              logger.log("ActionResponse: push http request at master node:"
                  + actionId);
            }
            else
            {
              logger.log(
                  "ActionResponse: http request at master node:" + actionId);
            }
          }
        }
      }

    };

  }

  // =========================================================================
  //
  // IMPLEMENTATIONS
  //
  // =========================================================================

  // ................................................................
  // Wraps a html fragment into a full html payload
  private static String _createFullHtml(UContext ctx, String fragment,
      String osName, String type)
  {
    String css = null;

    // Load specific CSS if defined.
    if (osName != null)
    {
      CustomConfig.Type t = null;
      KBDeviceInfo.OS os = KBDeviceInfo.OS.parse(osName);
      if (os == KBDeviceInfo.OS.android)
      {
        if ("tablet".equalsIgnoreCase(type))
        {
          t = CustomConfig.Type.androidTabContentCss;
        }
        else
        {
          t = CustomConfig.Type.androidPhoneContentCss;
        }
      }
      else if (os == KBDeviceInfo.OS.ios)
      {
        if ("tablet".equalsIgnoreCase(type))
        {
          t = CustomConfig.Type.iosTabContentCss;
        }
        else
        {
          t = CustomConfig.Type.iosPhoneContentCss;
        }
      }

      if (t != null)
      {
        CustomConfig ccs = CustomConfig.load(ctx, t);
        if (ccs != null)
        {
          String payload = ccs.getPayload();
          if (payload != null)
          {
            Map<String, Object> map = new JsonMarshaller().readAsMap(payload);
            css = (String) map.get("payload");
          }
        }
      }
    }

    String htmlTemplate = "<html id=\"z1_content_head_container\">"
        + "<head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">"
        + "%s" + "</head>"
        + "<body id=\"z1_content_body_container\" class=\"z1_content_body_container\" %s>"
        + "<div id=\"z1_content_main_wrapper\" class=\"z1_content_main_wrapper\">%s</div></body>"
        + "</html>";

    String cssStyle = "";
    if (css != null)
    {
      cssStyle = "<style>" + css + "</style>";
    }

    String bodyStyle = SystemConfig.getStringValue(ctx,
        "z1.uiTemplate.contentCSS", "");
    if (!bodyStyle.isEmpty())
    {
      bodyStyle = "style=\"" + bodyStyle + "\"";
    }
    String ret = String.format(htmlTemplate, cssStyle, bodyStyle, fragment);
    return ret;
  }

  /**
   * Method for returning the schema of E-comm industry domain events
   *
   * @param ctx
   * @param req
   * @param resp
   * @param subParts
   * @throws Exception
   */
  private void _getDomainEventSchema(final UContext ctx,
      final HttpServletRequest req, final HttpServletResponse resp,
      String[] subParts) throws Exception
  {
    if (StringUtils.isEmpty(ctx.getNamespace()))
    {
      ctx.setNamespace(req.getParameter("namespace"));
    }
    if (StringUtils.isEmpty(ctx.getNamespace()))
    {
      resp.sendError(HttpServletResponse.SC_BAD_REQUEST, "Missing namespace");
      return;
    }
    DomainHandler domainHandler = DomainHandler.instance(ctx);
    if (!(domainHandler instanceof EcomDomainHandler))
    {
      resp.sendError(HttpServletResponse.SC_BAD_REQUEST,
          "Getting schema is only for ecom domain");
      return;
    }
    new com.z1.handler.EventMappingHandler().get().handle(ctx, subParts, req,
        resp);
  }

  /**
   * Handles the connect endpoint call
   *
   * @param ctx
   * @param customerId
   * @param req
   * @param resp
   * @param optionalPayload
   * @throws Exception
   */
  private void _handleConnect(final UContext ctx, String customerId,
      final HttpServletRequest req, HttpServletResponse resp,
      Map<String, Object> optionalPayload) throws Exception
  {

    long startTime = System.currentTimeMillis();
    ULogger logger = ctx.getLogger(getClass());

    if (!Utils.isNotNullEmptyOrNone(customerId))
    {
      logger.warning("Fail to process connect call. CustomerId not found");
      resp.sendError(HttpServletResponse.SC_PRECONDITION_FAILED,
          "CustomerId not found.");
      return;
    }
    customerId = Normalizer.normalizeCustomerId(customerId);

    String ret = new ConnectResponseBuilder().build(ctx, customerId, req,
        optionalPayload);
    if (ret != null)
    {
      resp.getWriter().print(ret);
    }
    logger.info(String.format("[CONNECT] customerId=%s timeTaken=%d ms",
        customerId, (System.currentTimeMillis() - startTime)));
  }

}