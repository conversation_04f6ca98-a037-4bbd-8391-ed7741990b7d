package com.z1.handler.api;

import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import com.z1.handler.GeofenceHandler;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobQueue;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.SystemConfig;
import z1.c3.api.Commons.LocationInfo;
import z1.commons.geo.GeoLocation;
import z1.core.Profile;
import z1.core.utils.TimeUtils;

/**
 * Handles device Locations. It handles the location call from devices to create
 * the geofence payload by finding the matching geofence for the profile based
 * on the location send by the device
 */
public class LocationResponseBuilder
{

  public static final int MAX_GEOFENCE_SIZE = 20;
  
  private enum ConfigItem
  {
    geofence
  }

  // ...............................................................
  /**
   * Handles a device location call.
   * 
   * @param ctx
   *          Current runtime context.
   * @param custId
   *          Target customer ID.
   * @param req
   *          Incoming http request.
   * @return The constructed payload to be sent to the device.
   * @throws Exception
   *           for invalid api keys for the current channel.
   */
  public String build(UContext ctx, final HttpServletRequest req)
      throws Exception
  {
    ULogger logger = ctx.getLogger(getClass());

    String payload = ServletUtil.getPayload(req);
    if (logger.canLog())
    {
      logger.log("Location Received from the Device " + payload);
    }
    Map<String, Object> ret = new java.util.HashMap<>(2);
    Map<String, Object> eMap = new JsonMarshaller().readAsMap(payload);

    String pId = (String) eMap.get("profileId");
    if (pId != null && pId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
    {
      if (logger.canLog())
      {
        logger.log("originId call is coming from unique optout profileId: "
            + Profile.OPTOUT_PROFILE_ID + ". Ignoring.");
      }
      return null;
    }

    String time = new TimeUtils()
        .getDate(Calendar.getInstance().getTimeInMillis(), "yyyyMMddHHmmss");

    String z1Loc = (String) eMap.get("z1_loc");
    if (z1Loc == null || z1Loc.isEmpty())
    {

      if (logger.canLog())
      {
        logger.log("z1_loc Id of profileId: " + pId + " is null or empty.");
      }
      return null;
    }

    z1Loc = z1Loc + "|timestamp=" + time;

    // Update the z1_loc to pass it to job
    eMap.put("z1_loc", z1Loc);

    // Creating a job for p4 to create for push registration entry in relation
    JobQueue jQ = JobQueue.getInstance("LocationInfoHandler",
        z1.location.LocationInfoHandler.class);

    Job job = jQ.createJob("LocationInfoHandler", ctx);
    String pay = new JsonMarshaller().serialize(eMap);
    job.setPayload(pay);
    job.setPriority(Priority.assignment);
    try
    {
      jQ.submit(job);
    }
    catch (Exception e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }

    // We will load various configs to send along with the response
    _loadGeoFence(ctx, ret, z1Loc);

    return new JsonMarshaller().serialize(ret);
  }

  // Load the geo fence
  private void _loadGeoFence(UContext ctx, Map<String, Object> cfg, String loc)
  {
    double radiusInMiles = Double.parseDouble(SystemConfig.getStringValue(ctx, "z1.geofence.proxradius","5"));
   
    Map<LocationInfo, Object> z1Map = LocationInfo
        .parseLocationEncodedString((String) loc);

    String currLat = z1Map.get(LocationInfo.latitude).toString();
    String currLong = z1Map.get(LocationInfo.longitude).toString();
    GeoLocation currentLoc = new GeoLocation(Double.parseDouble(currLat),
        Double.parseDouble(currLong));
   
    List<Map<String, Object>> retList = new java.util.ArrayList<>(10);
    
    // Get the geo fences
    List<CustomConfig> ccGeoFence = CustomConfig
        .loadAll(ctx, CustomConfig.Type.geoFence).stream()
        .filter(Objects::nonNull).collect(Collectors.toList());
    
    if (ccGeoFence != null)
    {
        List<Map<String, Object>> listGF = GeofenceHandler.getGeoFences(ccGeoFence);

        for (Map<String, Object> m : listGF)
        {
          // Construct a shorter version to send to the device
          Map<String, Object> map = new java.util.HashMap<>(4);
          map.put("id", m.get("id"));
          map.put("lat", m.get("latitude"));
          map.put("long", m.get("longitude"));
          map.put("r", m.get("radius"));

          String lat = m.get("latitude").toString();
          String longt = m.get("longitude").toString();
          String state = (String) m.get(DefinitionItem.Fields.state.name());
          
          
          GeoLocation gLoc = new GeoLocation(Double.parseDouble(lat),
              Double.parseDouble(longt));
          if (state!=null && state.equalsIgnoreCase("published") && currentLoc.distanceInMilesFrom(gLoc) <= radiusInMiles
              && retList.size() < MAX_GEOFENCE_SIZE)
          {
            retList.add(map);
          }
        }

        if (!retList.isEmpty())
        {
          cfg.put(ConfigItem.geofence.name(), retList);
        }
    }
  }

}
