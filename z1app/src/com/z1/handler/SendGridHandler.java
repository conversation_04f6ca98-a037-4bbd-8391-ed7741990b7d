package com.z1.handler;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.commons.USession;
import z1.commons.encryption.Encryption;

public class SendGridHandler implements CommandHandlerFactory
{
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
              throws Exception
      {
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
              throws Exception
      {
        String payload = ServletUtil.getPayload(req);
        JsonMarshaller jm = new JsonMarshaller();
        Map<String, Object> m = jm.readAsMap(payload);
        String apiUser = Encryption.decodeBase64((String) m.get("userName"));
        String apiKey = Encryption.decodeBase64((String) m.get("password"));
        String url = "https://api.sendgrid.com/api/profile.get.json?api_user="
            + apiUser + "&api_key=" + apiKey;

        USession sess = new USession(ctx);
        String ret = sess.httpGet(url);
        resp.getWriter().print(ret);

        return;
      }

    };
  }

}
