package com.z1.handler;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.ApiKey;
import udichi.core.App;
import udichi.core.Const;
import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.commons.Utils;
import z1.commons.encryption.Encryption;

public class Apikey<PERSON>andler implements CommandHandlerFactory
{

  private enum GetCommand
  {
    all
  }

  private enum PostCommand
  {
    create,
    update,
    delete,
    activate,
    deactivate
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/apikey/all          
          case all:
          {
            Result<ApiKey> keys = ApiKey.loadAll(ctx);
            String retPayload = new JsonMarshaller().serialize(keys);
            resp.getWriter().print(retPayload);
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/apikey/create[?channel=<channel-id>] => returns the created key
          case create:
          {
            // Get the user id and namespace from the current user
            String ns = ctx.getNamespace();
            String email = null;
            String channel = req.getParameter("channel");
            
            udichi.core.system.User user = ctx.getUser();
            if (user != null)
            {
              email = user.getId();
            }
            
            String serverName = req.getServerName();
            String domainName = ".zineone.com";
            String subcloudKey = Utils.subcloudKey;
            char SEP = '@';
            String apiKeyPrefix = serverName; //this is for test environment to work
            if(serverName.indexOf(domainName)!=-1)
            {
              apiKeyPrefix = serverName.substring(0, serverName.indexOf(domainName));
            }
            String apiKeyPostfix = "Z1" + Encryption.makeHash(ns).asLong();
            if (!subcloudKey.isEmpty()) apiKeyPostfix += "SCK" + subcloudKey;

            // This will create a key with an unique ID. THe unique ID would be the 
            // api key.
            ApiKey apiKey = ApiKey.newInstance(ctx);
            String key = apiKeyPrefix + SEP + apiKey.getId() + apiKeyPostfix;
            apiKey.getValues().put("id", key);
            apiKey.getValues().put(ApiKey.Fields.namespace.name(), ns);
            if (channel != null)
            {
              apiKey.getValues().put(ApiKey.Fields.channel.name(), channel);
            }
            
            if (email != null)
            {
              apiKey.getValues().put(ApiKey.Fields.email.name(), email);
            }
            apiKey.getValues().put(ApiKey.Fields.state.name(),ApiKey.State.active.name());
            apiKey.save();
            
            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", apiKey.getId());
            map.put("name", apiKey.getValues().get(ApiKey.Fields.name.name()));
            
            String payload = new JsonMarshaller().serializeMap(map);
            
            resp.getWriter().print(payload);
            
            break;
          }
          
          // c3/data/apikey/update?key=<key> => request contains the name to update
          case update:
          {            
            String key = req.getParameter("key");            
            String payload = ServletUtil.getPayload(req);
                        
            JsonMarshaller j = new JsonMarshaller();
            Map<String, Object> m = j.readAsMap(payload);
            
            String name = (String) m.get("name");

            ApiKey apiKeys = ApiKey.load(ctx, key);            
            if(apiKeys != null)
            {
              apiKeys.getValues().put(ApiKey.Fields.name.name(),
                  name);
              apiKeys.save();
              resp.getWriter().print("{\"result\":\"Success\"}");
              // No need to invalidate cache for a name change only
            } 
            else
            {
              resp.getWriter().print("{\"result\":\"Failure\"}");
            }
            
            break;
          }
          
          // c3/data/apikey/delete?key=<key> => deletes a key
          case delete:
          {
            String key = req.getParameter("key");
            ApiKey apiKey = ApiKey.load(ctx, key);
            if(apiKey != null)
            {
              apiKey.delete();
              resp.getWriter().print("{\"result\":\"Success\"}");
              // Clear cache for apikeys
              App.notifyClearCache(Const.NS_UDICHI_CORE, ApiKey.PREFIX + "/" + key);              
            } 
            else
            {
              resp.getWriter().print("{\"result\":\"Failure\"}");
            }
                        
            break;
          }
          // c3/data/apikey/activate?key=<key>
          case activate:
          {
            String key = req.getParameter("key");
            ApiKey apiKey = ApiKey.load(ctx, key);
            if(apiKey != null)
            {
              apiKey.setState(ApiKey.State.active);
              apiKey.save();
              resp.getWriter().print("{\"result\":\"Success\"}");
              // Clear cache for apikeys
              App.notifyClearCache(Const.NS_UDICHI_CORE, ApiKey.PREFIX + "/" + key);              
            } 
            else
            {
              resp.getWriter().print("{\"result\":\"Failure\"}");
            }
            
            break;
          }
          // c3/data/apikey/deactivate?key=<key>
          case deactivate:
          { 
            String key = req.getParameter("key");
            
            ApiKey apiKey = ApiKey.load(ctx, key);
            if(apiKey != null)
            {
              apiKey.setState(ApiKey.State.inactive);
              apiKey.save();
              resp.getWriter().print("{\"result\":\"Success\"}");
              // Clear cache for apikeys
              App.notifyClearCache(Const.NS_UDICHI_CORE, ApiKey.PREFIX + "/" + key);              
            } 
            else
            {
              resp.getWriter().print("{\"result\":\"Failure\"}");
            } 
            
            break;
          }          
        }
        
      }
    };
  }

}
