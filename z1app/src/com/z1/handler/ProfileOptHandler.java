package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobQueue;
import udichi.core.util.JsonMarshaller;
import z1.c3.SystemConfig;
import z1.commons.Utils;
import z1.core.Context;
import z1.core.Profile;
import z1.core.profile.ProfileService;
import z1.core.utils.FileDownloadHelper;

/**
 * <AUTHOR>
 *
 */
public class ProfileOptHandler implements CommandHandlerFactory
{
  public static final String REQUEST_DATA_POSTSCRIPT = "ext/fileshare/z1profiledetail.sh";

  // Supported post commands
  private enum PostCommand
  {
    optin,
    optout
  }

  // Supported get commands
  private enum GetCommand
  {
    detail
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        ULogger logger = uctx.getLogger(getClass());
        String cStr = pathParts[pathParts.length - 1];

        GetCommand command = GetCommand.valueOf(cStr);

        Map<String, Object> response = new java.util.HashMap<>();

        switch (command)
        {
          case detail:
          {
            String deviceId = req.getParameter("deviceId");
            String key = req.getParameter("key");
            String val = req.getParameter("val");
            String id = req.getParameter("id");
            String email = req.getParameter("email");

            processRequestData(uctx, logger, response, deviceId, key, val, id,
                email);

            resp.getWriter().print(new JsonMarshaller().serializeMap(response));
            break;
          }
        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        ULogger logger = uctx.getLogger(getClass());
        String cStr = pathParts[pathParts.length - 1];
        PostCommand command = PostCommand.valueOf(cStr);

        Map<String, Object> response = new java.util.HashMap<>();

        switch (command)
        {
          // /public/api/v1/profile/optout?id=user234
          // /public/api/v1/profile/optout?email=<EMAIL>
          // /public/api/v1/profile/optout?deviceId=62ac323e191ebd51
          // /public/api/v1/profile/optout?key=someId&val=122323
          case optout:
          {
            String deviceId = req.getParameter("deviceId");
            String key = req.getParameter("key");
            String val = req.getParameter("val");
            String id = req.getParameter("id");
            String email = req.getParameter("email");

            processOptOut(uctx, logger, response, deviceId, key, val, id,
                email);

            resp.getWriter().print(new JsonMarshaller().serializeMap(response));
            break;
          }
          // /public/api/v1/profile/optin?id=user234
          // /public/api/v1/profile/optin?email=<EMAIL>
          // /public/api/v1/profile/optin?deviceId=xxxx
          // /public/api/v1/profile/optin?key=someId&val=122323
          case optin:
          {
            String deviceId = req.getParameter("deviceId");
            String key = req.getParameter("key");
            String val = req.getParameter("val");
            String id = req.getParameter("id");
            String email = req.getParameter("email");

            processOptIn(uctx, logger, response, deviceId, key, val, id, email);

            resp.getWriter().print(new JsonMarshaller().serializeMap(response));
            break;
          }
          default:
          {
            break;
          }
        }
      }
    };
  }

  /**
   * Process profile detail request
   * 
   * @param uctx
   * @param logger
   * @param response
   * @param deviceId
   * @param key
   * @param val
   * @param id
   * @param email
   */
  public void processRequestData(final UContext uctx, ULogger logger,
      Map<String, Object> response, String deviceId, String key, String val,
      String id, String email)
  {
    if (email != null && isCustomerIdOnlyKey(uctx))
    {
      response.put("status", "fail");
      response.put("message",
          "Flag to share emails or phone numbers among profiles is true for this namespace. Please provide either deviceId, customerId or SecondaryKey");
      return;
    }

    if (key != null && isSecondaryKeyIgnored(uctx))
    {
      response.put("status", "fail");
      response.put("message",
          "Flag to demote the usage of secondary key is true for this namespace. Please provide either deviceId, customerId or email");
      return;
    }

    ProfileService ps = new ProfileService(Context.getInstance(uctx));
    Profile p = Profile.identityResolver(Context.getInstance(uctx))
        .withCustomerId(id).withDeviceId(deviceId).withEmail(email)
        .withSecondaryKeyValue(key, val).findAProfile();
    if (p != null)
    {
      List<String> urls = FileDownloadHelper.executePostScript(uctx, logger,
          REQUEST_DATA_POSTSCRIPT, p.getKeyValue());

      if (urls != null && !urls.isEmpty())
      {
        logger.info("Profile data requested for profileId: " + p.getKeyValue());
        response.put("status", "success");
        response.put("message",
            "System accepted the request. Requested data will be available at the following link: "
                + urls.get(0));
      }
    }
    else
    {
      response.put("status", "fail");
      response.put("message", "Profile not found for the given data.");
    }
  }

  /**
   * Process profile optOut request
   * 
   * @param uctx
   * @param logger
   * @param response
   * @param deviceId
   * @param key
   * @param val
   * @param id
   * @param email
   */
  public void processOptOut(final UContext uctx, ULogger logger,
      Map<String, Object> response, String deviceId, String key, String val,
      String id, String email)
  {
    if (email != null && isCustomerIdOnlyKey(uctx))
    {
      response.put("status", "fail");
      response.put("message",
          "Flag to share emails or phone numbers among profiles is true for this namespace. Please provide either deviceId, customerId or SecondaryKey");
      return;
    }

    if (key != null && isSecondaryKeyIgnored(uctx))
    {
      response.put("status", "fail");
      response.put("message",
          "Flag to demote the usage of secondary key is true for this namespace. Please provide either deviceId, customerId or email");
      return;
    }

    ProfileService ps = new ProfileService(Context.getInstance(uctx));
    Profile p = Profile.identityResolver(Context.getInstance(uctx))
        .withCustomerId(id).withDeviceId(deviceId).withEmail(email)
        .withSecondaryKeyValue(key, val).findAProfile();
    if (p != null)
    {
      Map<String, Object> map = new HashMap<>();
      JobQueue jQ = JobQueue.getInstance("ProfileOptJobHandler",
          com.z1.handler.ProfileOptJobHandler.class);

      Job job = jQ.createJob("ProfileOptJobHandler", uctx);
      map.put(z1.commons.Const.P_PROFILE_ID, p.getKeyValue());
      map.put("jobtype", ProfileOptJobHandler.JobType.optout.name());
      String pay = new JsonMarshaller().serialize(map);
      job.setPayload(pay);
      job.setPriority(Priority.low);

      try
      {
        jQ.submit(job);
      }
      catch (Exception e)
      {
        z1.commons.Utils.showStackTraceIfEnable(e, null);
      }
      logger.info("OptOut requested for profile " + p.getKeyValue());
      response.put("status", "success");
      response.put("message", "System accepted the request.");
    }
    else
    {
      response.put("status", "fail");
      response.put("message", "Profile not found for the given data.");
    }
  }

  /**
   * Process profile optIn request
   * 
   * @param uctx
   * @param logger
   * @param response
   * @param deviceId
   * @param key
   * @param val
   * @param id
   * @param email
   */
  public void processOptIn(final UContext uctx, ULogger logger,
      Map<String, Object> response, String deviceId, String key, String val,
      String id, String email)
  {
    if (email != null && isCustomerIdOnlyKey(uctx))
    {
      response.put("status", "fail");
      response.put("message",
          "Flag to share emails or phone numbers among profiles is true for this namespace. Please provide either deviceId, customerId or SecondaryKey");
      return;
    }

    if (key != null && isSecondaryKeyIgnored(uctx))
    {
      response.put("status", "fail");
      response.put("message",
          "Flag to demote the usage of secondary key is true for this namespace. Please provide either deviceId, customerId or email");
      return;
    }

    Profile p = Profile.identityResolver(Context.getInstance(uctx))
        .withCustomerId(id).withDeviceId(deviceId).withEmail(email)
        .withSecondaryKeyValue(key, val).findAProfile();
    String isOptIn = p != null ? p.getProperty(Profile.Fields.isOptIn.value)
        : null;
    if (isOptIn != null && isOptIn.equals("false"))
    {

      Map<String, Object> map = new HashMap<>();
      JobQueue jQ = JobQueue.getInstance("ProfileOptJobHandler",
          com.z1.handler.ProfileOptJobHandler.class);

      Job job = jQ.createJob("ProfileOptJobHandler", uctx);
      map.put(z1.commons.Const.P_PROFILE_ID, p.getKeyValue());
      map.put("jobtype", ProfileOptJobHandler.JobType.optin.name());
      String pay = new JsonMarshaller().serialize(map);
      job.setPayload(pay);
      job.setPriority(Priority.low);

      try
      {
        jQ.submit(job);
      }
      catch (Exception e)
      {
        z1.commons.Utils.showStackTraceIfEnable(e, null);
      }
      logger.info("OptIn requested for profile " + p.getKeyValue());
      response.put("status", "success");
      response.put("message", "System accepted the request.");
    }
    else
    {
      response.put("status", "fail");
      response.put("message", (p != null) ? "Profile is still active."
          : "Profile not found for the given data");
    }
  }

  /**
   * Check if secondary key is ignored as primarykey or not
   * 
   * @param ctx
   * @return
   */
  boolean isSecondaryKeyIgnored(UContext ctx)
  {
    boolean ignoreSecondaryKey = true;
    String ulpd = SystemConfig.getStringValue(ctx, "z1.ignoreSecondaryKey", "true")
        .trim();
    if ("false".equalsIgnoreCase(ulpd))
    {
      ignoreSecondaryKey = false;
    }
    return ignoreSecondaryKey;
  }

  /**
   * Check if Email is PrimaryKey or not
   * 
   * @param ctx
   * @return
   */
  boolean isCustomerIdOnlyKey(UContext ctx)
  {
    // If the system is set to have customer ID is the only key or secondary key
    // This will allow multiple profiles to share same email or phone numbers.
    boolean customerIdOnlyKey = SystemConfig.getBooleanValue(ctx,
        "z1.customerIdOnlyKey", false);
    return customerIdOnlyKey;
  }

}
