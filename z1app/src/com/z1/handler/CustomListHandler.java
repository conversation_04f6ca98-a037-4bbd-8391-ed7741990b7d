/**
 * CustomAudienceHandler.java
 * Copyright 2013-2015 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc. 
 */
package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.commons.Normalizer;
import z1.commons.Utils;
import z1.core.Context;
import z1.core.CustomListItem;

/**
 * Handles the custom audience REST apis.
 */
public class CustomListHandler implements CommandHandler
{
  public static final String SEP = "|";

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandler#handle(udichi.core.UContext, java.lang.String[],
   * javax.servlet.http.HttpServletRequest,
   * javax.servlet.http.HttpServletResponse)
   */
  @Override
  public void handle(UContext ctx, String[] pathParts, HttpServletRequest req,
      HttpServletResponse resp) throws Exception
  {
    String subCmd = pathParts[0];

    // c3/data/insights/CustomList/all
    // Returns a list of name, description pair
    if ("all".equalsIgnoreCase(subCmd))
    {
      List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

      Context zCtx = Context.getInstance(ctx);
      List<String> names = CustomListItem.getNames(zCtx);

      for (String n : names)
      {
        Map<String, Object> map = new java.util.HashMap<String, Object>(10);
        map.put("name", n);
        ret.add(map);
      }

      String payload = new JsonMarshaller().serialize(ret);
      resp.getWriter().print(payload);
    }
    // c3/data/insights/CustomList/stat?days=<n>
    // Returns a list of name, count pair
    else if ("stat".equalsIgnoreCase(subCmd))
    {
      // get the number of days
      String days = req.getParameter("days");
      if (days == null) return;
      int nDays = Integer.parseInt(days);

      List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

      Context zCtx = Context.getInstance(ctx);
      List<String> names = CustomListItem.getNames(zCtx);

      for (String n : names)
      {
        List<Map<String, Object>> list = CustomListItem.findForDays(zCtx, n,
            nDays);

        Map<String, Object> map = new java.util.HashMap<String, Object>(10);
        map.put("name", n);
        map.put("count", list.size());

        ret.add(map);
      }

      String payload = new JsonMarshaller().serialize(ret);
      resp.getWriter().print(payload);

    }
    // c3/data/insights/CustomList/view?days="<n>"&name="<audience name>"
    else if ("view".equalsIgnoreCase(subCmd))
    {
      resp.setContentType("text/text");

      // get the number of days
      String days = req.getParameter("days");
      if (days == null) return;
      int nDays = Integer.parseInt(days);

      String name = req.getParameter("name");
      if (name == null) return;

      Context zCtx = Context.getInstance(ctx);

      List<Map<String, Object>> list = CustomListItem.findForDays(zCtx, name,
          nDays);

      resp.getWriter().print(new JsonMarshaller().serialize(list));
    }
    // c3/data/insights/CustomList/download?days="<n>"&name="<audience name>"
    else if ("download".equalsIgnoreCase(subCmd))
    {
      // get the number of days
      String days = req.getParameter("days");
      if (days == null) return;
      int nDays = Integer.parseInt(days);

      String name = req.getParameter("name");
      if (name == null) return;
      if (!Normalizer.isValidFileName(name)) return;

      resp.setContentType("text/csv");
      resp.setHeader("Content-Disposition", "attachment; filename=\"" + name
          + ".csv\"");

      Context zCtx = Context.getInstance(ctx);

      List<Map<String, Object>> list = CustomListItem.findForDays(zCtx, name,
          nDays);
      StringBuilder sb = new StringBuilder(10240);
      boolean isFirstRow = true;
      CustomListItem.Fields[] ignoreFields = { 
          CustomListItem.Fields.id,
          CustomListItem.Fields.feedId,
          CustomListItem.Fields.channelId,
          CustomListItem.Fields.location,
          CustomListItem.Fields.gender };

      for (Map<String, Object> map : list)
      {
        boolean isFirstCol = true;
        // Print the header
        if (isFirstRow)
        {
          for (CustomListItem.Fields fld : CustomListItem.Fields.values())
          {
            boolean isProceed = true;
            for (CustomListItem.Fields fi : ignoreFields)
            {
              if (fld == fi) 
              {
                isProceed = false;
                break;
              }
            }
            if (!isProceed) continue;
            
            
            if (!isFirstCol)
            {
              sb.append(SEP);
            }
            sb.append(fld.name());
            isFirstCol = false;
          }

          sb.append("\n");
          isFirstCol = true;
        }

        // Print data
        for (CustomListItem.Fields fld : CustomListItem.Fields.values())
        {
          boolean isProceed = true;
          for (CustomListItem.Fields fi : ignoreFields)
          {
            if (fld == fi) 
            {
              isProceed = false;
              break;
            }
          }
          if (!isProceed) continue;
          
          if (!isFirstCol)
          {
            sb.append(SEP);
          }
          String data = (String)map.get(fld.name());
          data = data.replace('\n', ' ');
          sb.append(data);
          isFirstCol = false;
        }

        sb.append("\n");
        isFirstRow = false;
      }

      resp.getWriter().print(sb.toString());
    }

  }

}
