package com.z1.handler;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;

public class OutcomesHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all, id
  }
  
  
  private enum PostCommand
  {
    create,
    update,
    delete
  }
  
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
          
          String cStr = pathParts[0];
          GetCommand command = GetCommand.valueOf(cStr);           

        switch (command)
        {
          // c3/data/outcomes/all
          case all:
          {
            List<CustomConfig> ccs = CustomConfig.forceLoadAll(ctx, Type.outcome);
            if (ccs == null || ccs.isEmpty())
            {
              resp.getWriter().print("{}");
            }
            else
            {
              List<Map<String,Object>> ret = new ArrayList<Map<String,Object>>();
              for (CustomConfig cc : ccs)
              {
                if (cc == null) continue;
                String payload = cc.getPayload();
                Map<String,Object> map = new JsonMarshaller().readAsMap(payload);
                map.put("id", cc.getId());
                ret.add(map);
                
              }
              String payloads = new JsonMarshaller().serialize(ret);
              resp.getWriter().print(payloads);
            }
            return;
          }
          // c3/data/outcomes/id?id=<outcome-id>
          case id:
          {
              // This must be a request for a specific outcome payload.
              String id = req.getParameter("id");
              
              // get the outcome payload
              CustomConfig cc = CustomConfig.load(ctx, id, Type.outcome, true);
              String payload = cc.getPayload();
              resp.getWriter().print(payload);
              
              return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/outcomes/create => payload has the definition
          // returns <outcome id>
          
          case create:
          {
            // Create a channel by loading the payload from the request
            String payload = ServletUtil.getPayload(req);

            CustomConfig cc = CustomConfig.create(ctx, Type.outcome);
            cc.setPayload(payload);
            cc.save();
            
            Map<String, Object> map = new java.util.HashMap<>();
            map.put(DefinitionItem.ID, cc.getId());

            resp.getWriter().print(new JsonMarshaller().serializeMap(map));
            return;
          }
          // c3/data/outcomes/update?id=<id> => payload has the definition
          case update:
          {
            String id = req.getParameter("id");
            
            String payload = ServletUtil.getPayload(req);

            CustomConfig cc = CustomConfig.load(ctx, id, Type.outcome, true);
            cc.setPayload(payload);
            cc.save();

            return;
          }
          // c3/data/outcomes/delete?id=<id>
          case delete:
          {
            String id = req.getParameter("id");
            
            CustomConfig.delete(ctx, id, Type.outcome);
            return;
          }  
        }

      }
    };

  }

  // /////////////////////////////////////////////////////////////////


}
