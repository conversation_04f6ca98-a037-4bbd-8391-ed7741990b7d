package com.z1.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import com.z1.Utils.ResponseMessage;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.encryption.Encryption;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class DeepLinkHandler implements CommandHandlerFactory
{
  private static final String ATTR_NAME = "name";
  private static final String ATTR_DESCRIPTION = "desc";
  private enum GetCommand
  {
    all,
    deepLinkConfigs
  }

  private enum PostCommand
  {
    save,
    delete
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length < 1) return;
        String cStr = pathParts[0];
        DeepLinkHandler.GetCommand command = DeepLinkHandler.GetCommand.valueOf(cStr);
        JsonMarshaller jm = new JsonMarshaller();

        switch (command)
        {
          // c3/data/deepLinks/all
          case all:
          {
            // We'll get all deep link definitions to send them back
            List<CustomConfig> configList = CustomConfig.forceLoadAll(uctx, Type.deepLinks);

            if (configList == null || configList.isEmpty())
            {
              resp.getWriter().print(jm.serialize(Collections.emptyList()));
              return;
            }

            List<Map<String, Object>> res = configList.stream()
                    .map(CustomConfig::getPayload).map(jm::readAsMap)
                    .collect(Collectors.toList());

            resp.getWriter().print(jm.serialize(res));
            break;
          }
          case deepLinkConfigs:
          {
            // Deprecate after deep links have been migrated on all clusters
            // c3/data/deepLink/deepLinkConfigs
            CustomConfig cfg = CustomConfig.load(uctx, CustomConfig.Type.deepLinkConfig, true);

            String sc = "[]";
            if (cfg != null && cfg.getPayload() != null) sc = cfg.getPayload();
            resp.getWriter().print(sc);
            break;
          }
          default:
          {
            break;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length > 0)
        {
          String str = pathParts[0];
          DeepLinkHandler.PostCommand cmd = DeepLinkHandler.PostCommand.valueOf(str);
          ResponseMessage msg = null;
          JsonMarshaller jm = new JsonMarshaller();

          switch (cmd)
          {
            // c3/data/deepLinks/save
            case save:
            {
              String deepLinkPayload = ServletUtil.getPayload(req);
              // If no payload is defined, we will simply return
              if (deepLinkPayload == null)
              {
                msg = new ResponseMessage(uctx,
                        ResponseMessage.Status.fail,
                        ResponseMessage.Type.invalidPayload, "No deep link to save.");
                resp.getWriter().print(msg.toString());
                return;
              }

              // Get the name from the payload
              Map<String, Object> pl = jm.readAsMap(deepLinkPayload);
              String payload = jm.serialize(pl);
              String name = (String) pl.get(ATTR_NAME);

              if (name == null)
              {
                msg = new ResponseMessage(uctx,
                        ResponseMessage.Status.fail,
                        ResponseMessage.Type.invalidPayload,
                        "Missing deep link name.");
                resp.getWriter().print(msg.toString());
                return;
              }
              String description = (String) pl.get(ATTR_DESCRIPTION);

              // hash the name to create an ID
              try {
                String id = Encryption.makeHash(name).toString();
                CustomConfig cc = CustomConfig.create(uctx, Type.deepLinks, id);
                cc.setPayload(payload);
                cc.setName(name);
                if (description != null && !description.isEmpty()) cc.setDescription(description);
                cc.save();

                Map<String, Object> map = new java.util.HashMap<>();
                map.put("name", name);
                resp.getWriter().print(jm.serializeMap(map));
              }
              catch (Exception e)
              {
                msg = new ResponseMessage(uctx,
                        ResponseMessage.Status.fail,
                        null,
                        "Failed to save deep link, please try again.");
                resp.getWriter().print(msg.toString());
              }

              break;
            }
            // c3/data/deepLinks/delete?name=<deepLink-name>
            case delete:
            {
              String name = req.getParameter("name");
              if (name == null || name.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                        ResponseMessage.Type.invalidParams,
                        "Missing required parameter 'name'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              try
              {
                String id = Encryption.makeHash(name).toString();
                CustomConfig cc = CustomConfig.load(uctx, id, Type.deepLinks, true);
                if (cc == null) {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                          ResponseMessage.Type.invalidParams, String.format(
                          "A deep link with the name '%s' does not exist.", name));
                  resp.getWriter().print(msg.toString());
                  return;
                }
                cc.delete();

                msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                        null, String.format(
                        "Deleted deep link with name '%s'", name));
                resp.getWriter().print(msg.toString());
              }
              catch ( Exception e )
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                        null, String.format(
                        "Failed to delete the deep link with name '%s'", name));
                resp.getWriter().print(msg.toString());
              }
              break;
            }
            default:
            {
              break;
            }
          }
        }
      }

    };
  }

}
