package com.z1.handler;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.JobQueue;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.core.Context;
import z1.core.Entity;
import z1.core.utils.EntityIterator;
import z1.core.utils.EntitySerializer;
import z1.core.utils.EntityUtils;
import z1.commons.FileDownloadWorker;
import z1.commons.FileLoader;
import z1.commons.FileLoader.ObjType;
import z1.commons.FileLoader.OpType;
import z1.core.utils.FileLoaderStats;
import z1.template.TemplateUtils;

public class EntityHandler implements CommandHandlerFactory
{  
  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete,
    upload,
    remoteupload,
    empty,
    deleteOneStats,
    deleteStats,
    download
  }

  private enum GetCommand
  {
    all,
    id,
    data,
    stats
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/entity/all => Sends all entities
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx, Type.entity);
            for (CustomConfig cc : ccList)
            {
              if (cc == null) continue;
              String id = cc.getId();
              if (TemplateUtils.isTemplateConfig(id, Type.entity.name()))  
                continue;

              Map<String, Object> map = new HashMap<String, Object>();
              map.put("id", id);
              map.put("name", cc.getName());
              map.put("description", cc.getDescription());
              ret.add(map);
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
          // c3/data/entity/id?eId=<id>
          case id:
          {
            // This must be a request for a specific entity.
            String eId = req.getParameter("eId");            
            CustomConfig cc = CustomConfig.load(uctx, eId, Type.entity, true);
            if (cc == null) return;
            String id = cc.getId();
            String pl = cc.getPayload();
            Map<String, Object> map = new HashMap<String, Object>();
            JsonMarshaller jm = new JsonMarshaller();
            List<Map<String, Object>> list = jm.readAsObject(pl, List.class);
            map.put("id", id);
            map.put("name", cc.getName());
            map.put("description", cc.getDescription());
            map.put("payload", list);
            resp.getWriter().print(new JsonMarshaller().serialize(map));

            return;
          }
          
          // c3/data/entity/data?type=<type> 
          case data:
          {
            // This must be a request for a specific entity.
            String type = req.getParameter("type"); 
            EntityIterator it = Entity.findEntities(Context.getInstance(uctx),
                type);

            String ret = EntitySerializer.serialize(uctx, it, type, 1000);
            resp.getWriter().print(ret);

            return;
          }
          
          // c3/data/entity/stats?entityType=<entity-type>[&opType=<op-type>]
          case stats: // Get all historical stats for a specific entity            
          {
            String entityType = req.getParameter("entityType");             
            String opTypeReq = req.getParameter("opType");             
            OpType opType = null;
                        
            if(opTypeReq != null) {
              opType = OpType.valueOf(opTypeReq); 
            }
            
            /*if (pathParts.length == 3)  
            {              
              //opType = OpType.valueOf(pathParts[2]);
            }*/
            
            List<Map<String, Object>> ret = FileLoaderStats.getStats(uctx, entityType, opType);
            
            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        ULogger logger = uctx.getLogger(getClass());
        switch (command)
        {
          // c3/data/entity/create => payload has the definition
          // returns <entity-id>
          case create:
          {
            String ep = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (ep == null) return;

            // Get the name from the payload
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(ep);
            String payload = jm.serialize(mPl.get(EntityUtils.PAYLOAD));
            String name = (String) mPl.get(EntityUtils.ATTR_NAME);
            if (name == null) return;
            String description = (String) mPl.get("description");

            CustomConfig cc = CustomConfig.load(uctx, name, Type.entity, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, Type.entity, name);
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              cc.save();
              id = cc.getId();
            }

            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", id);
            resp.getWriter().print(jm.serializeMap(map));
            
            ArtifactAudit.newInstance(uctx, ItemTypes.entity, id, name,
                Operations.create).save();

            return;
          }
          // c3/data/entity/update?id=<id>
          case update:
          {
            // update the existing entity by passing the id
            String id = req.getParameter("id");            
            
            String ep = ServletUtil.getPayload(req);

            // Get the name from the payload
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(ep);
            String payload = jm.serialize(mPl.get(EntityUtils.PAYLOAD));
            String name = (String) mPl.get(EntityUtils.ATTR_NAME);
            if (name == null) return;
            String description = (String) mPl.get("description");

            CustomConfig cc = CustomConfig.load(uctx, id, Type.entity, true);
            if (cc != null)
            {
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              cc.save();
            }
            
            ArtifactAudit.newInstance(uctx, ItemTypes.entity, id, name,
                Operations.edit).save();

            return;
          }
          
          // c3/data/entity/delete?id=<id>
          case delete:
          {
            String id = req.getParameter("id");            
            req.setAttribute("id", id);
            req.setAttribute("type", Type.entity.name());
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = Type.entity.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);
            
            return;
          }
          
          // c3/data/entity/empty?id=<id>
          case empty:
          {
            String id = req.getParameter("id");             
            Context ctx = Context.getInstance(uctx);

            CustomConfig cc = CustomConfig.load(uctx, id, Type.entity, true);
            if (cc != null)
            {
              Entity.deleteAll(ctx, id);
              ArtifactAudit.newInstance(uctx, ItemTypes.entity, id, id,
                  Operations.empty).save();
            }
                        
            return;
          }
          
          // c3/data/entity/upload?eId=<eId>
          // c3/data/entity/remoteupload?eId=<eId>
          case upload:
          case remoteupload:
          {
            String eId = req.getParameter("eId");

            if(eId == null || eId.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.uploadFailed,
                      "Invalid eid");
              resp.getWriter().print(msg.toString());
              return;
            }

            InputStream is;

            if (PostCommand.upload.equals(command))
            {
              List<FileItem> items = new ServletFileUpload(
                  new DiskFileItemFactory()).parseRequest(req);

              for (FileItem item : items)
              {
                final String fldName = item.getFieldName();
                if (!"file".equals(fldName))
                {
                  continue;
                }

                final byte[] fileContent = item.get();
                final String fileName = item.getName();

                if (fileName == null || fileName.isEmpty())
                {
                  ResponseMessage msg = new ResponseMessage(uctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.uploadFailed,
                      "name attribute is missing");
                  resp.getWriter().print(msg.toString());
                  return;
                }

                if (fileContent == null)
                {
                  ResponseMessage msg = new ResponseMessage(uctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.uploadFailed,
                      "File content is null");
                  resp.getWriter().print(msg.toString());
                  return;
                }

                is = new ByteArrayInputStream(fileContent);

                // launch new thread to handle work
                Thread uploadT = new Thread(new FileLoader(uctx, OpType.upload,
                    ObjType.entity, eId, is, fileName, new HashMap<>()));
                uploadT.setDaemon(true);
                uploadT.start();

              }
            }
            else // Remote upload
            {
              String payload = ServletUtil.getPayload(req);
              if (payload == null || payload.isEmpty())
              {
                ResponseMessage msg = new ResponseMessage(uctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.uploadFailed,
                    "Invalid payload: payload cannot be null or empty.");
                resp.getWriter().print(msg.toString());
                return;
              }

              Map<String, Object> m = new JsonMarshaller().readAsMap(payload);
              List<String> fileNames = (List<String>) m.get("fileNames");

              if (fileNames == null || fileNames.isEmpty())
              {
                ResponseMessage msg = new ResponseMessage(uctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.uploadFailed,
                    "fileNames attribute is missing");
                resp.getWriter().print(msg.toString());
                return;
              }

              boolean hasInvalidFileNames = fileNames.stream()
                  .anyMatch(name -> name == null || name.equals("null")
                      || name.isEmpty() || !name.contains("."));

              if (hasInvalidFileNames)
              {
                ResponseMessage msg = new ResponseMessage(uctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.uploadFailed, "Invalid fileName.");
                resp.getWriter().print(msg.toString());
                return;
              }

              is = null;

              for (String fileName : fileNames)
              {

                Map<String, Object> info = new HashMap<>(1);
                info.put(Entity.REMOTE_UPLOAD_INIT, true);

                // launch new thread to handle work
                Thread uploadT = new Thread(new FileLoader(uctx, OpType.upload,
                    ObjType.entity, eId, is, fileName, info));
                uploadT.setDaemon(true);
                uploadT.start();
              }
            }

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.processing,
                ResponseMessage.Type.uploadProcessing,
                "Processing upload. Monitor progress by selecting 'Upload Status'");
            resp.getWriter().print(msg.toString());

            ArtifactAudit.newInstance(uctx, ItemTypes.entity, eId, eId,
                Operations.upload).save();

            break;
          }
          // c3/data/entity/deleteOneStats?id=<id>
          case deleteOneStats: // delete one stats record, unique ID is given
          {
            String id = req.getParameter("id");
            
            //String id = pathParts[1];

            FileLoaderStats.deleteOneStats(uctx, id);
            String eId = id.substring(0, id.lastIndexOf('_'));
            ArtifactAudit.newInstance(uctx, ItemTypes.entity, eId, eId,
                Operations.deleteOneStat).save();
            return;
          }
          
          // c3/data/entity/deleteStats?entityType=<entity-type>[&opType=<op-type>]
          case deleteStats: // delete all stats records for a specific entity
          {
            String entityType = req.getParameter("entityType");                        
            String opTypeReq = req.getParameter("opType");             
            OpType opType = null;
                        
            if(opTypeReq != null) {
              opType = OpType.valueOf(opTypeReq); 
            }
                        
            /*if (pathParts.length == 3) 
            {              
              //opType = OpType.valueOf(pathParts[2]);
            }*/
            
            FileLoaderStats.deleteStats(uctx, entityType, opType);
            
            ArtifactAudit.newInstance(uctx, ItemTypes.entity, entityType, entityType,
                Operations.deleteStats).save();
            
            return;
          }
          

          // c3/data/entity/download?eId=<eId>
          case download:
          {
            // This is a request to download a specific entity.
            String eId = req.getParameter("eId");             
           
            try
            {
              // Prepare the params to be passed
              Map<String, Object> params = new java.util.HashMap<>(10);
              params.put(FileDownloadWorker.OBJ_NAME, eId);
              params.put(FileDownloadWorker.OBJ_TYPE, FileLoader.ObjType.entity.name());
              JobQueue jQ = JobQueue.getInstance("EntityDownloadWorker",
                  FileDownloadWorker.class);
              Job job = jQ.createJob("EntityDownloadWorker", uctx);
              job.setPriority(Job.Priority.counter);
              job.setPayload(new JsonMarshaller().serializeMap(params));
              jQ.submit(job);
            }
            catch (Throwable ex)
            {
             if(logger.canLog())
              logger.severe("Download not completed successfully" + ex);
            }
          
            ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.processing,
                ResponseMessage.Type.downloadProcessing, 
                "Processing download. Monitor progress by selecting 'Download Status'");
            resp.getWriter().print(msg.toString());
            
            ArtifactAudit.newInstance(uctx, ItemTypes.entity, eId, eId,
                Operations.download).save();
            
            break;
          }
        }
      }
    };
  }

}
