package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.Utils;
import z1.commons.Const;
import z1.core.utils.TimeUtils;
import z1.stats.PNSentStats;


public class PNSentStatsHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all, errordescs, id
  }
  
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // 
          String cStr = pathParts[0];
          GetCommand command;
          try
          {
            command = GetCommand.valueOf(cStr);
          }
          catch (Throwable e)
          {
            command = GetCommand.id;
          }
           

        switch (command)
        {
          // c3/data/pnsentstats/all?type=<os>
          case all:
          {
            String os = req.getParameter("type");
            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            if (startDate == null || endDate == null)
            {
              TimeUtils timeUtils = new TimeUtils();
              endDate = timeUtils.getDate();
              startDate = timeUtils.getPreviousDate(30);
            }
            if (Const.DeviceOS.android.name().equals(os)
                || Const.DeviceOS.ios.name().equals(os)
                || Const.HTML5Chrome.equals(os) || Const.HTML5Safari.equals(os)
                || Const.HTML5Firefox.equals(os) || Const.HTML5Edge.equals(os))
            {
              List<Map<String, Object>> all = PNSentStats.getCounts(ctx, os,
                  startDate, endDate);

              String payload = new JsonMarshaller().serialize(all);
              resp.getWriter().print(payload);
            }

            return;
          }
          
          // c3/data/pnsentstats/errordescs
          case errordescs:
          {
            String errdescs = Utils.loadFileResource("META-INF/pnerrordescs.json");
            resp.getWriter().print(errdescs);
          }
          default:
          {
            return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
      }
    };
  }

     
  // /////////////////////////////////////////////////////////////////


}
