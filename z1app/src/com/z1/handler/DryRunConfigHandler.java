package com.z1.handler;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import z1.c3.DryRunConfig;

public class DryRunConfigHandler implements CommandHandlerFactory
{
  
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length > 0) return;

        // c3/data/dryrunconfig/
        String dryRunConfigSchema = Utils.loadFileResource("META-INF/dryrunconfig.json");
        String sc = DryRunConfig.getAll(uctx, dryRunConfigSchema);       
        resp.getWriter().print(sc);
      }
    };
  }
  

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        if (pathParts.length > 0) return;
        
        Map<String, Object> map = new HashMap<>();
        String payload = ServletUtil.getPayload(req);
        if (payload != null && !payload.isEmpty()) 
        {
          JsonMarshaller m = new JsonMarshaller();
          map = m.readAsMap(payload);
        }
        DryRunConfig.saveAll(uctx, new JsonMarshaller().serializeMap(map));
      }

    };
  }

}
