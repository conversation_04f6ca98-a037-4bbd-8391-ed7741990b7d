package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.ssl.CertificateHelper;
import z1.ssl.CertificateStore;

public class SSLCertificateHandler implements CommandHandlerFactory
{
  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  // Supported get commands
  private enum GetCommand
  {
    all,
    references
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        JsonMarshaller jm = new JsonMarshaller();
        switch (command)
        {

          case all:
          {
            resp.getWriter().print(
                jm.serialize(CertificateStore.retrieveSSLCert(uctx, true)));
            return;
          }
          case references:
          {
            String name = req.getParameter("name");
            CertificateHelper certificateHelper = new CertificateHelper(uctx);
            List<Map<String, Object>> references = certificateHelper
                .getReferences(name);
            resp.getWriter().print(new JsonMarshaller().serialize(references));
            return;
          }
        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        ResponseMessage msg = null;
        switch (command)
        {

          case create:
          {
            List<FileItem> items = new ServletFileUpload(
                new DiskFileItemFactory()).parseRequest(req);
            String fileName = "";
            String name = "";
            String uploadDate = "";
            String desc = "";
            String type = "custom";
            byte[] certContent = null;
            for (FileItem item : items)
            {
              String fldName = item.getFieldName();
              if ("file".equals(fldName))
              {
                certContent = item.get();
              }
              else if ("fileName".equals(fldName))
              {
                fileName = item.getString();
              }
              else if ("name".equals(fldName))
              {
                name = item.getString();
              }
              else if ("uploadDate".equals(fldName))
              {
                uploadDate = item.getString();
              }
              else if ("desc".equals(fldName))
              {
                desc = item.getString();
              }
              else if ("type".equals(fldName))
              {
                if("google".equals(item.getString()) && CertificateStore.googleCertificateExists(uctx))
                {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.configExists,
                  "Please delete the existing google certificate before uploading a new one.");
                  resp.getWriter().print(msg);
                  return;  
                }
                
                type = item.getString();
              }
            }

            switch (Utils.getFileExtension(fileName))
            {
              case "cer":
              case "crt":
              case "pem":
              case "der":
              case "p12":
              case "p7c":
              case "p7b":
              case "json":
                msg = _createCertificateInfo(uctx, certContent, fileName, name,
                    desc, uploadDate,type);

                break;
              default:
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidPayload,
                    "Only file types cer, crt, pem, der, p12, p7c, json, and p7b are valid.");
            }

            resp.getWriter().print(msg);

            return;
          }
          case update:
          {
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty())
            {

              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Unable to update the data.");
              resp.getWriter().print(msg.toString());
              return;
            }

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(pl);
            String name = (String) map.get("name");
            String desc = (String) map.get("desc");
            resp.getWriter().print(_updateCertificateInfo(uctx, name, desc));
            return;
          }
          case delete:
          {
            String name = req.getParameter("name");

            if (name == null || name.isEmpty())
            {

              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Unable to upload the file " + name
                      + ". Name can not be empty.");
              resp.getWriter().print(msg.toString());
              return;
            }

            CustomConfig.delete(uctx, name, Type.certificateEntry);

            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.resourseDeletedSuccessfully,
                name + " file deleted successfully");
            resp.getWriter().print(msg.toString());
            return;
          }
        }

      }
    };
  }

  /**
   * Upload the certificate into mongodb into the config collection
   * 
   * @param uctx
   * @param cert
   * @param pwd
   * @param name
   * @param desc
   * @param uploadDate
   */
  private ResponseMessage _createCertificateInfo(UContext uctx, byte[] cert,
      String fileName, String name, String desc, String uploadDate, String type)
  {
    if (name == null || name.isEmpty())
    {

      return new ResponseMessage(uctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.invalidParams,
          "Unable to upload the file " + name + ". Name can not be empty.");
    }

    if (cert == null || cert.length <= 0)
    {

      return new ResponseMessage(uctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.invalidParams,
          "Unable to upload the file " + name + ".File is missing.");
    }

    CertificateStore.createSSLCert(uctx, cert, fileName, name, desc,
        uploadDate, type);

    return new ResponseMessage(uctx, ResponseMessage.Status.success,
        ResponseMessage.Type.uploadSuccess, name + " uploaded successfuly");

  }

  /**
   * Update the existing certificate's metadata
   * 
   * @param uctx
   * @param pwd
   * @param name
   * @param desc
   * @return
   */
  private ResponseMessage _updateCertificateInfo(UContext uctx, String name,
      String desc)
  {
    if (name == null || name.isEmpty())
    {

      return new ResponseMessage(uctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.invalidParams,
          "Unable to upload the file " + name + ". Name can not be empty.");
    }

    Map<String, Object> valueMap = new HashMap<>();
    CustomConfig sslCert = CustomConfig.load(uctx, name, Type.certificateEntry,
        true);

    if (sslCert == null)
    {
      return new ResponseMessage(uctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.resourceNotFound,
          "Unable to find the file " + name);
    }

    String certConfig = sslCert.getPayload();
    if (certConfig != null)
    {
      valueMap = new JsonMarshaller().readAsMap(certConfig);
    }

    if (desc != null && !desc.isEmpty()) sslCert.setDescription(desc);
    sslCert.setPayload(new JsonMarshaller().serialize(valueMap));
    sslCert.save();

    return new ResponseMessage(uctx, ResponseMessage.Status.success,
        ResponseMessage.Type.uploadSuccess, " Data updated successfully");

  }

}
