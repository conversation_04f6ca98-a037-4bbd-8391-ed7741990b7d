package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import org.apache.commons.lang.StringUtils;
import udichi.core.UContext;
import udichi.core.queue.Job;
import udichi.core.queue.JobQueue;
import udichi.core.queue.UnsupportedJobException;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.Journey;
import z1.c3.CustomConfig.Type;
import z1.c3.SystemConfig;
import z1.commons.Const;
import z1.commons.FileDownloadWorker;
import z1.commons.FileLoader;
import z1.commons.FileLoader.OpType;
import z1.commons.Utils;
import z1.config.NSFeatureService;
import z1.config.NSFeatureSwitches;
import z1.core.Context;
import z1.core.utils.FileLoaderStats;
import z1.expression.ScriptObject;
import z1.ml.ModelInfo;
import z1.ml.Pipeline;
import z1.ml.SystemModels;
import z1.ml.def.ModelInfoDef;
import z1.ml.def.PipelineDef;
import z1.ml.deploy.ModelDeployHandler;
import z1.ml.deploy.FinalizeModelDeploymentHandler;
import z1.ml.featureset.FeatureSetData;
import z1.ml.featureset.FeatureSetManager;
import z1.ml.featureset.FeatureSetManagerException;
import z1.ml.pipeline.MLHandlerHelper;
import z1.ml.pipeline.MLHandlerHelper.ValidationResult;
import z1.ml.train.MLTrainingHandler;
import z1.ml.train.MLTrainingHelper;
import z1.ml.train.MLTrainingStateHandler;
import z1.ml.train.MlaasUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Scanner;

public class MLHandler implements CommandHandlerFactory
{
  enum Query
  {
    // ModelInfo related queries
    createModelInfo("modelinfo/create", CustomConfig.Type.mlModelInfo),
    updateModelInfo("modelinfo/update", CustomConfig.Type.mlModelInfo),
    deleteModelInfo("modelinfo/delete", CustomConfig.Type.mlModelInfo),
    allModelInfo("modelinfo/all", CustomConfig.Type.mlModelInfo),
    modelInfoId("modelinfo/id", CustomConfig.Type.mlModelInfo),
    pauseModelInfo("modelinfo/pause", CustomConfig.Type.mlModelInfo),
    resumeModelInfo("modelinfo/resume", CustomConfig.Type.mlModelInfo),
    modelInfoStatus("modelinfo/status", CustomConfig.Type.mlModelInfo),

    // io model info related end points
    allIoModelInfo("iomodelinfo/all", CustomConfig.Type.ioModelInfo),
    idIoModelInfo("iomodelinfo/id", CustomConfig.Type.ioModelInfo),
    createIoModelInfo("iomodelinfo/create", CustomConfig.Type.ioModelInfo),
    updateIoModelInfo("iomodelinfo/update", CustomConfig.Type.ioModelInfo),
    deleteIoModelInfo("iomodelinfo/delete", CustomConfig.Type.ioModelInfo),

    // ModelInfo related queries
    createModelInfoParams("modelinfoparams/create", CustomConfig.Type.mlModelInfoParams),
    updateModelInfoParams("modelinfoparams/update", CustomConfig.Type.mlModelInfoParams),
    deleteModelInfoParams("modelinfoparams/delete", CustomConfig.Type.mlModelInfoParams),
    allModelInfoParams("modelinfoparams/all", CustomConfig.Type.mlModelInfoParams),
    modelInfoParamsId("modelinfoparams/id", CustomConfig.Type.mlModelInfoParams),

    // Feature related queries
    createFeature("feature/create", CustomConfig.Type.mlFeature),
    updateFeature("feature/update", CustomConfig.Type.mlFeature),
    deleteFeature("feature/delete", CustomConfig.Type.mlFeature),
    allFeatures("feature/all", CustomConfig.Type.mlFeature),
    featureId("feature/id", CustomConfig.Type.mlFeature),

    // Pipeline related queries
    createPipeline("pipeline/create", CustomConfig.Type.mlPipeline),
    deletePipeline("pipeline/delete", CustomConfig.Type.mlPipeline),
    updatePipeline("pipeline/update", CustomConfig.Type.mlPipeline),
    publishPipeline("pipeline/publish", CustomConfig.Type.mlPipeline),
    unPublishPipeline("pipeline/unpublish", CustomConfig.Type.mlPipeline),
    pausePipeline("pipeline/pause", CustomConfig.Type.mlPipeline),
    resumePipeline("pipeline/resume", CustomConfig.Type.mlPipeline),
    allPipelines("pipeline/all", CustomConfig.Type.mlPipeline),
    pipelineId("pipeline/id", CustomConfig.Type.mlPipeline),
    pipelineInfo("pipeline/info", CustomConfig.Type.mlPipeline),

    // Preprocessor related queries
    createPreproc("preproc/create", CustomConfig.Type.mlPreproc),
    updatePreproc("preproc/update", CustomConfig.Type.mlPreproc),
    deletePreproc("preproc/delete", CustomConfig.Type.mlPreproc),
    allPreprocs("preproc/all", CustomConfig.Type.mlPreproc),
    preprocId("preproc/id", CustomConfig.Type.mlPreproc),

    // Feature validator related queries
    createValidator("validator/create", CustomConfig.Type.mlValidator),
    updateValidator("validator/update", CustomConfig.Type.mlValidator),
    deleteValidator("validator/delete", CustomConfig.Type.mlValidator),
    allValidators("validator/all", CustomConfig.Type.mlValidator),
    validatorId("validator/id", CustomConfig.Type.mlValidator),

    // Feature Set related queries
    featureSetData("featureset/data", null),
    featureSetDownload("featureset/download", null),
    featureSetStats("featureset/stats", null),
    deleteOneStats("featureset/deleteOneStats", null),
    deleteStats("featureset/deleteStats", null),
    downloadMLData("featureset/mldata", null),

    // Predict Params related queries
    createPredictParams("predictparams/create", CustomConfig.Type.mlPredictParams),
    updatePredictParams("predictparams/update", CustomConfig.Type.mlPredictParams),
    deletePredictParams("predictparams/delete", CustomConfig.Type.mlPredictParams),
    allPredictParams("predictparams/all", CustomConfig.Type.mlPredictParams),
    predictParamsId("predictparams/id", CustomConfig.Type.mlPredictParams),

    // Training Params related queries
    createTrainingParams("trainingparams/create", CustomConfig.Type.mlTrainingParams),
    updateTrainingParams("trainingparams/update", CustomConfig.Type.mlTrainingParams),
    deleteTrainingParams("trainingparams/delete", CustomConfig.Type.mlTrainingParams),
    allTrainingParams("trainingparams/all", CustomConfig.Type.mlTrainingParams),
    trainingParamsId("trainingparams/id", CustomConfig.Type.mlTrainingParams),
    getTrainingParamsDefault("trainingparams/default", Type.mlTrainingParams),

    // Training related queries
    allMLTraining("trainingstate/all", CustomConfig.Type.mlTrainingState),
    mlTrainingId("trainingstate/id", CustomConfig.Type.mlTrainingState),
    startMLTraining("trainingstate/start", CustomConfig.Type.mlTrainingState),
    stopMLTraining("trainingstate/stop", CustomConfig.Type.mlTrainingState),
    pauseMLTraining("trainingstate/pause", CustomConfig.Type.mlTrainingState),
    resumeMLTraining("trainingstate/resume", CustomConfig.Type.mlTrainingState),

    // Score label related queries
    createScoreLabels("scorelabels/create", CustomConfig.Type.mlScoreLabels),
    updateScoreLabels("scorelabels/update", CustomConfig.Type.mlScoreLabels),
    deleteScoreLabels("scorelabels/delete", CustomConfig.Type.mlScoreLabels),
    allScoreLabels("scorelabels/all", CustomConfig.Type.mlScoreLabels),
    scoreLabelId("scorelabels/id", CustomConfig.Type.mlScoreLabels),

    modelDeploy("model/deploy", null),

    unknown("", null);

    Query(String command, CustomConfig.Type configType)
    {
      this._cmd = command;
      this.configType = configType;
    }

    public static Query parse(String query)
    {
      for (Query q : Query.values())
      {
        if (q._cmd.startsWith(query)) return q;
      }

      return unknown;
    }

    private final String _cmd;
    public final CustomConfig.Type configType;
  }

  public static final Properties scriptProperties = new Properties();
  private static final String T_UDICHI_HOME = "UDICHI_HOME";

  static
  {
    //get properties for scripts
    URL url = MLHandler.class.getClassLoader()
            .getResource("META-INF/datasink/script.properties");
    if (url != null)
    {
      // Load the properties file
      try (InputStream is = url.openStream())
      {
        scriptProperties.load(is);
      }
      catch (IOException e)
      {
        // noop
      }
    }
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        Query query = Query.parse(String.join("/", pathParts));

        switch (query)
        {
          // c3/data/ml/feature/all, c3/data/ml/pipeline/all ...
          case allModelInfo:
          case allPipelines:
          case allFeatures:
          case allPreprocs:
          case allValidators:
          case allScoreLabels:
          case allMLTraining:
          {
            List<Map<String, Object>> ret = new ArrayList<>();
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                query.configType);
            for (CustomConfig cc : ccList)
            {
              String cid = cc.getId();
              if (cid.startsWith("udc.system.core") ||
                  ModelInfo.isOOTB(cid)) continue;

              Map<String, Object> map = new HashMap<>();
              map.put("id", cid);
              map.put("name", cc.getName());
              map.put("description", cc.getDescription());
              map.put("properties", cc.getDefItem().getValues()
                  .get(DefinitionItem.Fields.properties.name()));
              if (query == Query.allPipelines)
              {
                map.put("state", cc.getState());
              }
              if (query == Query.allModelInfo)
              {
                map.put("state", cc.getState());
              }
              if (query == Query.allMLTraining)
              {
                map.put("state", cc.getState());
              }
              cc.updateGeneralStat(map);

              ret.add(map);
            }

            resp.getWriter().print(Const.jsonMarshaller.serialize(ret));
            break;
          }
          case allModelInfoParams:
          {
            List<Map<String, Object>> ret = new ArrayList<>();
            List<CustomConfig> ccList = CustomConfig.loadAll(uctx,
                query.configType);
            for (CustomConfig cc : ccList)
            {
              Map<String, Object> map = new HashMap<>();
              map.put("id", cc.getId());
              map.put("payload", Const.jsonMarshaller.readAsMap(cc.getPayload()));
              ret.add(map);
            }

            resp.getWriter().print(Const.jsonMarshaller.serialize(ret));
            break;
          }
          case allPredictParams:
          case allTrainingParams:
          {
            List<Map<String, Object>> ret = new ArrayList<>();
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                query.configType);
            for (CustomConfig cc : ccList)
            {
              String cid = cc.getId();

              Map<String, Object> map = new HashMap<>();
              map.put("id", cid);
              map.put("name", cc.getName());
              map.put("description", cc.getDescription());
              map.put("properties", cc.getDefItem().getValues()
                  .get(DefinitionItem.Fields.properties.name()));

              map.put("parameters", Const.jsonMarshaller.readAsMap(cc.getPayload()));
              cc.updateGeneralStat(map);
              ret.add(map);
            }

            resp.getWriter().print(Const.jsonMarshaller.serialize(ret));
            break;
          }
          // c3/data/ml/feature/id?id=<id>, c3/data/ml/pipeline/id?id=<id>, ...
          case modelInfoId:
          case modelInfoParamsId:
          case pipelineId:
          case featureId:
          case preprocId:
          case validatorId:
          case predictParamsId:
          case trainingParamsId:
          case mlTrainingId:
          {
            String id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              if (query.equals(Query.mlTrainingId))
              {
                id = checkForModelPipelineId(req);
              }

              if (id == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            CustomConfig cc = CustomConfig.load(uctx, id, query.configType, true);
            String payload = "";
            if (cc == null)
            {
              // if custom config is null, and it's for predict params
              // or training params, return with the default values; do not create
              if (query.equals(Query.predictParamsId) && ModelInfo.isOOTB(id))
              {
                if (Boolean.TRUE.equals(SystemModels.isSystemModel(id)))
                {
                  Map<String, Object> predictParams = ModelInfo.getCustomOrDefaultPredictParameters(
                      uctx, id, new HashMap<>());
                  payload = Const.jsonMarshaller.serializeMap(predictParams);
                }
                else
                {
                  ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      String.format("The predict param ID: %s does not exist", id));
                  resp.getWriter().print(msg.toString());
                }
              }
              else if (query.equals(Query.trainingParamsId) && ModelInfo.isOOTB(id))
              {
                if (Boolean.TRUE.equals(SystemModels.isSystemModel(id)))
                {
                  Map<String, Object> trainingParams = ModelInfo.getCustomOrDefaultTrainingParameters(
                      uctx, id, new HashMap<>(), true);
                  payload = Const.jsonMarshaller.serializeMap(trainingParams);
                }
                else
                {
                  ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed, String.format(
                      "The training param ID: %s does not exist", id));
                  resp.getWriter().print(msg.toString());
                }
              }
              else
              {
                return;
              }
            }
            else
            {
              payload = cc.getPayload();
            }
            resp.getWriter().print(payload);
            break;
          }
          case getTrainingParamsDefault:
          {
            String id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }
            if (Boolean.FALSE.equals(SystemModels.isSystemModel(id)))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "This is not the ID of an OOTB model. There are no set defaults for the model ID you gave");
              resp.getWriter().print(msg.toString());
              return;
            }
            Map<String, Object> trainingParams = ModelInfo.getCustomOrDefaultTrainingParameters(
                uctx, id, new HashMap<>(), false);
            Map<String, Object> defaultTrain = new HashMap<>();
            for (Map<String, Object> value: (List<Map>) trainingParams.get("trainParams"))
            {
              defaultTrain.put(value.get("name").toString(), value.get("value"));
            }
            String payload = Const.jsonMarshaller.serializeMap(defaultTrain);
            resp.getWriter().print(payload);
            break;
          }
          case scoreLabelId:
          {
            String id = req.getParameter("id");
            String isCandidate = Optional.ofNullable(req.getParameter("isCandidate"))
                .map(v -> v.toLowerCase()).orElse("false");

            if (id != null && !id.isEmpty())
            {
              String pipeline = id;
              boolean foundCandidate = false;
              if (isCandidate.equals("true") && ModelInfo.isOOTB(id))
              {
                ModelInfo mlInfo = ModelInfo.instance(uctx, id);
                if (mlInfo != null)
                {
                  String candidatePipeline = mlInfo.getCandidate();
                  if (candidatePipeline != null && !candidatePipeline.isEmpty())
                  {
                    pipeline = candidatePipeline;
                    foundCandidate = true;
                  }
                }
              }
              if (isCandidate.equals("true") && !foundCandidate)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Candidate model does not exist." );
                resp.getWriter().print(msg.toString());
                return;
              }
              // Retrieve the score labels for the given model id,
              // or the default score labels.
              String payload = ModelInfo.getCustomOrDefaultScoreLabels(uctx, id, pipeline,true);
              if (payload != null)
              {
                resp.getWriter().print(payload);
              }
            }
            break;
          }
          case pipelineInfo:
          {
            String id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            CustomConfig cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc == null) return;

            String payload = cc.getPayload();
            if (payload == null) return;

            // We will construct a list of info to be returned, extracted from
            // the definition
            PipelineDef pDef = Const.jsonMarshaller.readAsObject(payload, PipelineDef.class);

            // return info - we'll create a list of info extracting from the
            // definition
            Map<String, Object> ret = new java.util.HashMap<>(10);
            Optional.ofNullable(pDef.getName())
                .ifPresent(v -> ret.put("name", v));
            Optional.ofNullable(pDef.getDescription())
                .ifPresent(v -> ret.put("description", v));
            Optional.ofNullable(pDef.getProvider())
                .ifPresent(v -> ret.put("provider", v));
            Optional.ofNullable(pDef.getDisplay()).ifPresent(v -> {
              Optional.ofNullable(v.getModelType())
                  .ifPresent(d -> ret.put("modelType", d));
              Optional.ofNullable(v.getOutcomeType())
                  .ifPresent(d -> ret.put("outcome", d));
              ret.put("attributes", v.getAttributes());
            });

            cc.updateGeneralStat(ret);
            ret.put("state", cc.getState());

            resp.getWriter().print(Const.jsonMarshaller.serializeMap(ret));
            break;
          }
          // c3/data/ml/featureset/data?pipelineId=<pipelineId[&limit=<maxRecordsToReturn>]
          case featureSetData:
          {
            String pipelineId = req.getParameter("pipelineId");
            String limitParam = req.getParameter("limit");
            Integer limit = null;
            if (!StringUtils.isEmpty(limitParam))
            {
              limit = new Integer(limitParam);
            }

            try
            {
              FeatureSetManager featureSetManager = new FeatureSetManager(uctx);
              FeatureSetData featureSetData = featureSetManager
                  .getData(pipelineId, limit);
              resp.getWriter().print(Const.jsonMarshaller.serialize(featureSetData));
            }
            catch (IllegalArgumentException ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, ex.getMessage());
              resp.getWriter().print(msg.toString());
            }
            catch (FeatureSetManagerException ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  ex.getMessage());
              resp.getWriter().print(msg.toString());
            }
            break;
          }

          // c3/data/ml/featureset/stats?pipelineId=<pipelineId>[&opType=<op-type>]
          case featureSetStats:
          {
            String pipelineId = req.getParameter("pipelineId");
            String opTypeReq = req.getParameter("opType");
            OpType opType = OpType.valueOf(Optional.ofNullable(opTypeReq).orElse(OpType.download.name()));
            List<Map<String, Object>> ret = FileLoaderStats.getStats(uctx, pipelineId, opType);
            resp.getWriter().print(Const.jsonMarshaller.serialize(ret));
            break;
          }

          // c3/data/ml/modelInfo/status/?modelType=<modelType>
          case modelInfoStatus:
          {
            Map<String, Map<String, Object>> ret = new HashMap<>();

            String modelType = req.getParameter("modelType");
            if ((modelType == null) || (modelType.length() == 0))
            {
              resp.getWriter().print("Missing model type in request.");
              return;
            }

            // Check for matching OOTB models for the provided type.
            Boolean OOTB_EPPV2_activated = SystemConfig.getBooleanValue(uctx, SystemConfig.Z1_NS_ACTIVATE_OOTB_EPPV2, true);
            NSFeatureSwitches nsSwitches = NSFeatureService.getInstance().getConfiguration(uctx);
            // get OOTB models if type is EPPV2/EPPV2CLK4 and OOTB is activated OR not EPP type
            // and type is in enabled types
            if (((modelType.startsWith("EPPV2") && Boolean.TRUE.equals(OOTB_EPPV2_activated)) || !modelType.startsWith("EPPV2")) && SystemModels.isEnabledModelType(nsSwitches, modelType))
            {
              List<ModelInfoDef> ootbModelDefs = SystemModels.getSystemModels(uctx, modelType);
              for (ModelInfoDef def : ootbModelDefs)
              {
                CustomConfig cc = CustomConfig.load(uctx, def.getName(), query.configType, true);
                if (cc == null)
                {
                  cc = CustomConfig.create(uctx, query.configType, def.getName());
                }

                if (cc.getPayload2() == null)
                {
                  Map<String, Object> properties = new HashMap<>();
                  ModelInfo.initializeMSPipelineValues(def.getChannel(), def.getType(), properties);
                  ret.put(def.getName(), properties);
                  cc.save();
                }
                else
                {
                  Map<String, Object> properties = Const.jsonMarshaller.readAsMap(
                      cc.getPayload2());
                  ret.put(def.getName(), (Map<String, Object>) properties.get(def.getChampion()));
                }
              }
            }

            // Add in custom models for the provided type, if any.
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                    query.configType);
            for (CustomConfig cc : ccList)
            {
              // OOTB Models will not have a payload, only payload2.
              if (cc.getPayload() != null &&
                  cc.getState().equalsIgnoreCase(DefinitionItem.State.ready.name()))
              {
                Map<String, Object> mpl = Const.jsonMarshaller.readAsMap(cc.getPayload());
                if (mpl.get("type") != null &&
                    ((String)mpl.get("type")).equals(modelType))
                {
                  if (cc.getPayload2() == null)
                  {
                    cc.setPayload2(Const.jsonMarshaller.serializeMap(
                        ModelInfo.initCustomModelStatusPayload(cc)));
                  }
                  mpl = Const.jsonMarshaller.readAsMap(cc.getPayload2());

                  // only get the champion
                  ModelInfo mi = ModelInfo.instance(uctx, cc.getName());

                  // The pipeline can be null if it is suspended when the
                  // model is active.
                  Pipeline pl = mi.getChampionPipeline();
                  if (pl != null)
                  {
                    Map <String, Object> championStatus = (Map<String, Object>)
                        mpl.get(pl.name());

                    //deployVersion check -> there should be no lastDeployment if deployVersion does not exist
                    if (championStatus.get("deployVersion") == null && championStatus.get("lastDeployment") != null){
                      championStatus.remove("lastDeployment");
                    }
                    ret.put(cc.getId(), championStatus);
                  }
                }
              }
            }
            resp.getWriter().print(Const.jsonMarshaller.serialize(ret));
            break;
          }

          // c3/data/ml/iomodelinfo/all
          // Returns all the io model infos for the namespace.
          case allIoModelInfo:
          {
            try
            {
              List<CustomConfig> customConfigs = CustomConfig.loadAll(uctx, CustomConfig.Type.ioModelInfo);
              List<String> payloads = new ArrayList<>(customConfigs.size());
              for (CustomConfig cc : customConfigs)
              {
                payloads.add(cc.getPayload());
              }
              resp.getWriter().print(payloads);
              break;
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.serverError);
              resp.getWriter().print(msg.toString());
              uctx.getLogger(getClass()).error("Error loading all IO Model Info.", ex);
            }
            break;
          }

          // c3/data/ml/iomodelinfo/id?jid=<journeyId>
          // Returns the io model info for a particular journey id.
          case idIoModelInfo:
          {
            String jid = "";
            try
            {
              jid = req.getParameter("jid");
              if ((jid == null) || (jid.length() == 0))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidJourneyId);
                resp.getWriter().print(msg.toString());
                break;
              }

              CustomConfig cc = CustomConfig.load(uctx, jid, CustomConfig.Type.ioModelInfo, true);
              String payload = "";
              if (cc == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.resourceNotFound);
                resp.getWriter().print(msg.toString());
                return;
              }
              else
              {
                payload = cc.getPayload();
              }
              resp.getWriter().print(payload);
              break;
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.serverError);
              resp.getWriter().print(msg.toString());
              uctx.getLogger(getClass()).error("Error loading IO Model Info for journey id " + jid, ex);
            }
            break;
          }

          default:
            // send error for unsupported
            throw new Exception("Unknown REST command");

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        Query query = Query.parse(String.join("/", pathParts));
        if (query == Query.unknown)
        {
          throw new Exception("Unknown REST command");
        }

        String payload;
        String id;
        String name;
        String description;
        Map<String, Object> mpl;
        CustomConfig cc;

        ItemTypes itemType = null;
        switch (query)
        {
          case createFeature:
          case updateFeature:
          case deleteFeature:
          {
            itemType = ItemTypes.features;
            break;
          }
          case createModelInfo:
          case updateModelInfo:
          case deleteModelInfo:
          case pauseModelInfo:
          case resumeModelInfo:
          {
            itemType = ItemTypes.modelinfo;
            break;
          }
          case createModelInfoParams:
          case updateModelInfoParams:
          case deleteModelInfoParams:
          {
            itemType = ItemTypes.modelInfoParams;
            break;
          }
          case createPipeline:
          case publishPipeline:
          case unPublishPipeline:
          case pausePipeline:
          case resumePipeline:
          case updatePipeline:
          case deletePipeline:
          {
            itemType = ItemTypes.pipelines;
            break;
          }
          case createPreproc:
          case updatePreproc:
          case deletePreproc:
          {
            itemType = ItemTypes.preprocessors;
            break;
          }
          case createValidator:
          case updateValidator:
          case deleteValidator:
          {
            itemType = ItemTypes.validators;
            break;
          }
          case createPredictParams:
          case updatePredictParams:
          case deletePredictParams:
          {
            itemType = ItemTypes.predictparams;
            break;
          }
          case createTrainingParams:
          case updateTrainingParams:
          case deleteTrainingParams:
          {
            itemType = ItemTypes.trainingparams;
            break;
          }
          case startMLTraining:
          case stopMLTraining:
          case pauseMLTraining:
          case resumeMLTraining:
          case modelDeploy:
          {
            itemType = ItemTypes.mltraining;
            break;
          }
          case createScoreLabels:
          case updateScoreLabels:
          case deleteScoreLabels:
          {
            itemType = ItemTypes.scoreLabels;
            break;
          }
          default:
            break;
        }

        switch (query)
        {
          case createModelInfoParams:
          {
            id = req.getParameter("id");

            payload = ServletUtil.getPayload(req);
            if (id == null || id.trim().isEmpty() || payload.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, "Invalid inputs.");
              resp.getWriter().print(msg);
              return;
            }

            boolean isValidPl = false;
            try
            {
              Map<String, Object> plm = Const.jsonMarshaller.readAsMap(payload);
              List<Map<String, Object>> modelInfoParams = (List<Map<String, Object>>)
                plm.get(ModelInfo.MODELINFO_PARAMS);
              isValidPl = true;
            }
            catch (Exception ex)
            {
              // defaults to invalid payload
            }

            if (!isValidPl)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload, "Payload is invalid.");
              resp.getWriter().print(msg);
              return;
            }

            CustomConfig newcc = CustomConfig.load(uctx, id, CustomConfig.Type.mlModelInfoParams);

            if (newcc == null)
            {
              newcc = CustomConfig.create(uctx,
                  CustomConfig.Type.mlModelInfoParams, id);
              newcc.setPayload(payload);
              newcc.save();

              resp.getWriter().print(
                  new ResponseMessage(uctx, ResponseMessage.Status.success,
                      ResponseMessage.Type.requestProcessingDone, id));
              return;
            }

            resp.getWriter().print(
                new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "ModelInfoParams config already present with id."));
            break;
          }
          case createModelInfo:
          case createPipeline:
          case createFeature:
          case createPreproc:
          case createValidator:
          {
            payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            // Get the name/description from the payload
            mpl = Const.jsonMarshaller.readAsMap(payload);
            name = (String) mpl.get("name");

            // Do not CRUD OOTB model or pipeline
            if (ModelInfo.isOOTB(name))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
            }

            description = (String) mpl.get("description");

            // Add other info to properties
            Map<String, Object> props = new java.util.HashMap<>(4);
            Optional.ofNullable(mpl.get("version"))
                .ifPresent(v -> props.put("version", v));
            Optional.ofNullable(mpl.get("datatype"))
                .ifPresent(v -> props.put("datatype", v));
            Optional.ofNullable(mpl.get("provider"))
                .ifPresent(v -> props.put("provider", v));

            cc = CustomConfig.load(uctx, name, query.configType, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, query.configType, name);
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              if (!props.isEmpty())
              {
                cc.getDefItem().getValues()
                    .put(DefinitionItem.Fields.properties.name(), props);
              }

              if (query.equals(Query.createModelInfo))
              {
                cc.setPayload2(Const.jsonMarshaller.serializeMap(
                    ModelInfo.initCustomModelStatusPayload(cc)));
              }
              cc.save(DefinitionItem.State.ready);
              ArtifactAudit.newInstance(uctx, itemType, name, name,
                  Operations.create).save();
            }

            mpl.put("id", cc.getId());
            resp.getWriter().print(Const.jsonMarshaller.serializeMap(mpl));

            break;
          }
          case createPredictParams:
          {
            id = req.getParameter("id");
            if (id == null || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            // check if id is a systemModel ID, if not, do not create
            if (Boolean.FALSE.equals(SystemModels.isSystemModel(id)))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "This is not the ID of an OOTB model so the predict params cannot be created.");
              resp.getWriter().print(msg.toString());
              return;
            }

            payload = ServletUtil.getPayload(req);
            mpl = Const.jsonMarshaller.readAsMap(payload);

            if (mpl.get("predictParams") != null &&
                !MLHandlerHelper.isValidPredictParams(
                    (List<Map<String, Object>>) mpl.get("predictParams")))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "Predict params are invalid." );
              resp.getWriter().print(msg.toString());
            }

            Map<String, Object> predictParams = ModelInfo
                .getCustomOrDefaultPredictParameters(uctx, id, mpl);

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, query.configType, id);
              cc.setPayload(Const.jsonMarshaller.serializeMap(predictParams));
              cc.save();
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.create).save();
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg.toString());
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "This id already has predict params created for it.");
              resp.getWriter().print(msg.toString());
            }

            break;
          }
          case createTrainingParams:
          {
            id = req.getParameter("id");
            if (id == null || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            // check if id is a systemModel ID, if not, do not create
            if (Boolean.FALSE.equals(SystemModels.isSystemModel(id)))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "This is not the ID of an OOTB model so the training params cannot be created.");
              resp.getWriter().print(msg.toString());
              return;
            }

            payload = ServletUtil.getPayload(req);
            mpl = Const.jsonMarshaller.readAsMap(payload);

            if (mpl.get("trainParams") != null)
            {
              // validate training Params
              List<Map<String, Object>> trainParams = (List<Map<String, Object>>) mpl.get("trainParams");
              ValidationResult vr = MLHandlerHelper.isValidTrainParams(uctx, trainParams);
              if(!vr.isValid())
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed, vr.getMessage());
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            // validate databatch
            if (!MLHandlerHelper.isValidDataBatch((Map<String, Object>)mpl.get("dataBatch")))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Validation failed for training dataBatch property." );
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> trainingParams = ModelInfo.getCustomOrDefaultTrainingParameters(uctx, id,
                mpl, true);

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, query.configType, id);
              cc.setPayload(Const.jsonMarshaller.serializeMap(trainingParams));
              cc.save();
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.create).save();
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg.toString());
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "This id already has training params created for it.");
              resp.getWriter().print(msg.toString());
            }

            break;
          }
          case createScoreLabels:
          {
            payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            String labelId = req.getParameter("id");

            cc = CustomConfig.load(uctx, labelId, query.configType, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, query.configType, labelId);
              cc.setName(labelId);

              // verify if the payload contains "OTF" with min and max
              if(ModelInfo.verifyCreateScoreLabelsPayload(payload))
              {
                try
                {
                  payload = ModelInfo.populateCreateScoreLabelsPayload(payload);
                }
                catch (Exception e)
                {
                  ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidPayload,
                      String.format(
                        "Labels for %s must be a valid JSON "
                        + "containing OTF with min and max"
                        + "in decimal format.", labelId));
                      resp.getWriter().print(msg.toString());
                      return;
                }
                cc.setPayload(payload);
                cc.save(DefinitionItem.State.ready);
                ArtifactAudit.newInstance(uctx, itemType, labelId, labelId,
                    Operations.create).save();
  
                ResponseMessage msg = new ResponseMessage(uctx,
                    ResponseMessage.Status.success,
                    ResponseMessage.Type.configSaved);
                resp.getWriter().print(msg.toString());
              }
              else
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidPayload,
                String.format(
                  "Labels for %s must be a vaild JSON,contain OTF and its min and max", labelId));
                resp.getWriter().print(msg.toString());
              }
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.configExists,
                  String.format("Labels for %s already exist", labelId));
              resp.getWriter().print(msg.toString());
            }

            break;
          }
          case updateModelInfoParams:
          {
            id = req.getParameter("id");
            payload = ServletUtil.getPayload(req);

            if (id == null || id.trim().isEmpty() || payload.trim().isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, "invalid inputs.");
              resp.getWriter().print(msg);
              return;            }

            boolean isValidPl = false;
            try
            {
              Map<String, Object> plm = Const.jsonMarshaller.readAsMap(payload);
              List<Map<String,Object>> modelInfoParams = (List<Map<String, Object>>) plm.get(ModelInfo.MODELINFO_PARAMS);
              isValidPl = true;
            }
            catch(Exception ex)
            {
              // defaults to invalid payload
            }

            if (!isValidPl)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload, "Payload is invalid.");
              resp.getWriter().print(msg);
              return;
            }

            CustomConfig updcc = CustomConfig.load(uctx, id, CustomConfig.Type.mlModelInfoParams);

            if (updcc == null)
            {
              updcc = CustomConfig.create(uctx,
                  CustomConfig.Type.mlModelInfoParams, id);
            }

            updcc.setPayload(payload);
            updcc.save();
            resp.getWriter().print(
                new ResponseMessage(uctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone, id));

            break;
          }
          // c3/data/ml/pipeline/update?id=<id>
          case updateModelInfo:
          case updateFeature:
          case updatePipeline:
          case updatePreproc:
          case updateValidator:
          {
            id = req.getParameter("id");

            // Do not CRUD OOTB model or pipeline
            if (ModelInfo.isOOTB(id))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
            }

            if ((id == null) || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            // Get the name/description from the payload
            mpl = Const.jsonMarshaller.readAsMap(payload);
            name = (String) mpl.get("name");
            description = (String) mpl.get("description");

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc != null)
            {
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);

              Object propObj = cc.getDefItem().getValues()
                  .get(DefinitionItem.Fields.properties.name());

              Map<String, Object> props = (propObj instanceof Map)
                  ? (Map<String, Object>) propObj
                  : new HashMap<>(4);

              Optional.ofNullable(mpl.get("version"))
                  .ifPresent(v -> props.put("version", v));
              Optional.ofNullable(mpl.get("datatype"))
                  .ifPresent(v -> props.put("datatype", v));
              Optional.ofNullable(mpl.get("provider"))
                  .ifPresent(v -> props.put("provider", v));

              if (!props.isEmpty())
              {
                cc.getDefItem().getValues()
                    .put(DefinitionItem.Fields.properties.name(), props);
              }

              if (query.equals(Query.updateModelInfo))
              {
                cc.setPayload2(Const.jsonMarshaller.serializeMap(
                    ModelInfo.initCustomModelStatusPayload(cc)));
              }

              cc.save(DefinitionItem.State.valueOf(cc.getState()));

              ArtifactAudit.newInstance(uctx, itemType, name, name,
                  Operations.edit).save();

              this.invalidateScriptObjectCache(uctx, query.configType, name);
            }

            mpl.put("id", id);
            resp.getWriter().print(Const.jsonMarshaller.serializeMap(mpl));

            break;
          }
          case updatePredictParams:
          {
            id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            // check if id is a systemModel ID, if not, do not create
            if (Boolean.FALSE.equals(SystemModels.isSystemModel(id)))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "This is not the ID of an OOTB model and cannot be updated.");
              resp.getWriter().print(msg.toString());
              return;
            }

            payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0))
            {
              resp.getWriter().print("Missing required payload.");
              return;
            }
            mpl = Const.jsonMarshaller.readAsMap(payload);

            if (mpl.get("predictParams") != null)
            {
              if(!MLHandlerHelper.isValidPredictParams((List<Map<String, Object>>)mpl.get("predictParams")))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "Params are invalid." );
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc != null)
            {
              // cc exists so need to change update values
              cc.setPayload(Const.jsonMarshaller.serializeMap(mpl));
              cc.save(DefinitionItem.State.valueOf(cc.getState()));

              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.edit).save();

              this.invalidateScriptObjectCache(uctx, query.configType, id);
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg.toString());
            }
            else
            {
              // cc does not exist so need to create and get the
              // default values to fill for values not updated
              Map<String, Object> predictParams = ModelInfo
                  .getCustomOrDefaultPredictParameters(uctx, id, mpl);

              cc = CustomConfig.create(uctx, query.configType, id);
              cc.setPayload(Const.jsonMarshaller.serializeMap(predictParams));
              cc.save();
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.create).save();
              this.invalidateScriptObjectCache(uctx, query.configType, id);
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg.toString());
            }

            break;
          }
          case updateTrainingParams:
          {
            id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            // check if id is a systemModel ID, if not, do not create
            if (Boolean.FALSE.equals(SystemModels.isSystemModel(id)))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "This is not the ID of an OOTB model and cannot be updated.");
              resp.getWriter().print(msg.toString());
              return;
            }

            payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0))
            {
              resp.getWriter().print("Missing required payload.");
              return;
            }
            mpl = Const.jsonMarshaller.readAsMap(payload);

            if (mpl.get("trainParams") != null)
            {
              List<Map<String, Object>> trainParams = (List<Map<String, Object>>)mpl.get("trainParams");
              ValidationResult vr = MLHandlerHelper.isValidTrainParams(uctx, trainParams);
              if(!vr.isValid())
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed, vr.getMessage());
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            // validate dataBatch
            if (!MLHandlerHelper.isValidDataBatch((Map<String, Object>)mpl.get("dataBatch")))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Validation failed for training dataBatch property." );
              resp.getWriter().print(msg.toString());
              return;
            }

            cc = CustomConfig.load(uctx, id, query.configType, true);

            if (cc != null)
            {
              cc.setPayload(Const.jsonMarshaller.serializeMap(mpl));
              cc.save(DefinitionItem.State.valueOf(cc.getState()));

              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.edit).save();

              this.invalidateScriptObjectCache(uctx, query.configType, id);
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg.toString());
            }
            else
            {
              Map<String, Object> trainingParams = ModelInfo.getCustomOrDefaultTrainingParameters(uctx, id,
                  mpl, true);

              cc = CustomConfig.create(uctx, query.configType, id);
              cc.setPayload(Const.jsonMarshaller.serializeMap(trainingParams));
              cc.save();
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.create).save();
              this.invalidateScriptObjectCache(uctx, query.configType, id);
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.configSaved);
              resp.getWriter().print(msg.toString());
              resp.getWriter().print(cc.getPayload());
            }

            break;
          }
          case updateScoreLabels:
          {
            payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            id = req.getParameter("id");
            String isCandidate = Optional.ofNullable(req.getParameter("isCandidate"))
                .map(v -> v.toLowerCase()).orElse("false");

            boolean foundCandidate = false;
            if (isCandidate.equals("true") && ModelInfo.isOOTB(id))
            {
              ModelInfo mlInfo = ModelInfo.instance(uctx, id);
              if (mlInfo != null)
              {
                String candidatePipeline = mlInfo.getCandidate();
                if (candidatePipeline != null && !candidatePipeline.isEmpty())
                {
                  id = candidatePipeline;
                  foundCandidate = true;
                }
              }
            }
            if (isCandidate.equals("true") && !foundCandidate)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Candidate model does not exist." );
              resp.getWriter().print(msg.toString());
              return;
            }

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc == null)
            {
              // If the configuration is not found, create it. This
              // supports updates to model score labels when the UI
              // initially retrieved the default score labels.
              cc = CustomConfig.create(uctx, query.configType, id);
            }

            cc.setName(id);
            cc.setPayload(payload);
            cc.save(DefinitionItem.State.valueOf(cc.getState()));
            ArtifactAudit.newInstance(uctx, itemType, id, id,
                Operations.edit).save();

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.configSaved);
            resp.getWriter().print(msg.toString());

            this.invalidateScriptObjectCache(uctx, query.configType, id);

            break;
          }
          // c3/data/ml/pipeline/delete?id=<id>
          case deleteModelInfo:
          case deleteModelInfoParams:
          case deletePipeline:
          case deleteFeature:
          case deletePreproc:
          case deleteValidator:
          case deletePredictParams:
          case deleteTrainingParams:
          case deleteScoreLabels:
          {
            id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
              return;
            }

            // Do not delete OOTB model
            if (ModelInfo.isOOTB(id) && query.equals(Query.deleteModelInfo))
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(msg.toString());
            }

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc != null)
            {
              CustomConfig.delete(uctx, id, query.configType);
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.delete).save();
              this.invalidateScriptObjectCache(uctx, query.configType, id);
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.configUnavailable);
              resp.getWriter().print(msg.toString());
              return;
            }

            ResponseMessage msg = new ResponseMessage(uctx,ResponseMessage.Status.success);
            resp.getWriter().print(msg.toString());
          }
          break;

          // c3/data/ml/pipeline/publish ?id=<id>
          case publishPipeline:
            id = req.getParameter("id");
            if ((id == null) || (id.length() == 0)) return;

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc != null)
            {
              cc.getDefItem().getValues().put(
                  DefinitionItem.Fields.lastPublished.name(),
                  System.currentTimeMillis());
              cc.save(true);
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.publish).save();
            }
            break;

          case unPublishPipeline:
            id = req.getParameter("id");
            if ((id == null) || (id.length() == 0)) return;

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc != null)
            {
              cc.save(false);
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  Operations.unpublish).save();
            }
            break;

          case pauseModelInfo:
          case resumeModelInfo:
          case pausePipeline:
          case resumePipeline:
          case pauseMLTraining:
          case resumeMLTraining:
            id = req.getParameter("id");
            if ((id == null) || (id.length() == 0))
            {
              if (query.equals(Query.pauseMLTraining) ||
                  query.equals(Query.resumeMLTraining))
              {
                id = checkForModelPipelineId(req);
              }

              if (id == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            cc = CustomConfig.load(uctx, id, query.configType, true);
            if (cc != null)
            {
              DefinitionItem.State st = (query == Query.pausePipeline ||
                  query == Query.pauseModelInfo ||
                  query == Query.pauseMLTraining)
                  ? DefinitionItem.State.suspended
                  : DefinitionItem.State.ready;
              cc.save(st);
              Operations op = (query == Query.pausePipeline ||
                  query == Query.pauseModelInfo ||
                  query == Query.pauseMLTraining)
                  ? Operations.pause
                  : Operations.resume;
              ArtifactAudit.newInstance(uctx, itemType, id, id,
                  op).save();
            }
            break;

            // c3/data/ml/featureset/download?pipelineId=<pipelineId>
          case featureSetDownload:
          {
            // This is a request to download a featureset for specific pipeline.
            String pipelineId = req.getParameter("pipelineId");

            try
            {
              // Prepare the params to be passed
              Map<String, Object> params = new java.util.HashMap<>(10);
              params.put(FileDownloadWorker.OBJ_NAME, pipelineId);
              params.put(FileDownloadWorker.OBJ_TYPE, FileLoader.ObjType.featureset.name());
              JobQueue jQ = JobQueue.getInstance("FeatureSetDownloadWorker",
                  FileDownloadWorker.class);
              Job job = jQ.createJob("FeatureSetDownloadWorker", uctx);
              job.setPriority(Job.Priority.counter);
              job.setPayload(new JsonMarshaller().serializeMap(params));
              jQ.submit(job);
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.downloadFailed,
                  "Download not completed successfully" + ex);
              resp.getWriter().print(msg.toString());
            }

            ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.processing,
                ResponseMessage.Type.downloadProcessing,
                "Processing download. Monitor progress by selecting 'Download Status'");
            resp.getWriter().print(msg.toString());

            ArtifactAudit.newInstance(uctx, ItemTypes.featureset, pipelineId, pipelineId,
                Operations.download).save();
            break;
          }

          // c3/data/ml/featureset/mldata
          case downloadMLData:
          {
            try
            {
              payload = ServletUtil.getPayload(req);
              if ((payload == null) || (payload.length() == 0)) return;

              // Get the pipeline, model, startdate, enddate
              mpl = Const.jsonMarshaller.readAsMap(payload);
              String pipeline = Objects.requireNonNull(ModelInfo.replaceOOTBColon((String) mpl.get("pipeline")));
              String model = Objects.requireNonNull(ModelInfo.replaceOOTBColon((String) mpl.get("model")));

              // get current day since default value is current day
              String enddate = Objects.requireNonNull((String) mpl.get("enddate"));
              String startdate = Objects.requireNonNull((String) mpl.get("startdate"));

              // make a target directory in temp folder
              String targetdir = Utils.fixPath.apply(System.getProperty("java.io.tmpdir"), Const.SEP)
                      + "mldata" + Const.SEP + Context.getInstance(uctx).accountNum();

              File p = new File(targetdir);
              p.mkdirs();

              final String cmd = (String) scriptProperties.get("mldatadownload");

              String mlTimestamp = String.valueOf(System.currentTimeMillis());

              Map<String, Object> cmdParams = new HashMap<>();
              cmdParams.put("s3profile", Utils.getPropertyValue(Const.DATA_STORE_PROFILE_PROP));
              cmdParams.put("s3bucket", Utils.getPropertyValue(Const.DATA_STORE_BUCKET_PROP));
              cmdParams.put("accnum", Context.getInstance(uctx).accountNum());
              cmdParams.put("model", model);
              cmdParams.put("pipeline", pipeline);
              cmdParams.put("targetdir", targetdir);
              cmdParams.put("timestamp", mlTimestamp);
              cmdParams.put("start", startdate);
              cmdParams.put("end", enddate);
              String[] cmdParts = Utils.getProcessBuilderCmdParts(cmdParams, cmd);

              // the log file that appears in terminal when script is run
              File mlDataLog = new File(String.format("%s%s%s_%s_mldata.log", targetdir,
                Const.SEP, model, pipeline));

              // location of script
              String builder_directory = z1.ml.train.MLTrainingStateHandler.fixPath
                  .apply(Optional.ofNullable(System.getenv(T_UDICHI_HOME))
                      .orElseGet(udichi.core.util.Utils::getUdichiHome))
                  + "ext/script/storagedata" + Const.SEP
                  + Utils.getPropertyValue("data.store.provider", "s3");

              ProcessBuilder pb = new ProcessBuilder(cmdParts);
              Process proc = pb.directory(new File(builder_directory))
                      .redirectOutput(mlDataLog)
                      .start();
              proc.waitFor();

              String mlCSV = targetdir + String.format("/%s_%s_%s.csv", model, pipeline, mlTimestamp);
              resp.setHeader("Content-Type", "text/csv");
              resp.setHeader("Content-disposition", "attachment; filename=" +
                      String.format("%s_%s_%s.csv", model, pipeline, mlTimestamp));

              //print to response
              PrintWriter pr = resp.getWriter();
              Scanner scan = new Scanner(new File(mlCSV.trim()));
              while (scan.hasNextLine())
              {
                pr.println(scan.nextLine());
              }
              scan.close();
              pr.close();
              ArtifactAudit.newInstance(uctx, ItemTypes.featureset, pipeline, pipeline,
                      Operations.download).save();
            }
            catch(NullPointerException ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.invalidParams, ex.getMessage());
              resp.getWriter().print(msg.toString());
            }
            break;
          }

          // c3/data/ml/featureset/deleteStats?pipelineId=<pipelineId>[&opType=<op-type>]
          case deleteStats:
          {
            String pipelineId = req.getParameter("pipelineId");
            String opTypeReq = req.getParameter("opType");
            OpType opType = OpType.valueOf(Optional.ofNullable(opTypeReq).orElse(OpType.download.name()));
            FileLoaderStats.deleteStats(uctx, pipelineId, opType);

            ArtifactAudit.newInstance(uctx, ItemTypes.entity, pipelineId, pipelineId,
                Operations.deleteStats).save();
            break;
          }

          // c3/data/ml/featureset/deleteOneStats?id=<id>
          case deleteOneStats:
          {
            String statId = req.getParameter("id");
            FileLoaderStats.deleteOneStats(uctx, statId);
            String sid = statId.substring(0, statId.lastIndexOf('_'));
            ArtifactAudit.newInstance(uctx, ItemTypes.featureset, sid, sid,
                Operations.deleteOneStat).save();
            break;
          }

          // c3/data/ml/model/deploy?model=<model>&pipeline=<pipeline>
          case modelDeploy:
          {
            try
            {
              String model = req.getParameter("model");
              String pipeline = req.getParameter("pipeline");
              String experiment = req.getParameter("experiment");
              String copyLabel = Optional.ofNullable(req.getParameter("copyLabel"))
                  .map(v -> v.toLowerCase()).orElse("false");

              if (model == null || model.isEmpty() ||
                  pipeline == null || pipeline.isEmpty())
              {
                return;
              }

              // Send job for updating config and MLFlow
              Map<String, Object> params = new HashMap<>();
              params.put("model", model);
              params.put("pipeline", pipeline);
              params.put("experiment", experiment);
              params.put("copyLabel", copyLabel);

              if (MLTrainingStateHandler.useMlaaS(uctx))
              {
                ModelInfo mi = ModelInfo.instance(uctx, model);
                String type = mi.getModelTypeForPipeline(pipeline);
                long latestVersion = MlaasUtils.getLatestMlaasTrainedVersion(
                    Context.getInstance(uctx).accountNum(), model, pipeline,
                    uctx.getLogger(MLHandler.class));

                String debugpath = MLTrainingStateHandler.makePath(Context.getInstance(uctx).accountNum(), MLTrainingStateHandler.MODEL, model, pipeline);
                MlaasUtils.deployModel(uctx, model, pipeline, type, latestVersion, debugpath);

                params.put("version", String.valueOf(latestVersion));
                params.put("type", type);

                // Mlaas deployments take longer than platform deployments
                params.put("delayMS", ModelDeployHandler.DEPLOY_COMPLETE_WAIT_TIME * 10);
              }
              else
              {
                // Notify scoring nodes to update model.
                ModelDeployHandler.deploy(uctx, uctx.getNamespace(), model, pipeline, experiment);
                params.put("delayMS", ModelDeployHandler.DEPLOY_COMPLETE_WAIT_TIME);
              }

              JobQueue jQ = JobQueue.getInstance("ModelDeployHandlerJob",
                  FinalizeModelDeploymentHandler.class);

              Job job = jQ.createJob("MLDeployHandler", uctx);
              job.setPriority(Job.Priority.mlscoreChampion);
              job.setPayload(Const.jsonMarshaller.serializeMap(params));
              jQ.submit(job);

              String artifactName = String.format("%s_%s", model, pipeline);
              ArtifactAudit.newInstance(uctx, itemType, artifactName, artifactName,
                  Operations.deploy).save();

              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.finished,
                  ResponseMessage.Type.requestProcessingDone,
                  "Deployment request completed");
              resp.getWriter().print(msg.toString());
            }
            catch (UnsupportedJobException ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed, ex.getMessage());
              resp.getWriter().print(msg.toString());
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed, ex.getMessage());
              resp.getWriter().print(msg.toString());
            }

            break;
          }

          case startMLTraining:
          {
            // Create it if it does not exist or update a
            // completed or failed training state.
            String model = req.getParameter("model");
            String pipeline = req.getParameter("pipeline");

            if (model == null || model.isEmpty() ||
                pipeline == null || pipeline.isEmpty())
            {
              resp.getWriter().print("Missing model or pipeline name in request.");
              return;
            }

            ModelInfo mi = ModelInfo.instance(uctx, model);
            String type = mi.getModelTypeForPipeline(pipeline);
            if (type == null || type.isEmpty())
            {
              resp.getWriter().print("No type defined for model definition.");
              return;
            }

            Pipeline pl = Pipeline.getPipelineInstance(uctx, pipeline, model);
            MLTrainingHelper th = new MLTrainingHelper(uctx, pl);
            th.distribute();

            String artifactName = String.format("%s_%s", model, pipeline);
            ArtifactAudit.newInstance(uctx, itemType, artifactName, artifactName,
                Operations.start).save();
            this.invalidateScriptObjectCache(uctx, query.configType, artifactName);
            break;
          }
          case stopMLTraining:
          {
            // Create it if it does not exist or update a
            // completed or failed training state.
            String model = req.getParameter("model");
            String pipeline = req.getParameter("pipeline");

            if (model == null || model.isEmpty() ||
                pipeline == null || pipeline.isEmpty())
            {
              return;
            }

            // Set the state to failed if we stop it.
            MLTrainingHandler.stopTrainingState(uctx, model, pipeline);

            String artifactName = String.format("%s_%s", model, pipeline);
            ArtifactAudit.newInstance(uctx, itemType, artifactName, artifactName,
                Operations.stop).save();
            this.invalidateScriptObjectCache(uctx, query.configType, artifactName);
            break;
          }

          // c3/data/ml/iomodelinfo/create?jid=<journeyId>
          // Creates io model info for a particular journey.
          case createIoModelInfo:
          {
            String jid = "";
            try
            {
              jid = req.getParameter("jid");
              if ((jid == null) || (jid.length() == 0))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                break;
              }

              // validate jid exists
              Journey journey = Journey.loadDef(uctx, jid, z1.c3.Journey.Type.campaign);
              if (journey == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidJourneyId);
                resp.getWriter().print(msg.toString());
                break;
              }

              payload = ServletUtil.getPayload(req);
              if ((payload == null) || (payload.length() == 0))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                break;
              }

              cc = CustomConfig.load(uctx, jid, CustomConfig.Type.ioModelInfo, true);
              if (cc != null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.configExists);
                resp.getWriter().print(msg.toString());
                break;
              }
              cc = CustomConfig.create(uctx, CustomConfig.Type.ioModelInfo, jid);
              cc.setPayload(payload);
              cc.save(DefinitionItem.State.ready);
              ArtifactAudit.newInstance(uctx, ItemTypes.ioModelInfo, jid, jid,
                  Operations.create).save();

              ResponseMessage msg = new ResponseMessage(uctx,ResponseMessage.Status.success);
              resp.getWriter().print(msg.toString());
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.serverError);
              resp.getWriter().print(msg.toString());
              uctx.getLogger(getClass()).error("Error creating IO Model Info for journey id " + jid, ex);
            }
            break;
          }

          // c3/data/ml/iomodelinfo/update?jid=<journeyId>
          // Updates io model info for a particular journey.
          case updateIoModelInfo:
          {
            String jid = "";
            try
            {
              jid = req.getParameter("jid");
              if ((jid == null) || (jid.length() == 0))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                break;
              }

              // validate jid exists
              Journey journey = Journey.loadDef(uctx, jid, z1.c3.Journey.Type.campaign);
              if (journey == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidJourneyId);
                resp.getWriter().print(msg.toString());
                break;
              }

              payload = ServletUtil.getPayload(req);
              if ((payload == null) || (payload.length() == 0))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                break;
              }

              cc = CustomConfig.load(uctx, jid, CustomConfig.Type.ioModelInfo, true);
              if (cc == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.configUnavailable);
                resp.getWriter().print(msg.toString());
                break;
              }
              cc.setPayload(payload);
              cc.save(DefinitionItem.State.ready);
              ArtifactAudit.newInstance(uctx, ItemTypes.ioModelInfo, jid, jid,
                  Operations.updateconfig).save();
              ResponseMessage msg = new ResponseMessage(uctx,ResponseMessage.Status.success);
              resp.getWriter().print(msg.toString());
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.serverError);
              resp.getWriter().print(msg.toString());
              uctx.getLogger(getClass()).error("Error updating IO Model Info for journey id " + jid, ex);
            }
            break;
          }

          // c3/data/ml/iomodelinfo/delete?jid=<journeyId>
          // Deletes io model info for a particular journey.
          case deleteIoModelInfo:
          {
            String jid = "";
            try
            {
              jid = req.getParameter("jid");
              if ((jid == null) || (jid.length() == 0))
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams);
                resp.getWriter().print(msg.toString());
                return;
              }

              // validate jid exists
              Journey journey = Journey.loadDef(uctx, jid, z1.c3.Journey.Type.campaign);
              if (journey == null)
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidJourneyId);
                resp.getWriter().print(msg.toString());
                break;
              }

              cc = CustomConfig.load(uctx, jid, CustomConfig.Type.ioModelInfo);
              if (cc != null)
              {
                CustomConfig.delete(uctx, jid, CustomConfig.Type.ioModelInfo);
                ArtifactAudit.newInstance(uctx, ItemTypes.ioModelInfo, jid, jid,
                    Operations.delete).save();
                this.invalidateScriptObjectCache(uctx, query.configType, jid);
              }
              else
              {
                ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.configUnavailable);
                resp.getWriter().print(msg.toString());
                return;
              }

              ResponseMessage msg = new ResponseMessage(uctx,ResponseMessage.Status.success);
              resp.getWriter().print(msg.toString());
              break;
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.serverError);
              resp.getWriter().print(msg.toString());
              uctx.getLogger(getClass()).error("Error deleting IO Model Info for journey id " + jid, ex);
              return;
            }
          }

          default:
            break;
        }
      }

      /**
       * Invalidates the script object cache for a given configType and name.
       */
       void invalidateScriptObjectCache(UContext uctx, Type configType, String name)
      {
        switch(configType)
        {
          case mlFeature:
            ScriptObject.invalidateCache(uctx,
                ScriptObject.ScriptType.mlFeatureScript, name);
            break;
          case mlPreproc:
            ScriptObject.invalidateCache(uctx,
                ScriptObject.ScriptType.mlPreproc, name);
            break;
          case mlValidator:
            ScriptObject.invalidateCache(uctx,
                ScriptObject.ScriptType.mlValidator, name);
            break;
          default:
            break;
        }
      }
    };
  }

  private static String checkForModelPipelineId(HttpServletRequest req)
  {
    String id = null;
    String model = req.getParameter("model");
    String pipeline = req.getParameter("pipeline");
    if (model != null && !model.isEmpty() &&
        pipeline != null && !pipeline.isEmpty())
    {
      id = String.format("%s_%s", model, pipeline);
    }
    return id;
  }

}
