package com.z1.handler;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.Utils.ResponseMessage;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.Utils;
import z1.actions.pncert.PNCertAccess;
import z1.c3.SystemConfig;

public class UploadHandler implements CommandHandlerFactory
{
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length > 0) return;

        // c3/data/systemconfig/
        String sysConfigSchema = Utils
            .loadFileResource("META-INF/systemconfig.json");
        String sc = SystemConfig.getAll(uctx, sysConfigSchema);
        resp.getWriter().print(sc);
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        if (pathParts.length > 0) return;

        List<FileItem> items = new ServletFileUpload(new DiskFileItemFactory())
            .parseRequest(req);
        String pwd = "";
        String name = "";
        String dt = "";
        String topic = "";
        boolean production = false;
        byte[] certContent = null;
        for (FileItem item : items)
        {
          String fldName = item.getFieldName();
          if ("file".equals(fldName))
          {
            certContent = item.get();
          }
          else if ("pwd".equals(fldName))
          {
            pwd = item.getString();
          }
          else if ("production".equals(fldName))
          {
            production = Boolean.parseBoolean(item.getString());
          }
          else if ("name".equals(fldName))
          {
            name = item.getString();
          }
          else if ("creationDate".equals(fldName))
          {
            dt = item.getString();
          }
          else if ("topic".equals(fldName))
          {
            topic = item.getString();
          }
        }

        switch (z1.commons.Utils.getFileExtension(name))
        {
          case "p12":
          case "pem":
            PNCertAccess.uploadIpnCert(uctx, certContent, pwd, production, name,
                dt, topic);

            break;
          default:
            resp.getWriter()
                .print(new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidPayload,
                    "Only file types p12 and pem are valid."));
        }
      }
    };
  }

}
