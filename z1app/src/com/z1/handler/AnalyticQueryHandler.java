package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.analytics.businessops.BizOpsAnalyticQueryExecutor;
import z1.analytics.businessops.BizOpsAnalyticQueryExecutor.BizQueryConfigId;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AnalyticQueryHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    all,
    id
  }
  public enum PostCommand
  {
    create,
    update,
    delete
  }

  /**
   * 
   */
  public AnalyticQueryHandler()
  {
  }
  
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length < 1)
        {
          return;
        }

        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        
        ResponseMessage msg = null;
        JsonMarshaller jm = new JsonMarshaller();
        
        switch (command)
        {
          // c3/data/analyticquery/all
          case all:
          {
            List<CustomConfig> ccs = CustomConfig.forceLoadAll(uctx, CustomConfig.Type.analyticQuery);
            List<Map<String, Object>> ret = new ArrayList<>();
            for (CustomConfig cc : ccs)
            {
              Map<String, Object> info = new HashMap<>();
              info.put("id", cc.getId());
              info.put("name", cc.getName());
              info.put("description", cc.getDescription());
              info.put("lastUpdatedBy", cc.getLastUpdatedBy());
              info.put("lastUpdatedTime", cc.getLastUpdatedTime());
              ret.add(info);
            }
            
            resp.getWriter().print(jm.serialize(ret));
            break;
          }
          // c3/data/analyticquery/id?id=<AnalyticQueryConfigId>
          case id:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty())
            {              
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter(s) 'id'.");
              resp.getWriter().print(msg.toString());
              return;              
            }
            
            BizQueryConfigId bzId = BizQueryConfigId.lookupByName(id);
            Map<String, String> params = new HashMap<>();
            BizOpsAnalyticQueryExecutor bizOpsAnalyticQueryExecutor = new BizOpsAnalyticQueryExecutor(uctx);
            if (bzId != null)
            {
              if (req.getParameter("f") == null
                  || req.getParameter("t") == null)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing required parameter(s) 'f' or 't' for querying Biz Metric Data.");
                resp.getWriter().print(msg.toString());
                return;              
              }
              
              if ((bzId == BizQueryConfigId.CVR_action
                  || bzId == BizQueryConfigId.CVR_action_all
                  || bzId == BizQueryConfigId.CVR_action_all_day
                  || bzId == BizQueryConfigId.CVR_cg
                  || bzId == BizQueryConfigId.CVR_tg
                  || bzId == BizQueryConfigId.CVR_exp_date_tg
                  || bzId == BizQueryConfigId.CVR_exp_date_all
                  || bzId == BizQueryConfigId.RPS_action
                  || bzId == BizQueryConfigId.RPS_action_all
                  || bzId == BizQueryConfigId.RPS_action_all_day
                  || bzId == BizQueryConfigId.RPS_cg
                  || bzId == BizQueryConfigId.RPS_tg
                  || bzId == BizQueryConfigId.RPS_liftAll
                  || bzId == BizQueryConfigId.RPSAdjusted_liftAll
                  || bzId == BizQueryConfigId.RPS_lift
                  || bzId == BizQueryConfigId.CVR_liftAll
                  || bzId == BizQueryConfigId.CVR_lift
                  || bzId == BizQueryConfigId.incrementalRevenue
                  || bzId == BizQueryConfigId.incrementalRevenueAdjusted
                  || bzId == BizQueryConfigId.incrementalRevenue_All
                  || bzId == BizQueryConfigId.incrementalRevenueAdjusted_All
                  || bzId == BizQueryConfigId.activeSessionExpMetricsPerDayAll
                  || bzId == BizQueryConfigId.IR_exp_date_tg
                  || bzId == BizQueryConfigId.BP_liftAll
                  || bzId == BizQueryConfigId.BP_spark_liftAll
                  || bzId == BizQueryConfigId.ActionAll_lift
                  || bzId == BizQueryConfigId.CVR_RPS_tg
                  || bzId == BizQueryConfigId.TE_Channel_Filter
                  || bzId == BizQueryConfigId.TE_Identity_Filter
                  || bzId == BizQueryConfigId.TE_Channel_ActionAll_lift
                  || bzId == BizQueryConfigId.TE_Identity_ActionAll_lift)
                  && ((req.getParameter("jid") == null || req.getParameter("jid").isEmpty()) && 
                  (req.getParameter("journeyId") == null || req.getParameter("journeyId").isEmpty())))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing required parameter(s) 'jid/journeyId' for querying Biz Metric Data.");
                resp.getWriter().print(msg.toString());
                return;              
              }
              if ((bzId == BizQueryConfigId.CVR_exp_action
                  || bzId == BizQueryConfigId.CVR_exp
                  || bzId == BizQueryConfigId.CVR_exp_action_lift
                  || bzId == BizQueryConfigId.RPS_exp_action
                  || bzId == BizQueryConfigId.CVR_RPS_AOV_exp_tg
                  || bzId == BizQueryConfigId.BP_exp_liftAll)
                  && req.getParameter("jtype") == null)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing required parameter(s) 'jtype' for querying Biz Metric Data.");
                resp.getWriter().print(msg.toString());
                return;              
              }
              if (bzId == BizQueryConfigId.CVR_ns_avg) id = BizQueryConfigId.CVR_ns.name();
              if (bzId == BizQueryConfigId.RPS_ns_avg) id = BizQueryConfigId.RPS_ns.name();

              params.put("f", req.getParameter("f"));
              params.put("t", req.getParameter("t"));
              params.put("tu", "day");
              if (req.getParameter("tu") != null)
              {
                params.put("tu", req.getParameter("tu"));
              }
              String jid = req.getParameter("jid");
              if (jid != null) params.put("journeyId", jid);
              String journeyId = req.getParameter("journeyId");
              if (journeyId != null) params.put("journeyId", journeyId);
              String jtype = req.getParameter("jtype");
              if (jtype != null) params.put("jtype", jtype);
              
              // Support querying this period and last period data
              if (req.getParameter("period") != null
                  && req.getParameter("ds1") != null
                  && req.getParameter("ds2") != null)
              {
                try
                {
                  resp.getWriter()
                  .print(jm.serialize(bizOpsAnalyticQueryExecutor.executeQueryPeriod(bzId, params,
                      req.getParameter("period"), req.getParameter("ds1"),
                      req.getParameter("ds2"))));
                }
                catch (Exception ex)
                {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.queryRunFailed,
                      ex.getMessage());
                  resp.getWriter().print(msg.toString());
                  return;
                }
                return;
              }

            }

            if (bzId == null)
            {
              CustomConfig cc = CustomConfig.load(uctx, id, CustomConfig.Type.analyticQuery, true);
              if (cc == null)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    String.format("AnalyticQuery config id '%s' doesn't exist.", id));
                resp.getWriter().print(msg.toString());
                return;
              }
              
              String payload = cc.getPayload();
              resp.getWriter().print(payload);
            }
            else 
            {
              try
              {
                if (bzId == BizQueryConfigId.activeSessionExpMetricsPerDayAll && params.get("journeyId").isEmpty())
                {
                  // journeyId list is empty
                  // return all 0's for the selected date range
                  resp.getWriter()
                          .print(jm.serialize(bizOpsAnalyticQueryExecutor.executeQueryActiveSessionsExpMetricsPerDayAll(id, params)));
                  return;
                }
                
                if (bzId == BizQueryConfigId.CVR_exp_date_all)
                {
                  resp.getWriter()
                  .print(jm.serialize(bizOpsAnalyticQueryExecutor.executeQueryCVR_exp_date_all(id, bzId, params)));
                  return;
                }
                else if (bzId == BizQueryConfigId.activeSessionExpMetricsPerDayAll)
                {
                  resp.getWriter()
                  .print(jm.serialize(bizOpsAnalyticQueryExecutor.executeQueryActiveSessionsExpMetricsPerDayAll(id, params)));
                  return;
                }
                resp.getWriter()
                    .print(jm.serialize(bizOpsAnalyticQueryExecutor.executeQuery(id, bzId, params)));
              }
              catch (Exception ex)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.queryRunFailed,
                    ex.getMessage());
                resp.getWriter().print(msg.toString());
                return;
              }
            }
            
            break;
          }
        }
      }
    };
  }

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandlerFactory#post()
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length < 1)
        {
          return;
        }

        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        
        ResponseMessage msg = null;
        JsonMarshaller jm = new JsonMarshaller();
        
        switch (command)
        {
          // c3/data/analyticquery/<create|update>?id=<analyticQuery-config-id>
          // request payload:
          // {
          //   "name": "query name",
          //   "description": "query description",
          //   "queries":[
          //     {
          //       "metric": "metricId",
          //       "datasetName": "current week",
          //       "fromDate": "20210424|${f}",
          //       "toDate": "20210430|${t}",
          //       ...
          //     },
          //     ...
          //   ]
          // }
          // - for "update", "id" param is mandatory
          // - for "create", "id" is optional. Provide "id" when create to force a named
          //   analyticQuery config
          case create:
          case update:
          { 
            String id = null;
            CustomConfig cc = null;
            id = req.getParameter("id");
            if (command.equals(PostCommand.update))
            {
              if (id == null || id.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing required parameter(s) 'id'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              cc = CustomConfig.load(uctx, id, CustomConfig.Type.analyticQuery,
                  true);
              if (cc == null)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams, String.format(
                        "AnalyticQuery config id '%s' doesn't exist.", id));
                resp.getWriter().print(msg.toString());
                return;
              }
            }
            
            String payload = ServletUtil.getPayload(req);
            if (payload == null || payload.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing or empty request payload for AnalyticQuery config.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            Map<String, Object> plMap = jm.readAsMap(payload);
            String name = (String) plMap.get("name");
            if (name == null || name.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Request payload is missing AnalyticQuery config's 'name'");
              resp.getWriter().print(msg.toString());
              return;
            }
            String description = (String) plMap.get("description");
            
            List<Map<String, Object>> queries = (List<Map<String, Object>>) plMap.get("queries");
            
            if (command.equals(PostCommand.create))
            {
              if (id != null && !id.isEmpty())
              {
                cc = CustomConfig.create(uctx, CustomConfig.Type.analyticQuery, id);
              }
              else
              {
                cc = CustomConfig.create(uctx, CustomConfig.Type.analyticQuery);
              }
            }
            
            cc.setPayload(jm.serialize(queries));
            cc.setName(name);
            if (description != null && !description.isEmpty()) cc.setDescription(description);
            cc.save(true);
            
            ArtifactAudit.newInstance(uctx, ItemTypes.analyticQuery,
                id, name, (command.equals(PostCommand.create)) ? Operations.create : Operations.updateconfig).save(); 
            
            resp.getWriter().print(cc.getId());
            break;
          }
          // c3/data/analyticquery/delete
          case delete:
          {
            String id = req.getParameter("id");
            if (id == null || id.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter(s) 'id'.");
              resp.getWriter().print(msg.toString());
              return;
            }

            CustomConfig cc = CustomConfig.load(uctx, id, CustomConfig.Type.analyticQuery,
                true);
            if (cc == null)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams, String.format(
                      "AnalyticQuery config id '%s' doesn't exist.", id));
              resp.getWriter().print(msg.toString());
              return;
            }
            String name = cc.getName();
            cc.delete();
            
            ArtifactAudit.newInstance(uctx, ItemTypes.analyticQuery,
                id, name,  Operations.delete).save();            
            break;
          }
        }
        
        App.notifyClearCache(uctx.getNamespace(), CustomConfig.Type.analyticQuery.name());
      }
    };
  }
}
