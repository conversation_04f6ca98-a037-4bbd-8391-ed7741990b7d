package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.expression.ScriptObject;
import z1.expression.ScriptObject.ScriptType;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;

public class EventPostProcessingHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete,
    suspend,
    resume
  }

  private enum GetCommand
  {
    all
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/eventprocessing/all => Sends all journeys
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                Type.eventProcessing);
            JsonMarshaller jm = new JsonMarshaller();
            for (CustomConfig cc : ccList)
            {
              if (cc == null) continue;
              try 
              {
                String id = cc.getId();
                if (TemplateUtils.isTemplateConfig(id, Type.eventProcessing.name()))  
                  continue;

                String pl = cc.getPayload();
                Map<String, Object> map = jm.readAsMap(pl);
                map.put("id", id);
                Long lastUpdated = (Long) cc.getDefItem().getValues()
                    .get(DefinitionItem.Fields.lastUpdated.name());
                map.put("lastUpdated", lastUpdated);
                String lastUpdatedBy = (String) cc.getDefItem().getValues()
                    .get(DefinitionItem.Fields.lastUpdatedBy.name());
                map.put("lastUpdatedBy", lastUpdatedBy);
                ret.add(map);
              }
              catch (Throwable e)
              {
                // IGNORE
              }
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;
          }
          
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/eventprocessing/create
          case create:
          {
            String cap = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (cap == null) return;

            JsonMarshaller jm = new JsonMarshaller();
            CustomConfig cc = CustomConfig.load(uctx, Type.eventProcessing, true);
            if (cc == null)
            {
              //create the context attribute object and save the payload
              Map<String, Object> map = jm.readAsMap(cap);
              String name = (String) map.get("name");
              cc = CustomConfig.create(uctx, Type.eventProcessing, name);
              cc.setPayload(cap);
              cc.setName(name);
              cc.save();
              id = cc.getId();
              if (name != null)
              {
                ArtifactAudit.newInstance(uctx, ItemTypes.events, name,
                    name, Operations.edit).save();
              }
            }
            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", id);
            resp.getWriter().print(new JsonMarshaller().serializeMap(map));
            break;
          }
          // c3/data/eventprocessing/update?id=<id>
          case update:
          {
            //update the existing conext attribute by passing the id
            String id = req.getParameter("id");                        
            
            JsonMarshaller jm = new JsonMarshaller();
            String cap = ServletUtil.getPayload(req);
            CustomConfig cc = CustomConfig.load(uctx, id, Type.eventProcessing, true);
            if(cc != null)
            {
              Map<String, Object> map = jm.readAsMap(cap);
              String name = (String) map.get("name");
              cc.setPayload(cap);
              cc.save();
              if (name != null)
              {
                ArtifactAudit.newInstance(uctx, ItemTypes.events, name,
                    name, Operations.edit).save();
              }
              
              // invalidate script object cache
              ScriptObject.invalidateCache(uctx, ScriptType.eventProcessing, id);
            }
            break;
          }
          
          // c3/data/eventprocessing/delete?id=<id>
          case delete:
          {
            String id = req.getParameter("id");
            req.setAttribute("id", id);
            req.setAttribute("type", CustomConfig.Type.eventProcessing.name());
            String[] subParts = new String[pathParts.length + 1];
            subParts[0] = CustomConfig.Type.eventProcessing.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);
            break;
          }
          
          
          // c3/data/eventprocessing/<suspend|resume>?id=<id>
          case suspend:
          case resume:
          {
            String id = req.getParameter("id");

            CustomConfig cc = CustomConfig.load(uctx, id, Type.eventProcessing,
                true);
            if (cc == null) return;
            String payload2 = cc.getPayload2();
            Map<String, Object> ret = TemplateUtils
                .getModuleConfigInstanceReferences(uctx, payload2);
            if (ret == null || !ret.containsKey("type")) return;
            TemplateConst.InstanceConfigType ict = TemplateConst.InstanceConfigType
                .valueOf((String) ret.get("type"));
            Set<String> refs = (Set<String>) ret.get("refs");

            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            if (ict.equals(TemplateConst.InstanceConfigType.primary))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "This enrichment code is primary component of a module instance. Operation '"
                        + command.name()
                        + "' finished and was also applied on its supportive components: "
                        + references);
              }
            }
            else if (ict.equals(TemplateConst.InstanceConfigType.supportive))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream()
                    .collect(Collectors.joining(", "));
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "This enrichmen code is supportive config instance and still being referenced in: "
                        + references);
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            ModuleVisitor.VisitingOperation mvOp;
            if (command.equals(PostCommand.suspend))
            {
              mvOp = ModuleVisitor.VisitingOperation.suspend;
            }
            else
            {
              mvOp = ModuleVisitor.VisitingOperation.resume;
            }

            Artifact art = new Artifact();
            art.setSubtype(ScriptType.eventProcessing.name());
            art.setId(id);
            TemplateArtifact ta = new TemplateArtifact(uctx, null, art, null,
                null, ScriptType.eventProcessing.name(), null);

            ModuleVisitor mv = new ModuleVisitor(uctx, null, mvOp);
            ta.accept(mv);
            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            resp.getWriter().print(msg.toString());
            break;
          }
        }
      }

    };
  }

}
