package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.commons.Const;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class IncentivesHandler implements CommandHandlerFactory
{
  private final JsonMarshaller _jm = Const.jsonMarshaller;

  private enum Query
  {
    // goal related queries
    allGoals("goals/all", CustomConfig.Type.incentiveGoals),
    updateGoals("goals/update", CustomConfig.Type.incentiveGoals),
    unknown("", null);

    Query(String command, CustomConfig.Type configType)
    {
      this._cmd = command;
      this.configType = configType;
    }

    public static Query parse(String query)
    {
      for (Query q : Query.values())
      {
        if (q._cmd.startsWith(query))
          return q;
      }

      return unknown;
    }

    private final String _cmd;
    public final CustomConfig.Type configType;
  }

  private enum IncentiveGoal
  {
    discountRate,
    yield,
    unknown;

    public static IncentiveGoal parse(String goal)
    {
      for (IncentiveGoal g : IncentiveGoal.values())
      {
        if (g.name().equals(goal))
          return g;
      }

      return unknown;
    }
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler()
    {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        Query query = Query.parse(String.join("/", pathParts));

        switch (query)
        {
          case allGoals:
          {
            CustomConfig cc = CustomConfig.load(uctx,
                Query.allGoals.configType);

            if (cc == null)
            {
              resp.getWriter().print(_jm.serialize(Collections.emptyList()));
            }
            else
            {
              String pl = cc.getPayload();

              resp.getWriter().print(pl != null && !pl.trim().isEmpty() ?
                  pl :
                  _jm.serialize(Collections.emptyList()));
            }
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler()
    {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        Query query = Query.parse(String.join("/", pathParts));

        switch (query)
        {
          case updateGoals:
          {
            String payload = ServletUtil.getPayload(req);

            // Payload cannot be empty
            if (payload.trim().isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Invalid payload.");
              resp.getWriter().print(msg);
              return;
            }

            try
            {
              List<Map<String, Object>> newPl = _jm.readAsList(payload);

              // validate each goal in payload.
              long invalidObjs = newPl.stream().filter(
                  m -> m == null || !m.containsKey(
                      "id") || IncentiveGoal.unknown.equals(
                      IncentiveGoal.parse((String) m.get("id")))).count();

              // return failure if any obj in payload is invalid.
              if (invalidObjs > 0)
              {
                ResponseMessage msg = new ResponseMessage(uctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Payload contains invalid goals.");
                resp.getWriter().print(msg);
                return;
              }

              CustomConfig cc = CustomConfig.load(uctx,
                  Query.allGoals.configType, true);

              if (cc == null)
              {
                cc = CustomConfig.create(uctx,
                    CustomConfig.Type.incentiveGoals);
              }

              String ccpl = cc.getPayload();

              // Set new payload directly if new config
              if (ccpl == null || ccpl.trim().isEmpty())
              {
                cc.setPayload(_jm.serialize(newPl));
              }
              else
              {
                // Update new payload into existing payload
                List<Map<String, Object>> existingPl = _jm.readAsList(ccpl);

                newPl.forEach(newgoal -> {
                  // find existing goal if any.
                  Map<String, Object> existingGoal = existingPl.stream()
                      .filter(exg -> exg.get("id").equals(newgoal.get("id")))
                      .findAny().orElse(null);

                  // if not present create and save new goal
                  if (existingGoal == null)
                  {
                    existingPl.add(newgoal);
                  }
                  else
                  {
                    // update existing goal
                    existingGoal.put("value", newgoal.get("value"));
                  }
                });

                // save updated payload
                cc.setPayload(_jm.serialize(existingPl));
              }

              cc.save(true);
              App.notifyClearCache(uctx.getNamespace(),
                  CustomConfig.Type.incentiveGoals.name());
            }
            catch (Exception ex)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Invalid payload.");
              resp.getWriter().print(msg);
              return;
            }

            // Update success
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success);
            resp.getWriter().print(msg);
            return;
          }
        }
      }
    };
  }
}
