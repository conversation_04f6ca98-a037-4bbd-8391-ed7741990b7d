package com.z1.handler;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;

import udichi.core.UContext;
import z1.commons.Utils;
import z1.stats.TopicStatBuilder;

/**
 * Handles the custom audience REST apis.
 */
public class TopicHandler implements CommandHandler
{
  public static final String SEP = "|";

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandler#handle(udichi.core.UContext, java.lang.String[],
   * javax.servlet.http.HttpServletRequest,
   * javax.servlet.http.HttpServletResponse)
   */
  @Override
  public void handle(UContext ctx, String[] pathParts, HttpServletRequest req,
      HttpServletResponse resp) throws Exception
  {
    String subCmd = pathParts[0];

    //c3/data/insights/topic/queryDays?days="<n>"&channelType=<>&filter="channelId,intent,topic"&topk=<k>&x="<topic1,..,topicn>"&i="<topic1,..topicn>"
    if ("queryDays".equalsIgnoreCase(subCmd))
    {
      // get the number of days
      String days = req.getParameter("days");
      if (days == null) return;
      int nDays = Integer.parseInt(days);
      
      // get the topk topics
      int topk=-1;
      String top = req.getParameter("topk");
      if (top != null)
      {
        topk = Integer.parseInt(top);
      }
      
      // get the filter list if any
      String filterCsv = req.getParameter("filter");
      Set<String> filters = extractSet(filterCsv);
      
      //get include topics
      String include = req.getParameter("i");
      Set<String> includeTopics = extractSet(include);
      
      //get exclude topics
      String exclude = req.getParameter("x");
      Set<String> excludeTopics = extractSet(exclude);
    
      UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
      TopicStatBuilder b = new TopicStatBuilder(uctx);
      String channelType = req.getParameter("channelType");
      List<Map<String, Object>> topicRes = b.getTopicStat(uctx,channelType);
      String payload = null;
      if(includeTopics.isEmpty())
      {
        payload = b.heatMapPayload(uctx, topicRes,filters,nDays,null,null,topk,excludeTopics);        
      }
      else
      {
        payload = b.heatMapPayload(uctx, topicRes,filters,nDays,null,null,includeTopics);
      }
      resp.getWriter().print(payload);
    }
    //c3/data/insights/topic/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"&filter="channelId,intent,topic"&queryName="<>"
    else if("queryTimeRange".equalsIgnoreCase(subCmd))
    { 
      String startDate = req.getParameter("startDate");
      if (startDate == null) return;
      
      String endDate = req.getParameter("endDate");
      if (endDate == null) return;
     
      if(startDate.compareTo(endDate)>0) return;
      
      // get the topk topics
      int topk=-1;
      String top = req.getParameter("topk");
      if (top != null)
      {
        topk = Integer.parseInt(top);
      }
           
      // get the filter list if any
      String filterCsv = req.getParameter("filter");
      Set<String> filters = extractSet(filterCsv);
      
      //get include topics
      String include = req.getParameter("i");
      Set<String> includeTopics = extractSet(include);
      
      //get exclude topics
      String exclude = req.getParameter("x");
      Set<String> excludeTopics = extractSet(exclude);
    
      UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
      TopicStatBuilder b = new TopicStatBuilder(uctx);
      String channelType = req.getParameter("channelType");
      List<Map<String, Object>> topicRes = b.getTopicStat(uctx,channelType);
      String payload = null;
      if(includeTopics.isEmpty())
      {
        payload = b.heatMapPayload(uctx, topicRes,filters,-1,startDate,endDate,topk,excludeTopics);        
      }
      else
      {
        payload = b.heatMapPayload(uctx, topicRes,filters,-1,startDate,endDate,includeTopics);
      }  
      resp.getWriter().print(payload);    
    }
    
  }

  private Set<String> extractSet(String filterCsv)
  {
    Set<String> filters = new HashSet<String>();
    if(filterCsv==null || filterCsv.isEmpty()) return filters;
    
    if(filterCsv!=null)
    {
      String[] split = filterCsv.split(",");
      for(String s:split)
      {
        filters.add(s);
      }
    }
    return filters;
  }

}

