package com.z1.handler;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.analytic.aggregation.QueryHandler;
import udichi.core.analytic.query.def.QueryType;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.account.Z1Account;
import z1.account.Z1AccountService;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.AbstractSignal;
import z1.c3.CustomConfig.DataItemWritePolicy;
import z1.c3.CustomConfig.Type;
import z1.c3.EventInfo;
import z1.c3.mapping.EventMapping;
import z1.c3.mapping.EventMappingInfo;
import z1.channel.ChannelType;
import z1.commons.Const;
import z1.commons.EventUtils;
import z1.domains.DomainEventInfo;
import z1.domains.DomainHandler;
import z1.template.JourneyDesignUtils;

public class EventInfoHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    exceptionInfo,
    eventSources,
    mappedEventSources,
    eventMappingRefs,
    paramMappingRefs,
    stats,
    nestedParams
  }

  private enum PostCommand
  {
    delete,
    guidedEventSources,
    clearExceptions
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/eventinfo/all
          case all:
          {
            List<Map<String, Object>> eventList = EventMappingInfo
                .getEventInfo(ctx, true, true);
            resp.getWriter().print(new JsonMarshaller().serialize(eventList));
            break;
          }
          // c3/data/eventinfo/exceptionInfo?name=<event-name>
          case exceptionInfo:
          {
            String name = req.getParameter("name");
            if (name == null || name.length() == 0) return;
            List<Map<String, Object>> info = AbstractSignal
                .getExceptionInfo(ctx, name);
            resp.getWriter().print(new JsonMarshaller().serialize(info));
            break;
          }
          // c3/data/eventinfo/eventSources
          case eventSources:
          {
            List<Map<String, String>> esList = ChannelType.listEventSources();
            resp.getWriter().print(new JsonMarshaller().serialize(esList));
            break;
          }
          case mappedEventSources:
          {
            String event = req.getParameter("event");
            if (event == null || event.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'event'.");
              resp.getWriter().print(msg.toString());
              return;
            }

            List<ChannelType> eventSources = EventMappingInfo
                .getNSEventSources(ctx, event);
            List<Map<String, String>> esList = new ArrayList<>();

            if (eventSources == null || eventSources.isEmpty()
                || eventSources.contains(ChannelType.any))
            {
              esList = ChannelType.listEventSources();
            }
            else
            {
              esList = eventSources.stream().map(es -> {
                Map<String, String> esMap = new HashMap<>(3);
                esMap.put("id", es.name());
                esMap.put("displayName", es.getDisplayName());
                esMap.put("description", es.getDescription());
                return esMap;
              }).collect(Collectors.toList());
            }

            resp.getWriter().print(new JsonMarshaller().serialize(esList));
            break;
          }
          // c3/data/eventinfo/eventMappingRefs?event=<eventName>
          case eventMappingRefs:
          {
            String event = req.getParameter("event");
            if (event == null || event.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'event'.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> refs = EventMappingInfo.getEventMappingRefs(ctx,
                event, false);
            resp.getWriter().print(new JsonMarshaller().serialize(refs));
            break;
          }
          // c3/data/eventinfo/paramMappingRefs?event=<eventName>&param=<paramName>
          case paramMappingRefs:
          {
            String event = req.getParameter("event");
            String param = req.getParameter("param");
            if (event == null || event.isEmpty() || param == null
                || param.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing required parameter 'name' or 'param'.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> refs = EventMappingInfo
                .getEventParamMappingRefs(ctx, event, param, false);
            resp.getWriter().print(new JsonMarshaller().serialize(refs));
            break;
          }
          // c3/data/eventinfo/stats
          case stats:
          {
            LocalDate ld = new Date().toInstant().atZone(ZoneId.systemDefault())
                .toLocalDate();

            int month = ld.getMonthValue();
            String MM = month < 10 ? "0" + month : String.valueOf(month);

            // Fetch date as YYYYMM
            String YYYYMM = "" + ld.getYear() + MM;

            final String queryTemplate = "{"
                + "\"cube\": \"udc.system.core.ZineOne Processing:EventStatsCube\","
                + "\"axis\": [{\"id\": \"eventName\",\"src\": \"event/*\"}],"
                + "\"dataPoint\": [{\"id\": \"count\",\"src\": \"frequency\",\"method\": \"udc.system.aggregation:sum\"}],"
                + "\"filter\": [{\"ref\": \"date\",\"operator\": \">=\",\"value\": \""
                + YYYYMM + "\",\"name\": \"date\"}],"
                + "\"aggregate\": \"eventName\"}";

            QueryType finalQuery = new JsonMarshaller()
                .readAsObject(queryTemplate, QueryType.class);

            QueryHandler qh = new QueryHandler(ctx);
            List<Map<String, Object>> stats = qh.execute(finalQuery);

            resp.getWriter().print(new JsonMarshaller().serialize(stats));
            break;
          }
          case nestedParams:
          {
            resp.getWriter().print(Const.jsonMarshaller.serialize(
                EventMapping.getInstance().getNSMappingInfo(ctx)
                    .getNestedRuleDefList(ctx)));
            break;
          }
          default:
          {
            break;
          }
        }
      }
    };

  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length > 0)
        {
          String str = pathParts[0];
          PostCommand cmd = PostCommand.valueOf(str);

          switch (cmd)
          {
            // c3/data/eventinfo/delete?name=<event-name>
            case delete:
            {
              String eventName = req.getParameter("name");
              ResponseMessage msg = null;
              if (eventName == null || eventName.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing require parameter 'name'");
                resp.getWriter().print(msg.toString());
                return;
              }

              // check if this NS event still maps to any domain event.
              String domainEvent = EventMapping.getInstance()
                  .getNSMappingInfo(uctx).getMappedDomainEvent(eventName);
              if (domainEvent != null)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Event '" + eventName + "' still maps to domain event '"
                        + domainEvent
                        + "'. Remove the mapping before proceeding.");
                resp.getWriter().print(msg.toString());
                return;
              }

              EventInfo edh = new EventInfo(uctx);
              edh.deleteOneDataItem(eventName);
              ArtifactAudit.newInstance(uctx, ItemTypes.events, eventName,
                  eventName, Operations.delete).save();
              break;
            }
            // c3/data/eventinfo/clearExceptions?name=<event-name>
            case clearExceptions:
            {
              String name = req.getParameter("name");
              if (name == null || name.length() == 0) return;
              AbstractSignal.resolveException(uctx, name);
              break;
            }
            // c3/data/eventinfo/guidedEventSources?event=<nsEvent>
            case guidedEventSources:
            {
              String nsEvent = req.getParameter("event");
              ResponseMessage msg = null;
              if (nsEvent == null || nsEvent.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing module 'event' in request.");
                resp.getWriter().print(msg.toString());
                return;
              }

              String pl = ServletUtil.getPayload(req);
              if (pl == null || pl.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing or ill formatted request payload. Expecting list of currently selected sources.");
                resp.getWriter().print(msg.toString());
                return;
              }

              List<String> selected = new JsonMarshaller().readAsObject(pl,
                  List.class);
              List<Map<String, String>> selectedCT = selected.stream()
                  .map(s -> {
                    ChannelType ct = ChannelType.forName(s);
                    Map<String, String> ctMap = new HashMap<>(3);
                    ctMap.put("id", ct.name());
                    ctMap.put("displayName", ct.getDisplayName());
                    ctMap.put("description", ct.getDescription());
                    return ctMap;
                  }).collect(Collectors.toList());

              Map<String, Object> ret = new HashMap<>();

              ret.put("selected", selectedCT);

              List<ChannelType> eventSources = EventMappingInfo
                  .getEventSourcesForNSEvent(uctx, nsEvent);

              if (eventSources.isEmpty())
              {
                ret.put("available", eventSources);
                resp.getWriter().print(new JsonMarshaller().serialize(ret));
                return;
              }

              List<Map<String, String>> ctList = eventSources.stream()
                  .filter(ct -> {
                    return !selected.contains(ct.name());
                  }).map(ct -> {
                    Map<String, String> ctMap = new HashMap<>(3);
                    ctMap.put("id", ct.name());
                    ctMap.put("displayName", ct.getDisplayName());
                    ctMap.put("description", ct.getDescription());
                    return ctMap;
                  }).collect(Collectors.toList());

              ret.put("available", ctList);

              resp.getWriter().print(new JsonMarshaller().serialize(ret));
              break;
            }
            default:
            {
              break;
            }
          }
        }
        else
        {
          String pl = ServletUtil.getPayload(req);
          if (pl.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidPayload,
                "Missing or ill formatted request payload. Expecting event info payload to add/update.");
            resp.getWriter().print(msg);
            return;
          }

          List<Map<String, Object>> eventData = Const.jsonMarshaller.readAsObject(pl, List.class);

          String err = EventUtils.createEvent(uctx, eventData,
              req.getParameter("eventSourceChanged"), true);

          if (err != null)
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed, err);
            resp.getWriter().print(msg);
            return;
          }
        }

        App.notifyClearCache(uctx.getNamespace(), Type.eventInfo.name());
      }

    };

  }

  // /////////////////////////////////////////////////////////////////

}
