/**
 * CustomAudienceHandler.java
 * Copyright 2013-2015 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc. 
 */
package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UException;

/**
 * Implements the REST api for customer journey calls.
 */
public class CustomerJourneyHandler implements CommandHandlerFactory
{

  private static String SEP = "|";

  @Override
  public CommandHandler get()
  {
    throw new UException("Unsupported");
//    return new CommandHandler() {
//      @Override
//      public void handle(final UContext ctx, final String[] pathParts,
//          final HttpServletRequest req, final HttpServletResponse resp)
//          throws Exception
//      {
//        if (pathParts.length == 0) return;
//        String subCmd = pathParts[0];
//        
//        String channelType = req.getParameter("channelType");
//
//        // get the number of days
//        String days = req.getParameter("days");
//        String startDate = null;
//        String endDate = null;
//        int nDays = -1;
//        if (days != null)
//        {
//          try
//          {
//            nDays = Integer.parseInt(days);
//          }
//          catch(Exception e)
//          {
//            nDays = -1;
//          }
//        }
//        else
//        {
//          startDate = req.getParameter("start");
//          if (startDate == null) return;
//          endDate = req.getParameter("end");
//          if (endDate == null) return;
//        }
//        
//        //get the topk topics
//        String top = req.getParameter("topk");
//        int topK = Integer.MAX_VALUE;
//        if(top!=null)
//        {
//          try
//          {
//            topK = Integer.parseInt(top);
//          }
//          catch(Exception e)
//          {
//            topK = Integer.MAX_VALUE;
//          }
//        }
//
//        Context zCtx = Context.getInstance(ctx);
//
//        // c3/data/insights/journey/all?days="<n>" OR start=""&end=""
//        // Returns the full json for journey items distributed for channels.
//        if ("all".equalsIgnoreCase(subCmd))
//        {
//          List<Map<String, Object>> ret = null;
//          if (nDays != -1)
//          {
//            ret = CustomerJourneyItem.getJourneyAnalytics(zCtx, channelType, nDays);
//          }
//          else
//          {
//            ret = CustomerJourneyItem.getJourneyAnalytics(zCtx, channelType, startDate,
//                endDate);
//          }
//
//          String payload = new JsonMarshaller().serialize(ret);
//          resp.getWriter().print(payload);
//        }
//        // c3/data/insights/topics?days="<n>"&channelType=<> OR start=""&end=""
//        else if ("topics".equalsIgnoreCase(subCmd))
//        {
//          String intent = pathParts[1];
//          String intentName = CustomerJourneyItem.JourneyType.forText(intent).name().toUpperCase();
//          UContext uctx = UContext.getInstance().setNamespace(
//              ctx.getNamespace());
//          
//          TopicStatBuilder b = new TopicStatBuilder(uctx);
//          List<Map<String, Object>> topicRes = b.getTopicStatByIntent(uctx,
//              intentName, channelType);
//          String retData;
//          if (nDays != -1)
//          {
//            retData = b.topicTablePayload(uctx, topicRes, new HashSet<String>(), nDays,null,null,topK);
//          }
//          else
//          {
//            retData = b.topicTablePayload(uctx, topicRes, new HashSet<String>(), -1, startDate, endDate,topK);
//          }
//          resp.getWriter().print(retData);
//        }
//
//        // c3/data/insights/journey/<item type>/<channel
//        // id>?days="<n>"[&download="true"]
//        else
//        {
//          if (pathParts.length < 2) return;
//
//          String typeStr = subCmd;
//          String channelId = pathParts[1];
//
//          boolean isDownload = false;
//          String download = req.getParameter("download");
//          if (download != null)
//          {
//            isDownload = new Boolean(download);
//          }
//
//          JourneyType type = JourneyType.forText(typeStr);
//          if (type == null) return;
//
//          if (isDownload)
//          {
//            // For download set the response header
//            resp.setContentType("text/csv");
//            resp.setHeader("Content-Disposition", "attachment; filename=\""
//                + type.name() + ".csv\"");
//          }
//
//          // Returns a list of json fragments
//          List<String> jsons = null;
//          if (nDays != -1)
//          {
//            jsons = CustomerJourneyItem.getDetailData(zCtx, channelId, type,
//                nDays);
//          }
//          else
//          {
//            jsons = CustomerJourneyItem.getDetailData(zCtx, channelId, type,
//                startDate, endDate);
//          }
//
//          StringBuilder ret = new StringBuilder(1024);
//          ret.append("[");
//          boolean isFirst = true;
//          for (String s : jsons)
//          {
//            // Replace new lines for the UI
//            s = s.replaceAll("\\\\n", " ");
//            s = s.replaceAll("\\\\r", " ");
//
//            if (!isFirst)
//            {
//              ret.append(",");
//            }
//
//            ret.append(s);
//            isFirst = false;
//          }
//          ret.append("]");
//
//          // Load the list as a list of maps
//          List<Map<String, Object>> lst = new JsonMarshaller().readAsObject(
//              ret.toString(), List.class);
//
//          if (!isDownload)
//          {
//            // Loop through the data and create links etc.
//
//            for (Map<String, Object> obj : lst)
//            {
//              String handle = (String) obj
//                  .get(CustomerJourneyItem.DetailInfo.handle.name());
//              String name = (String) obj
//                  .get(CustomerJourneyItem.DetailInfo.name.name());
//              String ct = (String) obj
//                  .get(CustomerJourneyItem.Fields.channelType.name());
//
//              if (name != null)
//              {
//                if ((ct == null)
//                    || (Type.Channel.FACEBOOK.name().equalsIgnoreCase(ct)))
//                {
//                  String s = "<a  target='_blank' href='"
//                      + Type.Channel.FACEBOOK.getLink(handle) + "'>" + name
//                      + "</a>";
//                  obj.put(CustomerJourneyItem.DetailInfo.name.name(), s);
//                }
//              }
//            }
//
//            resp.getWriter().print(new JsonMarshaller().serialize(lst));
//          }
//          else
//          {
//            StringBuilder str = new StringBuilder(1024);
//
//            isFirst = true;
//            List<String> keys = new java.util.ArrayList<String>(20);
//            for (Map<String, Object> obj : lst)
//            {
//              if (!isFirst) str.append("\n");
//              // Print the header
//              if (isFirst)
//              {
//                boolean f = true;
//                for (String key : obj.keySet())
//                {
//                  if (key.equalsIgnoreCase(CustomerJourneyItem.DetailInfo.id
//                      .name()))
//                  {
//                    continue;
//                  }
//                  if (!f) str.append(SEP);
//                  str.append(key);
//                  f = false;
//
//                  keys.add(key);
//                }
//                str.append("\n");
//              }
//
//              // Print the data rows
//              boolean f = true;
//              // for (Object val : obj.values())
//              for (String s : keys)
//              {
//                if (!f) str.append(SEP);
//                Object val = obj.get(s);
//                if (val == null) val = "";
//                str.append(val);
//                f = false;
//              }
//              str.append("\n");
//              isFirst = false;
//            }
//
//            resp.getWriter().print(str.toString());
//          }
//
//        }
//
//      }
//    };
  }

  // .............................................
  @Override
  public CommandHandler post()
  {
    throw new UException("Unsupported");
    
//    return new CommandHandler() {
//      /**
//       * Supports post commands to modify a journey element.
//       */
//      @Override
//      public void handle(final UContext ctx, final String[] pathParts,
//          final HttpServletRequest req, final HttpServletResponse resp)
//          throws Exception
//      {
//        if (pathParts.length == 0) return;
//        String subCmd = pathParts[0];
//
//        // Changes the assignment of a journey item to other journey bucket(s)
//        // c3/data/insights/journey/update
//        // The POST payload will have "id" and "intent". Intent will be a CSV
//        if ("update".equalsIgnoreCase(subCmd))
//        {
//          Map<String, Object> data = ServletUtil.loadPayloadAsDataMap(req);
//
//          String id = (String) data.get("id");
//          String csv = (String) data.get("intent");
//
//          if ((id == null) || (csv == null)) return;
//
//          // We'll fire an asynchronous process to change the journey item and
//          // other relevant data
//
//          Map<String, Object> map = new java.util.HashMap<String, Object>(10);
//          map.put(CustomerJourneyItem.Fixer.PARAM_ID, id);
//          map.put(CustomerJourneyItem.Fixer.PARAM_TO, csv);
//          String payload = new JsonMarshaller().serialize(map);
//
//          // Create the job to submit
//          JobQueue jQ = JobQueue.getInstance("journeytypefixjob",
//              CustomerJourneyItem.Fixer.class);
//          Job job = jQ.createJob("execClass", ctx);
//          job.setPayload(payload);
//          try
//          {
//            jQ.submit(job);
//          }
//          catch (UnsupportedJobException e)
//          {
//            // TODO Auto-generated catch block
//
//          }
//
//        }
//        if ("delete".equalsIgnoreCase(subCmd))
//        {
//          Map<String, Object> data = ServletUtil.loadPayloadAsDataMap(req);
//
//          String id = (String) data.get("id");
//          Context zCtx = Context.getInstance(ctx);
//          CustomerJourneyItem.delete(zCtx, id);
//        }
//      }
//    };
  }

}
