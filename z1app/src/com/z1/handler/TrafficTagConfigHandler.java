package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.c3.config.TrafficTagConfig;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
public class TrafficTagConfigHandler implements CommandHandlerFactory
{
  public final static String MAX_GROUP_COUNT = "z1.group.count.max";

  static int maxGroupCount = Integer
  .parseInt(Optional
      .ofNullable((String) z1.commons.Utils
          .getAccountPropertyValue(MAX_GROUP_COUNT))
      .orElse("5"));


  public enum GetCommand
  {
    all
  }

  public enum PostCommand
  {
    add,
    delete,
    update,
    addOrUpdate
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        JsonMarshaller jm = new JsonMarshaller();
        switch (command)
        {
          // c3/data/traffictagconfig/all
          case all:
          {
            if (z1.commons.Utils.isMarketPlace) return;

            List<Map<String, Object>> ccMapList = TrafficTagConfig.getAll(uctx);
            List<Map<String, Object>> tt = ccMapList.stream().map(tag -> {
              tag.put("value", tag.get("allocation"));
              return tag;
            }).collect(Collectors.toList());
            resp.getWriter().print(jm.serialize(tt));
            break;
          }
          default:
            break;
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        final String INVALID_NAME = "Invalid name field. The 'name' field should not be empty, 'none', or exceed 30 characters in length and should not starts with '_' or 'z1' and should only contains alphanumeric values, underscores and spaces.";
        final String INVALID_VALUE = "Invalid value field. Please ensure the 'value' field is provided and is a positive integer and not exceed 100.";
        final String INVALID_SPLIT_COUNT = String.format("Invalid split count. Please ensure the number of splits does not exceed %s", maxGroupCount);
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        JsonMarshaller jm = new JsonMarshaller();
        switch (command)
        {

          // c3/data/traffictagconfig/add
          case add:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            ResponseMessage msg;
            String pl = ServletUtil.getPayload(req);
            if (pl.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload,
                  "Missing or ill formatted request payload.");
              resp.getWriter().print(msg);
              return;
            }

            List<Map<String, Object>> eList = jm.readAsObject(pl, List.class);
            if (eList == null || eList.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload,
                  "Request payload is missing or empty!");
              resp.getWriter().print(msg);
              return;
            }

            List<Map<String, Object>> ttList = TrafficTagConfig.getAll(uctx);

            Map<String, Object> response = new HashMap<>();
            response.put("status", "");
            response.put("message", "");
            response.put("successEntries", Collections.emptyList());
            response.put("failedEntries", Collections.emptyList());

            // if number of splits exceeds the max number allowed
            boolean isValidSplitCount = eList.size() <= maxGroupCount;

            if(!isValidSplitCount)
            {
              response.put("status", "failure");
              response.put("message", INVALID_SPLIT_COUNT);
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            // if value is null, negative, decimal
            boolean hasInvalidValue = eList.stream()
                .anyMatch(m -> !TrafficTagConfig
                    .validateValue(String.valueOf(m.get("value"))));

            if (hasInvalidValue)
            {
              response.put("status", "failure");
              response.put("message", INVALID_VALUE);
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            // existing allocation sum
            int existingSum = ttList.stream()
                .mapToInt(tt -> (int) tt.get("allocation")).sum();

            // payload allocation sum
            int sum = eList.stream()
                .mapToInt(m -> m.get("value") != null
                    ? Integer.parseInt(String.valueOf(m.get("value")))
                    : 0)
                .sum();

            if (sum + existingSum > TrafficTagConfig.MAX_TRAFFIC_ALLOCATION_VALUE)
            {
              response.put("status", "failure");
              response.put("message",
                  "Total % of split groups cannot exceed 100%.");
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            List<Map<String, Object>> successEntries = new ArrayList<>();
            List<Map<String, Object>> failedEntries = new ArrayList<>();
            for (Map<String, Object> eMap : eList)
            {
              Map<String, Object> failureMap = new HashMap<>();
              String name = String.valueOf(eMap.get("name"));

              if (!TrafficTagConfig.validateTagName(name))
              {
                failureMap.put("name", name);
                failureMap.put("error", INVALID_NAME);
                failedEntries.add(failureMap);
                continue;
              }

              // Do not allow if tag name already exists.
              boolean tagExistByName = ttList.stream()
                  .anyMatch(trafficTag -> trafficTag.get("name").equals(name));
              
              if (tagExistByName)
              {
                failureMap.put("name", name);
                failureMap.put("error", "Tag name already exists.");
                failedEntries.add(failureMap);
                // Skip if the tag already exists
                continue;
              }

              Map<String, Object> tg = new HashMap<>();
              tg.put("name", name);
              tg.put("description", eMap.get("description"));
              tg.put("allocation",
                  Integer.parseInt((String) eMap.get("value")));
              
              // Add this new entry as part of the existing tag
              ttList.add(tg);
              successEntries.add(eMap);

              // Adding audit artifact
              ArtifactAudit
                  .newInstance(uctx, ArtifactAudit.ItemTypes.trafficSplitting,
                      TrafficTagConfig.getId(), (String) tg.get("name"),
                      ArtifactAudit.Operations.create)
                  .save();
            }

            if (!successEntries.isEmpty())
            {
              // Save all
              TrafficTagConfig.save(uctx, jm.serialize(ttList));
            }

            // Prepare the final response
            response.put("status",
                successEntries.isEmpty() ? "failure" : "success");
            response.put("message",
                failedEntries.isEmpty() ? "Operation completed."
                    : "Operation completed with some errors.");
            response.put("successEntries", successEntries);
            response.put("failedEntries", failedEntries);

            resp.getWriter().print(jm.serialize(response));
            break;
          }
          // c3/data/traffictagconfig/delete
          case delete:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            ResponseMessage msg;
            String pl = ServletUtil.getPayload(req);
            boolean hasNonStringElements = pl.contains("{") || pl.contains(":");
            if (pl.isEmpty() || hasNonStringElements)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload,
                  "Missing or ill formatted request payload.Please provide List of tag names to delete.");
              resp.getWriter().print(msg);
              return;
            }

            List<String> eList = jm.readAsList(pl);
            if (eList == null || eList.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload,
                  "Request payload is missing or empty!");
              resp.getWriter().print(msg);
              return;
            }
            
            List<Map<String, Object>> ttList = TrafficTagConfig.getAll(uctx);

            List<Map<String, Object>> successEntries = new ArrayList<>();
            List<Map<String, Object>> failedEntries = new ArrayList<>();

            for (String nameToDelete : eList)
            {

              Map<String, Object> failureMap = new HashMap<>();

              if (nameToDelete == null || nameToDelete.trim().isEmpty())
              {
                failureMap.put("name",
                    nameToDelete != null ? nameToDelete : "null");
                failureMap.put("error",
                    "Invalid entry. Missing required 'name' or 'name' field is empty.");
                failedEntries.add(failureMap);
                continue;
              }

              boolean tagExistByName = ttList.stream().anyMatch(
                  trafficTag -> trafficTag.get("name").equals(nameToDelete));
              // Do not allow "none" name or check if tag name doesn't exists
              if ("none".equalsIgnoreCase(nameToDelete) || !tagExistByName)
              {
                failureMap.put("name", nameToDelete);
                failureMap.put("error", "Name does not exists.");
                failedEntries.add(failureMap);
                continue;
              }

              // find the tag and delete
              Optional<Map<String, Object>> ttToDelete = ttList.stream()
                  .filter(tt -> tt.get("name").equals(nameToDelete))
                  .findFirst();

              ttToDelete.ifPresent(ttList::remove);

              Map<String, Object> successMap = new HashMap<>();
              successMap.put("name", nameToDelete);
              successEntries.add(successMap);
              
              // Adding artifact audit
              ArtifactAudit
                  .newInstance(uctx, ArtifactAudit.ItemTypes.trafficSplitting,
                      TrafficTagConfig.getId(), nameToDelete,
                      ArtifactAudit.Operations.delete)
                  .save();
            }

            if (!successEntries.isEmpty())
            {
              // Save all
              TrafficTagConfig.save(uctx, jm.serialize(ttList));
            }

            // Prepare the final response
            Map<String, Object> response = new HashMap<>();
            response.put("status",
                successEntries.isEmpty() ? "failure" : "success");
            response.put("message",
                failedEntries.isEmpty() ? "Operation completed."
                    : "Operation completed with some errors.");
            response.put("successEntries", successEntries);
            response.put("failedEntries", failedEntries);

            resp.getWriter().print(jm.serialize(response));
            break;
          }
          // c3/data/traffictagconfig/update
          case update:
          {

            if (z1.commons.Utils.isMarketPlace) return;
            ResponseMessage msg;
            String pl = ServletUtil.getPayload(req);
            if (pl.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload,
                  "Missing or ill formatted request payload.");
              resp.getWriter().print(msg);
              return;
            }

            List<Map<String, Object>> eList = jm.readAsObject(pl, List.class);
            if (eList == null || eList.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload,
                  "Request payload is missing or empty!");
              resp.getWriter().print(msg);
              return;
            }

            List<Map<String, Object>> ttList = TrafficTagConfig.getAll(uctx);

            Map<String, Integer> trafficTagAllocationMap = TrafficTagConfig
                .getAllocatedTrafficTagAllocations(ttList);

            Map<String, Object> response = new HashMap<>();
            response.put("status", "");
            response.put("message", "");
            response.put("successEntries", Collections.emptyList());
            response.put("failedEntries", Collections.emptyList());

            // if number of splits exceeds the max number allowed
            boolean isValidSplitCount = eList.size() <= maxGroupCount;

            if(!isValidSplitCount)
            {
              response.put("status", "failure");
              response.put("message", INVALID_SPLIT_COUNT);
              resp.getWriter().print(jm.serialize(response));
              return;
            }


            // if value is null, negative, decimal
            boolean hasInvalidValue = eList.stream()
                    .anyMatch(m -> !TrafficTagConfig
                            .validateValue(String.valueOf(m.get("value"))));

            if (hasInvalidValue)
            {
              response.put("status", "failure");
              response.put("message", INVALID_VALUE);
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            // existing allocation sum
            int existingSum = ttList.stream()
                .mapToInt(tt -> (int) tt.get("allocation")).sum();

            // payload allocation sum
            int sum = eList.stream()
                .mapToInt(m -> m.get("value") != null
                    && trafficTagAllocationMap.containsKey(m.get("name"))
                        ? Integer.parseInt(String.valueOf(m.get("value")))
                            - trafficTagAllocationMap.get(m.get("name"))
                        : 0)
                .sum();

            if (sum + existingSum > TrafficTagConfig.MAX_TRAFFIC_ALLOCATION_VALUE)
            {
              response.put("status", "failure");
              response.put("message",
                  "Total % of split groups cannot exceed 100%.");
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            List<Map<String, Object>> successEntries = new ArrayList<>();
            List<Map<String, Object>> failedEntries = new ArrayList<>();

            for (Map<String, Object> eMap : eList)
            {
              Map<String, Object> failureMap = new HashMap<>();
              String name = String.valueOf(eMap.get("name"));

              if (!TrafficTagConfig.validateTagName(name))
              {
                failureMap.put("name", name);
                failureMap.put("error", INVALID_NAME);
                failedEntries.add(failureMap);
                continue;
              }

              if (!trafficTagAllocationMap.containsKey(name))
              {
                failureMap.put("name", name);
                failureMap.put("error",
                    "Tag doesn't exist. Please use the 'add' or 'addOrUpdate' API to add a new tag.");
                failedEntries.add(failureMap);
                continue;
              }

              // Add this new entry as part of the existing tag
              if (trafficTagAllocationMap.containsKey(name))
              {
                Optional<Map<String, Object>> ttToUpdate = ttList.stream()
                    .filter(tt -> tt.get("name").equals(name)).findFirst();
                ttToUpdate.ifPresent(tt -> tt.put("allocation",
                    Integer.parseInt((String) eMap.get("value"))));
              }
              successEntries.add(eMap);
              // Adding artifact audit
              ArtifactAudit
                  .newInstance(uctx, ArtifactAudit.ItemTypes.trafficSplitting,
                      TrafficTagConfig.getId(), (String) eMap.get("name"),
                      ArtifactAudit.Operations.edit)
                  .save();
            }

            if (!successEntries.isEmpty())
            {
              // Save all
              TrafficTagConfig.save(uctx,jm.serialize(ttList));
            }

            // Prepare the final response
            response.put("status",
                successEntries.isEmpty() ? "failure" : "success");
            response.put("message",
                failedEntries.isEmpty() ? "Operation completed."
                    : "Operation completed with some errors.");
            response.put("successEntries", successEntries);
            response.put("failedEntries", failedEntries);

            resp.getWriter().print(jm.serialize(response));
            break;
          }

          case addOrUpdate:
          {
            if (z1.commons.Utils.isMarketPlace) return;
            String pl = ServletUtil.getPayload(req);

            List<Map<String, Object>> eList = jm.readAsObject(pl, List.class);
            List<Map<String, Object>> ttList = TrafficTagConfig.getAll(uctx);

            Map<String, Object> response = new HashMap<>();
            response.put("status", "");
            response.put("message", "");
            response.put("successEntries", Collections.emptyList());
            response.put("failedEntries", Collections.emptyList());

            // if number of splits exceeds the max number allowed
            boolean isValidSplitCount = eList.size() <= maxGroupCount;

            if(!isValidSplitCount)
            {
              response.put("status", "failure");
              response.put("message", INVALID_SPLIT_COUNT);
              resp.getWriter().print(jm.serialize(response));
              return;
            }


            // if value is null, negative, decimal
            boolean hasInvalidValue = eList.stream()
                .anyMatch(m -> !TrafficTagConfig
                    .validateValue(String.valueOf(m.get("value"))));

            if (hasInvalidValue)
            {
              response.put("status", "failure");
              response.put("message", INVALID_VALUE);
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            int sum = eList.stream()
                .mapToInt(m -> m.get("value") != null
                    ? Integer.parseInt(String.valueOf(m.get("value")))
                    : 0)
                .sum();

            if (sum > TrafficTagConfig.MAX_TRAFFIC_ALLOCATION_VALUE)
            {
              response.put("status", "failure");
              response.put("message",
                  "Total % of split groups cannot exceed 100%.");
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            List<Map<String, Object>> successEntries = new ArrayList<>();
            List<Map<String, Object>> failedEntries = new ArrayList<>();
            Set<String> uniqueTraffics = new HashSet<>();
            List<Map<String, Object>> trafficTags = new ArrayList<>();

            for (Map<String, Object> eMap : eList)
            {
              Map<String, Object> failureMap = new HashMap<>();

              String name = String.valueOf(eMap.get("name"));

              if (!TrafficTagConfig.validateTagName(name))
              {
                failureMap.put("name", name);
                failureMap.put("error", INVALID_NAME);
                failedEntries.add(failureMap);
                continue;
              }

              if (uniqueTraffics.contains(name))
              {
                failureMap.put("name", name);
                failureMap.put("error",
                    "Invalid structured request payload. Duplicate tag name.");
                failedEntries.add(failureMap);
                continue;
              }

              Map<String, Object> tg = new HashMap<>();
              tg.put("name", name);
              tg.put("description", eMap.get("description"));
              tg.put("allocation",
                  Integer.parseInt((String) eMap.get("value")));
              trafficTags.add(tg);
              successEntries.add(eMap);
              uniqueTraffics.add(name);
            }

            // Adding artifact audit
            Map<String, Integer> trafficMap = TrafficTagConfig
                .getAllocatedTrafficTagAllocations(ttList);
            for (Map<String, Object> tag : trafficTags)
            {
              if (!trafficMap.containsKey(tag.get("name")))
              {
                ArtifactAudit
                    .newInstance(uctx, ArtifactAudit.ItemTypes.trafficSplitting,
                        TrafficTagConfig.getId(), (String) tag.get("name"),
                        ArtifactAudit.Operations.create)
                    .save();
              }
              else
              {
                if (trafficMap.get(tag.get("name")) != tag.get("allocation"))
                {
                  ArtifactAudit.newInstance(uctx,
                      ArtifactAudit.ItemTypes.trafficSplitting,
                      TrafficTagConfig.getId(), (String) tag.get("name"),
                      ArtifactAudit.Operations.edit).save();
                }
              }
            }
            for (Map.Entry<String, Integer> entry : trafficMap.entrySet())
            {
              String trafficName = entry.getKey();
              boolean isDeleted = !trafficTags.stream().anyMatch(
                  trafficTag -> trafficTag.get("name").equals(trafficName));
              if (isDeleted)
              {
                ArtifactAudit
                    .newInstance(uctx, ArtifactAudit.ItemTypes.trafficSplitting,
                        TrafficTagConfig.getId(), trafficName,
                        ArtifactAudit.Operations.delete)
                    .save();
              }
            }

            // Empty eList means need to delete all tags
            // Empty successEntries means all tags are invalid
            if (eList.isEmpty() || !successEntries.isEmpty())
            {
              // Save all
              TrafficTagConfig.save(uctx, jm.serialize(trafficTags));
            }

            // Prepare the final response
            response.put("status",
                successEntries.isEmpty() && !eList.isEmpty() ? "failure"
                    : "success");
            response.put("message",
                failedEntries.isEmpty() ? "Operation completed."
                    : "Operation completed with some errors.");
            response.put("successEntries", successEntries);
            response.put("failedEntries", failedEntries);

            resp.getWriter().print(jm.serialize(response));

            break;
          }
          default:
            break;
        }

      }

    };
  }
}
