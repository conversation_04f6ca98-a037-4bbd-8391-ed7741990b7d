package com.z1.handler;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.jar.JarEntry;
import java.util.jar.JarInputStream;
import java.util.jar.JarOutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringEscapeUtils;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ArtifactInstaller;
import com.z1.Utils.ArtifactInstaller.Subtype;

import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.application.def.AppItem;
import udichi.core.application.def.AppRegistry;
import udichi.core.application.def.Application;
import udichi.core.application.def.Application.Artifact;
import udichi.core.application.def.StateType;
import udichi.core.data.DataObject;
import udichi.core.data.Result;
import udichi.core.log.ULogger;
import udichi.core.util.JaxbMarshaller;
import udichi.core.util.JsonMarshaller;
import udichi.gateway.defservice.DefinitionItem;
import z1.kb.ContentStore;

public class ArtifactPackageHandler implements CommandHandlerFactory
{
  private ULogger logger;

  private Map<String, byte[]> artifactMap;
  private Map<String, String[]> applicationXMLMap;
  private Map<String, String> updatedIdMap;
  private Map<String, String> nameIdMap;
  private Map<String, String> srcMap; // For payloads in the src directory.

  private List<String> src_meta_mapping;

  private final String UDC_PLUGIN_NAME = "udc.system.core.C3Artifacts";
  private final String PACKAGE_DIR = "META-INF/applications/" + UDC_PLUGIN_NAME
      + "/";
  private final String SOURCE_DIR = "src/applications/" + UDC_PLUGIN_NAME + "/";
  private final String JAR_NAME = "z1artifacts.jar";
  private final String CONTENT = "content";
  private final String CONFIG_DIR = "configs";

  private boolean _overwrite;

  // Supported get commands
  private enum GetCommand
  {
    all
  }

  public ArtifactPackageHandler()
  {
    artifactMap = new HashMap<>();
    applicationXMLMap = new HashMap<>();
    updatedIdMap = new HashMap<>();
    nameIdMap = new HashMap<>();
    srcMap = new HashMap<>();
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {

      @Override
      public void handle(UContext uctx, String[] pathParts,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        logger = uctx.getLogger(getClass());
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/export/all
          case all:
          {
            src_meta_mapping = new ArrayList<>();
            _loadAllDefinitionsToExport(uctx);
            _exportInMemoryPackage(resp, _getInMemoryJar());
            return;
          }
        }
      }
    };
  }

  // c3/data/importartifact
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {

      @Override
      public void handle(UContext uctx, String[] pathParts,
          HttpServletRequest request, HttpServletResponse response)
          throws Exception
      {
        logger = uctx.getLogger(getClass());

        String val = request.getParameter("overwrite");

        _overwrite = "true".equals(val);

        logger.log("Overwrite Config : " + _overwrite);

        // Check if the request actually contains upload file
        if (!ServletFileUpload.isMultipartContent(request))
        {
          logger.log("Error: Form must have enctype=multipart/form-data.");
          return;
        }

        List<FileItem> items = new ServletFileUpload(new DiskFileItemFactory())
            .parseRequest(request);

        if (items == null || items.size() == 0)
        {
          logger.log("Error: "
              + ((items == null) ? "Items is null." : "Items array is 0."));
          return;
        }

        // Only one jar file is being uploaded.
        FileItem z1artifact = items.get(0);

        if (z1artifact != null)
        {
          _initSrcPayloads(uctx, z1artifact);
          _firstPass(uctx, z1artifact);
          _secondPass(uctx, z1artifact);
        }

        return;
      }
    };
  }

  /**
   * Fetches all definitions from mongodb and sets the content to a artifactMap.
   * 
   * @param uctx
   */
  private void _loadAllDefinitionsToExport(UContext uctx)
  {
    // Search for every artifact type.
    for (ArtifactType type : ArtifactType.values())
    {
      // Do not export instance data.
      if (type == ArtifactType.instancedata) continue;

      Result<DefinitionItem> result = DefinitionItem.getAllItems(uctx, type,
          new DefinitionItem.LoadOptions().includePayload(true));

      _setResultToMap(result, type.name());
    }

    // Search for every content type
    for (ContentStore.EntityType etype : ContentStore.EntityType.values())
    {
      Result<ContentStore> result = ContentStore.getAllContent(uctx,
          etype.name());

      _setResultToMap(result, CONTENT);
    }

    _setMappings();
    _setApplicationXML();
    _setAppRegistry();
  }

  /**
   * Save result returned from mongodb to hashmap. Uses generics since result
   * type returned for ArtifactType and ContentStore are different.
   * 
   * @param res
   *          Result object returned from mongo
   * @param artifacttype
   *          Eg: config, content, etc.
   */
  private <T extends DataObject> void _setResultToMap(Result<T> res,
      String artifacttype)
  {
    if (!res.isNull())
    {
      for (T definition : res.getData())
      {
        Map<String, Object> map = definition.getValues();
        if (map != null)
        {
          String path = PACKAGE_DIR + artifacttype.toLowerCase() + "/";
          String src_path = SOURCE_DIR + artifacttype.toLowerCase() + "/";

          String artifactsubtype = null;

          // Since config has subtypes,
          // we add one directory level for subtype under both.
          // Content also goes under "configs" dir.
          if ("config".equals(artifacttype))
          {
            path = PACKAGE_DIR + CONFIG_DIR + "/";
            src_path = SOURCE_DIR + CONFIG_DIR + "/";

            artifactsubtype = _getSubTypeName((String) map.get("custom"));
          }
          else if (CONTENT.equals(artifacttype))
          {
            // Content is also required to be added to the config directory
            path = PACKAGE_DIR + CONFIG_DIR + "/";
            src_path = SOURCE_DIR + CONFIG_DIR + "/";

            // Get content subtype basis AppInstallListener class
            artifactsubtype = _getSubTypeName((String) map.get("type"));
          }

          // Add sub-directory for subtype
          if (artifactsubtype != null)
          {
            // If subtype is unknown and definition id is 'accountContext',
            // add it to default directory
            if (artifactsubtype.equalsIgnoreCase("unknown")
                && "accountContext".equalsIgnoreCase(definition.getId()))
            {
              path = path + "default" + "/";
              src_path = src_path + "default" + "/";
            }
            // If subtype is unknown and it does not have a 'custom' attribute,
            // we are assuming that definition is a data service configuration.
            // Add it to default directory.
            else if (artifactsubtype.equalsIgnoreCase("unknown")
                && !map.containsKey("custom"))
            {
              path = path + "default" + "/";
              src_path = src_path + "default" + "/";
            }
            else
            {
              path = path + artifactsubtype.toLowerCase() + "/";
              src_path = src_path + artifactsubtype.toLowerCase() + "/";
            }
          }

          if (_skipExport(artifactsubtype, definition.getId()))
          {
            continue;
          }

          String artf_instc_name = definition.getId() + ".json";
          String artf_content = new JsonMarshaller().serializeMap(map);

          artifactMap.put(path + artf_instc_name, artf_content.getBytes());

          if (_shouldAddToSrc(artifacttype, artifactsubtype)
              && map.containsKey("payload"))
          {

            String payload = _getSrcPayload(artifacttype, artifactsubtype,
                map.get("payload"));

            if (payload != null)
            {
              String name = _getName(artifacttype, artifactsubtype, map);

              if (name == null) name = definition.getId();

              src_meta_mapping.add(definition.getId() + "=" + name);

              artf_instc_name = name + ".js";

              artifactMap.put(src_path + artf_instc_name, payload.getBytes());

            }
          }

          // Add instance ID as key and save type and subtype as array.
          applicationXMLMap.put(definition.getId(),
              new String[] { artifacttype, artifactsubtype });
        }
      }
    }
  }

  /**
   * For customaction returns 'name' attribute inside the 'payload' attribute,
   * for others returns 'name' attribute.
   * 
   * @param artifacttype
   * @param artifactsubtype
   * @param map
   * @return
   */
  private String _getName(String artifacttype, String artifactsubtype,
      Map<String, Object> map)
  {
    if (map == null) return null;

    if (Subtype.customaction.name().equalsIgnoreCase(artifactsubtype))
    {
      Map<String, Object> payloadmap = new JsonMarshaller()
          .readAsMap(map.get("payload").toString());

      return payloadmap.containsKey("name")
          ? StringEscapeUtils.unescapeJava(payloadmap.get("name").toString())
          : null;
    }
    else
    {
      return map.containsKey("name")
          ? StringEscapeUtils.unescapeJava(map.get("name").toString())
          : null;
    }
  }

  /**
   * Extracts and returns appropriate payloads for allowed artifact types in the
   * 'src' directory.
   * 
   * @param artifacttype
   * @param artifactsubtype
   * @param object
   * @return
   */
  private String _getSrcPayload(String artifacttype, String artifactsubtype,
      Object object)
  {
    if (object == null) return null;

    String payload = null;

    if (Subtype.eventprocessing.name().equalsIgnoreCase(artifactsubtype))
    {
      Map<String, Object> payloadmap = new JsonMarshaller()
          .readAsMap(object.toString());

      if (payloadmap.containsKey("codePayload"))
      {
        payload = payloadmap.get("codePayload").toString();
      }
    }
    else if (Subtype.customaction.name().equalsIgnoreCase(artifactsubtype))
    {
      Map<String, Object> payloadmap = new JsonMarshaller()
          .readAsMap(object.toString());

      // Only save customaction created through content section in c3.
      if (payloadmap.containsKey("type"))
      {
        String type = payloadmap.get("type").toString();
        if (!"CODE".equalsIgnoreCase(type)) return null;
      }

      if (payloadmap.containsKey("payload"))
      {
        payload = payloadmap.get("payload").toString();
      }
    }
    else if (Subtype.campaign.name().equalsIgnoreCase(artifactsubtype))
    {
      Map<String, Object> payloadmap = new JsonMarshaller()
          .readAsMap(object.toString());

      if (payloadmap.containsKey("params"))
      {
        List<Map<String, Object>> list = (List<Map<String, Object>>) payloadmap
            .get("params");

        if (list != null && list.size() > 0)
        {
          // Iterate over definition and check whether definition contains
          // "z1_postaction"
          for (Map<String, Object> temp : list)
          {
            if (temp.containsKey("name") && "z1_postaction"
                .equalsIgnoreCase(temp.get("name").toString()))
            {
              if (temp.containsKey("value"))
              {
                payload = temp.get("value").toString();
              }
              break;
            }
          }
        }
      }
    }
    else if (Subtype.channel.name().equalsIgnoreCase(artifactsubtype))
    {
      Map<String, Object> payloadmap = new JsonMarshaller()
          .readAsMap(object.toString());

      if (payloadmap.containsKey("pre"))
      {

        payloadmap = (Map<String, Object>) payloadmap.get("pre");

        if (payloadmap.containsKey("code"))
          payload = payloadmap.get("code").toString();

      }
    }

    return payload;
  }

  /**
   * Artifact subtypes that we allow to be exported to the 'src' directory.
   */
  private boolean _shouldAddToSrc(String artifacttype, String artifactsubtype)
  {
    return Subtype.campaign.name().equalsIgnoreCase(artifactsubtype)
        || Subtype.channel.name().equalsIgnoreCase(artifactsubtype)
        || Subtype.eventprocessing.name().equalsIgnoreCase(artifactsubtype)
        || Subtype.customaction.name().equalsIgnoreCase(artifactsubtype);
  }

  /**
   * Skip if artifact subtype ==> uploaddownloadstats , suspendedEventList.
   */
  private boolean _skipExport(String artifactsubtype, String id)
  {
    return "uploaddownloadstats".equalsIgnoreCase(artifactsubtype)
        || "suspendedEventList".equalsIgnoreCase(id)
        || "eventblacklist".equalsIgnoreCase(id);
  }

  /**
   * Returns subtype name with reference to AppInstallListener class
   * 
   * @param ctype
   *          SubType from Mongo payload. Config and KBStore collections use
   *          "custom" and "type" respectively to define subtypes
   * @return contentSubTypeName
   */
  private String _getSubTypeName(String ctype)
  {
    if (ctype == null) return Subtype.unknown.toString();

    for (Subtype t : Subtype.values())
    {
      if (t.toString().equalsIgnoreCase(ctype))
      {
        return t.name();
      }
    }

    // For types that we dont identify, return type as unknown.
    return Subtype.unknown.toString();
  }

  private void _exportInMemoryPackage(HttpServletResponse resp,
      ByteArrayOutputStream inMemJar) throws IOException
  {
    resp.setContentType("application/jar");
    resp.setHeader("Content-Disposition",
        "attachment;filename=\"" + JAR_NAME + "\"");

    InputStream is = new ByteArrayInputStream(inMemJar.toByteArray());

    OutputStream os = resp.getOutputStream();

    // Read contents of jar in bytes and write it to the browser through
    // outputstream
    int read = 0;
    byte[] noOfBytes = new byte[1024];

    while ((read = is.read(noOfBytes)) != -1)
    {
      os.write(noOfBytes, 0, read);
    }

    os.flush();
    os.close();
  }

  /**
   * Returns packaged artifacts of in-memory jar instance as
   * ByteArrayOutputStream.
   * 
   * @return In-Memory Jar.
   */
  private ByteArrayOutputStream _getInMemoryJar()
  {
    int jarEntries = 0;
    ByteArrayOutputStream baos = new ByteArrayOutputStream();

    try (JarOutputStream jos = new JarOutputStream(baos))
    {
      // For every entry in the map create a file and package it into the jar.
      for (Map.Entry<String, byte[]> e : artifactMap.entrySet())
      {
        // getKey() returns the artifact-instance-name which will be our file
        // name.
        JarEntry entry = new JarEntry(e.getKey());
        jos.putNextEntry(entry);
        // Write artifact-content to entry created above.
        jos.write(e.getValue());
        jos.closeEntry();
        // Keep count of artifacts exported.
        jarEntries++;
      }
    }
    catch (IOException e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }
    catch (Exception ex)
    {
      z1.commons.Utils.showStackTraceIfEnable(ex, null);
    }
    finally
    {
      logger.log("===============");
      logger.log("Total Files Exported : " + jarEntries);
      logger.log("===============");
    }

    // In-memory Jar instance.
    return baos;
  }

  /**
   * Creates a file entry to maintain mappings of definitions in src and meta
   * directory since artifacts in the src directory are saved by name.
   */
  private void _setMappings()
  {

    String listString = "";

    for (String s : src_meta_mapping)
      listString += s + "\n";

    artifactMap.put("META-INF/" + "mappings.txt", listString.getBytes());
  }

  private void _setApplicationXML()
  {
    Application app = new Application();
    app.setId(UDC_PLUGIN_NAME);
    app.setListener("z1.AppInstallListener");

    for (Entry<String, String[]> art : applicationXMLMap.entrySet())
    {
      // Create artifact
      Artifact arti = new Artifact();
      arti.setId(UDC_PLUGIN_NAME + ":" + art.getKey());
      String[] typearr = art.getValue();
      arti.setType(typearr[0]);

      // If no subtype found set '-'
      arti.setSubtype(typearr[1] != null ? typearr[1] : "-");

      // Add artifact to application.
      app.getArtifact().add(arti);
    }

    String appRegStr = JaxbMarshaller.toString(app);
    artifactMap.put("META-INF/applications/" + UDC_PLUGIN_NAME + ".xml",
        appRegStr.getBytes());

  }

  private void _setAppRegistry()
  {
    AppItem appitem = new AppItem();
    appitem.setId(UDC_PLUGIN_NAME);
    appitem.setIcon("res/add.png");
    appitem.setName("ZineOne Artifacts");
    appitem.setDescription("Stores all the artifacts created on a namespace.");
    appitem.setType("template");
    appitem.setState(StateType.READY);
    appitem.setTag("retail");
    appitem.setCategory("retail");
    appitem.setPublisherId("zineone.com");

    AppRegistry appreg = new AppRegistry();
    appreg.getApp().add(appitem);
    String appRegStr = JaxbMarshaller.toString(appreg);

    artifactMap.put("META-INF/app_reg.xml", appRegStr.getBytes());
  }

  /**
   * Initilize nameIdMap from mappings.txt file and srcMap from 'src' directory.
   * 
   * @param uctx
   * @param z1artifact
   */
  private void _initSrcPayloads(UContext uctx, FileItem z1artifact)
  {

    try (JarInputStream jarIS = new JarInputStream(z1artifact.getInputStream()))
    {
      JarEntry entry = null;

      while ((entry = jarIS.getNextJarEntry()) != null)
      {
        BufferedReader br = new BufferedReader(new InputStreamReader(jarIS));

        String name = entry.getName();

        if (name.endsWith(".DS_Store")) continue;

        // Create a map of name to id mapping for
        // changes that might have come through
        // the'src' directory.
        if (name.endsWith("mappings.txt"))
        {
          String line = null;

          while ((line = br.readLine()) != null)
          {
            String[] arr = line.split("=");
            if (arr.length == 2) nameIdMap.put(arr[0], arr[1]);
          }
        }
        // Create map of all pre/post processing scripts
        // which will be later updated while installing the
        // artifact.
        else if (name.startsWith(SOURCE_DIR)
            && (name.endsWith(".js") || name.endsWith(".xml")))
        {
          String filename = name.substring(name.lastIndexOf("/") + 1,
              name.lastIndexOf("."));

          StringBuilder strb = new StringBuilder();
          String line = null;

          while ((line = br.readLine()) != null)
          {
            strb = strb.append(line);

            if (!line.endsWith("\n")) strb = strb.append("\n");
          }

          if (filename != null) srcMap.put(filename, strb.toString());
        }
      }
    }
    catch (Exception e)
    {
      logger.log("Something went wrong when trying to set name-id map :"
          + e.getMessage());
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }
  }

  /**
   * Installs artifacts that are required by other artifacts. If new IDs are
   * created save them in nameIdMap. Dependent artifacts will replace them in
   * their definition in _secondPass().
   * 
   * @param uctx
   *          UContext
   * @param z1artifact
   *          Jar FileItem
   */
  private void _firstPass(UContext uctx, FileItem z1artifact)
  {
    int jarEntries = 0;
    int artifactsToInstall = 0;
    int artifactsInstalled = 0;
    int artifactsSkipped = 0;

    try (JarInputStream jarIS = new JarInputStream(z1artifact.getInputStream()))
    {
      JarEntry entry = null;

      while ((entry = jarIS.getNextJarEntry()) != null)
      {
        BufferedReader br = new BufferedReader(new InputStreamReader(jarIS));

        String name = entry.getName();
        jarEntries++;

        if (name.endsWith(".DS_Store")) continue;

        if (name.endsWith(".json"))
        {
          String filecontent = br.readLine();
          String artifacttype = getArtifactTypeName(name);
          String artifactsubtype = getArtifactSubTypeName(name);

          Map<String, Object> map = new JsonMarshaller().readAsMap(filecontent);
          if ("configs".equals(artifacttype))
          {
            Subtype st = Subtype.parse(artifactsubtype);

            // ArtifactInstaller class is yet to identify this subtype.
            //
            // NOTE:- There are 2 exceptions.
            // accountContext and other data service configs
            // are parsed as 'unknown' since they donot have a 'custom' attr,
            // but we will still install them.
            if (st == Subtype.unknown
                && !"accountContext".equalsIgnoreCase(map.get("id").toString())
                && !map.containsKey("custom"))
            {
              continue;
            }

            // Artifacts mentioned below are dependent on other artifacts. Hence
            // we will install them in _secondPass()
            if (st == Subtype.campaign || st == Subtype.onetimecampaign
                || st == Subtype.journey)
            {
              continue;
            }

            artifactsToInstall++;

            // Install Content
            if (st == Subtype.contentpush || st == Subtype.contentfullscreen
                || st == Subtype.contentbanner || st == Subtype.contentalert
                || st == Subtype.contentappbox || st == Subtype.contentarticle
                || st == Subtype.contentimage)
            {
              artifactsInstalled++;
              String regId = ArtifactInstaller.installContent(uctx,
                  ArtifactType.config, st, filecontent);

              if (regId != null)
                updatedIdMap.put(map.get("id").toString(), regId);
            }
            else
            {
              artifactsInstalled++;

              String id = map.get("id").toString();

              String regId = ArtifactInstaller.installArtifact(uctx,
                  ArtifactType.config, st, map, _overwrite, nameIdMap, srcMap);

              if (regId != null && !id.contains(regId))
              {
                updatedIdMap.put(map.get("id").toString(), regId);

                logger.log("====Pass 1 : Artifact install id is different====");
                logger.log("Orig ID : " + id);
                logger.log("New ID : " + regId);
                logger.log("===============");
              }
            }
          }
        }
      }
    }
    catch (Exception e)
    {
      logger.log("Something went wrong in _firstPass :" + e.getMessage());
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }
    finally
    {
      logger.log("===============");
      logger.log("Total Files in Jar : " + jarEntries);
      logger.log("===============");
      // logger.log("First Pass : Artifacts To Install : " +
      // artifactsToInstall);
      // logger.log("First Pass : Artifacts Installed : " + artifactsInstalled);
      // logger.log("First Pass : Artifacts Skipped : " + artifactsSkipped);
      // logger.log("===============");
    }
  }

  /**
   * Installs artifacts that are dependent on artifacts installed by
   * _firstPass() and were skipped then.
   * 
   * @param uctx
   *          UContext
   * @param z1artifact
   *          Jar FileItem
   */
  private void _secondPass(UContext uctx, FileItem z1artifact)
      throws IOException
  {
    int artifactsToInstall = 0;
    int artifactsInstalled = 0;
    int artifactsSkipped = 0;

    try (JarInputStream jarIS = new JarInputStream(z1artifact.getInputStream()))
    {
      JarEntry entry = null;
      while ((entry = jarIS.getNextJarEntry()) != null)
      {
        BufferedReader br = new BufferedReader(new InputStreamReader(jarIS));

        String name = entry.getName();

        if (name.endsWith(".DS_Store")) continue;

        // All artifact data is exported as json.
        if (name.endsWith(".json"))
        {
          String filecontent = br.readLine();
          String artifacttype = getArtifactTypeName(name);
          String artifactsubtype = getArtifactSubTypeName(name);

          Map<String, Object> map = new JsonMarshaller().readAsMap(filecontent);
          if ("configs".equals(artifacttype))
          {
            Subtype st = Subtype.parse(artifactsubtype);

            // ArtifactInstaller class is yet to identify this subtype.
            if (st == Subtype.unknown)
            {
              continue;
            }

            if (st == Subtype.campaign || st == Subtype.onetimecampaign
                || st == Subtype.journey)
            {
              artifactsToInstall++;
              artifactsInstalled++;

              String payload = map.get("payload").toString();

              // If any new IDs were generated in _firstPass() replace them in
              // definitions if present.
              for (Map.Entry<String, String> e : updatedIdMap.entrySet())
              {
                payload = payload.replace(e.getKey(), e.getValue().toString());
              }
              map.put("payload", payload);

              String id = map.get("id").toString();

              String regId = ArtifactInstaller.installArtifact(uctx,
                  ArtifactType.config, st, map, _overwrite, nameIdMap, srcMap);

              if (regId != null && !id.contains(regId))
              {
                updatedIdMap.put(map.get("id").toString(), regId);

                logger.log("====Pass 2 : Artifact install id is different====");
                logger.log("Orig ID : " + id);
                logger.log("New ID : " + regId);
                logger.log("===============");
              }
            }
          }
          else if ("datasheet".equals(artifacttype)
              || "dashboard".equals(artifacttype))
          {
            String payload = map.get("payload").toString();

            // If any new IDs were generated in _firstPass() replace them in
            // definitions if present.
            for (Map.Entry<String, String> e : updatedIdMap.entrySet())
            {
              payload = payload.replace(e.getKey(), e.getValue().toString());
            }

            map.put("payload", payload);
            
            //Get ArtifactType from string type
            ArtifactType art = ArtifactType.valueOf(artifacttype);
            
            ArtifactInstaller.installArtifact(uctx, art, null, map, _overwrite,
                nameIdMap, srcMap);
          }
        }
      }
    }
    catch (Exception e)
    {
      logger.log("Something went wrong in _secondPass :" + e.getMessage());
      z1.commons.Utils.showStackTraceIfEnable(e, null);
    }
    finally
    {
      // logger.log("===============");
      // logger.log("Second Pass : Artifacts To Install : " +
      // artifactsToInstall);
      // logger.log("Second Pass : Artifacts Installed : " +
      // artifactsInstalled);
      // logger.log("Second Pass : Artifacts Skipped : " + artifactsSkipped);
      // logger.log("===============");
    }
  }

  /**
   * Check if there is any other artifact already installed with same payload
   * 
   * @param uctx
   * @param payload
   *          Payload of artifact to be installed.
   * @return
   */
  private boolean _artifactExists(UContext uctx, String payload,
      Subtype subtype)
  {
    boolean artifactExists = false;

    for (ArtifactType type : ArtifactType.values())
    {
      Result<DefinitionItem> result = DefinitionItem.getAllItems(uctx, type,
          new DefinitionItem.LoadOptions().includePayload(true));

      for (DefinitionItem definition : result.getData())
      {
        Map<String, Object> defmap = definition.getValues();
        if (defmap != null)
        {
          if (subtype != Subtype.campaign)
          {
            if (defmap.containsKey("payload"))
            {
              String defpayload = defmap.get("payload").toString();
              if (payload.equals(defpayload))
              {
                artifactExists = true;
                break;
              }
            }
          }
          else
          {
            // TI contain signal reference which get updated[lost]
            // if user installs artifacts jar more than one time. Since
            // C3 prevents users from creating campaigns with same name we can
            // assume TI names to be unique. Hence TI installs need to checked
            // against names and not the payload they contain.

            if (defmap.containsKey("name"))
            {
              String defpayload = defmap.get("name").toString();
              if (payload.equals(defpayload))
              {
                artifactExists = true;
                break;
              }
            }
          }
        }
      }
    }
    return artifactExists;
  }

  private String getArtifactTypeName(String name)
  {
    String[] fileDir = name.split("/");
    return fileDir.length > 3 ? fileDir[3] : null;
  }

  private String getArtifactSubTypeName(String name)
  {
    String[] fileDir = name.split("/");

    // If fileDir[] length is 6,file has a subtype.
    return fileDir.length == 6 ? fileDir[4] : null;
  }
}