package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.analytic.aggregation.QueryHandler;
import udichi.core.application.def.Application.Artifact;
import udichi.core.queue.Job;
import udichi.core.queue.JobQueue;
import udichi.core.queue.UnsupportedJobException;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.actions.ActionUtils;
import z1.c3.ExecutionTimeInterval;
import z1.c3.Goal;
import z1.c3.Journey;
import z1.c3.def.GoalDef;
import z1.commons.Utils;
import z1.pubwf.UserTask;
import z1.stats.SegmentStatsRecorder;
import z1.stats.StatsProcessor;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;

public class GoalHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    rerun,
    timePayload,
    id
  }

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    updateTimeInterval,
    delete,
    suspend,
    resume,
    publish,
    unpublish
  }

  private enum GoalSummaryInfo
  {
    id,
    name,
    description,
    users,
    signals,
    actions,
    isSuspended,
    isComplete,
    hasTimeInterval
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);        

        switch (command)
        {
        // c3/data/goals/all => Sends all goals
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

            // We'll get all goal definitions to send them back
            List<Goal> goals = Goal.loadAll(ctx);
            for (Goal g : goals)
            {
              if (g == null) continue;
              try
              {
                // We won't send the hidden goal names
                if (g.isHidden()) continue;

                Boolean isSuspended = new Boolean(false);
                Boolean isComplete = new Boolean(true);
                Boolean hasTimeInterval = new Boolean(false);

                Map<String, Object> map = new java.util.HashMap<>(20);
                map.put(GoalSummaryInfo.id.name(), g.getId());
                map.put(GoalSummaryInfo.name.name(), g.getName());
                map.put(GoalSummaryInfo.description.name(), g.getDescription());

                // Check the status of the goals now
                DefinitionItem item = g.getDefItem();
                if (item != null)
                {
                  String st = (String) item.getValues().get(
                      DefinitionItem.Fields.state.name());
                  if (st != null)
                  {
                    DefinitionItem.State state = DefinitionItem.State
                        .valueOf(st);
                    if (state == State.draft)
                    {
                      isComplete = false;
                    }
                    else
                    {
                      if (state == State.suspended)
                      {
                        isSuspended = true;
                      }
                    }
                  }
                }

                _getGoalStat(ctx, g, map);

                if (ExecutionTimeInterval.hasTimeInterval(g.getTimePayload()))
                {
                  hasTimeInterval = true;
                }

                map.put(GoalSummaryInfo.isComplete.name(), isComplete);
                map.put(GoalSummaryInfo.isSuspended.name(), isSuspended);
                map.put(GoalSummaryInfo.hasTimeInterval.name(), hasTimeInterval);

                ret.add(map);
              }
              catch (Throwable e)
              {
                // Ignore the goal that are rogue and report them to the context
                ctx.getLogger(getClass()).warning("Failed to laod a goal.");
              }
            }

            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);

            return;
          }
          // c3/data/goals/id?goalId=<goal-id>
          case id:
          {
            // This must be a request for a specific goal payload.
            String goalId = req.getParameter("goalId");
            // get the goal payload
            Goal g = Goal.load(ctx, goalId);
            String payload = new JsonMarshaller().serialize(g.getDef());
            resp.getWriter().print(payload);

            return;
          }
          // c3/data/goals/timePayload?goalId=<goal-id>
          case timePayload:
          {
            // This must be a request for a specific goal with time payload
            // information.
            String goalId = req.getParameter("goalId");
            
            Goal g = Goal.load(ctx, goalId);
            String timePayload = g.getTimePayload();
            if (timePayload != null)
            {
              resp.getWriter().print(timePayload);
            }

            return;
          }
          // c3/data/goals/rerun?goalId=<goal-id>
          case rerun:
          {
            String goalId = req.getParameter("goalId");            

            JsonMarshaller jm = new JsonMarshaller();

            Map<String, Object> map = new java.util.HashMap<String, Object>(10);
            map.put("goalId", goalId);

            String payload = jm.serialize(map);

            // Create the job to submit
            JobQueue jQ = JobQueue.getInstance("system.core.goalhandler.rerun",
                z1.c3.GoalEvaluatorOnExistingData.class);
            Job job = jQ.createJob("publishData", ctx);
            job.setPayload(payload);
            try
            {
              jQ.submit(job);
            }
            catch (UnsupportedJobException e)
            {
              ctx.getLogger(getClass()).severe(
                  "Failed to distribute command to rerun goal - "
                      + e.toString());
            }

            return;
          }
          default:
          {
            return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
        // c3/data/goals/create => payload has the definition
        // returns <goal-id>
          case create:
          {
            // Create a channel by loading the payload from the request
            String payload = ServletUtil.getPayload(req);

            Goal g = Goal.create(ctx);
            g.setPayload(payload);
            g.save();
            String gid = g.getId();

            Map<String, Object> map = new java.util.HashMap<>();
            map.put(DefinitionItem.ID, gid);

            resp.getWriter().print(new JsonMarshaller().serializeMap(map));
            return;
          }
          // c3/data/goals/update?gid=<goal-id> => payload has the definition
          case update:
          {
            String gid = req.getParameter("gid");

            String payload = ServletUtil.getPayload(req);

            Goal g = Goal.load(ctx, gid);
            g.setPayload(payload);
            g.save();

            return;
          }
          // c3/data/goals/updateTimeInterval?gid=<goal-id> => execution Time
          // Payload
          case updateTimeInterval:
          {
            String gid = req.getParameter("gid");

            String timePayload = ServletUtil.getPayload(req);

            Goal g = Goal.load(ctx, gid);
            g.setTimePayload(timePayload);
            g.save();

            return;
          }
          // c3/data/goals/delete?gid=<goal-id>
          case delete:
          {
            String gid = req.getParameter("gid");
            req.setAttribute("id", gid);
            req.setAttribute("type", Goal.PREFIX);
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = Goal.PREFIX;
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(ctx, subParts, req, resp);                  
            return;
          }
          // c3/data/goals/resume?gid=<goal-id>
          case resume:
          {
            String gid = req.getParameter("gid");

            Goal g = Goal.load(ctx, gid);
            DefinitionItem item = g.getDefItem();
            if (item != null)
            {
              item.getValues().put(DefinitionItem.Fields.state.name(),
                  DefinitionItem.State.published.name());
              item.save();
            }
            return;
          }
          // c3/data/goals/suspend?gid=<goal-id>
          case suspend:
          {
            String gid = req.getParameter("gid");            

            Goal g = Goal.load(ctx, gid);
            DefinitionItem item = g.getDefItem();
            if (item != null)
            {
              item.getValues().put(DefinitionItem.Fields.state.name(),
                  DefinitionItem.State.suspended.name());
              item.save();
            }
            return;
          }
          // c3/data/goals/publish?gid=<goal-id>
          case publish:
          {
            String gid = req.getParameter("gid");            

            Goal g = Goal.load(ctx, gid);
            DefinitionItem item = g.getDefItem();
            if (item != null)
            {
              item.getValues().put(DefinitionItem.Fields.state.name(),
                  DefinitionItem.State.published.name());
              item.save();
            }
            return;
          }
          // c3/data/goals/unpublish?gid=<goal-id>
          case unpublish:
          {
            String gid = req.getParameter("gid");            

            Goal g = Goal.load(ctx, gid);
            DefinitionItem item = g.getDefItem();
            if (item != null)
            {
              item.getValues().put(DefinitionItem.Fields.state.name(),
                  DefinitionItem.State.draft.name());
              item.save();
            }
            return;
          }
        }

      }
    };

  }

  // /////////////////////////////////////////////////////////////////

  private void _getGoalStat(UContext ctx, Goal g, Map<String, Object> map)
  {
    // We'll calculate the goal reach and the goal action performed
    long totalReach = 0L;
    long totalActions = 0L;

    String gId = g.getId();

    // we'll get all segments used in this goal and get their populations.
    GoalDef def = g.getDef();

    List<GoalDef.Who> whos = def.getWho();

    // We need to get the stat for each segment from the segment frequency cube.
    // For each segment we will calculate the total number from
    // Calculate total reach
    for (GoalDef.Who who : whos)
    {
      // The name matched, we need to get the segments that define this who
      List<String> segments = who.getSegments();

      for (String s : segments)
      {
        long l = SegmentStatsRecorder.getTotalPopulation(ctx, s);
        if (l > 0) totalReach += l;
      }
    }

    // Now get the total action fired for the goal
    List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
    String queryPayload = "{ " 
        + "'cube': '" + StatsProcessor.actionGoalCube + "'," 
        + "'aggregate': 'true',"
        + "'axis': [" + "{'id': 'action','src': 'action/*'}],"
        + "'dataPoint': ["
        + "{ 'id': 'frequency','src': 'frequency', 'method': 'udc.system.aggregation:sum'}],"
        + "'filter': [{ "
        + "'ref': 'goal'," + "'value': '" + gId + "'," + "'operator': '='}]"
        + "}";

    try
    {
      QueryHandler handler = new QueryHandler(ctx);
      String payload = StringUtil.normalizePayload(queryPayload);
      result = handler.execute(payload);
      if ((result != null) && (!result.isEmpty()))
      {
        Map<String, Object> data = result.get(0);
        totalActions = (Long) data.get("frequency");
      }
    }
    catch (UException e)
    {
      // We should ignore it, but capture that in the context
      ctx.getLogger(getClass()).severe(
          "Failed to get actionGoal stats. Reason: " + e.toString());
    }

    map.put(GoalSummaryInfo.users.name(), totalReach);
    map.put(GoalSummaryInfo.actions.name(), totalActions);
    map.put(GoalSummaryInfo.signals.name(), totalActions);
  }

}
