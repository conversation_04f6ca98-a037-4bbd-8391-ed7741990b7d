package com.z1.handler;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.analytic.aggregation.QueryHandler;
import udichi.core.analytic.query.def.QueryType;
import udichi.core.application.def.Application.Artifact;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import z1.c3.CustomConfig;
import z1.c3.Journey;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.core.utils.FileLoaderStats;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;

public class CubeQueryHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete,
    multidelete,
    query,  // Returns data for a given query
  }

  private enum GetCommand
  {
    all, // All query definitions
    id, // name of the pipeline/query example: getTopKProductsByCategory
    checkUniqueName, // Check if a given name is unique for naming cube query
    query  // Returns data for a given query
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/cubequery/all?cubeId=<query-name>
          case all:
          {
            String cubeId = req.getParameter("cubeId");
            List<CustomConfig> configs = CustomConfig.loadAll(uctx,
                Type.cubeQuery);
            if (configs.isEmpty())
            {
              resp.getWriter().print("[]");
              return;
            }
            List<Map<String, Object>> cubeQueries = com.z1.Utils.ApiUtils.getQueriesForTheCube(cubeId, uctx);
            String queries = new JsonMarshaller().serialize(cubeQueries);
            resp.getWriter().print(queries);
            return;
          }
          // c3/data/cubequery/id?id=<query-name>
          case id:
          {
            String id = req.getParameter("id");
            
            String payload = null;
            CustomConfig ccs = CustomConfig.load(uctx, id, Type.cubeQuery, true);
            if (ccs == null)
            {
              resp.getWriter().print("[]");
            }
            else
            {
              payload = ccs.getPayload();
              resp.getWriter().print(payload);
            }
            
            return;
          }
          // c3/data/cubequery/checkUniqueName?name=<..>
          // check if a proposed name is unique for naming cubequeries
          case checkUniqueName:
          {
            String name = req.getParameter("name");
            ResponseMessage msg = null;
            if (name == null || name.isEmpty()) 
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Please provide a non-empty name");
            }
            else if (!com.z1.Utils.ApiUtils.isValidCubeQueryName(uctx, name))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Name is already used, please provide a different name");
            } else
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.uniqueCheckSuccess,
                  "The name '" + name + "' is unique.");
            }
            resp.getWriter().print(msg.toString());
            return;
          }
          // c3/data/cubequery/query?query=<query-name>
          // returns the data for a given cube query
          case query:
          {
            String query = req.getParameter("query");

            CustomConfig ccs = CustomConfig.load(uctx, query, Type.cubeQuery,
                true);
            if (ccs == null) return;

            String storedJson = ccs.getPayload();
            if (storedJson == null) return;

            Map<String, Object> reqParams = new java.util.HashMap<>(10);
            Enumeration<String> pNames = req.getParameterNames();
            while (pNames.hasMoreElements())
            {
              String n = pNames.nextElement();
              String v = req.getParameter(n);
              reqParams.put(n, v);
            }

            // Substitute the params value in the query def
            String queryJson = StringUtil.substitueValue(storedJson, reqParams,
                "", null);

            QueryType queryType = new JsonMarshaller()
                .readAsObject(queryJson.replace("\n", ""), QueryType.class);
            QueryHandler qh = new QueryHandler(uctx);
            List<Map<String, Object>> res = qh.execute(queryType);
            if (res == null) return;
            resp.getWriter().print(new JsonMarshaller().serialize(res));
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/cubequery/create?queryName=<query-name> => payload has the definition
          // returns <cube-id>
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            String queryName = req.getParameter("queryName");            

            CustomConfig cc = CustomConfig.load(uctx, queryName, Type.cubeQuery, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, Type.cubeQuery, queryName);
            }
            cc.setPayload(payload);
            cc.save();
            
            Map<String, Object> m = new java.util.HashMap<>();
            m.put("id", cc.getId());
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            break;
          }
          case update:
          {
            // c3/data/cubequery/update?id=<id>
            // update the existing conext attribute by passing the id
            String id = req.getParameter("id");
            
            String cap = ServletUtil.getPayload(req);
            CustomConfig cc = CustomConfig.load(uctx, id, Type.cubeQuery, true);
            if (cc != null)
            {
              cc.setPayload(cap);
              cc.save();
            }
            break;
          }
          case delete:
          {
            // c3/data/cubequery/delete?id=<id>
            String id = req.getParameter("id");
            
            // delete the given context attribute
            CustomConfig cc = CustomConfig.load(uctx, id, Type.cubeQuery);
            if (cc != null)
            {
              CustomConfig.delete(uctx, id, Type.cubeQuery);
            }
            
            req.setAttribute("id", id);
            req.setAttribute("type", "cubequery");
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = "cubequery";
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);
            break;
          }
          // c3/data/cubequery/multidelete?ids=id1,id2,id3,id4...
          // do a batch delete for cubequeries
          case multidelete:
          {
            String[] ids = null;
            if (req.getParameter("ids")!=null) {
              ids = req.getParameter("ids").split(",");
            }
            if (ids==null)
              break;
            
            for (String id: ids) {
              CustomConfig cc = CustomConfig.load(uctx, id, Type.cubeQuery);
              if (cc != null)
              {
                CustomConfig.delete(uctx, id, Type.cubeQuery);
              }
              req.setAttribute("id", id);
              req.setAttribute("type", "cubequery");
              String[] subParts = new String[pathParts.length + 1];
              subParts[0] = "cubequery";
              subParts[1] = "delete";
              try {
                new BXHandler().post().handle(uctx, subParts, req, resp);
              } catch (Exception e) {
                z1.commons.Utils.showStackTraceIfEnable(e, null);
              }
            }
            break;
          }
          
          // c3/data/cubequery/query
          // returns the data for a given cube query payload
          case query:
          {        
            String storedJson = ServletUtil.getPayload(req);
            if (storedJson == null) return;
            
            Map<String, Object> reqParams = new java.util.HashMap<>(10);
            Enumeration<String> pNames = req.getParameterNames();
            while (pNames.hasMoreElements())
            {
              String n = pNames.nextElement();
              String v = req.getParameter(n);
              reqParams.put(n, v);
            }
            
            // Substitute the params value in the query def
            String queryJson = StringUtil.substitueValue(storedJson, 
                reqParams, "", null);
          
            QueryType queryType = new JsonMarshaller().readAsObject(queryJson.replace("\n", ""),
                QueryType.class);
            QueryHandler qh = new QueryHandler(uctx);
            List<Map<String, Object>> res = qh.execute(queryType);
            if (res == null) return;
            resp.getWriter().print(new JsonMarshaller().serialize(res));
            return;
          }
        }

      }

    };
  }

  ///////////////////////////

}
