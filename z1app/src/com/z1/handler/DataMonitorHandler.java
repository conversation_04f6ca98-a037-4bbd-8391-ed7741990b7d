package com.z1.handler;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.datamonitor.def.MonitorDef;

/**
 * Handles CRUD operations for all DataMonitor configs
 */
public class DataMonitorHandler implements CommandHandlerFactory
{
  private final static JsonMarshaller jm = new JsonMarshaller();

  private enum GetCommand
  {
    all,
    id,
    pause,
    resume
  }

  private enum PostCommand
  {
    create,
    update,
    delete
  }

  @Override
  public CommandHandler get()
  {
    return (uctx, pathParts, req, resp) -> {
      // c3/data/monitor/
      String cStr = pathParts[0];

      switch (GetCommand.valueOf(cStr))
      {
        // c3/data/monitor/all
        case all:
        {
          List<CustomConfig> configList = CustomConfig.loadAll(uctx,
              CustomConfig.Type.datamonitor);

          if (configList == null || configList.isEmpty())
          {
            resp.getWriter().print(jm.serialize(Collections.emptyList()));
            return;
          }

          List<Map<String, Object>> res = configList.stream()
              .map(CustomConfig::getPayload).map(jm::readAsMap)
              .collect(Collectors.toList());

          resp.getWriter().print(jm.serialize(res));
          return;
        }
        // c3/data/monitor/id?id=<monitor-id>
        case id:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams);
            resp.getWriter().print(msg);
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, id,
              CustomConfig.Type.datamonitor, true);

          Map<String, Object> def = (cc != null) ? jm.readAsMap(cc.getPayload())
              : Collections.emptyMap();

          resp.getWriter().print(jm.serialize(def));
          return;
        }
        // c3/data/monitor/pause?id=<monitor-id>
        // c3/data/monitor/resume?id=<monitor-id>
        case resume:
        case pause:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams);
            resp.getWriter().print(msg);
            return;
          }

          try
          {
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.datamonitor, true);

            if (cc == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.configUnavailable);
              resp.getWriter().print(msg);
              return;
            }

            DefinitionItem.State state = GetCommand.resume.name().equals(cStr)
                ? DefinitionItem.State.published
                : DefinitionItem.State.suspended;
            
            cc.save(state);

            resp.getWriter().print(id);
            return;
          }
          catch (Exception e)
          {
            ULogger logger = ULogger.instance(uctx);
            if (logger.canLog())
              logger.log(String.format("c3/data/monitor/%s: Exception: %s",
                  cStr, e.getMessage()));
          }
          return;
        }
        default:
        {
          break;
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return (uctx, pathParts, req, resp) -> {
      // c3/data/monitor/
      String cStr = pathParts[0];

      switch (PostCommand.valueOf(cStr))
      {
        // c3/data/monitor/create?id=<monitor-id>
        case create:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams);
            resp.getWriter().print(msg);
            return;
          }

          try
          {
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.datamonitor, true);

            if (cc != null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.configExists);
              resp.getWriter().print(msg);
              return;
            }

            String payload = ServletUtil.getPayload(req);

            if (payload.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload);
              resp.getWriter().print(msg);
              return;
            }

            MonitorDef monitorDef = jm.readAsObject(payload, MonitorDef.class);

            // Return if qsp id mismatch with id from payload to update.
            if (!id.equals(monitorDef.getId()))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload);
              resp.getWriter().print(msg);
              return;
            }

            cc = CustomConfig.create(uctx, CustomConfig.Type.datamonitor, id);
            cc.setName(monitorDef.getId());
            cc.setPayload(jm.serialize(monitorDef));
            cc.save(true);

            resp.getWriter().print(id);
            return;
          }
          catch (Exception e)
          {
            ULogger logger = ULogger.instance(uctx);
            if (logger.canLog())
              logger.log(String.format("c3/data/monitor/create: Exception: %s",
                  e.getMessage()));
          }

          return;
        }
        // c3/data/monitor/update?id=<monitor-id>
        case update:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams);
            resp.getWriter().print(msg);
            return;
          }

          try
          {
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.datamonitor, true);

            if (cc == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.configUnavailable);
              resp.getWriter().print(msg);
              return;
            }

            String payload = ServletUtil.getPayload(req);

            if (payload.isEmpty())
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload);
              resp.getWriter().print(msg);
              return;
            }

            MonitorDef monitorDef = jm.readAsObject(payload, MonitorDef.class);

            // Return if cc id mismatch with id from payload to update.
            if (!cc.getId().equals(monitorDef.getId()))
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidPayload);
              resp.getWriter().print(msg);
              return;
            }

            cc.setPayload(jm.serialize(monitorDef));
            cc.save(DefinitionItem.State.valueOf(cc.getState()));

            resp.getWriter().print(id);
            return;
          }
          catch (Exception e)
          {
            ULogger logger = ULogger.instance(uctx);
            if (logger.canLog())
              logger.log(String.format("c3/data/monitor/update: Exception: %s",
                  e.getMessage()));
          }
          return;
        }
        // c3/data/monitor/delete?id=<monitor-id>
        case delete:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams);
            resp.getWriter().print(msg);
            return;
          }

          try
          {
            CustomConfig cc = CustomConfig.load(uctx, id,
                CustomConfig.Type.datamonitor, true);

            if (cc == null)
            {
              ResponseMessage msg = new ResponseMessage(uctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.configUnavailable);
              resp.getWriter().print(msg);
              return;
            }

            cc.delete();

            resp.getWriter().print(id);
            return;
          }
          catch (Exception e)
          {
            ULogger logger = ULogger.instance(uctx);
            if (logger.canLog())
              logger.log(String.format("c3/data/monitor/delete: Exception: %s",
                  e.getMessage()));
          }
          return;
        }
        default:
        {
          break;
        }
      }
    };
  }
}
