package com.z1.handler;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.commons.Utils;
import z1.stats.EventDistributionAnalytics;

/**
 * Handles the event flow data feed to UI.
 */
public class EventFlowHandler implements CommandHandlerFactory
{

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/eventflow?name=<event-name>&root=<root-name>&segment=<segment-id>&insession=<true|false>
        String eventName = req.getParameter("name");
        String rootEventName = req.getParameter("root");
        String depthStr = req.getParameter("depth");
        String segmentId = req.getParameter("segment");
        String isInSession = req.getParameter("insession");

        Integer depth = null;
        if (depthStr != null)
        {
          try
          {
            depth = Integer.valueOf(depthStr);
          }
          catch (Throwable e)
          {
            // Ignore
          }
        }
        EventDistributionAnalytics handler = new EventDistributionAnalytics(ctx);
        Map<String, Object> map = handler.getEventFlowStats(eventName,
            rootEventName, depth, segmentId, Boolean.parseBoolean(isInSession));

        resp.getWriter().print(new JsonMarshaller().serialize(map));
      }
    };

  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
      }

    };

  }

  // /////////////////////////////////////////////////////////////////

}
