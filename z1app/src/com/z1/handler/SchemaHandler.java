package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import udichi.core.util.JsonMarshaller;
import z1.commons.Const;
import z1.commons.EventUtils;
import z1.domains.DomainHandler;

import java.util.Map;
import java.util.stream.Collectors;

public class <PERSON>hema<PERSON><PERSON><PERSON> implements CommandHandlerFactory
{
  private enum Command
  {
    eventschema,
    datatypes
  }
  @Override
  public CommandHandler get()
  {
    return (uctx, pathParts, req, resp) -> {

      if (z1.commons.Utils.isMarketPlace)
      {
        return;
      }

      resp.setContentType("application/json");
      resp.setCharacterEncoding("UTF-8");

      Command command = Command.valueOf(pathParts[0]);
      JsonMarshaller jm = Const.jsonMarshaller;

      // Check if api is requesting oldschema
      boolean oldSchema = false;
      String oldsch = req.getParameter("oldSchema");
      if (oldsch != null && !oldsch.trim().isEmpty())
      {
        oldSchema = Boolean.parseBoolean(oldsch);
      }

      String version = oldSchema ? "v1" : "v2";

      switch (command)
      {
        case eventschema:
        {
          resp.getWriter().print(jm.serialize(EventUtils.getEventSchema(uctx, version)));
          break;
        }
        case datatypes:
        {
          DomainHandler domainHandler = DomainHandler.instance(uctx, version);

          String datatype = req.getParameter("id");

          String dataTypeSchema;
          if (datatype != null)
          {
            dataTypeSchema =  jm.serialize(domainHandler.getDataTypeSchema(datatype.trim()));
          }
          else
          {
            Map<String, Object> res = domainHandler.getComplexDataTypes()
                .stream().collect(Collectors.toMap(t -> t,
                    domainHandler::getDataTypeSchema));
            dataTypeSchema = jm.serialize(res);
          }

          resp.getWriter().print(dataTypeSchema);
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return null;
  }
}
