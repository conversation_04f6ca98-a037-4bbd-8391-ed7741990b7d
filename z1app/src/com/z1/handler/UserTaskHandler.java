package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.c3.Journey;
import z1.pubwf.UserTask;

public class UserTaskHandler implements CommandHandlerFactory
{

  private enum GetCommand
  {
    all
  }

  private enum PostCommand
  {

  }

  private enum ArtifactType
  {
    campaign,
    campaignonce,
    journey,
    segment
  }

  private enum Field
  {
    artifactId,
    artifactName,
    artifactType
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
        // c3/data/userTask/all -> Returns all tasks in a user's tray
          case all:
          {
            // if there are tasks then fetch the artifact name missing in
            // UserTask for each task
            // put that name in the list and return as payload
            List<UserTask> tasks = UserTask.getTasksForUser(ctx);

            // create a list which will hold the payload of the new map
            // containing Usertasks with artifactNames
            List<Map<String, Object>> list = new java.util.ArrayList<>(20);
            if (!tasks.isEmpty())
            {
              for (UserTask utask : tasks)
              {
                // get the artifact name for each artifact inside the task
                String artifactId = (String) utask.getValues().get(
                    Field.artifactId.name());
                String type = (String) utask.getValues().get(
                    Field.artifactType.name());

                Map<String, Object> newMap = getArtifactDetails(type,
                    artifactId, ctx);

                // get the new map without all unnecessary values
                Map<String, Object> map = new HashMap<String, Object>(10);
                utask.populate(map);
                // populate the map with the artifact name to be returned in
                // payload
                map.putAll(newMap);
                list.add(map);

              }
              String payload = new JsonMarshaller().serialize(list);
              resp.getWriter().print(payload);
            }
            else
            {
              resp.getWriter().print("[]");
            }

            return;
          }
        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {

        }

      }
    };
  }

  /**
   * Gets the artifact details(name & isComplete state) to be added to the task
   * details
   */
  private Map<String, Object> getArtifactDetails(String artifactType,
      String artifactId, UContext ctx)
  {
    if (artifactId == null) return null;
    Map<String, Object> map = new HashMap<String, Object>(10);
    boolean isComplete = true;
    switch (artifactType)
    {
      case "campaign":
      case "campaignonce":
      case "journey":
      {
        Journey.Type jType;
        if (artifactType.equals(ArtifactType.campaign.name()))
          jType = Journey.Type.campaign;
        else if (artifactType.equals(ArtifactType.campaignonce.name()))
          jType = Journey.Type.c1;
        else
          jType = Journey.Type.journey;
        Journey j = Journey.forceLoadDef(ctx, artifactId, jType);
        if (j != null)
        {
          // Add the artifact name to the map
          map.put(Field.artifactName.name(), j.getName());
        }

        break;
      }

    }
    return map;
  }

}
