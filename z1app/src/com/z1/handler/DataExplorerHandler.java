/**
 * 
 */
package com.z1.handler;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.commons.Utils;
import z1.tools.DataExplorer;

public class DataExplorerHandler implements CommandHandlerFactory
{
  private static final String NAMESPACE = "namespace";
  private static final String CUBENAME = "cubename";
  private static final String TIME = "time";
  private static final String INTERVAL = "interval";
  private static final String QUERY = "query";
  private static final String DOWNLOAD = "download";

  // All commands correspond to z1.tools.DataExplorer.Command
  private enum PostCommand
  {
    updateCubeData,
    deleteAllCubeData,
    deleteCubeData
  }

  private enum GetCommand
  {
    viewCubeData,
    viewActionLog,
    dumpActionLog,
    getRowKey
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {

      @Override
      public void handle(UContext uctx, String[] pathParts,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        final ResponseMessage err = new ResponseMessage(uctx,
            ResponseMessage.Status.fail, ResponseMessage.Type.invalidParams);

        GetCommand command = GetCommand.valueOf(pathParts[0]);

        switch (command)
        {
          // c3/data/explore/viewCubeData?namespace=<namespace>&cubename=<cubename>&download=<true/false>
          // 'download' param is optional
          case viewCubeData:
          {
            String namespace = req.getParameter(NAMESPACE);
            String cubename = req.getParameter(CUBENAME);
            String download = req.getParameter(DOWNLOAD);

            if (namespace.isEmpty() || cubename.isEmpty())
            {
              resp.getWriter().print(err.toString());
              return;
            }

            List<Map<String, Object>> result = DataExplorer.viewCubes(uctx,
                namespace, cubename);

            String finalres = new JsonMarshaller().serialize(result);

            if (Boolean.parseBoolean(download))
            {
              _exportFile(resp, "application/json", finalres.getBytes(),
                  cubename);
            }
            else
            {
              resp.getWriter().print(finalres);
            }
            return;
          }
          // c3/data/explore/getRowKey?namespace=<namespace>&cubename=<cubename>
          case getRowKey:
          {
            String namespace = req.getParameter(NAMESPACE);
            String cubename = req.getParameter(CUBENAME);

            if (namespace.isEmpty() || cubename.isEmpty())
            {
              resp.getWriter().print(err.toString());
              return;
            }

            String result = DataExplorer.getRowKeyforCubes(uctx, namespace, cubename);
           
            resp.getWriter().print(result);

            return;
          }
          // c3/data/explore/dumpActionLog?download=<true/false>
          // 'download' param is optional
          case dumpActionLog:
          {
            String download = req.getParameter(DOWNLOAD);

            Map<String, Object> result = DataExplorer.dumpActionLog(uctx);

            String finalres = new JsonMarshaller().serialize(result);

            if (Boolean.parseBoolean(download))
            {
              _exportFile(resp, "application/json", finalres.getBytes(),
                  "dumpActionLog_" + System.currentTimeMillis());
            }
            else
            {
              resp.getWriter().print(finalres);
            }

            return;
          }
          // c3/data/explore/viewActionLog?time=<_time_milisec_>&interval=<_interval_hours_>&download=<true/false>
          // 'download' param is optional
          case viewActionLog:
          {
            String time = req.getParameter(TIME);
            String interval = req.getParameter(INTERVAL);
            String download = req.getParameter(DOWNLOAD);

            if (time.isEmpty() || interval.isEmpty())
            {
              resp.getWriter().print(err.toString());
              return;
            }

            long parsedTime = -1;
            int parsedInterval = -1;

            try
            {
              parsedTime = Long.parseLong(time);
              parsedInterval = Integer.parseInt(interval);
            }
            catch (NumberFormatException ne)
            {
              resp.getWriter().print(err.toString());
              return;
            }

            if (parsedInterval < 0 || parsedTime < 0)
            {
              resp.getWriter().print(err.toString());
              return;
            }

            Map<String, Object> result = DataExplorer.viewActionLog(uctx,
                parsedTime, parsedInterval);

            String finalres = new JsonMarshaller().serialize(result);

            if (Boolean.parseBoolean(download))
            {
              _exportFile(resp, "application/json", finalres.getBytes(),
                  "viewActionLog_" + System.currentTimeMillis());
            }
            else
            {
              resp.getWriter().print(finalres);
            }
            return;
          }
        }
      }

      private void _exportFile(HttpServletResponse resp, String contenttype,
          byte[] barr, String filename) throws IOException
      {
        // Get rid of spl symbols in file name.
        filename = filename.replaceAll("[^A-Za-z0-9]", "_");

        if (contenttype.contains("json") && !filename.endsWith("json"))
        {
          filename += ".json";
        }

        resp.setContentType(contenttype);
        resp.setHeader("Content-Disposition",
            "attachment;filename=\"" + filename + "\"");

        InputStream is = new ByteArrayInputStream(barr);

        OutputStream os = resp.getOutputStream();

        int read = 0;
        byte[] noOfBytes = new byte[1024];

        while ((read = is.read(noOfBytes)) != -1)
        {
          os.write(noOfBytes, 0, read);
        }

        os.flush();
        os.close();
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {

      @Override
      public void handle(UContext uctx, String[] pathParts,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        final ResponseMessage err = new ResponseMessage(uctx,
            ResponseMessage.Status.fail, ResponseMessage.Type.invalidParams);

        final ResponseMessage success = new ResponseMessage(uctx,
            ResponseMessage.Status.success);

        PostCommand command = PostCommand.valueOf(pathParts[0]);

        String namespace = req.getParameter(NAMESPACE);

        if (namespace.isEmpty())
        {
          resp.getWriter().print(err.toString());
          return;
        }

        switch (command)
        {
          // c3/data/explore/deleteAllCubeData?namespace=<namespace>
          case deleteAllCubeData:
          {
            DataExplorer.deleteAllCubeData(uctx, namespace);

            resp.getWriter().print(success.toString());
            return;
          }
          // c3/data/explore/deleteAllCubes?namespace=<namespace>&cubename=<cubename>
          case deleteCubeData:
          {
            String cubename = req.getParameter(CUBENAME);

            if (cubename.isEmpty())
            {
              resp.getWriter().print(err.toString());
              return;
            }

            DataExplorer.deleteCubeData(uctx, namespace, cubename);

            resp.getWriter().print(success.toString());
            return;
          }
          // c3/data/explore/updateCubeData?namespace=<namespace>&cubename=<cubename>
          case updateCubeData:
          {

            String cubename = req.getParameter(CUBENAME);

            String query = null;
            try
            {
              query = ServletUtil.getPayload(req);
            }
            catch (Exception e)
            {
              ULogger logger = ULogger.instance(uctx);
              if (logger.canLog()) logger.log(
                  "c3/data/explore/updateCubeData:  payload is ill formatted or null.");
            }
            if (query.isEmpty() || cubename.isEmpty())
            {
              resp.getWriter().print(err.toString());
              return;
            }
            DataExplorer.updateCube(uctx, namespace, cubename, query);

            resp.getWriter().print(success.toString());
            return;
          }
        }
      }
    };
  }

}
