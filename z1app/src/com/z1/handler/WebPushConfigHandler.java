package com.z1.handler;

import java.security.InvalidAlgorithmParameterException;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.Security;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.ECNamedCurveTable;
import org.bouncycastle.jce.interfaces.ECPrivateKey;
import org.bouncycastle.jce.interfaces.ECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECNamedCurveParameterSpec;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;

public class WebPushConfigHandler implements CommandHandlerFactory
{

  private static final String PRIVATE_KEY = "privateKey";
  private static final String PUBLIC_KEY = "publicKey";
  private static final String STATUS_CODE = "status";
  private static final String STATUS_MSG = "message";

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/webpushconfig/all
        String cStr = pathParts[0];
        switch (cStr)
        {
          case "all":
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                Type.webPushConfig);
            JsonMarshaller jm = new JsonMarshaller();
            for (CustomConfig cc : ccList)
            {
              if (cc == null) continue;
              String pl = cc.getPayload();
              Map<String, Object> map = jm.readAsMap(pl);
              cc.updateGeneralStat(map);
              ret.add(map);
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;

          default:
            resp.getWriter().print("{}");
            break;
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        ULogger logger = uctx.getLogger(WebPushConfigHandler.class);
        Map<String, Object> m = new java.util.HashMap<>();
        String cStr = pathParts[0];

        switch (cStr)
        {
          case "create":
            String payload = ServletUtil.getPayload(req);
            Map<String, Object> payLoadMap = new HashMap<>();

            if (payload != null && !payload.isEmpty())
            {
              JsonMarshaller mRead = new JsonMarshaller();
              payLoadMap = mRead.readAsMap(payload);
            }
            else
            {
              createPublicPrivateKey(payLoadMap, logger);
            }

            if (payLoadMap != null && !payLoadMap.isEmpty())
            {
              String publicKey = (String) payLoadMap.get(PUBLIC_KEY);
              String privateKey = (String) payLoadMap.get(PRIVATE_KEY);
              if (publicKey == null || publicKey.isEmpty())
              {
                // Is empty response
                m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
                m.put(STATUS_MSG, "Public key not found.");
                payLoadMap.remove(PUBLIC_KEY);
              }
              else if (!Base64.isBase64(publicKey)
                  || publicKey.getBytes().length < 65)
              {
                m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
                m.put(STATUS_MSG,
                    "Public key is not valid (Check if given key is valid base64 format).");
                payLoadMap.remove(PUBLIC_KEY);
              }

              if (privateKey == null || privateKey.isEmpty())
              {
                // Is empty response
                m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
                m.put(STATUS_MSG, "Private key not found.");
                payLoadMap.remove(PRIVATE_KEY);
              }
              else if (!Base64.isBase64(privateKey)
                  || privateKey.getBytes().length < 32)
              {
                // Is not valid string
                m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
                m.put(STATUS_MSG,
                    "Private key is not valid (Check if given key is valid base64 format).");
                payLoadMap.remove(PRIVATE_KEY);
              }

              if (payLoadMap.containsKey(PUBLIC_KEY)
                  && payLoadMap.containsKey(PRIVATE_KEY))
              {
                CustomConfig cc = CustomConfig.load(uctx, Type.webPushConfig);
                if (cc == null)
                {
                  cc = CustomConfig.create(uctx, Type.webPushConfig);
                }

                String pload = new JsonMarshaller().serializeMap(payLoadMap);
                cc.setPayload(pload);
                cc.save();
                m.put(STATUS_CODE, ResponseMessage.Status.success.getStatus());
                m.put(STATUS_MSG, pload);
              }
            }
            else
            {
              m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
              m.put(STATUS_MSG, "Keypair generation failed");
            }

            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            break;

          case "delete":

            // c3/data/webpushconfig/delete/
            CustomConfig cc = CustomConfig.load(uctx, Type.webPushConfig);
            if (cc != null)
            {
              try
              {
                CustomConfig.delete(uctx, Type.webPushConfig);
                m.put(STATUS_CODE, ResponseMessage.Status.success.getStatus());
                m.put(STATUS_MSG, "Keypair deleted successfully.");
              }
              catch (Exception e)
              {
                m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
                m.put(STATUS_MSG,
                    "Error while deleting the keypaird." + e.getMessage());
              }

            }
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            break;

          default:
            m.put(STATUS_CODE, ResponseMessage.Status.fail.getStatus());
            m.put(STATUS_MSG, "Please specify parthPath create/delete");
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
        }

      }

    };
  }

  private void createPublicPrivateKey(Map<String, Object> map, ULogger logger)
  {
    Security.addProvider(new BouncyCastleProvider());
    ECNamedCurveParameterSpec parameterSpec = ECNamedCurveTable
        .getParameterSpec("prime256v1");
    KeyPairGenerator keyPairGenerator;
    try
    {
      keyPairGenerator = KeyPairGenerator.getInstance("ECDH", "BC");
      keyPairGenerator.initialize(parameterSpec);
      KeyPair vapidKey = keyPairGenerator.generateKeyPair();
      byte[] publicKey = nl.martijndwars.webpush.Utils
          .encode((ECPublicKey) vapidKey.getPublic());
      byte[] privateKey = nl.martijndwars.webpush.Utils
          .encode((ECPrivateKey) vapidKey.getPrivate());
      String publicKeyBase64 = Base64.encodeBase64URLSafeString(publicKey);
      String privateKeyBase64 = Base64.encodeBase64URLSafeString(privateKey);

      map.put(PRIVATE_KEY, privateKeyBase64);
      map.put(PUBLIC_KEY, publicKeyBase64);
    }
    catch (NoSuchAlgorithmException | NoSuchProviderException
        | InvalidAlgorithmParameterException e)
    {
      if (logger != null && logger.canLog()) logger.severe(
          "Error while creating public and private key. " + e.getMessage());
    }

  }

}
