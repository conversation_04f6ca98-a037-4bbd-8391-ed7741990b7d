package com.z1.handler;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ApiUtils;
import com.z1.Utils.ResponseMessage;

import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.analytic.cube.def.Cube;
import udichi.core.data.Result;
import udichi.core.util.JaxbMarshaller;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.cube.hbasestore.HBaseCubeData;
import z1.cube.hbasestore.HBaseCubeHandlerFactory;


public class CubeHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  private enum GetCommand
  {
    all, // All query definitions
    id, // name of the cube example: dailyProductPurchases
    checkUniqueName // check if a proposed name is unique for naming cubes
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/cube/all
          case all:
          {
            
            Result<DefinitionItem> items = DefinitionItem.getAllItems(uctx,
                ArtifactType.cube);
            List<Map<String, Object>> filteredCubes = new java.util.ArrayList<>(20);
            for (DefinitionItem di : items.getData())
            {
              String id = (String) di.getValues().get("id");
              
              if (!id.contains(":cube") && !id.startsWith("udc.system.core"))
              {
                di = DefinitionItem.getInstance(uctx, ArtifactType.cube, id);
                String pl2 = (String) di.getValues().get("payload2");
                String artifactId = "";
                if (pl2 != null && !pl2.isEmpty())
                {
                  Map<String, Object> pl2Map = new JsonMarshaller().readAsMap(pl2);
                  if (pl2Map.get("artifactId") != null) artifactId = (String) pl2Map.get("artifactId");
                }
                String name = (String)di.getValues().get("name");
                if (name==null || name.trim().isEmpty()) {
                  di.getValues().put("name", id);
                  di.save();
                }
                
                Object lastUpdatedBy = di.getValues().get(DefinitionItem.Fields.lastUpdatedBy.name());
                String lastUpdatedByName = ApiUtils.userNameFromEmail(uctx, lastUpdatedBy);
                di.getValues().put("lastUpdatedByName", lastUpdatedByName);
                

                // Not return any cubes that is part of a Module’s instance, exclude payload2 has “artifactId”
                if (artifactId == null || artifactId.isEmpty()) {
                  Map<String, Object> cubePayload = new LinkedHashMap<>();
                  cubePayload.put("id", id);
                  for (DefinitionItem.Fields field: DefinitionItem.Fields.values()) {
                    cubePayload.put(field.name(), di.getValues().get(field.name()));
                  }
                  cubePayload.put("lastUpdatedByName", lastUpdatedByName);
                  filteredCubes.add(cubePayload);
                }
              }
            }
            if (!filteredCubes.isEmpty())
            {
              String payload = new JsonMarshaller().serialize(filteredCubes);
              resp.getWriter().print(payload);
            }
            else
            {
              resp.getWriter().print("[]");
            }

            break;
          }
          // c3/data/cube/id?id=<cube-id>
          case id:
          {
            String id = req.getParameter("id");

            String payload = Utils.loadDefinition(uctx, id, ArtifactType.cube);
            resp.getWriter().print(payload);

            break;
          }
          // c3/data/cube/checkUniqueName?name=<..>
          case checkUniqueName:
          {
            String name = req.getParameter("name");
            ResponseMessage msg = null;
            if (name == null || name.isEmpty()) 
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Please provide a non-empty name.");
            }
            else if (!com.z1.Utils.ApiUtils.isValidCubeName(uctx, name))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.uniqueCheckFailed,
                  "Name is already used, please provide a different name.");
            } else
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.uniqueCheckSuccess,
                  "The name '" + name + "' is unique.");
            }
            resp.getWriter().print(msg.toString());
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/cube/create
          // c3/data/cube/update
          case create:
          case update:
          {
            String payload = ServletUtil.getPayload(req);

            if (payload == null) throw new UException(
                "Empty payload. Can't create or update cube definition.");

            // Cube definition is not dependent on any external DTD. Block
            // access to any external DTD to prevent possible XML Attacks.
            // <!DOCTYPE ...>
            if (payload.contains("!DOCTYPE") || payload.contains("!doctype"))
            {
              resp.getWriter().print("{");
              ServletUtil.printFailure(resp.getWriter(),
                  "Invalid payload. Cube definitions cannot contain 'DOCTYPE'");
              resp.getWriter().print("}");
              return;
            }
            
            Cube cube = JaxbMarshaller.toObject(payload);
            String id = cube.getId();
            String desc = cube.getDescription();
            
            String name = (String)new java.util.HashMap<>().getOrDefault("name", "");

            String reason = "";
            DefinitionItem di = DefinitionItem.getInstance(uctx, ArtifactType.cube, id);
            
            if (name != null && !name.isEmpty() && 
                !com.z1.Utils.ApiUtils.isValidCubeName(uctx, name)) {
              reason = "Cube Name ' " + name + " ' already exists";
              resp.getWriter().print("{");
              ServletUtil.printFailure(resp.getWriter(), reason);
              resp.getWriter().print("}");
              return;
            }
            
            if (command == PostCommand.create)
            {
              if (di != null)
              {
                reason = "Cube ID '" + id  + "' already exists";
                resp.getWriter().print("{");
                ServletUtil.printFailure(resp.getWriter(), reason);
                resp.getWriter().print("}");
                return;
              }
              else
              {
                di = DefinitionItem.newInstance(uctx, id, ArtifactType.cube);
              }
            }
                
            if (di == null) return;  

            if (desc != null)
            {
              di.getValues().put(DefinitionItem.Fields.description.name(),
                  desc);
            }
         
            di.getValues().put(DefinitionItem.Fields.payload.name(), payload);
            di.save();

            resp.getWriter().print("{");
            ServletUtil.printSuccess(resp.getWriter());
            resp.getWriter().print("}");

            break;
          }
          // c3/data/cube/delete?id=<cube-name>
          case delete:
          {
            String id = req.getParameter("id");
            if (id == null) throw new UException("No ID specified to 'delete'");
            
            // Delete data cube
            udichi.core.data.CubeHandler<HBaseCubeData> ch = HBaseCubeHandlerFactory
                .getInstance(uctx)
                .createCubeHandler(uctx.getNamespace() + ":" + id);
            ch.reset();

            DefinitionItem.delete(uctx, ArtifactType.cube, id);
            
            
            List<Map<String, Object>> queries = ApiUtils.getQueriesForTheCube(id, uctx);
            for(Map<String, Object> query: queries) {
              String queryId = (String)query.get("name");
              CustomConfig cc = CustomConfig.load(uctx, queryId, Type.cubeQuery);
              if (cc != null)
              {
                CustomConfig.delete(uctx, queryId, Type.cubeQuery);
              }
            }

            
            resp.getWriter().print("{");
            ServletUtil.printSuccess(resp.getWriter());
            resp.getWriter().print("}");

            break;

          }
        }
      }

    };
  }
  ///////////////////////////

}
