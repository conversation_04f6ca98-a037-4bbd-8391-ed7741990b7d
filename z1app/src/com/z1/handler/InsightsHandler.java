package com.z1.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;

public class InsightsHandler implements CommandHandlerFactory
{
  private enum Command
  {
    customList,
    journey,
    topic,
    stats
  }



  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        Command command = Command.valueOf(cStr);               

        // Create an array to pass for the relevant commands
        String[] subParts = new String[pathParts.length - 1];
        int j = 0;
        for (int i = 1; i < pathParts.length; i++, j++)
        {
          subParts[j] = pathParts[i];
        }
        
        
        CommandHandler ch = null;
        switch (command)
        {
          // c3/data/insights/customList/...
          case customList:
          {
            ch = new CustomListHandler(); 
            break;
          }
          // c3/data/insights/journey/...
          case journey:
          {
            ch = new CustomerJourneyHandler().get();
            break;
          }
          // c3/data/insights/topic/...
          case topic:
          {
            ch = new TopicHandler();
            break;    
          }
          // c3/data/insights/stats/...
          case stats:
          {
            ch = new StatsHandler().get();
            break;    
          }
          default:
          {
            return;
          }
        }
        
        ch.handle(ctx, subParts, req, resp);
      }
    };

  }



  /* (non-Javadoc)
   * @see com.z1.CommandHandlerFactory#post()
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        Command command = Command.valueOf(cStr);               

        // Create an array to pass for the relevant commands
        String[] subParts = new String[pathParts.length - 1];
        int j = 0;
        for (int i = 1; i < pathParts.length; i++, j++)
        {
          subParts[j] = pathParts[i];
        }
        
        
        CommandHandler ch = null;
        switch (command)
        {
          // c3/data/insights/journey/...
          case journey:
          {
            ch = new CustomerJourneyHandler().post();
            break;
          }
          // c3/data/insights/stats/...
          case stats:
          {
            ch = new StatsHandler().post();
            break;    
          }
          default:
          {
            return;
          }
        }
        
        ch.handle(ctx, subParts, req, resp);
      }
    };

  }


  
  ///////////////////////////////////////////////////////
  
}
