package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomActionDef;
import z1.commons.Utils;
import z1.expression.ScriptObject;
import z1.processing.JourneyRuntimeUtils;
import z1.template.TemplateUtils;

public class CustomActionHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    id,
    group
  }

  private enum PostCommand
  {
    create,
    update,
    delete
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/customaction/<customaction-id>
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          command = GetCommand.id;
        }

        switch (command)
        {
          // c3/data/customaction/all => Sends all custom actions
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

            // We'll get all custom action definitions to send them back
            List<CustomActionDef> caAll = CustomActionDef.loadAll(ctx);
            for (CustomActionDef s : caAll)
            {
              if (s == null || TemplateUtils.isTemplateConfig(s.getId(), CustomActionDef.PREFIX))  
                continue;

              Map<String, Object> map = s.getDefAsMap();
              if (map != null)
              {
                map.put("id", s.getId());
                ret.add(map);
              }
            }

            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);

            return;
          }
          
          // c3/data/customaction/id?caId=<customaction-id>
          case id:
          {
              // This must be a request for a specific custom action payload
              String caId = req.getParameter("caId");
              // get the goal payload
              CustomActionDef s = CustomActionDef.load(ctx, caId);
              String payload = s.getPayload();
              //String payload = new JsonMarshaller().serialize(s.getDef());
              resp.getWriter().print(payload);

              return;
          }
          
          // c3/data/customaction/group?gId=<group-id> => Sends all custom actions for the given <group-id>
          case group:
          {
            // This must be a request for a specific group of custom actions
            String gId = req.getParameter("gId");

            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

            // Get all the custom actions in the given group
            List<CustomActionDef> caAll = CustomActionDef.loadGroup(ctx, gId);
            for (CustomActionDef s : caAll)
            {
              Map<String, Object> map = s.getDefAsMap();
              if (map != null)
              {
                map.put("id", s.getId());
                ret.add(map);
              }
            }

            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);

            return;
          }
          default:
          {
            return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/customaction/create => payload has the definition
          // returns <customaction id>
          case create:
          {
            // Create a custom action by loading the payload from the request
            String payload = ServletUtil.getPayload(req);

            CustomActionDef s = CustomActionDef.create(ctx);
            s.setPayload(payload);
            s.save();
            String sid = s.getId();
            resp.getWriter().print("{ \"id\": \"" + sid + "\" }");

            // estimateSegmentPopulation(ctx, s);
            return;
          }
          // c3/data/customaction/update?sid=<customaction-id> => payload has the definition
          case update:
          {
            String sid = req.getParameter("sid");
            String payload = ServletUtil.getPayload(req);

            CustomActionDef s = CustomActionDef.load(ctx, sid);
            s.setPayload(payload);
            s.save();

            // Invalidate the script object
            ScriptObject.invalidateCache(ctx, ScriptObject.ScriptType.customActionScript, sid);
            return;
          }
          // c3/data/customaction/delete?sid=<customaction-id>
          case delete:
          {
            String sid = req.getParameter("sid");
            Map<String,Object> refs = JourneyRuntimeUtils.getContenRef(ctx, sid);
            Boolean isContentInUse = false;
            for (Object refList : refs.values())
            {
              if (!((List<?>) refList).isEmpty()) isContentInUse = true;
            }
            if (!isContentInUse)
            {
              CustomActionDef.delete(ctx, sid);
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.success, null,
                  "Content deleted successfully");
              resp.getWriter().print(msg);
              
              // Invalidate the script object
              ScriptObject.invalidateCache(ctx, ScriptObject.ScriptType.customActionScript, sid);
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.contentDeletionFailed,
                  new JsonMarshaller().serializeMap(refs));
              resp.getWriter().print(msg);
            }
            return;
          }
        }

      }
    };

  }

  // /////////////////////////////////////////////////////////////////

}
