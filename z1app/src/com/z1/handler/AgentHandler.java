package com.z1.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.handler.model.UserSettings;

import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.data.DataObject;
import udichi.core.data.Result;
import udichi.core.log.ULogger;
import udichi.core.system.Account;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import z1.account.Z1Account;
import z1.account.Z1AccountService;
import z1.c3.SystemConfig;
import z1.commons.Const;
import z1.commons.encryption.AesUtil;
import z1.commons.encryption.Encryption;
import z1.core.utils.Utils;
import z1.email.Email;
import z1.email.EmailSender;
import z1.users.AccessControl;
import z1.users.AgentService;
import z1.users.AgentServiceResponse;
import z1.users.User;
import z1.users.User.Fields;
import z1.users.User.Role;
import z1.users.User.Status;

public class AgentHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    register,
    update,
    updateUserSettings,
    delete,
    activate,
    deactivate,
    permissions
  }

  // Supported post commands
  private enum GetCommand
  {
    validate,
    all,
    id,
    userInfo,
    userSettings
  }
  

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        ULogger logger = ctx.getLogger(AgentHandler.class);

        switch (command)
        {
        // c3/data/agent/validate?email=<email>
          case validate:
          {
            AccessControl acl = new AccessControl(ctx);
            if (!emailDomainBelongToNameSpace(acl, req)) {
              resp.sendError(HttpServletResponse.SC_FORBIDDEN);
              return;
            }
            String email = req.getParameter(Fields.email.name());
            User user = User.load(ctx, email);
            Result<User> res = User.getUsersForEmail(ctx, email);

            if (user != null || (res != null && !res.isNull()))
            {
              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.userExists);
              resp.getWriter().print(rm.toString());
            }
            else
            {
              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.userDoesNotExist);
              resp.getWriter().print(rm.toString());
            }
            return;
          }
          // c3/data/agent/all
          case all:
          {
            Result<User> res = User.loadAll(ctx);

            if ((res == null) || (res.isNull()))
            {
              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.fail, 
                  ResponseMessage.Type.userDoesNotExist);
              resp.getWriter().print(rm.toString());
              return;
            }

            List<Map<String, String>> l = new ArrayList<Map<String, String>>();
            for (User agent : res.getData())
            {
              if (agent == null || agent.getRole().hasRole(User.Role.anonymous))
              {
                continue;
              }
              Map<String, String> agentMap = new HashMap<String, String>();
              String localPart = Utils.getLocalPartFromEmail(agent.getId());
              // skip the sysadmin user that is system generated.
              if (localPart != null
                  && (localPart.equalsIgnoreCase(User.SYSADMIN_NAME))
                    || (localPart.equalsIgnoreCase(User.SYSANALYST_NAME)))
              {
                continue;
              }
              agentMap.put("id", agent.getId());
              agentMap.put(Fields.email.name(),
                  (String) agent.getValues().get(Fields.email.name()));
              agentMap.put(Fields.fname.name(),
                  (String) agent.getValues().get(Fields.fname.name()));
              agentMap.put(Fields.lname.name(),
                  (String) agent.getValues().get(Fields.lname.name()));
              agentMap.put(Fields.phone.name(),
                  (String) agent.getValues().get(Fields.phone.name()));
              agentMap.put(Fields.status.name(),
                  (String) agent.getValues().get(Fields.status.name()));
              agentMap.put(Fields.acl.name(),
                  (String) agent.getValues().get(Fields.acl.name()));
              agentMap.put(Fields.role.name(),
                  (String) agent.getValues().get(Fields.role.name()));
              agentMap.put(
                  Fields.createdTime.name(),
                  Long.toString((long) agent.getValues().get(
                      Fields.createdTime.name())));
              if (null != agent.getValues().get(Fields.lastLoggedInTime.name()))
                agentMap.put(
                    Fields.lastLoggedInTime.name(),
                    Long.toString((long) agent.getValues().get(
                        Fields.lastLoggedInTime.name())));
              else
                agentMap.put(Fields.lastLoggedInTime.name(), "");
              User.UserSettings userSettings = agent.getUserSettings();
              agentMap.put(Fields.weeklyMetricSnapshotReport.name(),
                  userSettings.weeklyMetricSnapshotReport.toString());
              l.add(agentMap);
            }
            resp.getWriter().print(new JsonMarshaller().serialize(l));
            return;
          }
          // c3/data/agent/id?id=<id>
          case id:
          {
            String id = req.getParameter("id");
            if (id == null)
            {
              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams);
              resp.getWriter().print(rm.toString());
              return;
            }

            User agent = User.load(ctx, id);

            if ((agent == null))
            {
              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.userDoesNotExist);
              resp.getWriter().print(rm.toString());
              return;
            }

            Map<String, String> agentMap = new HashMap<String, String>();

            agentMap.put("id", agent.getId());
            agentMap.put(Fields.email.name(),
                (String) agent.getValues().get(Fields.email.name()));
            agentMap.put(Fields.fname.name(),
                (String) agent.getValues().get(Fields.fname.name()));
            agentMap.put(Fields.lname.name(),
                (String) agent.getValues().get(Fields.lname.name()));
            agentMap.put(Fields.phone.name(),
                (String) agent.getValues().get(Fields.phone.name()));
            agentMap.put(Fields.status.name(),
                (String) agent.getValues().get(Fields.status.name()));
            agentMap.put(Fields.acl.name(),
                (String) agent.getValues().get(Fields.acl.name()));
            agentMap.put(Fields.role.name(),
                (String) agent.getValues().get(Fields.role.name()));
            agentMap.put(Fields.createdTime.name(), Long.toString(
                (long) agent.getValues().get(Fields.createdTime.name())));
            if (null != agent.getValues().get(Fields.lastLoggedInTime.name()))
              agentMap.put(Fields.lastLoggedInTime.name(),
                  Long.toString((long) agent.getValues()
                      .get(Fields.lastLoggedInTime.name())));
            else
              agentMap.put(Fields.lastLoggedInTime.name(), "");
            User.UserSettings userSettings = agent.getUserSettings();
            agentMap.put(Fields.weeklyMetricSnapshotReport.name(),
                userSettings.weeklyMetricSnapshotReport.toString());

            resp.getWriter().print(new JsonMarshaller().serialize(agentMap));
            return;
          }
          
          // c3/data/agent/userInfo
          case userInfo:
          {
            // fetch current C3 login user from context
            User u = (User) ctx.getUser();
            if (u != null)
            {
              Map<String, Object> map = new HashMap<>(10);
              String fname = (String) u.getValues()
                  .get(User.Fields.fname.name());
              String lname = (String) u.getValues()
                  .get(User.Fields.lname.name());
              map.put("firstName", fname != null ? fname : "");
              map.put("lastName", lname != null ? lname : "");
              if ((fname == null || fname.isEmpty())
                  && (lname == null || lname.isEmpty()))
              {
                map.put("name", "SessionAI User"); // Default name if both are
                                                   // missing
              }
              else if (lname == null)
              {
                map.put("name", fname);
              }
              else
              {
                map.put("name", fname + " " + lname);
              }
              Object email = u.getValues().get(User.Fields.email.name());
              map.put("email", email != null ? email.toString() : "");
              map.put("lastLoggedInTime", (Long) u.getValues()
                  .get(User.Fields.lastLoggedInTime.name()));
              map.put("companyName",
                  u.getAccount().getValues().get(Account.NAME));
              map.put("version", z1.commons.Utils.installationType);
              
              String nsDisplayName = SystemConfig.getStringValue(ctx, "z1.nsDisplayName", "");
              Z1AccountService as = new Z1AccountService(ctx);
              Z1Account account = as.getAccount(ctx.getNamespace());
              String domainName = account.getDomain();
              
              Map<String, Object> namespaceInfo = new HashMap<>();
              namespaceInfo.put("displayName", nsDisplayName);
              namespaceInfo.put("domainName", domainName);
              map.put("namespace", namespaceInfo);

              User.UserSettings userSettings = u.getUserSettings();
              Map<String, Object> preferences = new HashMap<>();

              String enableChampionChallenger = userSettings.userProperties.get(Fields.championChallenger.name());
              if (enableChampionChallenger != null && !enableChampionChallenger.isEmpty())
              {
                preferences.put("enableChampionChallenger", enableChampionChallenger);
              }

              String enableCommandCenter = userSettings.userProperties.get(Fields.commandCenter.name());
              if (enableCommandCenter != null && !enableCommandCenter.isEmpty())
              {
                preferences.put("enableCommandCenter", enableCommandCenter);
              }

              String enableCommandCenterCatalog = userSettings.userProperties.get(Fields.commandCenterCatalog.name());
              if (enableCommandCenterCatalog != null && !enableCommandCenterCatalog.isEmpty())
              {
                preferences.put("enableCommandCenterCatalog", enableCommandCenterCatalog);
              }

              map.put("preferences", preferences);

              String payload = new JsonMarshaller().serialize(map);
              resp.getWriter().print(payload);
            }
            return;
          }
          
          // c3/data/agent/userSettings
          case userSettings:
          {
            // fetch current C3 login user from context
            User user = (User) ctx.getUser();
            if (user != null)
            {
              User.UserSettings userSettings = user.getUserSettings();
              UserSettings userSettingsResponse = new UserSettings();
              userSettingsResponse.firstName = userSettings.firstName;
              userSettingsResponse.lastName = userSettings.lastName;
              userSettingsResponse.email = userSettings.email;
              Boolean weeklyMetricSnapshotReport = userSettings.weeklyMetricSnapshotReport;
              weeklyMetricSnapshotReport = weeklyMetricSnapshotReport == Boolean.TRUE;
              userSettingsResponse.weeklyMetricSnapshotReport = weeklyMetricSnapshotReport
                  .toString();

              String payload = new JsonMarshaller().serialize(userSettingsResponse);
              resp.getWriter().print(payload);
            }
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        ULogger logger = ctx.getLogger(AgentHandler.class);

        switch (command)
        {

        // c3/data/agent/register => payload has the definition
          case register:
          {
            try
            {
              String payload = ServletUtil.getPayload(req);
              
              if (payload == null || payload.trim().length() == 0)
              {
                ResponseMessage rm = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.userRegistrationFailed,
                    "Request payload is empty or null.");
                resp.getWriter().print(rm.toString());
                return;
              }

              AccessControl acl = new AccessControl(ctx);
              JsonMarshaller j = new JsonMarshaller();
              Map<String, Object> agentMap = j.readAsMap(payload);
              
              String ns = ctx.getNamespace();
              
              Z1AccountService accountService = new Z1AccountService(ctx);
              Z1Account account = accountService.getAccount(ns);
              boolean isSsoEnabled = account.getProperties().getIsSsoEnabled();

              ResponseMessage respm = verifyPayload(agentMap, ctx, isSsoEnabled, false);
              
              // Payload is invalid
              if (respm != null)
              {
                resp.getWriter().print(respm.toString());
                return;
              }

              String id = (String) agentMap.get(DataObject.ID);
              
              String nameSpace = Utils.generateNameSpaceFromEmail(id);
              if (!acl.belongToNameSpace(nameSpace)) {
                resp.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
              }

              // Does the user ID exist in the system already?
              if (User.load(ctx, id) != null)
              {
                ResponseMessage rm = new ResponseMessage(
                    ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.userExistsSelectDifferentId);
                resp.getWriter().print(rm.toString());                
                return;
              }
              
              String emailAddress = (String) agentMap.get(Fields.email.name());
              boolean emailAddressHasValue = StringUtils.isNotEmpty(emailAddress);

              if ((!isSsoEnabled && !isValidEmail(emailAddress))
                  || (isSsoEnabled && emailAddressHasValue && !isValidEmail(emailAddress)))
              {
                ResponseMessage rm = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.userRegistrationFailed,
                    "Invalid Email.");
                resp.getWriter().print(rm.toString());
                return;
              }
              
              // In the case of SSO the email address of the user does not have
              // to match the domain since the user's email could be
              // associated with multiple namespaces.
              // If a whitelist of allowed domain is provided, the list will also
              // be used to match the user email address
              if (!isSsoEnabled && !(emailBelongToDomain(emailAddress, ns)
                      || checkIfDomainInWhitelist(emailAddress, ctx, account)) )
              {
                String domains =  String.join(", ", getEmailDomainWhiteList(ctx, account));
                String responseMsg = "Invalid Email Domain";
                if (domains != null && !domains.isEmpty())
                {
                  responseMsg = "Invalid Email Domain. Allowed domains : " + domains;
                }
                
                ResponseMessage rm = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.userRegistrationFailed,
                    responseMsg);
                resp.getWriter().print(rm.toString());
                return;
              }

              User agent = User.newInstance(ctx, id);
              if (emailAddressHasValue)
              {
                agent.getValues().put(Fields.email.name(), emailAddress.toLowerCase());
              }
              else if (isSsoEnabled && !emailAddressHasValue)
              {
                // For SSO email address is optional so if email address does not have
                // a value then set email to empty string.
                agent.getValues().put(Fields.email.name(), "");
              }
              agent.getValues().put(Fields.fname.name(),
                  (String) agentMap.get(Fields.fname.name()));
              agent.getValues().put(Fields.lname.name(),
                  (String) agentMap.get(Fields.lname.name()));
              
              String password = null;
              if (!isSsoEnabled)
              {
                password = (String) agentMap.get(Fields.password.name());
                password = Encryption.decodeBase64(password);
                boolean successfullySetPassword = agent.setPassword(password, true);
                if (!successfullySetPassword)
                {
                  ResponseMessage rm = new ResponseMessage(ctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.userRegistrationFailed,
                      "User registration failed.");
                  resp.getWriter().print(rm.toString());
                  return;
                }
              }
              
              agent.getValues().put(Fields.phone.name(),
                  (String) agentMap.get(Fields.phone.name()));
              agent.getValues().put(Fields.role.name(),
                  (String) agentMap.get(Fields.role.name()));
              Calendar currentTimeInMillis = Calendar.getInstance(TimeZone
                  .getTimeZone("UTC"));
              agent.getValues().put(Fields.createdTime.name(),
                  currentTimeInMillis.getTimeInMillis());
              agent.getValues().put(Fields.status.name(), Status.active.name());
              
              String weeklyMetricSnapshotReportString = (String) agentMap
                  .get(Fields.weeklyMetricSnapshotReport.name());
              Boolean weeklyMetricSnapshotReport = Boolean
                  .parseBoolean(weeklyMetricSnapshotReportString);
              agent.getValues().put(Fields.weeklyMetricSnapshotReport.name(),
                  weeklyMetricSnapshotReport);
              
              agent.save();

              // check is account exists.
              agent.setAccount(account);
              User.updateUser(ctx, agent);

              HashMap<String, Object> hm = new HashMap<String, Object>();
              hm.put("FirstName",
                  (String) agent.getValues().get(User.Fields.fname.name()));
              
              String scheme = Utils.getReqScheme(req);
              int port = req.getServerPort();
              if (scheme.equalsIgnoreCase("https")) port = -1;
              
              hm.put("username", id);
              if (!isSsoEnabled)
              {
                hm.put("password", password);
              }
              
              //${schema}://${servername}:${port}/c3
              String activationUrl = scheme + "://" + req.getServerName();
              if (port != -1) activationUrl += ":" + port;
              activationUrl += "/c3";
              hm.put("activationUrl", activationUrl);

              // Do we want to send an email out if sso is 
              // configured and the user has an email address?
              if (!isSsoEnabled)
              {
                EmailSender es = new EmailSender();
                Email email = new Email();
                email.addSubject("Your Session AI account has been created");
                email.addToAddress(emailAddress.toLowerCase());
                email.addBody(StringUtil.substitueValue(
                    Const.getTemplate("AgentRegistration.txt"), hm, "",
                    new HashMap<String, Object>()));
                es.send(email);
              }

              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.userSuccessfullyRegistered);
              resp.getWriter().print(rm.toString());
              
              return;
            }
            catch (UException ue)
            {
              logger.log("User Creating failed " + ue.getMessage());
              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.userRegistrationFailed);
              resp.getWriter().print(rm.toString());
              return;
            }
          }
          // c3/data/agent/update => payload has the definition
          case update:
          {
            String id = "";
            try
            {
              AccessControl acl = new AccessControl(ctx);
              if (!emailDomainBelongToNameSpace(acl, req)) {
                resp.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
              }
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller j = new JsonMarshaller();
              Map<String, Object> agentMap = j.readAsMap(payload);
              id = req.getParameter(Fields.email.name());
              User agent = User.load(ctx, id);
              if (agent == null) return;

              String weeklyMetricSnapshotReportString = (String) agentMap
                  .get(Fields.weeklyMetricSnapshotReport.name());
              Boolean weeklyMetricSnapshotReport = Boolean
                  .parseBoolean(weeklyMetricSnapshotReportString);
              agent.getValues().put(Fields.weeklyMetricSnapshotReport.name(),
                  weeklyMetricSnapshotReport);
              
              String emailAddress = (String) agentMap.get(Fields.email.name());
              
              Z1AccountService accountService = new Z1AccountService(ctx);
              Z1Account account = accountService.getAccount(ctx.getNamespace());
              boolean isSsoEnabled = account.getProperties().getIsSsoEnabled();
              boolean emailAddressHasValue = StringUtils.isNotEmpty(emailAddress);

              ResponseMessage respm = verifyPayload(agentMap, ctx, isSsoEnabled, true);
              // Payload is invalid
              if (respm != null)
              {
                resp.getWriter().print(respm.toString());
                return;
              }

              if ((!isSsoEnabled 
                  && isUpdatingEmail(ctx, emailAddress, agent)
                  && !isEmailUnique(ctx, emailAddress))
                  || (isSsoEnabled 
                      && emailAddressHasValue
                      && isUpdatingEmail(ctx, emailAddress, agent)
                      && !isEmailUnique(ctx, emailAddress)))
              {
                ResponseMessage rm = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.emailIdInUserUseAnother);
                resp.getWriter().print(rm.toString());
                return;
              }
              // In the case of SSO the email address of the user does not have
              // to match the domain since the user's email could be
              // associated with multiple namespaces.
              // If a whitelist of allowed domain is provided, the list will also
              // be used to match the user email address
              if (!isSsoEnabled 
                  && isUpdatingEmail(ctx, emailAddress, agent)
                  && !(emailBelongToDomain (emailAddress, ctx.getNamespace())
                      || checkIfDomainInWhitelist(emailAddress, ctx, account)) )
              {                
                String domains =  String.join(", ", getEmailDomainWhiteList(ctx, account));
                String responseMsg = "Invalid Email Domain";
                if (domains != null && !domains.isEmpty())
                {
                  responseMsg = "Invalid Email Domain. Allowed domains : " + domains;
                }
                
                ResponseMessage rm = new ResponseMessage(ctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.userDataUpdateFailed,
                    responseMsg);
                resp.getWriter().print(rm.toString());
                return;
              }
              
              if (emailAddressHasValue)
              {
                agent.getValues().put(Fields.email.name(), emailAddress.toLowerCase());
              }
              else if (isSsoEnabled && !emailAddressHasValue)
              {
                // For SSO email address is optional so if email address does not have
                // a value then set email to empty string.
                agent.getValues().put(Fields.email.name(), "");
              }
              agent.getValues().put(Fields.fname.name(),
                  (String) agentMap.get(Fields.fname.name()));
              agent.getValues().put(Fields.lname.name(),
                  (String) agentMap.get(Fields.lname.name()));
              agent.getValues().put(Fields.phone.name(),
                  (String) agentMap.get(Fields.phone.name()));
              agent.getValues().put(Fields.role.name(),
                  (String) agentMap.get(Fields.role.name()));
              if (!isSsoEnabled && agentMap.get(Fields.password.name()) != null
                  && !((String) agentMap.get(Fields.password.name()))
                      .equalsIgnoreCase("*****"))
              {

                String password = (String) agentMap.get(Fields.password.name());
                password = Encryption.decodeBase64(password);
                boolean successfullySetPassword = agent.setPassword(password, true);
                if (!successfullySetPassword)
                {
                  ResponseMessage rm = new ResponseMessage(ctx,
                      ResponseMessage.Status.fail,
                      ResponseMessage.Type.userDataUpdateFailed,
                      "User registration failed.");
                  resp.getWriter().print(rm.toString());
                  return;
                }
                
                agent.getValues().put(Fields.password.name(), "");
                agent.getValues().put(Fields.numTries.name(), 0);
              }
              agent.save();

              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.userDataSuccessfullyUpdated);
              resp.getWriter().print(rm.toString());
              return;
            }
            catch (UException ue)
            {
              if (logger.canLog())
              {
                logger.log("User updation failed for user id: " + id
                    + " with error message " + ue.getMessage());
              }
              
              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.userDataUpdateFailed);
              resp.getWriter().print(rm.toString());
              return;
            }
          }
          // c3/data/agent/updateUserSettings => payload has the definition
          case updateUserSettings:
          {
            String userId = "";
            try
            {
              User user = (User) ctx.getUser();
              userId = user.getId();
              AgentService agentService = new AgentService(ctx);
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller j = new JsonMarshaller();
              UserSettings userSettingsPayload = j.readAsObject(payload,
                  UserSettings.class);
              User.UserSettings userSettings = user.new UserSettings();
              userSettings.firstName = userSettingsPayload.firstName;
              userSettings.lastName = userSettingsPayload.lastName;
              userSettings.email = userSettingsPayload.email;
              userSettings.weeklyMetricSnapshotReport = Boolean
                  .parseBoolean(userSettingsPayload.weeklyMetricSnapshotReport);
              AgentServiceResponse agentServiceResponse = agentService
                  .updateUserSettings(userId, userSettings);

              ResponseMessage rm;
              if (agentServiceResponse
                  .getStatus() == AgentServiceResponse.Status.success)
              {
                rm = new ResponseMessage(ctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.userDataSuccessfullyUpdated);
              }
              else
              {
                rm = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.userDataUpdateFailed,
                    agentServiceResponse.getSummary());
              }
              resp.getWriter().print(rm.toString());
              return;
            }
            catch (UException ue)
            {
              if (logger.canLog())
              {
                logger.log("User settings update failed for user id: " + userId
                    + " with error message " + ue.getMessage());
              }

              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.userDataUpdateFailed);
              resp.getWriter().print(rm.toString());
              return;
            }
          }
          // c3/data/agent/delete?email=<email>
          case delete:
          {
            String id = "";
            try
            {
              AccessControl acl = new AccessControl(ctx);
              if (!emailDomainBelongToNameSpace(acl, req)) {
                resp.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
              }
              id = req.getParameter(Fields.email.name());
              User agent = User.load(ctx, id);

              agent.delete();

              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.success,
                  ResponseMessage.Type.userDeletedSuccessfully);
              resp.getWriter().print(rm.toString());
              return;
            }
            catch (UException ue)
            {
              logger.log("User deletion failed for agent id:" + id
                  + " with error message " + ue.getMessage());
              ResponseMessage rm = new ResponseMessage(
                  ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.userDeletionFailed);
              resp.getWriter().print(rm.toString());
              return;
            }
          }
          
          // c3/data/agent/activate?email=<email>
          case activate:
          {
            AccessControl acl = new AccessControl(ctx);
            if (!emailDomainBelongToNameSpace(acl, req)) {
              resp.sendError(HttpServletResponse.SC_FORBIDDEN);
              return;
            }
            String id = "";
            id = req.getParameter(Fields.email.name());
            User agent = User.load(ctx, id);
            agent.getValues().put(User.Fields.status.name(),
                User.Status.active.name());
            agent.getValues().put(User.Fields.lastActivationTime.name(),
                Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis());
            agent.save();
            return;
          }
          // c3/data/agent/deactivate?email=<email>
          case deactivate:
          {
            AccessControl acl = new AccessControl(ctx);
            if (!emailDomainBelongToNameSpace(acl, req)) {
              resp.sendError(HttpServletResponse.SC_FORBIDDEN);
              return;
            }
            String id = "";
            id = req.getParameter(Fields.email.name());
            User agent = User.load(ctx, id);
            agent.getValues().put(User.Fields.status.name(),
                User.Status.disabled.name());
            agent.save();
            return;
          }
          // c3/data/agent/permissions?email=<email>
          case permissions:
          {
            AccessControl acl = new AccessControl(ctx);
            if (!emailDomainBelongToNameSpace(acl, req)) {
              resp.sendError(HttpServletResponse.SC_FORBIDDEN);
              return;
            }
            String id = "";
            id = req.getParameter(Fields.email.name());
            String aclPayload = ServletUtil.getPayload(req);
           
            User agent = User.load(ctx, id);
            if (agent == null) return;
           
            agent.getValues().put(User.Fields.acl.name(),
                aclPayload);
            agent.save();
            return;
          }

        }
        
      }
    };
  }

  private boolean isEmailUnique(UContext ctx, String emailId)
  {
    Result<User> ag = User.getUsersForEmail(ctx, emailId);

    Iterable<User> it = ag.getData();
    Iterator<User> i = it.iterator();
    return !i.hasNext();
  }

  private boolean isUpdatingEmail(UContext ctx, String emailId, User currentUser)
      throws Exception
  {
    if (currentUser == null || emailId == null || emailId.isEmpty())
    {
      throw new Exception("Expected information is empty.");
    }
    String currUserEmail = (String) currentUser.getValues().get(
        User.Fields.email.name());
    return !emailId.equalsIgnoreCase(currUserEmail);

  }
  
  private ResponseMessage verifyPayload(Map<String, Object> agentMap,
      UContext ctx, boolean isSsoEnabled, boolean isUpdate)
  {
    final String[] PAYLOAD_ATTR = { "id", "fname", "lname", "email", "password",
        "role" };
    
    // For SSO these fields are not required.
    final List<String> SSO_BYPASS_PAYLOAD_ATTR = Arrays.asList("email", "password");

    String summary = null;

    for (String attr : PAYLOAD_ATTR)
    {
      if (isSsoEnabled)
      {
        if (SSO_BYPASS_PAYLOAD_ATTR.contains(attr))
        {
          continue;
        }
      }

      // Update call does not send 'password' in payload.
      if(isUpdate && attr.equals("password"))
      {
        continue;
      }

      // Every attr in PAYLOAD_ATTR array must be present in the payload.
      if (!agentMap.containsKey(attr))
      {
        summary = "Incomplete set of attributes in the payload.";
      }

      String val = agentMap.get(attr).toString();

      // Value must not be null or empty string.
      if (val == null || val.trim().length() == 0)
      {
        summary = "Invalid attribute values in the payload.";
      }
    }

    // Role must be valid.
    // Role can be a | separated string of multiple roles
    String[] roleNames = agentMap.get("role").toString().split("\\|"); 
    for (String r: roleNames)
    {
      if (Role.parse(r) == Role.unknown)
      {
        summary = "Invalid User Role.";
        break;
      }
    }

    ResponseMessage.Type resType = isUpdate ?
        ResponseMessage.Type.userDataUpdateFailed :
        ResponseMessage.Type.userRegistrationFailed;

    return (summary != null) ?
        new ResponseMessage(ctx, ResponseMessage.Status.fail, resType,
            summary) :
        null;
  }
  
  private boolean isValidEmail(String emailId)
  {
    Pattern pattern = z1.commons.Utils.getEmailPattern();
    Matcher matcher = pattern.matcher(emailId);
    return matcher.matches();
  }
  
  private boolean emailBelongToDomain (String emailAddress, String ns ) {
    String domainName = Utils.getDomainNameFromEmail(emailAddress);
    if (Utils.generateNameSpaceFromDomainName (domainName).equalsIgnoreCase(ns)) return true;
    // Get the account
    UContext ctx = UContext.getInstance()
        .setNamespace(udichi.core.Const.NS_UDICHI_CORE);
    Z1AccountService as = new Z1AccountService(ctx);
    Z1Account account = as.getAccount(ns);
    if (account != null) 
    {
      String domain = account.getDomain();
      if (domain != null)
      {
        if (domainName.equalsIgnoreCase(domain)) return true;
      }
      // one more check for backward compatibility 
      String id = account.getId();
      if (id != null)
      {
        if (Utils.generateNameSpaceFromDomainName (domainName).equalsIgnoreCase(id)) return true;
      }
    }
    return false;
  }
  
  public boolean emailDomainBelongToNameSpace(AccessControl ac, HttpServletRequest req) {
    String email = req.getParameter(Fields.email.name());
    if (email != null) {
      String domainName = Utils.getDomainNameFromEmail(email);
      if (domainName != null) {
        String nameSpace = Utils.generateNameSpaceFromDomainName(domainName);
        if (ac.belongToNameSpace(nameSpace)) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * checkIfDomainInWhitelist method will check whether an email address exists in the provided whitelist
   *
   * @param emailAddress
   *     the email address provided by the user
   * @param ctx
   *     the user context we are going to use to find the whitelist in this namespace
   * @param account
   *    the z1account assocaited with the user
   * @return <code>True</code>
   *     if the email address is allowed by the whitelist in this namespace
   */
  private boolean checkIfDomainInWhitelist(String emailAddress, UContext ctx, Z1Account account){
    String domainName = Utils.getDomainNameFromEmail(emailAddress);
    return getEmailDomainWhiteList(ctx, account).contains(domainName);
  }

  /**
   * The getEmailDomainWhiteList gives the allowed email domains in this namespace
   *
   * @param ctx
   *    the user context we are going to use to load the whitelist from SystemConfig in
   *    this namespace
   * @param account
   *    the z1account assocaited with the user
   * @return a set of email domain whitelist (all in lowercases and with leading/trailing
   * space trimmed)
   */
  public Set<String> getEmailDomainWhiteList(UContext ctx, Z1Account account){
    Object whitelistPayload = SystemConfig.getValue(ctx, "z1.allowedEmailDomain");
    Set<String> res = new HashSet<>();
    if (whitelistPayload == null){
      return res;
    }
    String whitelist = (String)whitelistPayload;
    for (String domain: whitelist.split(",")){
      res.add(domain.trim().toLowerCase());
    }
    res.add(account.getDomain());
    return res;
  }
}
