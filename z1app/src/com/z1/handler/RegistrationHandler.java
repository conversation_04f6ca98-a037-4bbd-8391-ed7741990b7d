package com.z1.handler;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.factory.channeltype.ChannelTypeFactory;
import com.z1.factory.channeltype.Facebook;
import com.z1.factory.channeltype.IChannelType;
import com.z1.registration.def.RegistrationType;
import com.z1.registration.def.UrlInfoType;
import com.z1social.fb.PageSearch;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.system.Account;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import udichi.core.workspace.WorkspaceService;
import udichi.gateway.AppServiceFacade;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.account.Z1Account;
import z1.account.Z1AccountService;
import z1.accountcontext.AccountContextWrapper;
import z1.accountcontext.def.AccountContextType;
import z1.accountcontext.def.CompanyType;
import z1.accountcontext.def.CustomType;
import z1.accountcontext.def.CustomType.Nv;
import z1.channel.ChannelDefWrapper;
import z1.channel.def.ChannelDef;
import z1.channel.def.CredentialsDef;
import z1.channel.def.KvpairDef;
import z1.commons.Const;
import z1.commons.USession;
import z1.commons.encryption.AesUtil;
import z1.commons.encryption.Encryption;
import z1.core.utils.Utils;
import z1.email.Email;
import z1.email.EmailSender;
import z1.users.User;
import z1.users.User.LoginStatus;
import z1.users.User.Status;

public class RegistrationHandler implements CommandHandlerFactory
{
  public enum Fields
  {
    sourcePayload // a channel url payload that is used to create a channel
  }

  // Supported post commands
  private enum PostCommand
  {
    channel,
    channelUrl,
    oauth
  }

  // Supported post commands
  private enum GetCommand
  {
    industryList,
    validate
  }

  private enum channelInfo
  {
    channelType,
    companyName,
    competitors,
    industry
  }

  private String _tempPwd = "";
  private final String DEF_WORKSPACE_ID = "z1default";

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/registration/validate/<id>
          case validate:
          {
            // TODO
            // Do we need this "case"? Fixing based on some assumptions only.
            // Need to comeback and review
            // ------------            
            String id = req.getParameter("id");
            String nsTemp = Utils.generateNameSpaceFromEmail(id);
            ctx.setNamespace(nsTemp);

            // AccountService as = new AccountService(ctx);
            // Account account = as.getAccount(ctx.getNamespace());
            // if (account != null)
            // {
            // List<User> users = User.getUsersForRole(ctx, Role.admin);
            // String emailid = "";
            // if (users.isEmpty())
            // {
            // ResponseMessage rm = new ResponseMessage(
            // ctx,
            // ResponseMessage.Status.fail,
            // ResponseMessage.Type.userDoesNotExist);
            // resp.getWriter().print(rm.toString());
            // return;
            // }
            //
            // boolean isDuplicateId = false;
            // for (User agent : users)
            // {
            // emailid = (String) agent.getValues().get(
            // User.Fields.email.name());
            // break;
            // }
            //
            // ResponseMessage rm = new ResponseMessage(
            // ctx,
            // ResponseMessage.Status.fail,
            // ResponseMessage.Type.emailIdInUserUseAnother);
            // resp.getWriter().print(rm.toString());
            // return;
            // }

            User u = User.load(ctx, id);
            if (u != null)
            {
              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail, ResponseMessage.Type.userExists);
              resp.getWriter().print(rm.toString());
            }
            else
            {
              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.success, LoginStatus.Reason.noAccount);
              resp.getWriter().print(rm.toString());
            }
            return;
          }
          
          // c3/data/registration/industryList
          case industryList:
          {
            ArrayList<HashMap<String, String>> l = new ArrayList<HashMap<String, String>>();
            String listStr = z1.commons.Utils.getPropertyValue("industryList");
            if (listStr != null)
            {
              String[] names = listStr.split(",");

              for (String s : names)
              {
                generateIndustryList(l, s);
              }
            }

            resp.getWriter().print(new JsonMarshaller().serialize(l));

            return;
          }

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/registration/oauth
          case oauth:
          {
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            Map<String, Object> m = j.readAsMap(payload);
            AccountContextWrapper acw = AccountContextWrapper.load(ctx);
            AccountContextType act = acw.getDef();
            z1.accountcontext.def.CredentialsDef cd = act.getAuthCredentials();
            cd.setAccessToken((String) m.get("accessToken"));
            Calendar currentTimeInMillis = Calendar
                .getInstance(TimeZone.getTimeZone("UTC"));
            cd.setAuthTimeInMilliSec(currentTimeInMillis.getTimeInMillis());
            cd.setUserId((String) m.get("userID"));
            cd.setSignedRequest((String) m.get("signedRequest"));
            cd.setExpiresIn((Integer) m.get("expiresIn"));

            act.setAuthCredentials(cd);

            acw.setPayload(j.serialize(act));
            acw.save();
          }

          // c3/system/registration/channel
          case channel:
          {
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            Map<String, Object> channelInput = j.readAsMap(payload);
            String channelType = (String) channelInput
                .get(channelInfo.channelType.name());
            IChannelType ic = ChannelTypeFactory.getChannel(channelType);

            List<Map<String, Object>> companyUrls = ic
                .getChannelInfoByCompanyName(channelInput);

            List<Map<String, Object>> competitorList = (List<Map<String, Object>>) channelInput
                .get(channelInfo.competitors.name());
            List<Map<String, Object>> competitorUrls = new ArrayList<Map<String, Object>>();
            for (Map<String, Object> competitor : competitorList)
            {
              List<Map<String, Object>> competitorInfoList = ic
                  .getChannelInfoByCompanyName(competitor);

              for (Map<String, Object> competitorInfo : competitorInfoList)
              {
                competitorUrls.add(competitorInfo);
              }

            }

            HashMap<String, Object> response = new HashMap<String, Object>();
            response.put("companyUrls", companyUrls);
            response.put("competitorUrls", competitorUrls);

            resp.getWriter().print(new JsonMarshaller().serializeMap(response));
            return;
          }
          
          // c3/system/registration/channelUrl
          case channelUrl:
          {
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            Map<String, Object> channelInput = j.readAsMap(payload);

            String channelType = (String) channelInput
                .get(channelInfo.channelType.name());
            IChannelType ic = ChannelTypeFactory.getChannel(channelType);

            List<Map<String, Object>> companyUrls = ic
                .getChannelInfoByUrl(channelInput);

            HashMap<String, Object> response = new HashMap<String, Object>();
            response.put("url", companyUrls);
            resp.getWriter().print(new JsonMarshaller().serializeMap(response));
            return;
          }

        }

      }
    };
  }

  // ...................................................................

  public static CompanyType createCompanyType(UrlInfoType company)
  {

    if (company != null && company.getName() != null
        && !company.getName().isEmpty())
    {
      CompanyType ct = new CompanyType();
      HashMap<String, Boolean> aliasestemp = new HashMap<String, Boolean>();
      ct.setName(company.getName().trim());
      String a = company.getName().toLowerCase().trim();
      ct.getAliases().add(a);
      aliasestemp.put(a, true);

      if (company.getAlias() != null && !company.getAlias().isEmpty())
      {
        a = company.getAlias().toLowerCase().trim();
        if (!aliasestemp.containsKey(a))
        {
          aliasestemp.put(a, true);
          ct.getAliases().add(a);
        }
      }
      return ct;
    }

    return null;
  }

  /**
   * Registers channels from a list of channel url data.
   * 
   * @param ctx
   *          Current runtime context.
   * @param channelUrls
   *          List of channel data.
   * @param isCompetitor
   *          If the channel is for a competitor channel.
   * @param suspend
   *          create the channel in suspended mode if true.
   */
  public static void createChannel(UContext ctx, List<UrlInfoType> channelUrls,
      String isCompetitor)
  {

    createChannel(ctx, channelUrls, isCompetitor, false);
  }

  /**
   * Registers channels from a list of channel url data.
   * 
   * @param ctx
   *          Current runtime context.
   * @param channelUrls
   *          List of channel data.
   * @param isCompetitor
   *          If the channel is for a competitor channel.
   */
  public static void createChannel(UContext ctx, List<UrlInfoType> channelUrls,
      String isCompetitor, boolean suspend)
  {

    for (UrlInfoType channelUrl : channelUrls)
    {
      // Create channel definition
      ChannelDefWrapper channel = ChannelDefWrapper.create(ctx);
      JsonMarshaller jm = new JsonMarshaller();
      ChannelDef cDef = new ChannelDef();
      CredentialsDef cd = new CredentialsDef();
      String channelType = channelUrl.getChannelType();
      IChannelType ic = ChannelTypeFactory.getChannel(channelType);

      ic.setCredentialDef(channelUrl, cd);

      cDef.setAuthCredentials(cd);
      if (channelUrl.getChannelType() != null)
      {
        cDef.setHandler(ic.getFeedHandler());
      }
      else
      {
        cDef.setHandler(new Facebook().getAccessToken());// for now we
                                                         // only have
                                                         // facebook
                                                         // so hardcode
                                                         // info.
      }

      // check if we have no id, as sfdc will not send this. and we need to get
      // channel info.
      if ((channelUrl.getId() == null || channelUrl.getId().isEmpty())
          && channelType.equalsIgnoreCase("facebook"))
      {
        // if url is empty at this point just ignore the channel. it is supposed
        // to have it though
        if (channelUrl.getUrl() == null || channelUrl.getUrl().isEmpty())
        {
          continue;
        }
        PageSearch ps = new PageSearch(); // TODO when we add more channeltype
                                          // we need to use the right search
                                          // file.
        List<Map<String, Object>> companyUrls = ps
            .fetchPageByURL(cd.getAccessToken(), (String) channelUrl.getUrl());
        if (!companyUrls.isEmpty())
        {
          Iterator<Map<String, Object>> it = companyUrls.iterator();
          if (it.hasNext())
          {
            Map<String, Object> mp = it.next();
            channelUrl.setName((String) mp.get("name"));
            channelUrl.setUrl((String) mp.get("url"));
            channelUrl.setId((String) mp.get("id"));
            channelUrl.setPtat((String) mp.get("ptat"));
            channelUrl.setLikes((String) mp.get("likes"));
          }
        }
        else
        {
          // if the url query did not produce any results ignore the channel
          continue;
        }

      }

      // if there is no id there is no way to continue for google play
      if ((channelUrl.getId() == null || channelUrl.getId().isEmpty())
          && (channelType.equalsIgnoreCase("googleplay")
              || channelType.equalsIgnoreCase("applestore")))
      {
        continue;
      }

      if (channelUrl.getIsCompetitor() == null
          || channelUrl.getIsCompetitor().trim().isEmpty())
        channelUrl.setIsCompetitor(isCompetitor);

      KvpairDef kv = new KvpairDef();
      kv.setKey("companyName");
      kv.setValue(channelUrl.getName());
      cDef.getParams().add(kv);

      kv = new KvpairDef();
      kv.setKey("companyUrl");
      kv.setValue(channelUrl.getUrl());
      cDef.getParams().add(kv);

      kv = new KvpairDef();
      kv.setKey("companyId");
      kv.setValue(channelUrl.getId());
      cDef.getParams().add(kv);

      kv = new KvpairDef();
      kv.setKey("isCompetitor");
      kv.setValue(channelUrl.getIsCompetitor());
      cDef.getParams().add(kv);

      kv = new KvpairDef();
      kv.setKey("fetchDataSince");
      kv.setValue("3");
      cDef.getParams().add(kv);

      cDef.setSourceType(channelUrl.getChannelType());
      cDef.setIndustry(channelUrl.getIndustry());
      for (String language : channelUrl.getLanguages())
      {
        cDef.getLanguages().add(language);
      }

      // We'll serialize the channelUrl payload as a string
      String srcPayload = jm.serialize(channelUrl);
      kv = new KvpairDef();
      kv.setKey(Fields.sourcePayload.name());
      kv.setValue(srcPayload);
      cDef.getParams().add(kv);

      // Setting company type
      z1.channel.def.CompanyType ct = new z1.channel.def.CompanyType();
      ct.setName(channelUrl.getName());
      ct.getAliases().add(channelUrl.getName());
      if (channelUrl.getAlias() != null && !channelUrl.getAlias().isEmpty())
      {
        ct.getAliases().add(channelUrl.getAlias());
      }
      cDef.setCompany(ct);
      cDef.setIndustry(channelUrl.getIndustry());

      channel.setPayload(jm.serialize(cDef));

      // Setting name
      channel.setName(channelUrl.getName());
      if (suspend)
      {
        channel.setState(State.suspended);
      }
      channel.save();
    }

  }

  public static void inviteUserToZineone(User zUser,
      final HttpServletRequest req, final HttpServletResponse resp,
      String tempPwd) throws Exception
  {
    final String REG_SEND_EMAIL_TO_CLIENT = "registration.send.email.to.client";

    UContext ctx = UContext.getInstance(req);
    if (((String) zUser.getValues().get(z1.users.User.Fields.status.name()))
        .equals(Status.active.name()))
    {
      ResponseMessage rm = new ResponseMessage(ctx, ResponseMessage.Status.fail,
          ResponseMessage.Type.accountIsActive);
      resp.getWriter().print(rm.toString());
      return;
    }

    boolean sendActivationEmailToClient = false;
    String p = z1.commons.Utils.getPropertyValue(REG_SEND_EMAIL_TO_CLIENT);
    if (p != null)
    {
      sendActivationEmailToClient = Boolean.parseBoolean(
          z1.commons.Utils.getPropertyValue(REG_SEND_EMAIL_TO_CLIENT).trim());
    }

    Account acc = zUser.getAccount();
    String companyName = (String) acc.getValues().get(Account.NAME);

    String userId = (String) zUser.getValues().get(User.Fields.email.name());
    String token = (String) zUser.getValues().get(User.Fields.regToken.name());

    // We'll encode the userId and token now
    userId = Encryption.encodeBase64(userId);
    token = Encryption.encodeBase64(token);

    if (!sendActivationEmailToClient)
    {

      ResponseMessage rm = new ResponseMessage(ctx,
          ResponseMessage.Status.success,
          ResponseMessage.Type.registrationReceived);
      resp.getWriter().print(rm.toString());
      // Send email to user
      EmailSender es = new EmailSender();
      Email eMail = new Email();
      eMail.addSubject(
          z1.commons.Utils.getPropertyValue("email.to.user.subject"));
      eMail.addToAddress(
          (String) zUser.getValues().get(User.Fields.email.name()));

      HashMap<String, Object> hm = new HashMap<String, Object>();
      hm.put("FirstName",
          (String) zUser.getValues().get(User.Fields.fname.name()));
      eMail.addBody(
          StringUtil.substitueValue(Const.getTemplate("EmailToUser.txt"), hm,
              "", new HashMap<String, Object>()));
      es.send(eMail);

      // Send email to zineone
      es = new EmailSender();
      eMail = new Email();

      hm.clear();
      hm.put("domain", req.getServerName());
      eMail.addSubject(StringUtil.substitueValue(
          z1.commons.Utils.getPropertyValue("email.to.zineone.subject"), hm, "",
          new HashMap<String, Object>()));
      eMail.addToAddress(
          z1.commons.Utils.getPropertyValue("email.registeration.internal"));

      hm.clear();
      hm.put("FirstName",
          (String) zUser.getValues().get(User.Fields.fname.name()));
      hm.put("LastName",
          (String) zUser.getValues().get(User.Fields.lname.name()));
      hm.put("CompanyName", companyName);
      hm.put("EmailId",
          (String) zUser.getValues().get(User.Fields.email.name()));
      hm.put("TempPwd", tempPwd);

      String scheme = Utils.getReqScheme(req);
      int port = req.getServerPort();
      if (scheme.equalsIgnoreCase("https")) port = -1;

      // ${schema}://${servername}:${port}/c3/system/validate?user=<userId>&token=<token>
      String activationUrl = scheme + "://" + req.getServerName();
      if (port != -1) activationUrl += ":" + port;
      activationUrl += "/c3/system/validate?user=" + userId + "&token=" + token;
      hm.put("ActivationLink", activationUrl);

      eMail.addBody(
          StringUtil.substitueValue(Const.getTemplate("EmailToZineone.txt"), hm,
              "", new HashMap<String, Object>()));
      es.send(eMail);
    }
    else
    {
      ResponseMessage rm = new ResponseMessage(ctx,
          ResponseMessage.Status.success,
          ResponseMessage.Type.registrationWelcome);
      resp.getWriter().print(rm.toString());
      HashMap<String, Object> hm = new HashMap<String, Object>();
      hm.put("FirstName",
          (String) zUser.getValues().get(User.Fields.fname.name()));
      hm.put("LastName",
          (String) zUser.getValues().get(User.Fields.lname.name()));
      hm.put("CompanyName", companyName);

      hm.put("EmailId",
          (String) zUser.getValues().get(User.Fields.email.name()));
      hm.put("TempPwd", tempPwd);

      String scheme = Utils.getReqScheme(req);
      int port = req.getServerPort();
      if (scheme.equalsIgnoreCase("https")) port = -1;

      // ${schema}://${servername}:${port}/c3/system/validate?user=<userId>&token=<token>
      String activationUrl = scheme + "://" + req.getServerName();
      if (port != -1) activationUrl += ":" + port;
      activationUrl += "/c3/system/validate?user=" + userId + "&token=" + token;
      hm.put("ActivationLink", activationUrl);

      // send verification email
      EmailSender es = new EmailSender();
      Email eMail = new Email();
      eMail.addSubject("Activate your Session AI account");
      eMail.addToAddress(
          (String) zUser.getValues().get(User.Fields.email.name()));
      eMail.addBody(StringUtil.substitueValue(
          Const.getTemplate("EmailForRegistration.txt"), hm, "",
          new HashMap<String, Object>()));
      es.send(eMail);

      // Send email to zineone
      es = new EmailSender();
      eMail = new Email();

      hm.clear();
      hm.put("domain", req.getServerName());
      eMail.addSubject(StringUtil.substitueValue(
          z1.commons.Utils.getPropertyValue("email.to.zineone.subject"), hm, "",
          new HashMap<String, Object>()));
      eMail.addToAddress(
          z1.commons.Utils.getPropertyValue("email.registeration.internal"));

      hm.clear();
      hm.put("FirstName",
          (String) zUser.getValues().get(User.Fields.fname.name()));
      hm.put("LastName",
          (String) zUser.getValues().get(User.Fields.lname.name()));
      hm.put("CompanyName", companyName);
      hm.put("EmailId",
          (String) zUser.getValues().get(User.Fields.email.name()));
      hm.put("TempPwd", tempPwd);

      // ${schema}://${servername}:${port}/c3/system/validate?user=<userId>&token=<token>
      // insert same activationUrl as earlier
      hm.put("ActivationLink", activationUrl);

      eMail.addBody(
          StringUtil.substitueValue(Const.getTemplate("EmailToZineone.txt"), hm,
              "", new HashMap<String, Object>()));
      es.send(eMail);

    }
  }

  // //////////////////////////////////////////////////////////////////
  private void generateIndustryList(List<HashMap<String, String>> l,
      String value)
  {
    HashMap<String, String> h = new HashMap<String, String>();
    h.put("name", value);
    l.add(h);
  }
}
