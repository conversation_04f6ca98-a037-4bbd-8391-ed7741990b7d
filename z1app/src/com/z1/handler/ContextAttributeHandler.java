package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.ContextAttributes;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.ProfileSchema;
import z1.expression.ScriptObject;

public class ContextAttributeHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    all,
    create,
    update,
    delete
  }

  private enum GetCommand
  {
    ns,
    keys
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);
        ContextAttributes contextAttributes = new ContextAttributes(uctx);

        switch (command)
        {
          // c3/data/contextattribute/ns => Retrieve ns context attr info
          case ns:
          {
            List<Map<String, Object>> ret = contextAttributes.getNameSpaceContextAttributes();

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
          // c3/data/contextattribute/keys => Retrieve all context attr info with isKey attr indication
          case keys:
          {
            List<Map<String, Object>> ret = contextAttributes.getKeyContextAttributes();

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        ContextAttributes contextAttributes = new ContextAttributes(uctx);

        switch (command)
        {
          // c3/data/contextattribute/all => Retrieve all context attr info
          case all:
          {
            String reqPayload = ServletUtil.getPayload(req);
            JsonMarshaller jm = new JsonMarshaller();
            if (reqPayload == null || reqPayload.isEmpty()) {
              reqPayload = "{}";
            }
            Map<String, Object> payload = jm.readAsMap(reqPayload);
            List<String> types = (List<String>) payload.get("types");
            List<Map<String, Object>> ret = contextAttributes.getAllContextAttributes(types);
            
            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            break;
          }
          
        // c3/data/contextattribute/create => payload has the definition
        // returns <contextattribute-id>
          case create:
          {
            String cap = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (cap == null) return;

            // Get the name from the payload
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(cap);
            String name = (String) mPl.get("name");
            if (name == null) return;

            CustomConfig cc = CustomConfig.load(uctx, name,
                Type.contextAttribute, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, Type.contextAttribute, name);
              cc.setPayload(cap);
              cc.setName(name);
              cc.save();
              id = cc.getId();
            }

            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", id);
            resp.getWriter().print(jm.serializeMap(map));
            break;
          }
          
          // c3/data/contextattribute/update?id=<id>
          case update:
          {
            // update the existing conext attribute by passing the id            
            String id = req.getParameter("id");
            String cap = ServletUtil.getPayload(req);
            CustomConfig cc = CustomConfig
                .load(uctx, id, Type.contextAttribute);
            if (cc != null)
            {
              cc.setPayload(cap);
              cc.save();
              
              // we'll check if this is a computed attribute. In that case
              // we need to invalidate the script cache.
              Map<String, Object> map = new JsonMarshaller().readAsMap(cap);
              if ("system:computed".equals(map.get(ProfileSchema.ProfileSchemaAttr.useAs.name())))
              {
                ScriptObject.invalidateCache(uctx, ScriptObject.ScriptType.computedAttribute, id);
              }
            }
            break;
          }
          
          // c3/data/contextattribute/delete?id=<id>
          case delete:
          {
            // delete the given context attribute
            String id = req.getParameter("id");            
            req.setAttribute("id", id);
            req.setAttribute("type", Type.contextAttribute.name());
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = Type.contextAttribute.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);

            break;
          }
        }
        // Invalidate the cache
        App.notifyClearCache(uctx.getNamespace(), ProfileSchema.K_CONTEXT_ATTR);
      }

    };
  }

}
