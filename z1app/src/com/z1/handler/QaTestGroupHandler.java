package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;

/**
 * Handles REST for QA Test group CRUD.
 */
public class QaTestGroupHandler implements CommandHandlerFactory
{
  private enum Fields
  {
    name,
    description,
    memberList
  }

  private enum Command
  {
    id,
    all,
    create,
    update,
    delete
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");
        Command type = Command.valueOf(pathParts[0]);

        switch (type)
        {
          // c3/data/qatestgroup/id/<group-id>
          case id:
          // TODO
          {
            // This must be a request for a specific entity.
            String eId = req.getParameter("eId");
            CustomConfig cc = CustomConfig.load(ctx, eId, Type.qaTestGroup,
                true);
            if (cc == null) return;
            String id = cc.getId();
            String ep = cc.getPayload();

            Map<String, Object> map = new HashMap<String, Object>();
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(ep);

            String name = (String) mPl.get(Fields.name.name());
            if (name == null) return;
            String description = (String) mPl.get(Fields.description.name());

            @SuppressWarnings("unchecked")
            String payload = jm.serialize(mPl.get(Fields.memberList.name()));

            map.put("id", id);
            map.put("name", name);
            map.put("description", description);
            map.put("payload", payload);
            resp.getWriter().print(new JsonMarshaller().serialize(map));

            return;
          }

          // c3/data/qatestgroup/all
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(ctx,
                Type.qaTestGroup);
            for (CustomConfig cc : ccList)
            {
              String id = cc.getId();
              Map<String, Object> map = new HashMap<String, Object>();
              map.put("id", id);
              map.put("name", cc.getName());
              map.put("description", cc.getDescription());
              map.put("payload", cc.getPayload());
              // map.put("createdOn", cc.)
              ret.add(map);
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }

          default:
            // Not supported
            break;
        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");
        Command type = Command.valueOf(pathParts[0]);

        switch (type)
        {
          // c3/data/qatestgroup/create
          case create:
          {
            String ep = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (ep == null || ep.isEmpty()) return;

            // Get the name from the payload
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(ep);

            String name = (String) mPl.get(Fields.name.name());
            if (name == null) return;
            String description = (String) mPl.get(Fields.description.name());

            @SuppressWarnings("unchecked")
            String payload = jm.serialize(mPl.get(Fields.memberList.name()));

            CustomConfig cc = CustomConfig.load(ctx, name, Type.qaTestGroup,
                true);
            if (cc == null)
            {
              cc = CustomConfig.create(ctx, Type.qaTestGroup, name);
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              cc.save();
              id = cc.getId();
            }

            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", id);
            resp.getWriter().print(jm.serializeMap(map));

            ArtifactAudit.newInstance(ctx, ItemTypes.qaTestGroup, id, name,
                Operations.create).save();

            return;
          }

          // c3/data/qatestgroup/update/<group-id>
          case update:
          // TODO
          {
            // update the existing test group member by passing the id
            String id = pathParts[1];
            String ep = ServletUtil.getPayload(req);

            // Get the name from the payload
            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> mPl = jm.readAsMap(ep);
            String name = (String) mPl.get(Fields.name.name());
            String description = (String) mPl.get(Fields.description.name());
            
            @SuppressWarnings("unchecked")
            String payload = jm.serialize(mPl.get(Fields.memberList.name()));

            CustomConfig cc = CustomConfig.load(ctx, id, Type.qaTestGroup,
                true);
            if (cc == null) return;

            cc.setPayload(payload);
            cc.setDescription(description);
            cc.save();

            ArtifactAudit.newInstance(ctx, ItemTypes.qaTestGroup, id, name,
                Operations.edit).save();

            return;
          }

          // c3/data/qatestgroup/delete/<group-id>
          case delete:
          {
            String id = pathParts[1];
            CustomConfig cc = CustomConfig.load(ctx, id, Type.qaTestGroup,
                true);
            if (cc != null)
            {
              cc.delete();
            }

            ArtifactAudit.newInstance(ctx, ItemTypes.qaTestGroup, id, id,
                Operations.delete).save();

            return;
          }

          default:
            // Not supported
            break;
        }

      }

    };
  }

}
