package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import z1.actions.AbstractMobileAction.ActionDeliveryStatus;
import z1.actions.ActionUtils;
import z1.actions.UserInteractionsQuery;
import z1.actions.UsersTargetedQuery;
import z1.c3.CustomConfig;
import z1.c3.ExpChangeLog;
import z1.commons.Utils;
import z1.processing.JourneyRuntimeUtils;
import z1.stats.ActionAckStatRecorder;
import z1.stats.ActionStateMetrics;
import z1.stats.ActionsTargetedMetrics;
import z1.stats.ConversionMetrics;
import z1.stats.ExperienceTotalStatsRecorder;
import z1.stats.JourneySegmentStatsRecorder;
import z1.stats.UsersTargetedStatsRecorder;

public class InteractionMetricsHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    debug,
    actions,
    users,
    actionsNotSentReasons,
    userenagementstats,
    userconversionstats
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];       
        String journeyId = req.getParameter("journeyId");
        GetCommand command = null;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          z1.commons.Utils.showStackTraceIfEnable(e, null);
          return;
        }

        switch (command)
        {          
          // c3/data/interactions/debug?journeyId=<journeyId>
          case debug:
          {
            // Total number of times actions were sent for this journey
            long actionsSent = ActionsTargetedMetrics.getActionsSent(ctx,
                journeyId);

            long actionSentReadFromCube = ActionsTargetedMetrics
                .getActionsSentFromCube(ctx, journeyId);

            // Total number of times actions were not sent because a user was in
            // CG
            long actionsNotSentCG = ActionsTargetedMetrics
                .getActionsNotSentCG(ctx, journeyId);
            // Total number of times actions were not sent due to reasons other
            // than CG (limit reached, failures, etc)
            long actionsNotSentOtherReasons = ActionsTargetedMetrics
                .getActionsNotSent(ctx, journeyId);

            // Total number of times actions reached the users
            long actionsReached = ActionStateMetrics.getActionsReachedCount(ctx,
                journeyId);

            // Total number of times users interacted with any action as part of
            // this journey
            long actionsInteractedWith = ActionStateMetrics
                .getInteractedCount(ctx, journeyId);
            // Total number of times any action as part of this journey was
            // dismissed
            long actionsDismissed = ActionStateMetrics.getDismissedCount(ctx,
                journeyId);

            Map<String, Object> map = new HashMap<>();
            map.put("actionsSent_ReadFromCache", actionsSent);
            map.put("actionsSent_ReadFromCube", actionSentReadFromCube);

            map.put("actionsNotSentCG", actionsNotSentCG);
            map.put("actionsNotSentOtherReasons", actionsNotSentOtherReasons);

            map.put("actionsReached", actionsReached);
            map.put("actionsInteractedWith", actionsInteractedWith);
            map.put("actionsDismissed", actionsDismissed);

            // UsersTargetedQuery users = new UsersTargetedQuery();
            // long targetUsers = users.getTargetUsers(ctx,journeyId);
            // long usersTargeted =
            // users.getDistinctUsersTargetedCount(ctx,journeyId);
            // long usersReached =
            // users.getDistinctUsersReachedCount(ctx,journeyId);

            // map.put("targetUsers from db (c3 calls it targeted)",
            // targetUsers);
            // map.put("usersTargeted from db (c3 calls it sent)",
            // usersTargeted);
            // map.put("usersReached from db", usersReached);

            // map.put("targetUsers from from db (c3 calls it targeted)",
            // targetUsers);

            UsersTargetedStatsRecorder ur = new UsersTargetedStatsRecorder(ctx);
            long targetUsers = ur.getUsersTargetedPopulation(journeyId);

            map.put("targetUsers from from cube (c3 calls it targeted)",
                targetUsers);

            JsonMarshaller jm = new JsonMarshaller();
            String payload = jm.serialize(map);
            resp.getWriter().print(payload);

            return;
          }          
          // c3/data/interactions/actionsNotSentReasons?journeyId=<journeyId>
          case actionsNotSentReasons:
          {
            String changeLogId = req.getParameter("changeLogId");
            String fromDate = req.getParameter("fromDate");
            String toDate = req.getParameter("toDate");
            ActionUtils au = new ActionUtils(ctx, journeyId);
            JsonMarshaller jm = new JsonMarshaller();
            if (fromDate == null || toDate == null)
            {
              String payload = jm
                  .serialize(au.actionsNotSentReasons(changeLogId));
              resp.getWriter().print(payload);
            }
            else
            {
              String payload = jm.serialize(
                  au.actionsNotSentReasons(changeLogId, fromDate, toDate));
              resp.getWriter().print(payload);
            }

            return;
          }          
          // c3/data/interactions/users?journeyId=<journeyId>
          case users:
          {

            UsersTargetedQuery users = new UsersTargetedQuery();

            // Distinct Users that were identified as potential targets for this
            // journey
            long targetUsers = users.getTargetUsers(ctx, journeyId);
            ULogger logger = ctx.getLogger(getClass());
            logger.log("Users Identified As Targets:" + targetUsers);

            // Distinct Users that were sent any action
            long usersTargeted = users.getDistinctUsersTargetedCount(ctx,
                journeyId);
            // Users Not sent an action because they belong to the Control Group
            long usersInControlGroup = users
                .getDistinctControlGroupUsersCount(ctx, journeyId);

            // Distinct Users to whom actions reached
            long usersReached = users.getDistinctUsersReachedCount(ctx,
                journeyId);

            UserInteractionsQuery interactions = new UserInteractionsQuery();

            // Distinct Users who Clicked on any action 1 or more times
            long usersInteracted = interactions
                .getDistinctUsersInteractedCount(ctx, journeyId);
            // Distinct Users who Clicked And Converted
            long usersDismissedAction = interactions
                .getDistinctUsersDismissedCount(ctx, journeyId);

            long clickedAndConverted = ConversionMetrics
                .getInteractedAndConvertedCount(ctx, journeyId);
            long dismissedYetConverted = ConversionMetrics
                .getDismissedYetConvertedCount(ctx, journeyId);
            long reachedAndConverted = ConversionMetrics
                .getReachedAndConvertedCount(ctx, journeyId);
            long othersWhoConverted = ConversionMetrics
                .getCountOfOtherUsersWhoConverted(ctx, journeyId);

            logger.log("Other users who converted:" + othersWhoConverted);

            List<Map<String, Object>> nodes = new ArrayList<>();
            String name = "name";
            String displayName = "displayName";

            Map<String, Object> n1 = new HashMap<>();
            n1.put(name, "usersIdentifiedAsTargets");
            n1.put(displayName, "Users Identified As Targets");

            Map<String, Object> n21 = new HashMap<>();
            n21.put(name, "usersTargeted");
            n21.put(displayName, "Users Targeted");
            Map<String, Object> n22 = new HashMap<String, Object>();
            n22.put(name, "usersNotTargetedCG");
            n22.put(displayName, "Not Targeted Control Group");
            Map<String, Object> n23 = new HashMap<String, Object>();
            n23.put(name, "usersNotTargetedOtherReasons");
            n23.put(displayName, "Not Targeted Other Reasons");

            Map<String, Object> n3 = new HashMap<String, Object>();
            n3.put(name, "usersReached");
            n3.put(displayName, "Users Reached");
            Map<String, Object> n4 = new HashMap<String, Object>();
            n4.put(name, "usersInteracted");
            n4.put(displayName, "Users Interacted");
            Map<String, Object> n5 = new HashMap<String, Object>();
            n5.put(name, "usersDismissedAction");
            n5.put(displayName, "Users Dismissed Action");

            Map<String, Object> n6 = new HashMap<String, Object>();
            n6.put(name, "interactedAndConverted");
            n6.put(displayName, "Interacted and Converted");
            Map<String, Object> n7 = new HashMap<String, Object>();
            n7.put(name, "dismissedYetConverted");
            n7.put(displayName, "Dismissed Yet Converted");

            Map<String, Object> n8 = new HashMap<String, Object>();
            n8.put(name, "reachedAndConverted");
            n8.put(displayName, "Reached and Converted");

            nodes.add(n1);
            nodes.add(n21);
            nodes.add(n22);
            nodes.add(n23);
            nodes.add(n3);
            nodes.add(n4);
            nodes.add(n5);
            nodes.add(n6);
            nodes.add(n7);
            nodes.add(n8);

            List<Map<String, Object>> links = new ArrayList<>();
            String source = "source";
            String target = "target";
            String weight = "value";

            Map<String, Object> m11 = new HashMap<String, Object>();
            m11.put(source, "usersIdentifiedAsTargets");
            m11.put(target, "usersTargeted");
            m11.put(weight, usersTargeted);
            links.add(m11);

            Map<String, Object> m12 = new HashMap<String, Object>();
            m12.put(source, "usersIdentifiedAsTargets");
            m12.put(target, "usersNotTargetedCG");
            m12.put(weight, usersInControlGroup);
            links.add(m12);

            Map<String, Object> m13 = new HashMap<String, Object>();
            m13.put(source, "usersIdentifiedAsTargets");
            m13.put(target, "usersNotTargetedOtherReasons");
            m13.put(weight, usersInControlGroup);
            links.add(m13);

            Map<String, Object> m21 = new HashMap<String, Object>();
            m21.put(source, "usersTargeted");
            m21.put(target, "usersReached");
            m21.put(weight, usersReached);
            links.add(m21);

            Map<String, Object> m31 = new HashMap<String, Object>();
            m31.put(source, "usersReached");
            m31.put(target, "usersInteracted");
            m31.put(weight, usersInteracted);
            links.add(m31);

            Map<String, Object> m32 = new HashMap<String, Object>();
            m32.put(source, "usersReached");
            m32.put(target, "usersDismissedAction");
            m32.put(weight, usersDismissedAction);
            links.add(m32);

            Map<String, Object> m41 = new HashMap<String, Object>();
            m41.put(source, "usersInteracted");
            m41.put(target, "interactedAndConverted");
            m41.put(weight, clickedAndConverted);
            links.add(m41);

            Map<String, Object> m42 = new HashMap<String, Object>();
            m42.put(source, "usersDismissedAction");
            m42.put(target, "dismissedYetConverted");
            m42.put(weight, dismissedYetConverted);
            links.add(m42);

            Map<String, Object> m43 = new HashMap<String, Object>();
            m43.put(source, "usersReached");
            m43.put(target, "reachedAndConverted");
            m43.put(weight, reachedAndConverted);
            links.add(m43);

            Map<String, Object> map = new HashMap<>();
            map.put("nodes", nodes);
            map.put("links", links);

            JsonMarshaller jm = new JsonMarshaller();
            String payload = jm.serialize(map);
            resp.getWriter().print(payload);

            return;
          }
          // c3/data/interactions/actions?journeyId=<journeyId>
          case actions:
          {
            String changeLogId = req.getParameter("changeLogId");
            String fromDate = req.getParameter("fromDate");
            String toDate = req.getParameter("toDate");
            if (z1.commons.Utils.isMarketPlace)
            {
              return;
            }
            boolean isMigrated = false;

            // handle change log if changeLogId is 1, then check it is migrated
            ExpChangeLog changeLog = null;
            if (changeLogId == null)
            {
              changeLog = JourneyRuntimeUtils.getLatestChangeLog(ctx, journeyId);
              changeLogId = changeLog.getId();

              if (changeLogId.equals("1"))
              {
                isMigrated = changeLog.isMigrated();
              }
            }
            else if (changeLogId.equals("1"))
            {
              CustomConfig cc = CustomConfig.load(ctx, journeyId,
                  CustomConfig.Type.expChangeLog);
              if (cc != null)
              {
                JsonMarshaller jm = new JsonMarshaller();
                @SuppressWarnings("unchecked")
                List<ExpChangeLog> expChangeLogs = jm
                    .readAsObject(cc.getPayload(), ArrayList.class);
                if (expChangeLogs != null && !expChangeLogs.isEmpty())
                {
                  changeLog = jm.readAsObject(
                      jm.serialize(expChangeLogs.get(0)), ExpChangeLog.class);

                  isMigrated = changeLog.isMigrated();
                }
              }
            }

            long actionsSent = 0;
            long actionsNotSentCG = 0;
            long actionsNotSentOtherReasons = 0;
            long actionsReached = 0;
            long actionsInteractedWith = 0;
            long actionsDismissed = 0;
            long actionsTargeted = 0;

            // Total number of times actions were sent for this journey
            actionsSent = ExperienceTotalStatsRecorder.getActionsSent(ctx,
                journeyId, changeLogId, fromDate, toDate);
            // Total number of times actions were not sent because a user was in

            actionsNotSentCG = ExperienceTotalStatsRecorder.getActionsCG(ctx,
                journeyId, changeLogId, fromDate, toDate);
            // Total number of times actions were not sent due to reasons other
            // than CG (limit reached, failures, etc)
            actionsNotSentOtherReasons = ExperienceTotalStatsRecorder
                .getActionsOther(ctx, journeyId, changeLogId, fromDate, toDate);
            actionsTargeted = actionsSent + actionsNotSentCG
                + actionsNotSentOtherReasons;

            // Total number of times actions reached the users
            // long actionsReached =
            // ActionStateMetrics.getActionsReachedCount(ctx, journeyId);
            actionsReached = ExperienceTotalStatsRecorder.getActionsReached(ctx,
                journeyId, changeLogId, fromDate, toDate);
            // Total number of times users interacted with any action as part of
            // this journey
            // long actionsInteractedWith =
            // ActionStateMetrics.getInteractedCount(ctx, journeyId);
            actionsInteractedWith = ExperienceTotalStatsRecorder
                .getActionsInteracted(ctx, journeyId, changeLogId, fromDate, toDate);
            // Total number of times any action as part of this journey was
            // dismissed
            // long actionsDismissed = ActionStateMetrics.getDismissedCount(ctx,
            // journeyId);
            actionsDismissed = ExperienceTotalStatsRecorder
                .getActionsDismissed(ctx, journeyId, changeLogId, fromDate, toDate);

            if (isMigrated)
            {
              // Total number of times actions were sent for this journey
              actionsSent += ActionsTargetedMetrics.getActionsSent(ctx,
                  journeyId);
              // Total number of times actions were not sent because a user was
              // in
              // CG
              actionsNotSentCG += ActionsTargetedMetrics
                  .getActionsNotSentCG(ctx, journeyId);
              // Total number of times actions were not sent due to reasons
              // other
              // than CG (limit reached, failures, etc)
              actionsNotSentOtherReasons += ActionsTargetedMetrics
                  .getActionsNotSent(ctx, journeyId);

              // long actionsTargeted = actionsSent + actionsNotSentCG +
              // actionsNotSentOtherReasons;

              // Total number of times actions reached the users
              actionsReached += ActionStateMetrics.getActionsReachedCount(ctx,
                  journeyId);

              // Total number of times users interacted with any action as part
              // of
              // this journey
              actionsInteractedWith += ActionStateMetrics
                  .getInteractedCount(ctx, journeyId);
              // Total number of times any action as part of this journey was
              // dismissed
              actionsDismissed += ActionStateMetrics.getDismissedCount(ctx,
                  journeyId);
            }

            long clickedAndConverted = ConversionMetrics
                .getInteractedAndConvertedCount(ctx, journeyId);
            long dismissedYetConverted = ConversionMetrics
                .getDismissedYetConvertedCount(ctx, journeyId);
            long reachedAndConverted = ConversionMetrics
                .getReachedAndConvertedCount(ctx, journeyId);
            long othersWhoConverted = ConversionMetrics
                .getCountOfOtherUsersWhoConverted(ctx, journeyId);

            ULogger logger = ctx.getLogger(getClass());
            logger.log("Other users who converted:" + othersWhoConverted);

            List<Map<String, Object>> nodes = new ArrayList<>();
            String name = "name";
            String displayName = "displayName";

            Map<String, Object> n1 = new HashMap<String, Object>();
            n1.put(name, "targetActions");
            n1.put(displayName, "Total Actions to Target");

            Map<String, Object> n21 = new HashMap<String, Object>();
            n21.put(name, "actionsTargeted");
            n21.put(displayName, "Actions Targeted");
            Map<String, Object> n22 = new HashMap<String, Object>();
            n22.put(name, "actionsNotTargetedCG");
            n22.put(displayName, "Actions Not Targeted Control Group");
            Map<String, Object> n23 = new HashMap<String, Object>();
            n23.put(name, "actionsNotTargetedOtherReasons");
            n23.put(displayName, "Actions Not Targeted Other Reasons");

            Map<String, Object> n3 = new HashMap<String, Object>();
            n3.put(name, "actionsReached");
            n3.put(displayName, "Actions Reached");
            Map<String, Object> n4 = new HashMap<String, Object>();
            n4.put(name, "actionsInteracted");
            n4.put(displayName, "Actions Interacted");
            Map<String, Object> n5 = new HashMap<String, Object>();
            n5.put(name, "dismissedActions");
            n5.put(displayName, "Actions Dismissed");

            Map<String, Object> n6 = new HashMap<String, Object>();
            n6.put(name, "interactedAndConverted");
            n6.put(displayName, "Interacted and Converted");
            Map<String, Object> n7 = new HashMap<String, Object>();
            n7.put(name, "dismissedYetConverted");
            n7.put(displayName, "Dismissed Yet Converted");

            Map<String, Object> n8 = new HashMap<String, Object>();
            n8.put(name, "reachedAndConverted");
            n8.put(displayName, "Reached and Converted");

            nodes.add(n1);
            nodes.add(n21);
            nodes.add(n22);
            nodes.add(n23);

            nodes.add(n3);
            nodes.add(n4);
            nodes.add(n5);

            nodes.add(n6);
            nodes.add(n7);

            nodes.add(n8);

            List<Map<String, Object>> links = new ArrayList<>();
            String source = "source";
            String target = "target";
            String weight = "value";

            Map<String, Object> m11 = new HashMap<String, Object>();
            m11.put(source, "targetActions");
            m11.put(target, "actionsTargeted");
            m11.put(weight, actionsSent);
            links.add(m11);

            Map<String, Object> m12 = new HashMap<String, Object>();
            m12.put(source, "targetActions");
            m12.put(target, "actionsNotTargetedCG");
            m12.put(weight, actionsNotSentCG);
            links.add(m12);

            Map<String, Object> m13 = new HashMap<String, Object>();
            m13.put(source, "targetActions");
            m13.put(target, "actionsNotTargetedOtherReasons");
            m13.put(weight, actionsNotSentOtherReasons);
            links.add(m13);

            Map<String, Object> m21 = new HashMap<String, Object>();
            m21.put(source, "actionsTargeted");
            m21.put(target, "actionsReached");
            m21.put(weight, actionsReached);
            links.add(m21);

            Map<String, Object> m31 = new HashMap<String, Object>();
            m31.put(source, "actionsReached");
            m31.put(target, "actionsInteracted");
            m31.put(weight, actionsInteractedWith);
            links.add(m31);

            Map<String, Object> m32 = new HashMap<String, Object>();
            m32.put(source, "actionsReached");
            m32.put(target, "dismissedActions");
            m32.put(weight, actionsDismissed);
            links.add(m32);

            Map<String, Object> m41 = new HashMap<String, Object>();
            m41.put(source, "actionsInteracted");
            m41.put(target, "interactedAndConverted");
            m41.put(weight, clickedAndConverted);
            links.add(m41);

            Map<String, Object> m42 = new HashMap<String, Object>();
            m42.put(source, "dismissedActions");
            m42.put(target, "dismissedYetConverted");
            m42.put(weight, dismissedYetConverted);
            links.add(m42);

            Map<String, Object> m43 = new HashMap<String, Object>();
            m43.put(source, "actionsReached");
            m43.put(target, "reachedAndConverted");
            m43.put(weight, reachedAndConverted);
            links.add(m43);

            Map<String, Object> map = new HashMap<>();
            map.put("nodes", nodes);
            map.put("links", links);

            JsonMarshaller jm = new JsonMarshaller();
            String payload = jm.serialize(map);
            resp.getWriter().print(payload);

            return;
          }
          // c3/data/interactions/userenagementstats?date=<date>
          case userenagementstats:
          {
            String date = req.getParameter("date");
            JsonMarshaller jm = new JsonMarshaller();
            String payload = jm
                .serialize(ActionAckStatRecorder.getUserEnagementStats(ctx,
                    date, ActionDeliveryStatus.reached));
            resp.getWriter().print(payload);
            return;
          }
          // c3/data/interactions/userconversionstats?date=<date>
          case userconversionstats:
          {
            String date = req.getParameter("date"); 
            JsonMarshaller jm = new JsonMarshaller();
            String payload = jm.serialize(
                JourneySegmentStatsRecorder.getUserConversionStats(ctx, date));
            resp.getWriter().print(payload);
            return;
          }
          default:
          {
            return;
          }
        }

      }
    };

  }

  @Override
  public CommandHandler post()
  {
    return null;
  }

}
