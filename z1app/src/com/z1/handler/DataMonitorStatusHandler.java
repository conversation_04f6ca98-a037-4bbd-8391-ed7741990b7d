package com.z1.handler;

import java.util.Collections;
import java.util.List;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import udichi.core.util.JsonMarshaller;
import z1.datamonitor.DataMonitorSubsystem;
import z1.datamonitor.MonitorRunRecordKeeper;

/**
 * Handles status' for data monitors
 */
public class DataMonitorStatusHandler implements CommandHandlerFactory
{
  private static JsonMarshaller jm = new JsonMarshaller();

  private enum GetCommand
  {
    all,
    id
  }

  @Override
  public CommandHandler get()
  {
    return (uctx, pathParts, req, resp) -> {
      // c3/data/monitorstatus/
      String cStr = pathParts[0];
      GetCommand command = GetCommand.valueOf(cStr);

      switch (command)
      {
        // c3/data/monitorstatus/all?namespace=<ns>
        case all:
        {
          MonitorRunRecordKeeper recordKeeper = DataMonitorSubsystem.instance()
              .monitorRecordKeeper(uctx);

          if (recordKeeper == null)
          {
            resp.getWriter().print(jm.serialize(Collections.emptyList()));
            return;
          }

          resp.getWriter().print(jm.serialize(recordKeeper.lastRunRecords()));
          break;
        }
        case id:
        {
          // c3/data/monitorstatus/id?namespace=<ns>&monid=<id>&[?testid=<testid>]
          String monid = req.getParameter("monid");
          String testid = req.getParameter("testid");

          if (monid == null || monid.isEmpty()){
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams);
            resp.getWriter().print(msg);
            return;
          }

          MonitorRunRecordKeeper recordKeeper = DataMonitorSubsystem.instance()
              .monitorRecordKeeper(uctx).forMonitor(monid);

          if (recordKeeper == null)
          {
            resp.getWriter().print(jm.serialize(Collections.emptyList()));
            return;
          }

          List<MonitorRunRecordKeeper.Record> recordList = recordKeeper
              .lastRunRecord();

          // If testRunId was provided fetch testId from result list.
          if (testid != null && !testid.isEmpty())
          {
            MonitorRunRecordKeeper.Record rec1 = recordList.stream()
                .filter(rec -> testid.equals(rec.getTestId())).findFirst()
                .orElse(null);

            resp.getWriter().print(jm.serialize(rec1 != null ? rec1 : Collections.emptyMap()));
            return;
          }

          resp.getWriter().print(jm.serialize(recordList));
          break;
        }
        default:
        {
          break;
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return null;
  }
}
