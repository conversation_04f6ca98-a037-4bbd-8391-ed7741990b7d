package com.z1.handler;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.system.Account;
import udichi.core.system.AccountService;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.channel.ChannelDefWrapper;
import z1.channel.def.ChannelDef;
import z1.commons.Utils;
import z1.core.Context;
import z1.core.Feed;
import z1.core.Feed.CategoryType;
import z1.core.Feed.Fields;
import z1.core.Feed.KeyInfo;
import z1.core.Feed.ReviewStatus;
import z1.core.Type.IndexType;
import z1.core.utils.FeedIterator;
import z1.nlp.intent.Intent;
import z1.nlp.interest.TaxanomyTree;
import z1.nlp.topicextractor.Topic;
import z1.nlp.training.SentencePhraseExtractor;
import z1.nlp.training.TrainingDataWriter;
import z1.stats.TopicStatBuilder;

/**
 * Handles REST request to get and update a feed for PLP processing.
 */
public class FeedHandler implements CommandHandlerFactory
{
  private static TaxanomyTree tree = new TaxanomyTree();

  private enum GetCommand
  {
    assign // Get a feed available to assign for review
  }

  private enum PostCommand
  {
    update // updates a feed with PLP inputs
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
        // c3/data/feed/assign?agent=<agent_id>[&account=<accountNS>&intentList=<intent-list>]
          case assign:
          {
            // Get the agent id that the feed can be assigned to
            String accountNS = req.getParameter("account");
            String intentList = req.getParameter("intentList");
            
            long accountNum = 0;
            // If we have an accountId passed, we need to find the corresponding
            // account number
            if (accountNS != null)
            {
              // Get the account
              AccountService as = new AccountService(ctx);
              Account ac = as.getAccount(accountNS);
              if (ac != null)
              {
                accountNum = ac.getNumber();
              }
              else
              {
                accountNum = -1;
              }
            }

            Context zCtx = Context.getInstance(ctx);
            FeedIterator iter = null;

            if (intentList == null || intentList.equalsIgnoreCase("all"))
            {
              iter = Feed.findUnAssigned(zCtx, accountNum);
              if(!iter.hasNext()) return;
            }
            else
            {
              // intent feed desired by the user
              String[] intents = intentList.split(",");
              for (String intent : intents)
              {
                iter = Feed.findFeedsForCategoryTypes(zCtx, accountNum,
                    Feed.ReviewStatus.plp_waiting, Feed.CategoryType.intent,
                    intent.toUpperCase());
                
                if (!iter.hasNext()) continue; // no feed found for this intent
                break; 
              }
            }
            
            Feed f = iter.next();
            if(f==null) return;
            
            String text = f.getValue(Feed.Fields.feed);
            
            Map<String, Object> map = transformFeedResultsToMap(f,text);
            SentencePhraseExtractor spe = SentencePhraseExtractor.create(); 
            try
            {
              spe.extract(map, text);//split sentences into phrases for labelling
            }
            catch(Exception e)
            {
              ULogger logger = ctx.getLogger(getClass());
              logger.warning("Long sentence with no punctuation. Not sent for labelling:" + text);
            }
            
            String payload = new JsonMarshaller().serialize(map);
            resp.getWriter().println(payload);

            f.flush();
            return;
          }
        }
      }

      private Map<String, Object> transformFeedResultsToMap(Feed f, String text)
      {
        Map<String, Object> map = new java.util.HashMap<>();
        
        map.put("id", f.getKeyValue()); // feed id
        map.put("text", text);

        List<String> sentiments = f.getCategoryValues(CategoryType.sentiment);
        if (sentiments != null && !sentiments.isEmpty())
        {
          map.put("sentiment", sentiments.get(0));
        }

        // "themes":[{"name":"roadside assistance","decision":0},{"name":"some theme","decision":1}]
        List<Map<String, Object>> themesMap = new java.util.ArrayList<>();
        for (String themeName : f.getCategoryValues(CategoryType.theme))
        {
          Map<String, Object> theme = new java.util.HashMap<String, Object>();
          theme.put("name", themeName);
          theme.put("decision", 1);
          themesMap.add(theme);
        }
        map.put("themes", themesMap);

        // "topics":[{"name":"health","decision":0},{"name":"sports","decision":1}]
        List<Map<String, Object>> topicsMap = new java.util.ArrayList<>();
        for (String topicName : f.getCategoryValues(CategoryType.topic))
        {
          Map<String, Object> topic = new java.util.HashMap<String, Object>();
          topic.put("name", topicName);
          topic.put("decision", 1);
          topicsMap.add(topic);
        }

        map.put("topics", topicsMap);

        List<Map<String, Object>> intentMapList = new java.util.ArrayList<>();
        Set<String> intentsFound = new HashSet<String>();
        for (String intentName : f.getCategoryValues(CategoryType.intent))
        {
          Map<String, Object> intentMap = new java.util.HashMap<String, Object>();
          intentMap.put("name", intentName);
          intentMap.put("decision", 1);
          intentMapList.add(intentMap);
          intentsFound.add(intentName);
        }

        // Publish the rest of the intents
        EnumSet<Intent> intents = EnumSet.allOf(Intent.class);
        for (Intent intent : intents)
        {
          if (!intentsFound.contains(intent.name()))
          {
            Map<String, Object> intentMap = new java.util.HashMap<String, Object>();
            intentMap.put("name", intent.name());
            intentMap.put("decision", 0);
            intentMapList.add(intentMap);
          }
        }

        map.put("intent", intentMapList);

        return map;
      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      /* (non-Javadoc)
       * @see com.z1.CommandHandler#handle(udichi.core.UContext, java.lang.String[], javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
       */ 
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/feed/update
          // The payload will store the changes made to the feed
          case update:
          {
            Map<String, Object> map = ServletUtil.loadPayloadAsDataMap(req);
            
            String feedId = (String) map.get("id");
            // TODO verify the next line if it works for all cases
            Context zCtx = Context.getInstance(uctx);
            Feed f = new Feed(zCtx, feedId);
            
            transformMapToFeed(uctx, f, feedId, map);
            //hardcode for now
            String source = IndexType.FACEBOOK.name();
            String industry = "none";
            
            //write training data
            String channelId = f.getValue(Feed.Fields.channel);
            String company = getCompany(uctx, channelId);
            TrainingDataWriter tdw = new TrainingDataWriter(source, industry, company, map);
            tdw.process(uctx);
            
            List<String> topics = extractTopicsFromMap(map);
            List<String> intents = extractIntentsFromMap(map);
            
            //collect topic stats
            String channelType = f.getValue(Fields.channelType);
            collectTopicStats(uctx,feedId,channelId,channelType,topics,intents);
          }
        }

      }
      
      private void collectTopicStats(UContext uctx, String feedId, String channelId, String channelType,
                            List<String> topics, List<String> intents)
      {
        if(topics==null || topics.isEmpty()) return;
        if(intents==null || intents.isEmpty()) return;
        if(intents.contains(Intent.UNKNOWN.name())) return;
                       
        TopicStatBuilder b = new TopicStatBuilder(uctx);
        
        for(String intent:intents)
        {
          for(String t:topics)
          {              
            KeyInfo keyInfo = KeyInfo.create(feedId);
            String[] splits = t.split("\\|");
            
            if(splits.length==1)
            {
              Topic topicData = new Topic(splits[0],splits[0]); 
              b.updateTopicStats(channelId,intent,channelType,topicData,keyInfo.time);
            }
            else if (splits.length>1)
            {
              Topic topicData = new Topic(splits[0],splits[1]); //label,stem
              b.updateTopicStats(channelId,intent,channelType,topicData,keyInfo.time);
            }          
          }
        }
      }

      private String getCompany(UContext uctx, String channelId)
      {             
        //company name of the channel
        ChannelDefWrapper cdefWrapper = ChannelDefWrapper.forceLoad(uctx, channelId);
        ChannelDef cdef = cdefWrapper.getDef();
        z1.channel.def.CompanyType company = cdef.getCompany();
        
        return company.getName();
      }

      // Get the feed id and all the properties from the payload and
      // update the feed object.
      private Feed transformMapToFeed(UContext ctx, Feed f, String feedId, Map<String, Object> map)
      {
        // Remove existing sentiment,intent,interests
        try
        {
          f.removeCategory(CategoryType.sentiment);
          f.removeCategory(CategoryType.intent);
          f.removeCategory(CategoryType.topic);
          f.removeCategory(CategoryType.spam);

          // Store the
          f.flush();
        }
        catch (Throwable e)
        {
          z1.commons.Utils.showStackTraceIfEnable(e, null);
        }

        // Spam
        Boolean isSpam = (Boolean) map.get("spam");
        if (isSpam == null)
        {
          isSpam = new Boolean(false);
        }
        f.addCategory(CategoryType.spam, isSpam.toString());

        // Sentiment
        String sentiment = (String) map.get("sentiment");
        if (sentiment != null)
        {
          f.addCategory(CategoryType.sentiment, sentiment);
        }

        List<String> topics = extractTopicsFromMap(map);
        for(String topic:topics)
        {
          f.addCategory(CategoryType.topic, topic);
        }
        
        List<String> intents = extractIntentsFromMap(map);
        StringBuilder sb = new StringBuilder();
        for(String intent:intents)
        {
          f.addCategory(CategoryType.intent, intent);
          sb.append(intent).append(" ");
        }

        // Themes
        List<Map<String, Object>> themes = (List<Map<String, Object>>) map
            .get("themes");
        if (themes != null)
        {
          for (Map<String, Object> themeMap : themes)
          {
            String themeName = (String) themeMap.get("name");
            int decision = (Integer) themeMap.get("decision");
            if (decision == 1) // write only if the user says so
            {
              f.addCategory(CategoryType.theme, themeName);
            }
          }
        }

      
        

        // now we set the feed status as reviewed
        f.setReviewStatus(ReviewStatus.resolved);
        f.flush();
        
        return f;
      }

      private List<String> extractTopicsFromMap(Map<String, Object> map)
      {
        // Topics
        List<String> topicsList = new ArrayList<String>();
        
        List<Map<String, Object>> topics = (List<Map<String, Object>>) map
            .get("topics");
        if (topics != null)
        {
          for (Map<String, Object> topicMap : topics)
          {
            String topicName = (String) topicMap.get("name");
            int decision = (Integer) topicMap.get("decision");
            if (decision == 1) // write only if the user says so
            {
              topicsList.add(topicName);
            }
          }
        }
        
        return topicsList;
      }
      
      private List<String> extractIntentsFromMap(Map<String, Object> map)
      {
        // Topics
        List<String> intentsList = new ArrayList<String>();
        
        List<Map<String, Object>> intents = (List<Map<String, Object>>) map
            .get("intent");
        if (intents != null)
        {
          for (Map<String, Object> intentMap : intents)
          {
            String intentName = (String) intentMap.get("name");
            int decision = (Integer) intentMap.get("decision");
            if (decision == 1) // write only if the user says so
            {
              if(intentName.equalsIgnoreCase(Intent.UNKNOWN.name()))
              {
                intentsList.clear();
                return intentsList;
              }
              intentsList.add(intentName);
            }
          }
        }
        
        return intentsList;
      }      
      
    };

  }

}
