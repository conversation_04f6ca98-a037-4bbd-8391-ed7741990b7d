package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.users.User;

public class SuccessMetricHandler implements CommandHandlerFactory
{
  private static JsonMarshaller jm = new JsonMarshaller();
  
  private enum GetCommand
  {
    all,
    id
  }

  private enum PostCommand
  {
    create,
    update,
    delete
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/successmetric/
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);        

        switch (command)
        {
          // c3/data/successmetric/all?namespace=<ns>
          case all:
            List<Map<String, Object>> ret = new ArrayList<>();
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                CustomConfig.Type.successmetric);
            for (CustomConfig cc : ccList)
            {
              String cid = cc.getId();
              Map<String, Object> map = new HashMap<>();
              map.put("id", cid);
              map.put("name", cc.getName());
              map.put("description", cc.getDescription());
              map.put("properties", cc.getDefItem().getValues()
                  .get(DefinitionItem.Fields.properties.name()));
              map.put("state", cc.getDefItem().getValues()
                  .get(DefinitionItem.Fields.state.name()));
              if (cid.startsWith("udc.system.core")) map.put("isReadOnly", true);
              else map.put("isReadOnly", false);
              ret.add(map);
            }
            resp.getWriter().print(jm.serialize(ret));
            break;
          case id:
            // c3/data/successmetric/id?namespace=<ns>&id=<id>
            String id = req.getParameter("id");
            if ((id == null) || (id.length() == 0)) return;

            CustomConfig cc = CustomConfig.load(uctx, id, CustomConfig.Type.successmetric, true);
            if (cc == null) return;

            String payload = Optional.ofNullable(cc.getPayload()).orElse("");
            resp.getWriter().print(payload);
            break;
          default:
            break;
        }
      }
    };
  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/successmetric/create?namespace=<ns>&id=<namedId>
          case create:
          {
            String pl = ServletUtil.getPayload(req);
            if (pl == null || pl.isEmpty()) return;

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = jm.readAsMap(pl);
            String name = (String) map.get("name");
            if (name == null) return;
            String description = (String) map.get("description");
            String id = req.getParameter("id");

            if (!isSuccessMetricExistWithName(uctx, name))
            {
              CustomConfig cc = null;
              if (id != null && !id.isEmpty())
              {
                cc = CustomConfig.create(uctx, CustomConfig.Type.successmetric, id);
              }
              else
              {
                cc = CustomConfig.create(uctx, CustomConfig.Type.successmetric);
              }
              cc.setPayload(pl);
              cc.setName(name);
              cc.setDescription(description);
              // Get payload2 from the request or create it.
              Map<String,Object> payload2 = Optional.ofNullable(
                  (Map<String,Object>) map.get("payload2"))
                       .orElse(new HashMap<String, Object>());

              // Add createdBy to payload2.
              payload2.put("createdBy", uctx.getUser().getValues().get(User.ROLE));          
              cc.setPayload2(jm.serialize(payload2));
              cc.save(State.published);
              cc.save();
              id = cc.getId();
            }
            else
            {
              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed, "Success metric already exists with name ." + name);
              resp.getWriter().print(msg.toString());
              return;
            }              
            
            ArtifactAudit.newInstance(uctx, ItemTypes.successmetric, id, name,
                Operations.create).save();
            ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone, "Success metric created successfully with id ." + id);
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/successmetric/update?namespace=<ns>&id=<id>
          case update:
          {
            String id = req.getParameter("id");            
            String payload = ServletUtil.getPayload(req);
            Map<String, Object> map = jm.readAsMap(payload);
            String name = (String) map.get("name");
            String description = (String) map.get("description");

            CustomConfig cc = CustomConfig.load(uctx, id, Type.successmetric, true);
            if (cc != null)
            {
              if (name != null && !name.isEmpty())
              {
                if (!isSuccessMetricExistWithName(uctx, name))
                {
                  cc.setName(name);
                }
                else
                {
                  ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed, "Success metric already exists with name ." + name);
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
              cc.setPayload(payload);
              cc.setDescription(description);
              if (map.containsKey("payload2")) cc.setPayload2((String)map.get("payload2"));
              cc.save();
            }
            
            ArtifactAudit.newInstance(uctx, ItemTypes.successmetric, id, name,
                Operations.edit).save();
            ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone, "Success metric updated successfully with id ." + id);
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/successmetric/delete?namespace=<ns>&id=<id>
          case delete:
          {
            String id = req.getParameter("id");
            req.setAttribute("id", id);
            req.setAttribute("type", CustomConfig.Type.successmetric.name());
            String[] subParts = new String[pathParts.length + 1];
            subParts[0] = CustomConfig.Type.successmetric.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);
            break;
          }
        }
      }
    };
  }

  /**
   * Checks for success metric with given name
   *
   * @param uctx
   * @param name
   * @return
   */
  private boolean isSuccessMetricExistWithName(UContext uctx, String name)
  {
    List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
        CustomConfig.Type.successmetric);
    for (CustomConfig cc : ccList)
    {
      if (cc.getName().equals(name))
      {
        return true;
      }      
    }
    return false;
  }
}
