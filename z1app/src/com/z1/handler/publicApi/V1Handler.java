package com.z1.handler.publicApi;

import java.util.Enumeration;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.http.HttpHeaders;
import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ApiUtils;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.ServletUtil;
import z1.c3.AbstractSignal;
import z1.c3.SystemConfig;
import z1.channel.ChannelHandler;
import z1.channel.ChannelType;
import z1.commons.HandlerUtils;
import z1.commons.Normalizer;
import z1.core.Profile;

public class V1Handler implements CommandHandlerFactory
{

  private static final String[] IP_HEADER_CANDIDATES = { "X-Forwarded-For",
      "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_X_FORWARDED_FOR",
      "HTTP_X_FORWARDED", "HTTP_X_CLUSTER_CLIENT_IP", "HTTP_CLIENT_IP",
      "HTTP_FORWARDED_FOR", "HTTP_FORWARDED", "HTTP_VIA", "REMOTE_ADDR" };

  private enum Command
  {
    none,
    event,
    service,
    query,
    profile;

    static Command parse(String val)
    {
      for (Command c : Command.values())
      {
        if (c.name().equalsIgnoreCase(val)) return c;
      }
      return none;
    }
  }

  // ............................................................
  // GET Implementation

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        Command command = Command.parse(cStr);

        switch (command)
        {
          case none:
            break;
          // -----------------------------
          // public/api/v1/service/sevice_name?key=val&...
          // -----------------------------
          case service:
            _processService(ctx, pathParts, req, resp);
            break;
          // -----------------------------
          // public/api/v1/event?channel="channel-name"&event="event-name"&...
          // -----------------------------
          case event:
            _processEvent(ctx, pathParts, req, resp);
            break;
          // -----------------------------
          // public/api/v1/query/cube?query-id[&param1="val", ...]
          // public/api/v1/query/stream?query-id[&param1="val", ...]
          // -----------------------------
          case query:
            if (pathParts.length != 2) return;
            resp.setContentType("application/json");
            String type = pathParts[1].toLowerCase();
            if ("cube".equals(type))
            {
              _processCubeQuery(ctx, req, resp);
            }
            else if ("stream".equals(type))
            {
              _processStreamQuery(ctx, req, resp);
            }
            break;
          case profile:
            new com.z1.handler.ProfileOptHandler().get().handle(ctx, pathParts,
                req, resp);
            break;
          default:
            break;
        }
      }
    };

  }

  // ............................................................
  // POST Implementation

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        Command command = Command.valueOf(cStr);

        switch (command)
        {
          case none:
            break;
          // -----------------------------
          // public/api/v1/service/<service name>"
          // -----------------------------
          case service:
            _processService(ctx, pathParts, req, resp);
            break;
          // public/api/v1/event?channel="channel-name"
          // -----------------------------
          case event:
            _processEvent(ctx, pathParts, req, resp);
            break;
          // -----------------------------
          // public/api/v1/profile/optout?REQUEST-PARAMS
          // public/api/v1/profile/optin?REQUEST-PARAMS
          // -----------------------------
          case profile:
            new com.z1.handler.ProfileOptHandler().post().handle(ctx, pathParts,
                req, resp);
            break;
          default:
            break;
        }
      }
    };

  }

  // ////////////////////////////////////////////////////////
  private void _processEvent(final UContext ctx, final String[] pathParts,
      final HttpServletRequest req, final HttpServletResponse resp)
      throws Exception
  {
    ULogger logger = ctx.getLogger(V1Handler.class);

    // The payload will have a list of maps encoded in JSON.
    // Each map must have a mandatory entry called "event" that holds
    // the event name. All other entries are event parameters.

    // Note from Danielle: Get payload before fetching channel name and other
    // parameters.
    // Otherwise HttpServletRequest.getParameter() triggers the reading of the
    // input stream
    // to the end, and payload would be consumed and become empty.
    String payload = ServletUtil.getPayload(req);
    if (payload != null)
    {
      logger.info(String.format("Event payload length = %d", payload.length()));
      if (logger.canLog())
      {
        logger.log("Public api payload: " + payload);
      }
    }

    Map<String, String> reqParams = new java.util.HashMap<>(10);
    Enumeration<String> pNames = req.getParameterNames();
    while (pNames.hasMoreElements())
    {
      String n = pNames.nextElement();
      String v = req.getParameter(n);
      reqParams.put(n, v);
    }

    String channelName = reqParams.get("channel");
    if (!Normalizer.isValidFileName(channelName))
    {
      if (logger.canLog())
      {
        logger.log(String.format(
            "Public API called with invalid channel name %s, discarding event.",
            channelName));
      }
      resp.sendError(HttpServletResponse.SC_NOT_FOUND,
          "Public event endpoint called with invalid channel name.");
      return;
    }

    // ==============================================
    // The payload will have the following structure
    // {
    // someKeyForCustomerIdAsDefinedInChannel:
    // "device_id or any unique id",
    // events: [
    // {
    // same structure as the current c3 api
    // }
    // ...
    // ]
    // }
    // ==============================================

    ChannelHandler channel = null;
    ChannelType cType = ChannelType.push;
    channel = ChannelHandler.forName(ctx, channelName, cType);
    if (channel == null)
    {
      if (logger.canLog())
      {
        logger.log("Public event endpoint called with unknown channel "
            + "name. No channel is defined for name - " + channelName
            + ". Discarding.");
      }
      resp.sendError(HttpServletResponse.SC_NOT_FOUND,
          "Public event endpoint called with unknown channel name. No channel is defined for name - "
              + channelName);
      return;
    }

    // Check if the channel name matches the API Key used
    if (!ApiUtils.checkIfPublic(ctx, pathParts, req)
        && !ApiUtils.isValidContext(ctx, channel.getId()))
    {
      if (logger.canLog())
      {
        logger.log("Wrong API Key used for channel - " + channelName
            + ". Discarding.");
      }
      resp.sendError(HttpServletResponse.SC_NOT_FOUND,
          "Public event endpoint called with invalid API key for channel name - "
              + channelName);
      return;
    }

    if (!channel.isActive())
    {
      if (logger.canLog())
      {
        logger.log("Channel is not active - " + channelName + ". Discarding.");
      }
      resp.sendError(HttpServletResponse.SC_NOT_FOUND,
          "Public event endpoint called with inactive channel name - "
              + channelName);
      return;
    }

    channel.setInputData(payload);
    channel.setRequestParams(reqParams);

    if (logger.canLog())
    {
      logger.log("Picked up channel: " + channel.getName());
    }

    List<Map<String, Object>> inputEList = null;
    List<Map<String, Object>> eList = null;
    String profileId = null;
    try
    {
      // Let's prepare the channel for the current event data.
      channel.prepare();
      inputEList = channel.getEvents();
      if ((inputEList == null) || (inputEList.isEmpty()))
      {
        if (logger.canLog())
        {
          logger.log(
              "Public event endpoint called with no event information found. Check channel pre-processing code if set. Channel: "
                  + channel.getName());
        }
        // resp.sendError(
        // HttpServletResponse.SC_BAD_REQUEST,
        // "Public event endpoint called with no event information found. Check
        // channel pre-processing code if set. Channel: "
        // + channel.getName());
        return;
      }
      // check event payload size
      if (!HandlerUtils.isValidPayloadSize(payload))
      {
        logger.warning(
            "event payload size limit exceed " + inputEList.toString());
        return;
      }

      eList = inputEList.stream()
          .filter(e -> !AbstractSignal.isEventBlacklisted(ctx,
              (String) e.get(AbstractSignal.EVENT)))
          .collect(java.util.stream.Collectors.toList());

      if (eList == null || eList.isEmpty()) return;

      boolean blockEventIP = SystemConfig.getBooleanValue(ctx, SystemConfig.Z1_BLOCK_EVENT_IP, true);

      // Add user-agent as part of the event payload.
      final String userAgent = req.getHeader(HttpHeaders.USER_AGENT);
      if (userAgent != null && !userAgent.trim().isEmpty())
      {
        for (Map<String, Object> e : eList)
        {
          e.put(z1.commons.Const.USER_AGENT, userAgent);
        }
      }

      // Add client ip address to incoming payload as an event parameter.
      // (if merchant has enabled Z1_FETCH_CUSTOMER_IP toggle)
      String ip = blockEventIP ? null : ApiUtils.getCustomerIpAddress(req);

      if (ip != null && !ip.trim().isEmpty())
      {
        for (Map<String, Object> e : eList)
        {
          e.put(z1.commons.Const.IP_ADDRESS, ip);
        }
      }

      profileId = channel.getProfileId();
      if (logger.canLog())
      {
        logger.log("Profile found. Profile ID: " + profileId);
      }

      if (profileId != null
          && profileId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
      {
        return;
      }
    }
    catch (ChannelHandler.BlockedEvent ble)
    {
      if (logger.canLog())
      {
        logger.log("Event sent is blocked.");
      }
      resp.sendError(HttpServletResponse.SC_BAD_REQUEST,
          "Event is not allowed.");
      return;
    }
    catch (ChannelHandler.BadRequest bre)
    {
      if (logger.canLog())
      {
        logger.log(bre.getMessage());
      }
      resp.sendError(HttpServletResponse.SC_BAD_REQUEST, bre.getMessage());
      return;
    }
    catch (ChannelHandler.InternalError iee)
    {
      if (logger.canLog())
      {
        logger.log(iee.getMessage());
      }
      resp.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
          iee.getMessage());
      return;
    }

    // If the channel pre-processing is marked as return immediately, we need to
    // return
    if (channel.isReturnImmediately())
    {
      channel.setupResponse(resp);
      return;
    }

    // Processing events now

    z1.EventHandler eh = new z1.EventHandler(ctx)
        .setIncomingChannel(ChannelType.push);
    // Is this a system event
    eh.handleAsSystemEvents(channel.isForSystemEvent());
    // Is this a synchronous channel? In that case we will execute
    // the event processing in process and get the response back
    if (channel.isSynchronous())
    {
      eh.handleInProcess();
    }

    if (profileId != null)
    {
      eh.setProfileId(profileId).handle(eList, logger);
    }
    else
    {
      eh.handle(eList, logger);
    }

    // Set the response body in case of sync execution
    if (channel.isSynchronous())
    {
      channel.setResponseBody(z1.EventHandler.getSyncExecutionResponse(ctx));
    }

    // Check if the channel sets the response
    channel.setupResponse(resp);

  }


  // ......................................................
  // Handles both GET and POST requests.
  private void _processService(final UContext ctx, final String[] pathParts,
      final HttpServletRequest req, final HttpServletResponse resp)
      throws Exception
  {
    ULogger logger = ctx.getLogger(V1Handler.class);
    if (pathParts.length < 2)
    {
      resp.sendError(HttpServletResponse.SC_BAD_REQUEST,
          "Missing service name");
      return;
    }

    String serviceName = pathParts[1];
    ChannelHandler channel = ChannelHandler.forName(ctx, serviceName,
        ChannelType.service);
    if (channel == null)
    {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND,
          "Uknown service endpoint.");
      return;
    }

    if (!ApiUtils.checkIfPublic(ctx, pathParts, req))
    {
      // Check if the channel name matches the API Key used
      if (!ApiUtils.isValidContext(ctx, channel.getId()))
      {
        resp.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid API key");
        return;
      }
    }

    if (!channel.isActive())
    {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND, "Service is not active");
      return;
    }

    // Is this a GET or POST call?
    boolean isGet = req.getMethod().equalsIgnoreCase("GET");
    boolean isPost = req.getMethod().equalsIgnoreCase("POST");
    if (!isGet && !isPost)
    {
      resp.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
      return;
    }
    if (isGet && !"get".equalsIgnoreCase(channel.getSubtype()))
    {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND);
      return;
    }
    if (isPost && !"post".equalsIgnoreCase(channel.getSubtype()))
    {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND);
      return;
    }

    String payload = ServletUtil.getPayload(req);
    Map<String, String> reqParams = new java.util.HashMap<>(10);
    Enumeration<String> pNames = req.getParameterNames();
    while (pNames.hasMoreElements())
    {
      String n = pNames.nextElement();
      String v = req.getParameter(n);
      reqParams.put(n, v);
    }

    channel.setInputData(payload);
    channel.setRequestParams(reqParams);
    channel.prepare();
    channel.setupResponse(resp);

    return;
  }

  // ......................................................
  // Processes a cube query
  private void _processCubeQuery(final UContext ctx,
      final HttpServletRequest req, final HttpServletResponse resp)
      throws Exception
  {
    // We'll call the CubeQueryHandler to handle the query
    String[] queryParts = new String[] { "query" };
    new com.z1.handler.CubeQueryHandler().get().handle(ctx, queryParts, req,
        resp);
  }

  // ......................................................
  // Processes a stream query
  private void _processStreamQuery(final UContext ctx,
      final HttpServletRequest req, final HttpServletResponse resp)
      throws Exception
  {
    // We'll call the StreamQueryHandler to handle the query
    String[] queryParts = new String[] { "query" };
    new com.z1.handler.StreamQueryHandler().get().handle(ctx, queryParts, req,
        resp);
  }

}
