package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import z1.commons.Utils;
import z1.commons.Const.DeviceProfileInfo;
import z1.css.Css;
import z1.css.CssLoader;
import z1.css.CssLoader.ContentCssType;
import z1.css.PlugableCSS;
import z1.css.PlugableCSS.Fields;
import z1.css.PlugableCSS.Mode;

public class CssHandler implements CommandHandlerFactory
{
  
  private static final String SEP = "|";

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  // Supported post commands
  private enum GetCommand
  {
    id,
    all
  }
  
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/css/id?id=<id>&mode=<mode>
          case id:
          {
            String id = req.getParameter("id");
            PlugableCSS pcss = PlugableCSS.load(ctx, id);                        
            String payload = null;
            String mode = req.getParameter("mode"); 
            Css c = new Css();
            
            if(pcss==null)
            {
              //<os>|<module>|<css/html>|<deviceType>|<subCategory> 
              String[] tokens = id.split("\\|");
              String os = tokens[0]; 
              String module = tokens[1];
              String deviceType = tokens[3];
              String subCategory = tokens[4];
              
              //should i load .css file (mode=advanced) or a -template.css (mode=simple) file
              boolean loadTemplate = Mode.simple.name().equals(mode) ? true : false;
              payload = CssLoader.loadCss(ctx, loadTemplate, module, os, deviceType, subCategory);
            }
            else
            {
               payload = (String) pcss.getValues().get(Fields.payload.name());
               mode = (String)pcss.getValues().get(Fields.mode.name());
               if(mode.equals(Mode.simple.name()))
               {
                 c.setKeyValuePayload((String) pcss.getValues().get(Fields.keyValuePayload.name()));
               }
               c.isDefault = false; //This conveys to the UI that the custom css from mongo was loaded
            }
                      
            c.id = id;
            c.payload = payload;
            c.mode = mode; //UI needs this if not loaded from mongo give it back what it sent, else give the mongo value                      
            resp.getWriter().print(new JsonMarshaller().serialize(c));
            return;
          }
          // c3/data/css/all
          case all:
          {
            JsonMarshaller jm = new JsonMarshaller();

            // Send data for all channels created so far
            Result<PlugableCSS> res = PlugableCSS
                .loadAll(ctx);
            List<PlugableCSS> l = new ArrayList<PlugableCSS>();
            for (PlugableCSS css : res.getData())
            {            
              if (css == null) continue;
              l.add(css);
            }

            resp.getWriter().print(jm.serialize(l));
            return;
          }

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/css/create
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            
            Map<String, Object> vals = j.readAsMap(payload);
            String os = (String)vals.get(DeviceProfileInfo.os.name());
            String deviceType = (String)vals.get("devicetype");  //phone, tablet, desktop, etc
            String module = (String)vals.get("module");  //module also implies the message type: fullscreen/alrt/article/chat         
            String mode = (String)vals.get("mode");  // simple | advanced
            String subCategory = (String)vals.get("subcategory"); // implies message component --> list, content, main (for default message component)
                    
            // <os>|<module>|<css/html>|<deviceType>|<subCategory> 
            StringBuilder id = new StringBuilder().append(os).append("|").append(module).append("|").append("css").append("|");
            id.append(deviceType).append("|");
            id.append(subCategory);
            
            String cssId = id.toString(); //ios|kb|css|phone|list
            
            PlugableCSS pcss = PlugableCSS.newInstance(ctx, cssId);
            String pload = null;
            if(mode.equals(PlugableCSS.Mode.simple.name()))
            {
              String kvpload = (String)vals.get("payload");
              pcss.getValues().put(Fields.keyValuePayload.name(), kvpload);
                            
              pload = mergeTemplateCssWithKVPayload(kvpload, module, os, deviceType, subCategory, ctx);
            }
            else if(mode.equals(Mode.advanced.name()))
            {
              pload = (String)vals.get("payload");
            }
            if(pload == null) {
              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.resourceNotFound);
              resp.getWriter().print(new JsonMarshaller().serialize(rm));
              return;
            }
            pcss.getValues().put(Fields.payload.name(), pload);
            pcss.getValues().put(Fields.mode.name(), mode);
            pcss.save();
            Css c = new Css((String) pcss.getValues().get(Fields.id.name()),
                pload, mode);
            if(mode.equals(Mode.simple.name()))
            {
              c.setKeyValuePayload((String) pcss.getValues().get(Fields.keyValuePayload.name()));
            }
            c.setDefault(false);
            resp.getWriter().print(new JsonMarshaller().serialize(c));

            return;
          }
          // c3/data/css/update
          case update:
          {
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            
            Map<String, Object> vals = j.readAsMap(payload);

            String os = (String)vals.get(DeviceProfileInfo.os.name());
            String deviceType = (String)vals.get("devicetype");  //phone, tablet, desktop, etc
            String module = (String)vals.get("module");  //module also implies the message type: fullscreen/alrt/article/chat         
            String mode = (String)vals.get("mode");  // simple | advanced
            String subCategory = (String)vals.get("subcategory"); // implies message component --> list, content, main (for default message component)   
            
            String id = (String)vals.get("id");
            PlugableCSS pcss = PlugableCSS.load(ctx, id);

            String pload = null;
            
            if(mode.equals(PlugableCSS.Mode.simple.name()))
            {
              String kvpload = (String)vals.get("payload");
              pcss.getValues().put(Fields.keyValuePayload.name(), kvpload);
              pload = mergeTemplateCssWithKVPayload(kvpload, module, os, deviceType, subCategory, ctx);
            }
            else if(mode.equals(Mode.advanced.name()))
            {
              pload = (String)vals.get("payload"); 
            }
            
            if(pload == null) 
            {
              ResponseMessage rm = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.resourceNotFound);
              resp.getWriter().print(rm.toString());
              return;        
            }
            pcss.getValues().put(Fields.payload.name(), pload);
            pcss.getValues().put(Fields.mode.name(), mode);
            pcss.save();
            Css c = new Css((String) pcss.getValues().get(Fields.id.name()),
                pload,mode);
            if(mode.equals(Mode.simple.name()))
            {
              c.setKeyValuePayload((String) pcss.getValues().get(Fields.keyValuePayload.name()));
            }
            c.isDefault = false;
            resp.getWriter().print(new JsonMarshaller().serialize(c));
            return;
          }
          
          // c3/data/css/delete          
          case delete:
          {
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();
            String id = (String)j.readAsMap(payload).get("id");
            PlugableCSS.delete(ctx, id);
            
            ResponseMessage rm = new ResponseMessage(ctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.resourseDeletedSuccessfully);
            resp.getWriter().print(rm.toString());
            return;
          }
        }
      }
    };
    

  }
  
  /**
   * 
   * @param kvpload
   * @param module - feature or message type such as - chat|alrt|article
   * @param os
   * @param deviceType - phone|tablet|desktop
   * @param subCategory - message component such as main|list|content|...
   * @param ctx
   * @return
   */
        
  private String mergeTemplateCssWithKVPayload(String kvpload, String module,
      String os, String deviceType, String subCategory, UContext ctx)
  {
    String cssTemplate = CssLoader.loadDefaultCssTemplate(ctx, ContentCssType.content, true, module, os, deviceType, subCategory);
    JsonMarshaller jm = new JsonMarshaller();
    Map<String, Object> hm = jm.readAsMap(kvpload);
    if (cssTemplate != null)
    {
      String mergedCss = StringUtil.substitueValue(cssTemplate, hm, "",
          new HashMap<String, Object>());
      return mergedCss;
    }
    else
    {
      return null;
    }
  }
    

}
