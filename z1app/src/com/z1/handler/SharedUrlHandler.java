package com.z1.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ApiUtils;

import udichi.core.App;
import udichi.core.Const;
import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.SharedUrl;
import z1.c3.SharedUrl.Fields;
import z1.c3.SharedUrlService;
import z1.core.utils.Utils;


public class SharedUrlHandler implements CommandHandlerFactory
{
  // Supported Get commands
  private enum GetCommand
  {
    all,
    id
  }

  private enum PostCommand
  {
    create,
    delete
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/sharedurl/all => requests all sharedurls
          case all:
          {
            SharedUrlService ss = new SharedUrlService(uctx);
            Result<SharedUrl> urls = ss.getAll();
            if (urls == null || urls.isNull())
            {
              resp.getWriter().println("{}");
              return;
            }
            Iterator<SharedUrl> iter = urls.getData().iterator();
            List<String> res = new ArrayList<>(1);
            JsonMarshaller jm = new JsonMarshaller();
            while (iter.hasNext())
            {
              Map<String, Object> map = new HashMap<>();
              SharedUrl url = iter.next();
              @SuppressWarnings("unchecked")
              List<Map<String, Object>> list = jm.readAsObject(url.getParams(), List.class);
              map.put(Fields.urlId.name(), url.getUrlId());
              map.put(Fields.itemId.name(), url.getItemId());
              map.put(Fields.itemType.name(), url.getItemType());
              map.put(Fields.url.name(), _getSharedUrlWithNamespaceAndApikey(url));
              map.put(Fields.params.name(), list);
              String urlObjStr = jm.serialize(map);
              res.add(urlObjStr);
            }
            resp.getWriter().print(jm.serialize(res));
            return;
          }
          
          // c3/data/sharedurl/id?itemId=<itemId>[&itemType=<item-type>&namespace=<namespace>] => request a specific sharedurl by providing the dashboardId
          case id:
          {
                        
            String itemId = req.getParameter("itemId");            

            if (itemId == null)
            {
              throw new Exception("itemId is not defined.");
            }
            String itemType = _getItemType(req);
            String namespace = _getNamespace (uctx, req);
            
            SharedUrlService ss = new SharedUrlService(uctx);
            
            SharedUrl url = ss.getSharedUrlByIdTypeAndNamespace(itemId, itemType, namespace);
            
            if (url == null)
            {
              resp.getWriter().println("{}");
              return;
            }

            Map<String, Object> map = new HashMap<String, Object>();
            JsonMarshaller jm = new JsonMarshaller();
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> list = jm.readAsObject(url.getParams(), List.class);
            map.put(Fields.urlId.name(), url.getUrlId());
            map.put(Fields.itemType.name(), url.getItemType());
            map.put(Fields.itemId.name(), url.getItemId());
            map.put(Fields.url.name(), _getSharedUrlWithNamespaceAndApikey(url));
            map.put(Fields.params.name(), list);
            resp.getWriter().print(jm.serialize(map));
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/sharedurl/create => payload has the definition
          // returns <url>
          case create:
          {           
            java.util.Map<String, Object> dataMap = udichi.core.util.ServletUtil
                .loadPayloadAsDataMap(req);
            if (dataMap != null)
            {
              // Load the info from the payload
              SharedUrlService ss = new SharedUrlService(uctx);
              String itemId = _getItemId (dataMap);
              if (itemId == null)
              {
                throw new Exception("itemId is not defined.");
              }
              
              List<Map<String, Object>> params = (List<Map<String, Object>>) dataMap.get(Fields.params.name());
              String paramsStr = "";
              String namespace = _extractNamespace(uctx, params); 
              //Check in params if namespace is provided
              if (params != null && !params.isEmpty())
              {
                paramsStr = new JsonMarshaller().serialize(params);
              }
              
              String scheme = Utils.getReqScheme(req);
              int port = req.getServerPort();
              
              if (scheme.equalsIgnoreCase("https")) port = -1;

              String serverName = scheme + "://" + req.getServerName();

              if (port != -1)
              {
                serverName += ":" + port;
              }
              String itemType = _getItemType(req);
              
              // check if the account already exists
              SharedUrl url = ss.getSharedUrlByIdTypeAndNamespace(itemId, itemType, namespace);
              
              if (url == null)
              {
                url = ss.create(itemId, itemType, namespace, serverName, paramsStr);
              }
              else
              {
                url = ss.update(url, itemId, itemType, namespace, serverName, paramsStr);
              }

              resp.getWriter().println("{");
              resp.getWriter().println("\"url\":\"" + _getSharedUrlWithNamespaceAndApikey(url) + "\"");
              resp.getWriter().println("}");
            }
            
            break;
          }

          // c3/data/sharedurl/delete?itemId=<itemId>[&itemType=<item-type>&namespace=<namespace>]
          //=> delete shared url for the provided itemId
          case delete:
          {
            String itemId = req.getParameter("itemId");
            SharedUrlService ss = new SharedUrlService(uctx);
            if (itemId == null)
            {
              throw new Exception("itemId is not defined.");
            }
            String itemType = _getItemType (req);
            String namespace = _getNamespace (uctx, req);
             
            ss.delete(itemId, itemType, namespace);
            
            resp.getWriter().println("{");
            ServletUtil.printSuccess(resp.getWriter());
            resp.getWriter().println("}");
            
            break;
          }
        }
        App.notifyClearCache(Const.NS_UDICHI_CORE, ApiUtils.SHARED_URL_CACHE);
      }
    };
  }

  private String _getItemType(final HttpServletRequest req)
  {
    String itemType = req.getParameter(Fields.itemType.name()); 
    if (itemType == null) {
      itemType = Fields.itemType.getValue();
    }
    return itemType;
  }
  
  private String _getItemId (Map<String, Object> dataMap) {
    String itemId = (String) dataMap.get(Fields.itemId.name());
    if (itemId == null) {
      return (String) dataMap.get(Fields.itemId.getValue());
    }
    return itemId;
  }
  
  private String _getNamespace(UContext uctx, final HttpServletRequest req) {
    String namespace = req.getParameter(Fields.namespace.name());
    if (namespace == null) {
      namespace = uctx.getNamespace();
    }
    return namespace;
  }

  private String _extractNamespace(UContext uctx,
      List<Map<String, Object>> params)
  {
    String namespace = uctx.getNamespace(); // use uctx namespace first
    if (params != null) { 
      for (Map<String, Object> param : params)
      {
        String name = (String) param.get("name");
        if (name.equals(Fields.namespace.name()))
        {
          namespace = (String) param.get("value");
          break;
        }
      }
    }
    return namespace;
  }

  private String _getSharedUrlWithNamespaceAndApikey(SharedUrl sharedUrl){
    String sckey = z1.commons.Utils.subcloudKey.toString();
    String apikey = sckey.isEmpty() == false ? z1.commons.Const.C3_KEY_PREFIX.toString().concat(":").concat(sckey) : 
            z1.commons.Const.C3_KEY_PREFIX.toString();
    return new StringBuilder().append(sharedUrl.getUrl())
            .append("&namespace=").append(sharedUrl.getNamespace())
            .append("&" + z1.commons.Const.C3_KEY + "=").append(apikey).toString();
  }
}
