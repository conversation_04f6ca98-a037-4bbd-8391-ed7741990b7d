package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.c3.CustomConfig;
import z1.c3.Journey;
import z1.c3.Journey.ExeMode;
import z1.c3.Journey.Type;
import z1.c3.Segment;
import z1.commons.Const;
import z1.commons.Utils;
import z1.core.utils.TimeUtils;
import z1.stats.ActionAckStatRecorder;
import z1.stats.EventStatsProcessor;
import z1.stats.ExperienceDailyStatsRecorder;
import z1.stats.ExperienceTotalStatsRecorder;
import z1.stats.ProfileCountStat;
import z1.stats.SegmentStatsRecorder;

public class GoalStatsHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    stats,
    eventStatsFromDateRange,
    profileStatsFromDateRange,
    actionStatsFromDateRange,
    pushAnalysisByOS,
    pushAnalysisByActionName
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/goalStats/stats
          case stats:
          {
            Map<String, Object> map = new HashMap<>(10);
            long profileCount = SegmentStatsRecorder.getTotalPopulation(ctx,
                Const.ALL_USERS);
            // get total events count for last 90 days including current day
            long eventCount = new EventStatsProcessor(ctx)
                .getTotalEventsCountInLast90Days();

            int segmentCount = Segment.loadAll(ctx, true).size();
            int triggerCount = CustomConfig
                .loadAll(ctx, z1.c3.CustomConfig.Type.signal).size();
            int interactionCount = Journey.forceLoadAll(ctx, Type.c1).size()
                + Journey.forceLoadAll(ctx, Type.campaign).size()
                + Journey.forceLoadAll(ctx, Type.journey).size();
            String mode = req.getParameter("exeMode");
            ExeMode exeMode = mode == null ? ExeMode.live : ExeMode.parse(mode);
            // Find count of all actions sent across all experiences.
            long actionCount = ExperienceTotalStatsRecorder
                .getActionsTotalSent(ctx, "*",exeMode);

            map.put("actionCount", actionCount);

            map.put("triggerCount", triggerCount);
            map.put("eventCount", eventCount);
            map.put("interactionCount", interactionCount);
            map.put("profileCount", profileCount < 0 ? 0 : profileCount);
            map.put("segmentCount", segmentCount);
            map.put("namespace", ctx.getNamespace());
            map.put("userEmail", ctx.getUser().getId());
            String payload = new JsonMarshaller().serialize(map);
            resp.getWriter().print(payload);
            return;
          }
          // c3/data/goalStats/eventStatsFromDateRange?startDate=<yyyymmdd>&endDate=<yyyymmdd>
          case eventStatsFromDateRange:
          {
            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            if (startDate != null && endDate != null)
            {
              EventStatsProcessor b = new EventStatsProcessor(ctx);
              List<Map<String, Object>> list = b
                  .getEventStatsFromRange(startDate, endDate);
              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(jm.serialize(list));
            }

            return;
          }
          // c3/data/goalStats/profileStatsFromDateRange?startDate=<yyyymmdd>&endDate=<yyyymmdd>
          case profileStatsFromDateRange:
          {

            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            if (startDate != null && endDate != null)
            {
              List<Map<String, Object>> list = ProfileCountStat
                  .getTotalPopulationForRange(ctx, startDate, endDate);
              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(jm.serialize(list));
            }
            return;
          }
          // c3/data/goalStats/actionStatsFromDateRange?startDate=<yyyymmdd>&endDate=<yyyymmdd>
          case actionStatsFromDateRange:
          {

            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            String mode = req.getParameter("exeMode");
            ExeMode exeMode = mode == null ? ExeMode.live : ExeMode.parse(mode);

            if (startDate != null && endDate != null)
            {
              List<Map<String, Object>> list = ExperienceDailyStatsRecorder
                  .getTotalActionsForRange(ctx, startDate, endDate, exeMode);
              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(jm.serialize(list));
            }
            return;
          }
          case pushAnalysisByOS:
          {
            String ack = req.getParameter("ack");
            String mode = req.getParameter("exeMode");
            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            if (startDate == null || endDate == null)
            {
              TimeUtils timeUtils = new TimeUtils();
              endDate = timeUtils.getDate();
              startDate = timeUtils.getPreviousDate(30);
            }
            ExeMode exeMode = mode == null ? ExeMode.live : ExeMode.parse(mode);
            if (ack != null)
            {

              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(
                  jm.serialize(ActionAckStatRecorder.getPushActionStatsByOS(ctx,
                      ack, exeMode, startDate, endDate)));
            }
            return;
          }
          case pushAnalysisByActionName:
          {
            String ack = req.getParameter("ack");
            String mode = req.getParameter("exeMode");
            String startDate = req.getParameter("startDate");
            String endDate = req.getParameter("endDate");
            if (startDate == null || endDate == null)
            {
              TimeUtils timeUtils = new TimeUtils();
              endDate = timeUtils.getDate();
              startDate = timeUtils.getPreviousDate(30);
            }
            ExeMode exeMode = mode == null ? ExeMode.live : ExeMode.parse(mode);
            if (ack != null)
            {
              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter()
                  .print(jm.serialize(
                      ActionAckStatRecorder.getPushActionStatsByActionName(ctx,
                          ack, exeMode, startDate, endDate)));
            }
            return;
          }
          default:
          {
            return;
          }
        }

      }
    };

  }

  @Override
  public CommandHandler post()
  {
    return null;
  }

}
