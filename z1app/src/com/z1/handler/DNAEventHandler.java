package com.z1.handler;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import com.z1.ml.DNAEventListManager;
import com.z1.ml.DNAEventListManagerException;

import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.commons.Utils;

public class DNAEventHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    allAdded
  }

  public enum PostCommand
  {
    add,
    delete
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(UContext uctx, String[] pathElemets,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        JsonMarshaller jm = new JsonMarshaller();
        if (pathElemets.length > 0)
        {
          String cStr = pathElemets[0];
          GetCommand command = GetCommand.valueOf(cStr);
          switch (command)
          {
            // c3/data/dnaevent/allAdded
            case allAdded:
            {
              if (z1.commons.Utils.isMarketPlace)
              {
                return;
              }

              DNAEventListManager dnaEventListManager = new DNAEventListManager(
                  uctx);
              List<String> dnaEventList = dnaEventListManager.getAllExplicitlyAdded();

              resp.getWriter().print(jm.serialize(dnaEventList));
              break;
            }
            default:
            {
              break;
            }
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(UContext uctx, String[] pathElemets,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        // None of the post commands should run on market place...
        if (z1.commons.Utils.isMarketPlace)
        {
          return;
        }
        
        JsonMarshaller jm = new JsonMarshaller();
        if (pathElemets.length > 0)
        {
          String cStr = pathElemets[0];
          PostCommand command = PostCommand.valueOf(cStr);
          switch (command)
          {
            // c3/data/dnaevent/add
            case add:
            {
              ResponseMessage msg = null;
              String forceParam = req.getParameter("force");
              boolean force = (forceParam != null
                  && forceParam.equalsIgnoreCase("true")) ? true : false;

              String payload = ServletUtil.getPayload(req);
              if (payload == null || payload.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Request payload is missing. Expecting a list of namespace events to add to the dna event list.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              List<String> namespaceEventsToAdd;
              try
              {
                namespaceEventsToAdd = jm.readAsObject(payload, List.class);
              }
              catch (UException exception)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Failed to parse payload... Expecting a list of namespace events to add to the dna event list.");
                resp.getWriter().print(msg.toString());
                return;
              }

              DNAEventListManager dnaEventListManager = new DNAEventListManager(
                  uctx);
              try
              {
                dnaEventListManager.addToDNAEventList(namespaceEventsToAdd, force);
              }
              catch (DNAEventListManagerException ex)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Failed to add namespace events... "+ex.getMessage());
                resp.getWriter().print(msg.toString());
                return;
              }
              

              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done adding list of namespace events to the dna event list.");
              resp.getWriter().print(msg.toString());
              break;
            }
            // c3/data/dnaevent/delete
            case delete:
            {
              ResponseMessage msg = null;

              String payload = ServletUtil.getPayload(req);
              if (payload == null || payload.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Request payload is missing. Expecting a list of namespace events to remove from the dna event list.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              List<String> namespaceEventsToRemove;
              try
              {
                namespaceEventsToRemove = jm.readAsObject(payload, List.class);
              }
              catch (UException exception)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Failed to parse payload... Expecting a list of namespace events to remove from the dna event list.");
                resp.getWriter().print(msg.toString());
                return;
              }

              DNAEventListManager dnaEventListManager = new DNAEventListManager(
                  uctx);
              try 
              {
                dnaEventListManager.removeFromDNAEventList(namespaceEventsToRemove);
              }
              catch (DNAEventListManagerException ex)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Failed to remove namespace events... "+ex.getMessage());
                resp.getWriter().print(msg.toString());
                return;
              }

              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done removing list of namespace events from the dna event list.");
              resp.getWriter().print(msg.toString());
              break;
            }
            default:
            {
              break;
            }
          }
        }
      }
    };
  }

}
