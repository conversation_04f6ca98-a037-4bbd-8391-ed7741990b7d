package com.z1.handler;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.commons.Utils;
import z1.core.utils.SessionUtils;
import z1.core.utils.TimeUtils;

/*
 * Servlet which provides REST apis related to session
 *
 */
public class SessionHandler implements CommandHandlerFactory
{

  private enum Command
  {
    backfill
  }

  /**
   * Returns a GET request handler.
   */
  @Override
  public CommandHandler get()
  {
    return null;
  }

  /**
   * Returns a POST request handler.
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");

        SessionHandler.Command command = SessionHandler.Command
            .valueOf(pathParts[0]);
        switch (command)
        {
          // c3/data/session/backfill
          case backfill:
          {
            ResponseMessage msg = new ResponseMessage(uctx,
                ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");

            String payload = ServletUtil.getPayload(req);
            if (payload == null || payload.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing or empty request payload.");
              resp.getWriter().print(msg.toString());
              return;
            }

            try
            {
              JsonMarshaller j = new JsonMarshaller();
              Map<String, Object> m = j.readAsMap(payload);
              String fromDate = (String) m.get("fromDate");
              String toDate = (String) m.get("toDate");

              if (!Utils.isNotNullEmptyOrNone(fromDate)
                  || !Utils.isNotNullEmptyOrNone(toDate))
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing params in payload.");
                resp.getWriter().print(msg.toString());
                return;
              }

              long startDate = new TimeUtils().parseTime(fromDate);
              long endDate = new TimeUtils().parseTime(toDate);

              SessionUtils.sessionEndForNamespace(uctx.getNamespace(),
                  startDate, endDate, true, false);

              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  String.format(
                      "Session back filling request submitted from %s to %s.",
                      fromDate, toDate));
            }
            catch (Exception e)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Failed to back fill sessions : " + e.getMessage());
            }
            resp.getWriter().print(msg.toString());
            return;
          }
        }
      }

    };
  }
}
