package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.util.JsonMarshaller;
import z1.audit.ArtifactAudit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

public class AuditHandler implements CommandHandlerFactory
{
  private static final String ARTIFACT_NAME = "artifactName";
  
  private enum GetCommand
  {
    item,
    type,
    owner,
    all
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Exception e)
        {
          return;
        }
        
//        String type = pathParts[1];
//        String id = null;
//        if (pathParts.length > 2)
//        {
//          id = pathParts[2];
//        }
        Result<ArtifactAudit> res = null;

        switch (command)
        {
         // c3/data/audit/item?type=<type-name>&id=<item-id> => Sends all audit trails for a given item ID
          case item:
          {
            String type = req.getParameter("type");
            String id = req.getParameter("id");
            if (id != null)
            {
              // Encode space to '+'.
              if (id.contains(" "))
              {
                id = URLEncoder.encode(id, "UTF-8");
              }

              res = ArtifactAudit.loadAllForItem(ctx, ArtifactAudit.ItemTypes.parse(type), id);
            }
            break;
          }
         
          // c3/data/audit/owner?ownerName=<owner-name> => Sends all audit trails for a given owner
          case owner:
          {
            String ownerName = req.getParameter("ownerName");
            res = ArtifactAudit.loadAllForOwner(ctx, ownerName);
            break;
          }
          
         // c3/data/audit/type?type=<type-name> => Sends all audit trails for a given item type
          case type:
          {
            String type = req.getParameter("type");
            res = ArtifactAudit.loadAllForType(ctx, ArtifactAudit.ItemTypes.parse(type));
            break;
          }
          // Sends all audit trails for a given date range
          // c3/data/audit/all?f=<from-date>&t=<to-date>
          case all:
          {
            String fromDate = req.getParameter("f");
            String toDate = req.getParameter("t");
            res = ArtifactAudit.loadAllForDateRange(ctx, fromDate, toDate);
            break;
          }
          default:
          {
            break;
          }
        }
        
        if (res == null)
        {
          resp.getWriter().print("[]");
          return;
        }

        List<Map<String, Object>> list = new java.util.ArrayList<>(20);
        for (ArtifactAudit a : res.getData())
        {
          Map<String, Object> map = new java.util.HashMap<>(10);
          a.populate(map);
          
          //for backwards compatibility to show old ArtifactAudit items
          if (!map.containsKey(ARTIFACT_NAME))
          {
            String itemName = a.getArtifactName();
            if (itemName != null)
            {
              map.put(ARTIFACT_NAME, itemName);
              list.add(map);
            }
          }
          else
          {
            list.add(map);
          }
        }

        if (list.isEmpty())
        {
          resp.getWriter().print("[]");
          return;
        }

        resp.getWriter().print(new JsonMarshaller().serialize(list));

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return null;
  }
}
