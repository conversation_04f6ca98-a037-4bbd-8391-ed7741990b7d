package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.core.utils.FileLoaderStats;
import z1.expression.ScriptObject;
import z1.expression.ScriptObject.ScriptType;
import z1.stats.DataServiceOperationalStat;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateUtils;

public class RemoteLinkHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  private enum GetCommand
  {
    all, stat
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/remotelinks/all => Sends all journeys
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                Type.remoteLinks);
            JsonMarshaller jm = new JsonMarshaller();
            for (CustomConfig cc : ccList)
            {
              if (cc == null) continue;
              String id = cc.getId();
              if (TemplateUtils.isTemplateConfig(id, Type.remoteLinks.name()))  
                continue;

              String pl = cc.getPayload();
              Map<String, Object> map = jm.readAsMap(pl);
              map.put("id", id);              

              cc.updateGeneralStat(map);
              
              ret.add(map);
            }

            resp.getWriter().print(new JsonMarshaller().serialize(ret));
            return;
          }
          // c3/data/remotelinks/stat
          case stat:
          {
            List<Map<String, Object>> res = new DataServiceOperationalStat(uctx).getStat();
            if (res != null) 
            {
              resp.getWriter().print(new JsonMarshaller().serialize(res));
            }
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/contextattribute/create => payload has the definition
          // returns <contextattribute-id>
          case create:
          {
            String cap = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (cap == null) return;

            JsonMarshaller jm = new JsonMarshaller();
            CustomConfig cc = CustomConfig.load(uctx, Type.remoteLinks, true);
            if (cc == null)
            {
              // create the context attribute object and save the payload
              Map<String, Object> map = jm.readAsMap(cap);
              String name = (String) map.get("_z1_name");
              // For data services, ID must be equal to their "name". The usage 
              // refers the data services by its name only.
              cc = CustomConfig.create(uctx, Type.remoteLinks, name);
              cc.setPayload(cap);
              cc.setName(name);
              cc.save();
              id = cc.getId();
            }
            Map<String, Object> map = new java.util.HashMap<>();
            map.put("id", id);
            resp.getWriter().print(jm.serializeMap(map));
            
            ArtifactAudit.newInstance(uctx, ItemTypes.dataservices, id, id,
                Operations.create).save();
            
            break;
          }
          // c3/data/remotelinks/update?id=<id>
          case update:
          {
            // update the existing conext attribute by passing the id            
            String id = req.getParameter("id");
            String cap = ServletUtil.getPayload(req);
            JsonMarshaller jm = new JsonMarshaller();
            CustomConfig cc = CustomConfig.load(uctx, id, Type.remoteLinks, true);
            if (cc != null)
            {
              cc.setPayload(cap);
              Map<String, Object> map = jm.readAsMap(cap);
              String name = (String) map.get("_z1_name");
              cc.setName(name);
              cc.save();
              
              ScriptObject.invalidateCache(uctx, ScriptType.dataService, id);
              ArtifactAudit.newInstance(uctx, ItemTypes.dataservices, id, name,
                  Operations.edit).save();
            }
            break;
          }
          
          // c3/data/remotelinks/delete?id=<id>
          case delete:
          {
            // delete the given context attribute
            String id = req.getParameter("id");
            req.setAttribute("id", id);
            req.setAttribute("type", CustomConfig.Type.remoteLinks.name());
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = CustomConfig.Type.remoteLinks.name();
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(uctx, subParts, req, resp);
            ArtifactAudit.newInstance(uctx, ItemTypes.dataservices, id, id,
                Operations.delete).save();
            break;
          }
        }
        App.notifyClearCache(uctx.getNamespace(), "remoteLinks");
      }
      
    };
  }

}
