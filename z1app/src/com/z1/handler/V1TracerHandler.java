package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ApiUtils;
import org.apache.http.HttpHeaders;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.SystemConfig;
import z1.c3.api.Commons;
import z1.channel.ChannelType;
import z1.commons.Const;
import z1.commons.DebugTrace;
import z1.commons.HandlerUtils;
import z1.core.Context;
import z1.core.Profile;
import z1.core.Profile.SubscriptionType;
import z1.core.Type.IndexType;
import z1.core.profile.ProfileService;
import z1.expression.ScriptObject;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

public class V1TracerHandler implements CommandHandlerFactory
{
  private enum PostCommand
  {
    event
  }
  // ............................................................
  // POST Implementation

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings({ "unchecked", "incomplete-switch" })
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/v1tracer/*
        if (pathParts.length == 0) return;
        String cStr = pathParts[0];
        PostCommand command;
        try
        {
          command = PostCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          command = PostCommand.event;
        }

        switch (command)
        {
          // c3/api/v1tracer/event
          // -----------------------------

          case event:
          {
            // The payload will have a list of maps encoded in JSON.
            // Each map must have a mandatory entry called "event" that holds
            // the event name. All other entries are event parameters.
            ULogger logger = ULogger.instance(ctx);
            if (logger.canLog())
            {
              ctx.getLogger(this.getClass()).log("Event received");
              String apiKey = (String) ctx.get(Commons.ReqFields.apiKey.name());
              ctx.getLogger(this.getClass())
                  .log("Event received for ApiKey = " + apiKey);
            }

            String payload = ServletUtil.getPayload(req);
            if (payload != null)
            {
              logger.info(
                  String.format("Event payload length = %d", payload.length()));
              if (logger.canLog())
              {
                logger.log("SDK event payload: " + payload);
              }
            }

            // check event payload size
            if (!HandlerUtils.isValidPayloadSize(payload))
            {
              logger.warning("Event payload size limit exceeded.");
              return;
            }

            // Load the list of events from the payload
            List<Map<String, Object>> inputEList = new JsonMarshaller()
                .readAsObject(payload, List.class);
            if ((inputEList == null) || (inputEList.isEmpty()))
            {
              logger.log(
                  "Event end point called with no payload for events. Ignoring.");
              return;
            }


            List<Map<String, Object>> eList = new java.util.ArrayList<>(inputEList);

            boolean blockEventIP = SystemConfig.getBooleanValue(ctx, SystemConfig.Z1_BLOCK_EVENT_IP, true);

            String ip = blockEventIP ? null : ApiUtils.getCustomerIpAddress(req);

            // Add user-agent as part of the event payload. Similar to ip
            // address it won't persist
            final String userAgent = req.getHeader(HttpHeaders.USER_AGENT);

            for (Map<String, Object> e : eList)
            {
              if (ip != null && !ip.trim().isEmpty())
              {
                e.put(z1.commons.Const.IP_ADDRESS, ip);
              }
              if (userAgent != null && !userAgent.trim().isEmpty())
              {
                e.put(Const.USER_AGENT, userAgent);
              }
            }

            String pId = req.getParameter("p");
            String source = req.getParameter("source");
            _fireForTraceOnly(ctx, pId, source, eList.get(0));

            Map<String, Object> map = new java.util.HashMap<>(10);
            map.put("status", "success");
            map.put("data", DebugTrace.get(ctx).messages());
            resp.getWriter().print(new JsonMarshaller().serialize(map));

            String name = eList.get(0).get("event").toString();
            if (name != null)
            {
              ArtifactAudit.newInstance(ctx, ItemTypes.eventTracer, name,
                  name, Operations.trace).save();
            }
            break;
          }
        }
      }
    };

  }

  // =========================================================================
  //
  // IMPLEMENTATIONS
  //
  // =========================================================================

  // ................................................................
  private void _fireForTraceOnly(UContext ctx, String pId, String source,
      Map<String, Object> item)
  {
    final String Z1_SYSTEM = "z1_system";
    Long startTime = Calendar.getInstance().getTimeInMillis();
    ctx.traceOn(true);

    String traceInfo = "===========< Session AI Event Trace >============"
        + "</br>| Executes as a test bot. "
        + "</br>| No push notification action can be fired. Please use <span style=\"background: #555;padding: 2px 4px;\">Push Simulator</span> to test push actions."
        + "</br>============================================";
    
    DebugTrace dt = DebugTrace.get(ctx);
    dt.addTraceInfo(traceInfo);

    String eventName = (String) item
        .get(z1.c3.api.Commons.ReqFields.event.name());
    if (eventName == null) return;
    item.put(z1.c3.api.Commons.ReqFields.event.name(), eventName.toLowerCase());

    // We'll massage the input as we can get a JSON data as value of a name
    // value sent
    // from test. But the json data will be stored as string here.
    JsonMarshaller jm = new JsonMarshaller();
    item.forEach((key, val) -> {
      if (!key.equalsIgnoreCase(z1.commons.Const.Z1_PROFILE))
      {
        String v = (String) val;
        if (v.startsWith("{"))
        {
          Map<String, Object> map = jm.readAsMap(v);
          item.put(key, map);
        }
        else if (v.startsWith("["))
        {
          @SuppressWarnings("unchecked")
          List<Map<String, Object>> list = jm.readAsObject(v, List.class);
          item.put(key, list);
        }
        else if (v.startsWith("'"))
        {
          v = v.replaceAll("'", "");
          item.put(key, v);
        }
      }
    });

    // Create the profile
    Context zCtx = Context.getInstance(ctx);
    String profileId = null;
    boolean isSystemEvent = false;
    Profile p = null;

    if (pId == null)
    {
      
      // Default case, with default trace user
      String custId = z1.commons.Const.TRACE_CONTEXT;
      ProfileService ps = new ProfileService(zCtx);
      p = ps.findAProfile(custId, IndexType.CUSTOMERID);
      if (p == null)
      {
        // This is a 1st time user, we will create a profile
        p = Profile.instance(zCtx);
        // We will update the profile irrespective. this is IMPORTANT.
        // I'll explain
        // the reason someday when I have some time to relax...
        p.addProperty(Profile.ID, custId);
        p.addHandle(custId, IndexType.CUSTOMERID);
        p.add2Namespace(SubscriptionType.asProfile); // profile to account
        p.flush();
      }
      profileId = p.getKeyValue();
    }
    else if (!Z1_SYSTEM.equalsIgnoreCase(pId))
    {
      // When we have a profile id
      profileId = pId;
      // Special case where we load from cache cache even though
      // request is from master
      p = Profile.instance(zCtx, profileId, true);
      // set the wstoekn and origin id so that we can show the
      // action on the app as well
      p.loadContextForAction();

      // Reset the incoming z1_profile with the user's last known device info
      String di = p.getProperty(Profile.Fields.device.value);
      @SuppressWarnings("unchecked")
      Map<String, Object> z1Profile = (Map<String, Object>) item
          .get(z1.commons.Const.Z1_PROFILE);
      if (di != null && z1Profile != null)
      {
        dt.addTraceInfo(
            "<div style='font-size:12px;border-left:4px solid #735836;padding:10px;margin: 10px 0;color: #ccc;'>"
                + "You can view the actions (if any) live on a device. Please start the application for this user before you test.</div>");

        Map<String, Object> m = new JsonMarshaller().readAsMap(di);
        if (!m.isEmpty())
        {
          z1Profile.putAll(m);
        }
      }
      else
      {
        dt.addTraceInfo(
            "<div style='font-size:12px;border-left:4px solid #735836;padding:10px;margin: 10px 0;color: #ccc;'>"
                + "The system couldn't find any device used by this profile yet.</div>");

      }

    }
    else
    {
      // It's a system event
      if (source != null && !source.isEmpty())
      {
        dt.addTraceInfo(
            "<div style='border: thin dashed #888;padding: 20px;'>NOTE: Event source is applicable to profile event only. Selected source will be ignored for this test.</div>");
      }
      isSystemEvent = true;
      dt.addTraceInfo(
          "<div style='border: thin dashed #888;padding: 20px;'>NOTE: A system event can execute for all users, if configured. You will not see the progress in this console.</div>");
    }

    if (p != null)
    {
      ctx.put(z1.commons.Const.P_PROFILE, p);
    }

    List<Map<String, Object>> eventList = new java.util.ArrayList<>();
    eventList.add(item);
    z1.EventHandler eh = new z1.EventHandler(ctx);
    if (!isSystemEvent && source != null && !source.isEmpty())
    {
      ChannelType ct = ChannelType.forName(source);
      ct.setCustomChannel(true);
      eh.setIncomingChannel(ct);
    }
    eh.handleInProcess()
        .handleAsSystemEvents(isSystemEvent).setProfileId(profileId);
    eh.handle(eventList);

    // if (!isSystemEvent)
    // {
    // item.put(MobileEventSignal.Field.profileId.name(), profileId);
    //
    // // Fire the event
    // DebugTrace.get(ctx).addTraceInfo("POST request received for event: "
    // + eventName);
    // PipelineExecutor pe = PipelineExecutor.createActionExecutor(
    // Const.MOBILE_ACTION_NAME, ctx);
    // pe.setParams(item);
    // pe.setAsyncExec(false);
    // pe.run();
    // }

    Long endTime = Calendar.getInstance().getTimeInMillis();

    dt.addTraceInfo(String.format(
        "<div style='padding: 20px 4px;color: #c6f7c6;font-weight: bold;'>"
            + "<span class='ti-check'></span><span style='margin-left: 10px;'>Event Tracer has completed execution. [%d ms]"
            + "</span></div>",
        (endTime - startTime)));
    
    dt.addTraceInfo("=========================");
    dt.addTraceInfo("API Call Report");
    dt.addTraceInfo("-------------------------");
    dt.addTraceInfo(ScriptObject.getApiCallReport(ctx));
  }

  @Override
  public CommandHandler get()
  {
    return null;
  }

}