package com.z1.handler;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Utils;
import z1.css.Css;
import z1.css.CssLoader;
import z1.css.PlugableCSS.Mode;


public class ContentCssHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    id
  }

  // Supported post commands
  private enum PostCommand
  {
    id,
    delete
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          return;
        }

        switch (command)
        {
          // c3/data/contentcss2/all
          case all:
          {
            List<Map<String, Object>> out = new ArrayList<>();
            List<CustomConfig> contentCssEntries = CustomConfig.forceLoadAll(ctx, Type.contentcss);
            for (CustomConfig cc : contentCssEntries)
            {
              if (cc == null) continue;
              boolean empty = false;
              String pl = cc.getPayload();
              if (pl == null || pl.isEmpty())
              {
                empty = true;
              }
              Map<String, Object> entry = new HashMap<>();
              entry.put("id", cc.getId());
              entry.put("empty", String.valueOf(empty));
              out.add(entry);
            }
            String payload = new JsonMarshaller().serialize(out);
            resp.getWriter().print(payload);
            return;
          }
          // c3/data/contentcss2/id?id=<id>
          case id:
          {
            String id = req.getParameter("id");
            
            String payload = "";
            Css css = new Css();
            css.id = id;
            
            CustomConfig cc = CustomConfig.load(ctx, id, Type.contentcss, true);
            if (cc == null)
            {
              //<os>|<module>|<css/html>|<deviceType>|<subCategory> 
              String[] tokens = id.split("\\|");
              String os = tokens[0]; 
              String module = tokens[1];
              String deviceType = tokens[3];
              String subCategory = tokens[4];
              payload = CssLoader.loadContentBoxCss(ctx, false, module, os, deviceType, subCategory);
            }
            else
            {
              payload = cc.getPayload();
              css.isDefault = false; //custom css exists not default           
            }          
            
            if (payload == null) return;
            css.payload = payload;
            css.mode = Mode.advanced.name(); //non templatized css
            resp.getWriter().print(new JsonMarshaller().serialize(css));
            
            return;
          }

          default:
          {
            return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command;
        try
        {
          command = PostCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          return;
        }
        
        switch (command)
        {
          // c3/data/contentcss2/id?id=<id>
          case id:
          {
            String id = req.getParameter("id");           
            
            CustomConfig cc = CustomConfig.load(ctx, id, Type.contentcss, true);
            Css css = new Css();
            css.id = id;
                                   
            if (cc == null)
            {
              cc = CustomConfig.create(ctx, Type.contentcss, id);
            }
            
            String payload = ServletUtil.getPayload(req);
            JsonMarshaller j = new JsonMarshaller();           
            Map<String, Object> vals = j.readAsMap(payload);
            payload = (String)vals.get("payload");
                    
            cc.setPayload(payload);
            cc.save();             
            
            return;
          }
          
          // c3/data/contentcss2/delete?id=<id>
          case delete:
          {
            String id = req.getParameter("id");
            
            CustomConfig.delete(ctx, id, Type.contentcss);
                      
            ResponseMessage rm = new ResponseMessage(ctx, 
                ResponseMessage.Status.success, 
                ResponseMessage.Type.resourseDeletedSuccessfully);
            resp.getWriter().print(rm.toString());
            return;
          }
        }
      }
    };

  }
}
