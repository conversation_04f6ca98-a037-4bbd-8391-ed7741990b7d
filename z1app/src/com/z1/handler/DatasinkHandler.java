package com.z1.handler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.commons.Const;
import z1.commons.Utils;

public class DatasinkHandler implements CommandHandlerFactory
{
  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  private enum GetCommand
  {
    all,
    id
  }

  private enum Fields
  {
    id,
    name,
    description,
    payload
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/datasink/all => Sends all entities
          case all:
          {
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                Type.datasink);
            List<Map<String, Object>> ret = ccList.stream()
                .filter(Objects::nonNull).map(cc -> {
                  Map<String, Object> map = new HashMap<>();
                  map.put(Fields.id.name(), cc.getId());
                  map.put(Fields.name.name(), cc.getName());
                  map.put(Fields.description.name(), cc.getDescription());
                  return map;
                }).collect(Collectors.toList());

            resp.getWriter().print(Const.jsonMarshaller.serialize(ret));
            return;
          }
          // c3/data/datasink/id?id=<id>
          case id:
          {
            // This must be a request for a specific datasink.
            String id = req.getParameter("id");
            CustomConfig cc = CustomConfig.load(uctx, id, Type.datasink, true);
            if (cc == null) return;
            String pl = cc.getPayload();
            resp.getWriter().print(pl);
            break;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        ULogger logger = uctx.getLogger(getClass());
        switch (command)
        {
          // c3/data/datasink/create => payload has the definition
          // returns <datasink-id>
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            if (payload == null) return;

            // Get the name from the payload
            JsonMarshaller jm = Const.jsonMarshaller;
            Map<String, Object> mPl = jm.readAsMap(payload);
            String name = (String) mPl.get(Fields.name.name());
            // ID is the name of the data sink
            if (name == null) return;
            String id = name;
            String description = (String) mPl.get(Fields.description.name());

            CustomConfig cc = CustomConfig.load(uctx, name, Type.datasink,
                true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, Type.datasink, name);
              cc.setPayload(payload);
              cc.setName(name);
              cc.setDescription(description);
              cc.save();
              id = cc.getId();
            }

            Map<String, Object> map = new java.util.HashMap<>();
            map.put(Fields.id.name(), id);
            resp.getWriter().print(jm.serializeMap(map));

            ArtifactAudit.newInstance(uctx, ItemTypes.datasink, id, name,
                Operations.create).save();

            if (logger.canLog()) logger.log(String.format(
                "Datasink with name \"%s\" created with an ID = \"%s\"", name,
                id));
            return;
          }
          // c3/data/datasink/update?id=<id>
          case update:
          {
            // update the existing datasink by passing the id
            String id = req.getParameter(Fields.id.name());
            String payload = ServletUtil.getPayload(req);
            if (id == null || payload == null) return;

            CustomConfig cc = CustomConfig.load(uctx, id, Type.datasink, true);
            if (cc == null) throw new IllegalArgumentException();

            // Get the name from the payload
            Map<String, Object> mPl = Const.jsonMarshaller.readAsMap(payload);
            String description = (String) mPl.get(Fields.description.name());
            String name = (String) mPl.get(Fields.name.name());
            
            if (!id.equals(name))
            {
              name = id;
              // Make sure that ID is not changed
              mPl.put(Fields.name.name(), id);
              payload = Const.jsonMarshaller.serialize(mPl);
            }
            
            cc.setPayload(payload);
            cc.setName(name);
            cc.setDescription(description);
            cc.save();

            ArtifactAudit.newInstance(uctx, ItemTypes.datasink, id, name,
                Operations.edit).save();

            if (logger.canLog())
              logger.log(String.format("Datasink \"%s\" updated", id));
            return;
          }

          // c3/data/datasink/delete?id=<id>
          case delete:
          {
            String id = req.getParameter(Fields.id.name());
            CustomConfig.delete(uctx, id, Type.datasink);
            if (logger.canLog())
              logger.log(String.format("Datasink \"%s\" deleted", id));
            return;
          }

        }
      }
    };
  }

}
