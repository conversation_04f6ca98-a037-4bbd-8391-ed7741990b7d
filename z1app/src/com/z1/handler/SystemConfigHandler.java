package com.z1.handler;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.Utils;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.SystemConfig;

public class SystemConfigHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    etesec,
    timezone
  }
  
  private enum PostCommand
  {
    save, 
    etesec
  }
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        try
        {
          command = GetCommand.valueOf(cStr);
        }
        catch (Throwable e)
        {
          command = GetCommand.all;
        }
        //if (pathParts.length > 0) return;

        switch(command)
        {
          case all:
          {
            // c3/data/systemconfig/all
            String sysConfigSchema = Utils.loadFileResource("META-INF/systemconfig.json");
            String sc = SystemConfig.getAll(uctx, sysConfigSchema);
            resp.getWriter().print(sc);
            
            return;
          }
          // c3/data/systemconfig/etesec
          case etesec:
          {
            Map<String, Object> m = new HashMap<String,Object>();
            String ente = SystemConfig.getBooleanValue(uctx, "z1.security.end2EndEncryption", false).toString();
            m.put("ete", ente);
            JsonMarshaller jm = new JsonMarshaller();
            resp.getWriter().print(jm.serializeMap(m));
            return;
          }
          // c3/data/systemconfig/timezone
          case timezone:
          {
            Map<String, Object> m = new HashMap<String, Object>();
            m.put("timezone", SystemConfig.getNsTimeZone(uctx));
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            return;
          }
          default:
          {
            return;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        //if (pathParts.length > 0) return;
        
        switch(command)
        {
          // c3/data/systemconfig/save
          case save:
          {
            Map<String, Object> map = new HashMap<>();
            String payload = ServletUtil.getPayload(req);
            if (payload != null && !payload.isEmpty()) 
            {
              JsonMarshaller m = new JsonMarshaller();
              map = m.readAsMap(payload);
              if (map.containsKey("android.tablet.ttl.value"))
              {

                ArtifactAudit.newInstance(uctx,
                    ItemTypes.actionMappingAndroidTablet,
                    map.get("android.tablet.ttl.value").toString() + " minutes",
                    map.get("android.tablet.ttl.value").toString() + " minutes",
                    Operations.changeTagTTL).save();
              } else if (map.containsKey("android.phone.ttl.value"))
              {

                ArtifactAudit
                    .newInstance(uctx, ItemTypes.actionMappingAndroidPhone,
                        map.get("android.phone.ttl.value").toString() + " minutes",
                        map.get("android.phone.ttl.value").toString() + " minutes",
                        Operations.changeTagTTL)
                    .save();
              } else if (map.containsKey("ios.tablet.ttl.value"))
              {

                ArtifactAudit
                    .newInstance(uctx, ItemTypes.actionMappingIosTablet,
                        map.get("ios.tablet.ttl.value").toString() + " minutes",
                        map.get("ios.tablet.ttl.value").toString() + " minutes",
                        Operations.changeTagTTL)
                    .save();
              } else if (map.containsKey("ios.phone.ttl.value"))
              {

                ArtifactAudit
                    .newInstance(uctx, ItemTypes.actionMappingIosPhone,
                        map.get("ios.phone.ttl.value").toString() + " minutes",
                        map.get("ios.phone.ttl.value").toString() + " minutes",
                        Operations.changeTagTTL)
                    .save();
              }
            }
            SystemConfig.saveAll(uctx, new JsonMarshaller().serializeMap(map));
            resp.getWriter()
                .print(new ResponseMessage(uctx, ResponseMessage.Status.success)
                    .toString());
            return;
          }
          // c3/data/systemconfig/etesec
          case etesec:
          {
            Map<String, Object> map = new HashMap<>();
            String payload = ServletUtil.getPayload(req);
            if (payload != null && !payload.isEmpty()) 
            {
              JsonMarshaller m = new JsonMarshaller();
              map = m.readAsMap(payload);
              SystemConfig.putValue(uctx, "z1.security.end2EndEncryption", (String)map.get("z1etesec"));
            }
          }
          default:
          {
            return;
          }
        }
        
      }

    };
  }

}
