package com.z1.handler;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.account.Z1Account;
import z1.account.Z1AccountProperties;
import z1.account.Z1AccountService;
import z1.sso.saml.SamlProperties;

public class SSOHandler implements CommandHandlerFactory
{
  private static JsonMarshaller jm = new JsonMarshaller();

  private static final String SSO = "sso";
  private static final String SSO_PROPS = "ssoprops";

  public enum GetCommand
  {
    config
  }

  public enum PostCommand
  {
    save
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length > 0)
        {
          String cStr = pathParts[0];
          GetCommand path = GetCommand.valueOf(cStr);
          switch (path)
          {
            // c3/data/sso/config
            case config:
              Z1AccountService as = new Z1AccountService(uctx);
              Z1Account account = as.getAccount(uctx.getNamespace());
              if (account != null)
              {
                Map<String, Object> ssoConfigMap = new HashMap<>();
                Z1AccountProperties accountProperties = account.getProperties();
                ssoConfigMap.put(SSO, accountProperties.getIsSsoEnabled());
                Map<String, Object> samlProps = accountProperties
                    .getSamlProperties().getMap();
                // Currently the UI treats this field as a string field
                // containing a single value.
                // When the UI changes this will need to change.
                if (samlProps.containsKey(
                    SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY))
                {
                  @SuppressWarnings("unchecked")
                  List<String> signingCerts = ((List<String>) samlProps
                      .get(SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY));
                  if (signingCerts != null && signingCerts.size() > 0)
                  {
                    samlProps.put(
                        SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY,
                        signingCerts.get(0));
                  }
                  else
                  {
                    samlProps.put(
                        SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY, "");
                  }
                }
                ssoConfigMap.put(SSO_PROPS,
                    accountProperties.getSamlProperties().getMap());
                String ssoConfig = jm.serialize(ssoConfigMap);
                resp.getWriter().print(ssoConfig);
              }
              break;
            default:
              throw new Exception("Unknown REST command");
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        final String cStr = pathParts[0];
        final PostCommand path = PostCommand.valueOf(cStr);

        switch (path)
        {
          // c3/data/sso/save
          case save:
          {
            final String payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0))
            {
              _respondRequestFailMessage(uctx, resp, "No payload provided in request.");
              return;
            }

            Map<String, Object> ssoConfig = null;
            try
            {
              // Parse request payload
              ssoConfig = jm.readAsMap(payload);
            }
            catch (Exception ex)
            {
              _respondProcessingFailMessage(uctx, resp,
                  "An exception was thrown parsing JSON request body.");
              return;
            }

            Z1AccountService as = new Z1AccountService(uctx);
            Z1Account account = as.getAccount(uctx.getNamespace());
            if (account != null)
            {
              Boolean enabled = (Boolean) ssoConfig.get(SSO);
              if (enabled == null)
              {
                _respondProcessingFailMessage(uctx, resp,
                    String.format("Missing required property [%s]", SSO));
                break;
              }

              Map<String, Object> samlProperties = (Map<String, Object>) ssoConfig
                  .get(SSO_PROPS);
              if (enabled && samlProperties == null)
              {
                _respondProcessingFailMessage(uctx, resp,
                    String.format("Missing required property [%s]", SSO_PROPS));
              }

              // Currently the UI treats this field as a string field containing
              // a single value.
              // When the UI changes this will need to change.
              if (samlProperties.containsKey(
                  SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY))
              {
                List<String> signingCerts = new ArrayList<>(1);
                String signingCert = (String) samlProperties
                    .get(SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY);
                if (!signingCert.isEmpty())
                {
                  signingCerts.add(signingCert);
                }
                samlProperties.put(
                    SamlProperties.IDP_SIGNING_X509CERTS_PROPERTY_KEY,
                    signingCerts);
              }

              Z1AccountProperties accountProperties = account.getProperties();
              accountProperties.setIsSsoEnabled(enabled);
              accountProperties.setSamlProperties(samlProperties);
              as.updateAccount(account);
              _respondSuccessMessage(uctx, resp);
            }
            break;
          }

          default:
            break;
        }
      }
    };
  }

  private void _respondProcessingFailMessage(UContext uctx, final HttpServletResponse resp, final String message)
      throws IOException
  {
    ResponseMessage rmsg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
        ResponseMessage.Type.requestProcessingFailed, message);
    resp.getWriter().print(rmsg.toString());
  }

  private void _respondRequestFailMessage(UContext uctx, final HttpServletResponse resp, final String message)
      throws IOException
  {
    ResponseMessage rmsg = new ResponseMessage(uctx, ResponseMessage.Status.fail, 
            ResponseMessage.Type.invalidParams, message);
    resp.getWriter().print(rmsg.toString());
  }

  private void _respondSuccessMessage(UContext uctx, final HttpServletResponse resp)
      throws IOException
  {
    resp.getWriter().print(new ResponseMessage(uctx, ResponseMessage.Status.success,
        ResponseMessage.Type.requestProcessingDone).toString());
  }
}
