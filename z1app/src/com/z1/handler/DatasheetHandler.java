package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.system.User;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.workspace.WorkspaceService;
import udichi.gateway.defservice.DefinitionItem;
import z1.commons.Utils;

public class DatasheetHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    update,
    create
  }

  private enum GetCommand
  {
    all // All datasheet definitions
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/datasheet/all?wid=<workspace-id>
          case all:
          {
            String wId = req.getParameter("wid");
            if (wId == null)
            {
              resp.getWriter().print("[]");
              return;
            }
            
            Result<DefinitionItem> items = DefinitionItem.getAllItems(uctx, ArtifactType.datasheet);
            
            List<Map<String, Object>> dsList = new java.util.ArrayList<Map<String, Object>>(10);  
            
            for (DefinitionItem di : items.getData())
            {
              Map<String, Object> ds = new java.util.HashMap<String, Object>();
              
              String id = (String) di.getValues().get("id");
              String prefix = wId.concat("/");
              // remove z1default/ or any other wId from the id and assign it as id
              if (id.startsWith(prefix))
              {
                id = id.substring(prefix.length());
              }
              Long lastUpdatedTime = (Long) di.getValues().get("lastUpdated");
              String lastUpdatedBy = (String) di.getValues().get("lastUpdatedBy");
              String description = (String) di.getValues().get("description");
              String state = (String) di.getValues().get("state");
              
              ds.put("id", id);
              ds.put("state", state);
              ds.put("description", description);
              ds.put("lastUpdatedBy", lastUpdatedBy);
              ds.put("lastUpdatedTime", lastUpdatedTime);
              
              dsList.add(ds);
            }
            if (!dsList.isEmpty())
            {
              String payload = new JsonMarshaller().serialize(dsList);
              resp.getWriter().print(payload);
            }
            else
            {
              resp.getWriter().print("[]");
            }
            
            break;
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/datasheet/update?id=<id> where id = workspace + "/" + datasheet name (ex: z1default/MyChart)
          case update:
          {
            String payload = ServletUtil.getPayload(req);
            String id = req.getParameter("id");
            
            ResponseMessage msg = null;

            if (payload == null || payload.isEmpty() || id == null || id.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, null,
                  "Request payload or id is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }

            DefinitionItem di = DefinitionItem.getInstance(uctx,
                ArtifactType.datasheet, id);
            if (di == null)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, null,
                  "Id is not found.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            Map<String, Object> data = new JsonMarshaller().readAsMap(payload);
            String desc = "";
            if (data.get(DefinitionItem.Fields.description.name()) != null)
            {
              desc = (String) data
                  .get(DefinitionItem.Fields.description.name());
            }
            di.getValues().put(DefinitionItem.Fields.description.name(),
                  desc);
            di.getValues().put(DefinitionItem.Fields.payload.name(), payload);
            di.save();
            
            // Store the data sheet ref/description to the workspace
            String[] parts = id.split("/");
            WorkspaceService ws = new WorkspaceService(uctx);
            String wId = parts[0];
            String dsId = parts[1];              
            DefinitionItem wObj = ws.getWorkspace(wId);            
            ws.saveDatasheet(wObj, dsId, desc);

            App.notifyClearCache(uctx.getNamespace(), ArtifactType.datasheet.name());

            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                null, "Upgraded datasheet successfully for " + id);
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/datasheet/create?id=<id> where id = workspace + "/" + datasheet name (ex: z1default/MyChart)
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            String did = req.getParameter("id");

            ResponseMessage msg = null;
            if (payload == null || payload.isEmpty() || did == null || did.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, null,
                  "Request payload or id is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String[] parts = did.split("/");
            if (parts.length != 2)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Invalid datasheet id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            DefinitionItem di = DefinitionItem.getInstance(uctx,
                ArtifactType.datasheet, did);
            if (di != null)
            {
              String name = parts.length > 1 ? parts[1] : did;
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, 
                  ResponseMessage.Type.chartCreateFailed,
                  "A chart with the name '" + name + "' already exists.");
              resp.getWriter().print(msg.toString());
              return;
            }

            Map<String, Object> data = new JsonMarshaller().readAsMap(payload);
            di = DefinitionItem.newInstance(uctx, did,
                ArtifactType.datasheet);
            String desc = (String) data
                .get(DefinitionItem.Fields.description.name());
            if (desc != null)
              di.getValues().put(DefinitionItem.Fields.description.name(),
                  desc);

            User user = uctx.getUser();
            if (user != null)
              di.getValues().put(DefinitionItem.Fields.owner.name(),
                  user.getId());
            di.getValues().put(DefinitionItem.Fields.payload.name(), payload);
            di.save();

            // Now store the data sheet ref to the workspace
            WorkspaceService ws = new WorkspaceService(uctx);
            String wId = parts[0];
            String id = parts[1];
            DefinitionItem wObj = ws.getWorkspace(wId);
            ws.saveDatasheet(wObj, id, desc);
            App.notifyClearCache(uctx.getNamespace(), ArtifactType.datasheet.name());

            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                null, "Create datasheet successfully for " + id);
            resp.getWriter().print(msg.toString());
            break;
          }

          default:
            break;
        }
      }

    };
  }

  ///////////////////////////

}
