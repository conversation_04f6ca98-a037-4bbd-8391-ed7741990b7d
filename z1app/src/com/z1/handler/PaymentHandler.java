package com.z1.handler;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.payments.BraintreeProcessor;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.account.Z1Account;
import z1.account.Z1AccountService;
import z1.accountcontext.AccountContextWrapper;
import z1.accountcontext.def.AccountContextType;
import z1.core.Context;

/*
 * Servlet which provides REST apis related to payment and billings
 * 
 */
public class PaymentHandler implements CommandHandlerFactory
{
  private enum Command
  {
    //customer
    createCustomer,    
    validateCustomer,
       
    //3rd party
    createSubscription, // 
    updateSubscription, //
    deleteSubscription, //
    validateCreditCard,
    
    //system
    clientToken,
    nonce,
    
    paymentPlan,
    create,
    update,
    delete
    
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");
        Command type = Command.valueOf(pathParts[0]);

        switch (type)
        {   
            case createCustomer:
            {
              //String customerId = req.getParameter("createCustomer");
              
              Context ctx = Context.getInstance(uctx);              
             
            }
            
            // c3/data/payment/clientToken            
            case clientToken:
            {
            	BraintreeProcessor btp = new BraintreeProcessor();
            	String token = btp.createClientToken();
                resp.getWriter().print("{\"token\":\""+token+"\"}");
                //String retPayload = new JsonMarshaller().serialize(token);
                //resp.getWriter().print(retPayload);
                return;
            }

        }

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");

        UContext uCtx = UContext.getInstance(req);
        Context ctx = Context.getInstance(uCtx);

        Command command = Command.valueOf(pathParts[0]);
        switch (command)
        {
          // A client (customer client side will always initiate a chat) connects to
          // c3/data/payment/nonce 
	        case nonce:
	        {
	            String payload = ServletUtil.getPayload(req);
	            JsonMarshaller j = new JsonMarshaller();
	            Map<String, Object> m = j.readAsMap(payload);
	            AccountContextWrapper acw = AccountContextWrapper.load(uctx);
	            AccountContextType act =  acw.getDef();

	            String nonce = (String)m.get("nonce");
	            
	            //z1.accountcontext.def.CredentialsDef cd = act.getAuthCredentials();
	            //cd.setAccessToken((String)m.get("accessToken"));
	            //cd.setExpiresIn((Integer)m.get("expiresIn"));
	            //act.setAuthCredentials(cd);
	            //acw.setPayload(j.serialize(act));
	            //acw.save();
	            BraintreeProcessor.createCustomerwithCreditCardInfo(nonce);
	            Z1AccountService zas = new Z1AccountService(uCtx);
	            Z1Account za = zas.getAccount(uCtx.getNamespace());
	            JsonMarshaller js = new JsonMarshaller();
	            String acctPayload = za.getPayload();
	            if(acctPayload == null)
	            {
	              // return some error message.
	            }  
	            
	            //Map<String, Object> map = js.readAsMap(acctPayload);
	            
	            /*String paymentMethodToken ="fbqkwb";//map.get("paymentMethodToken");
	            String planId = "test_plan_1";//map.get("planId");
	            BraintreeProcessor.createSubscription(planId, paymentMethodToken);*/
	        
            	uctx.getLogger(this.getClass()).log("nonce = "+nonce);            	
                return;	        	
	        }
        }
      }

    };
  }
  
 

}
