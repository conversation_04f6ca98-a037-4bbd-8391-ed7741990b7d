package com.z1.handler;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.actions.TestPushAction;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.commons.Utils;
import z1.core.Type.IndexType;

public class TestPushHandler implements CommandHandlerFactory
{
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {

      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        // c3/data/testpush?customerId=<customer-id>&deviceId=<device-id>
        List<String> results = new ArrayList<>();
        String payload = ServletUtil.getPayload(req);

        String customerId = req.getParameter("customerId");
        String deviceId = req.getParameter("deviceId");
        if (customerId != null && !customerId.isEmpty())
        {
          results = new TestPushAction(ctx).execute(customerId,
              IndexType.CUSTOMERID, payload);
          ArtifactAudit.newInstance(ctx, ItemTypes.testPush, customerId,
              customerId, Operations.testPush).save();
        }
        else if (deviceId != null && !deviceId.isEmpty())
        {
          results = new TestPushAction(ctx).execute(deviceId, IndexType.DEVICE,
              payload);
          ArtifactAudit.newInstance(ctx, ItemTypes.testPush, deviceId, deviceId,
              Operations.testPush).save();
        }
        else
        {
          results.add("e.invalid_input");
        }
        String json = new JsonMarshaller().serialize(results);
        resp.getWriter().print(json);
      }
    };
  }
}