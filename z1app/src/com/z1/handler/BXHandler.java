package com.z1.handler;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.util.JsonMarshaller;
import udichi.core.workspace.WorkspaceService;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.template.ModuleVisitor;
import z1.template.ModuleVisitor.VisitingOperation;
import z1.template.TemplateArtifact.Subtype;
import z1.template.TemplateArtifact;
import z1.template.TemplateUtils;

public class BXHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    modRefs,
    categoryAndShared,
    wsAll, // only for dashboard and datasheet 
    listOOTBExp;
  }

  public enum PostCommand
  {
    create,
    all,
    allInstances,
    unpublishModule,
    delete;   
  }

  /**
   * 
   */
  public BXHandler()
  {
    // TODO Auto-generated constructor stub
  }

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandlerFactory#get()
   */
  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        if (pathParts.length > 1)
        {
          // Create an array to pass for the relevant commands
          String[] subParts = new String[pathParts.length - 1];
          int j = 0;
          for (int i = 1; i < pathParts.length; i++, j++)
          {
            subParts[j] = pathParts[i];
          }
          cStr = pathParts[1];
        }

        GetCommand command = GetCommand.valueOf(cStr);
        String subtype = pathParts[0];
        
        // c3/data/bx/<channel|entity|segment>/modRefs?id=<id>
        switch (command)
        {
          case modRefs:
          {
            String id = req.getParameter("id");
            if (subtype.equals(Subtype.dashboard.name())
                || subtype.equals(Subtype.datasheet.name()))
            {
              String[] parts = id.split("/");
              if (parts.length < 2)
              {
                ResponseMessage msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Invalid dashboard/datasheet id in request.");
                resp.getWriter().print(msg.toString());
                return;
              }
              id = parts[1];
            }
            
            Map<String, Object> ret = new HashMap<>();
            Artifact art = new Artifact();
            art.setId(id);
            
            if (subtype.equals(Type.actionMappingAndroidPhone.name()) ||
                subtype.equals(Type.actionMappingAndroidTablet.name()) ||
                subtype.equals(Type.actionMappingHtml5Desktop.name()) ||
                subtype.equals(Type.actionMappingHtml5Phone.name()) ||
                subtype.equals(Type.actionMappingHtml5Tablet.name()) ||
                subtype.equals(Type.actionMappingIosPhone.name()) ||
                subtype.equals(Type.actionMappingIosTablet.name()))
            {            
              art.setSubtype(subtype);
            }
            
            TemplateArtifact artifact = new TemplateArtifact(ctx, null, art,
                null, null, subtype, null);
            ModuleVisitor mv = new ModuleVisitor(ctx, null,
                VisitingOperation.retrieveDefItem);
            mv.visit(artifact);
            if (artifact.getDefItem() != null)
            {
              Object payload2 = artifact.getDefItem().getValues()
                  .get("payload2");
              String pl2Str = (payload2 != null
                  && !payload2.toString().isEmpty()) ? payload2.toString()
                      : null;
              ret = TemplateUtils.getModuleConfigInstanceReferences(ctx,
                  pl2Str);
            }
            resp.getWriter().print(new JsonMarshaller().serialize(ret));

            break;
          }
          // c3/data/bx/<streamQuery|cubeQuery|query|dashboard|datasheet|cube>/categoryAndShared?id=<id>
         case categoryAndShared:
          {
            if (subtype == null || subtype.isEmpty() ||
                (!subtype.equals(CustomConfig.Type.streamQuery.name()) &&
                 !subtype.equals(CustomConfig.Type.cubeQuery.name()) &&
                 !subtype.equals(CustomConfig.Type.query.name()) &&
                 !subtype.equals(Subtype.dashboard.name()) &&
                 !subtype.equals(Subtype.datasheet.name()) &&
                 !subtype.equals(Subtype.cube.name()))) return;
           
            String id = req.getParameter("id");
            if (id.startsWith("udc.system.core")) return;

            Artifact art = new Artifact();
            art.setId(id);
            TemplateArtifact artifact = new TemplateArtifact(ctx, null, art,
                null, null, subtype, null);
            ModuleVisitor mv = new ModuleVisitor(ctx, null,
                VisitingOperation.retrieveDefItem);
            mv.visit(artifact);
            if (artifact.getDefItem() != null)
            {
              Object payload2 = artifact.getDefItem().getValues()
                  .get("payload2");
              String pl2Str = (payload2 != null
                  && !payload2.toString().isEmpty()) ? payload2.toString(): null;
              
              if (pl2Str != null)
              {
                JsonMarshaller jm = new JsonMarshaller();
                Map<String, Object> pl2Map = jm.readAsMap(pl2Str);
                Map<String, Object> ret = new HashMap<>();
                ret.put("category", pl2Map.get("category"));
                ret.put("sharedDisplay", pl2Map.get("sharedDisplay"));
                resp.getWriter().print(jm.serialize(ret));
              }
            }
            break; 
          }
          
         // c3/data/bx/<dashboard|datasheet>/wsAll?id=<id>
         case wsAll:
         {
           if (subtype == null || subtype.isEmpty() ||
                !subtype.equals(Subtype.dashboard.name()) &&
                !subtype.equals(Subtype.datasheet.name())) return;

           String wid = req.getParameter("wid");
           WorkspaceService ws = new WorkspaceService(ctx);
           DefinitionItem wObj = ws.getWorkspace(wid);
           Map<String, Object> dsMap = new java.util.HashMap<String, Object>();
           if(subtype.equals(Subtype.dashboard.name()))
           {
             dsMap = ws.getAllDashboards(wObj);
           }
           else if(subtype.equals(Subtype.datasheet.name()))
           {
             dsMap = ws.getAllDataSheets(wObj);
           }

           List<Map<String, Object>> dsList = new java.util.ArrayList<Map<String, Object>>(10);        
           JsonMarshaller jm = new JsonMarshaller();
           for (Map.Entry<String, Object> entry : dsMap.entrySet())
           {
             String id = entry.getKey();
             if (id.startsWith("udc.system.core")) continue;

             String desc = null;
             String state = DefinitionItem.State.draft.name();
             String category = null;
             Object sharedDisplay = null;

             Map<String, Object> ds = new java.util.HashMap<String, Object>();
             Map<String, Object> info = (Map<String, Object>) entry.getValue();
             if (info != null)
             {
               desc = (String) info.get(DefinitionItem.Fields.description.name());
               state = (String) info.get(DefinitionItem.Fields.state.name());
               
               Artifact art = new Artifact();
               art.setId(id);
               TemplateArtifact artifact = new TemplateArtifact(ctx, null, art,
                   null, null, subtype, null);
               ModuleVisitor mv = new ModuleVisitor(ctx, null,
                   VisitingOperation.retrieveDefItem);
               mv.visit(artifact);
               if (artifact.getDefItem() != null)
               {
                 Object payload2 = artifact.getDefItem().getValues()
                     .get("payload2");
                 String pl2Str = (payload2 != null
                     && !payload2.toString().isEmpty()) ? payload2.toString(): null;
                 
                 if (pl2Str != null)
                 {
                   Map<String, Object> pl2Map = jm.readAsMap(pl2Str);
                   category = pl2Map.get("category") != null ? (String)pl2Map.get("category"):null;
                   sharedDisplay = pl2Map.get("sharedDisplay");
                 }
               }
             }
             
             // Populate
             ds.put("id", id);
             if (desc != null) ds.put("desc", desc);
             if (category != null) ds.put("category", category);
             if (sharedDisplay != null) ds.put("sharedDisplay", sharedDisplay);
             ds.put("state", state);
             
             dsList.add(ds);
           }
           
           String payload = new JsonMarshaller().serialize(dsList);
           resp.getWriter().print(payload);        
           
           break;
         }
         case listOOTBExp:
         {
           List<Object> ootbExps = new ArrayList<>(6);
           ootbExps.add(createOOTBExp("Site/App Experience",
                "Create a new experience with alerts, banners, data, and overlays.",
                "zi-siteExp", Arrays.asList("Alert", "Banner", "Custom Action", "Full Screen", "Raw Data")));
           ootbExps.add(createOOTBExp("Email Experience",
               "Create a new in experience",
               "zi-emailExp", Arrays.asList("Adobe Email", "Sendgrid Email", "Mailgun Email")));
           ootbExps.add(createOOTBExp("SMS Experience",
               "Create a new in experience",
               "zi-smsExp", Arrays.asList("SMS Message")));
           ootbExps.add(createOOTBExp("Push Experience",
               "Create a new in experience",
               "zi-pushExp", Arrays.asList("Push")));
           ootbExps.add(createOOTBExp("App Box Experience",
               "Create a new in experience",
               "zi-appBoxExp", Arrays.asList("App Box")));
           ootbExps.add(createOOTBExp("Custom Script Experience",
               "Create a new in experience",
               "zi-scriptExp", Arrays.asList("Custom Action")));
           
           resp.getWriter().print(new JsonMarshaller().serialize(ootbExps));
           break;
         }
           
        }
      }
    };
  }
  
  private Map<String, Object> createOOTBExp(String name, String desc,
      String icon, List<String> actionGroup)
  {
    Map<String, Object> map = new HashMap<>(4);

    map.put("name", name);
    map.put("description", desc);
    map.put("icon", icon);
    map.put("actionGroups", actionGroup);

    return map;
  }

  /*
   * (non-Javadoc)
   * 
   * @see com.z1.CommandHandlerFactory#post()
   */
  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length < 2)
        {
          return;
        }

        // Create an array to pass for the relevant commands
        String[] subParts = new String[pathParts.length - 1];
        int j = 0;
        for (int i = 1; i < pathParts.length; i++, j++)
        {
          subParts[j] = pathParts[i];
        }
        String cStr = pathParts[1];
        PostCommand command = PostCommand.valueOf(cStr);
        String subtype = pathParts[0];
        
        // c3/data/bx/<channel|entity|segment|signal|c1|campaign|journey|experience>/delete?id=<id>&type=<type>
        switch (command)
        {
          case delete:
          {            
            new ModulesHandler().post().handle(uctx, subParts, req, resp);
            break;
          }
          // c3/data/bx/<experience>/allInstances?type=experience&category=<retail|..etc>
          // c3/data/bx/segment/allInstances?type=segment&category=<retail|..etc>
          // c3/data/bx/signal/allInstances?type=signal&category=<retail|..etc>
          // c3/data/bx/c1/allInstances?type=signal&category=<retail|..etc>&filter=<active|inactive|draft|last5days>
          // c3/data/bx/campaign/allInstances?type=signal&category=<retail|..etc>
          // c3/data/bx/journey/allInstances?type=signal&category=<retail|..etc>
          case allInstances:
          {
            if (subtype.equals("segment") || 
                subtype.equals("signal") ||
                subtype.equals("c1") ||
                subtype.equals("campaign") ||
                subtype.equals("journey") ||
                subtype.equals("experience"))
            {
              new com.z1.handler.ModulesHandler().post().handle(uctx, subParts, req, resp);
            }
            break;
          }
          // c3/data/bx/<experience>/create?id=<moduleId>&name=<instanceName>&type=experience
          // c3/data/bx/segment/create?id=<moduleId>&name=<instanceName>
          // c3/data/bx/signal/create?id=<moduleId>&name=<instanceName>
          case create:
          {
            if (subtype.equals("segment") || 
                subtype.equals("signal") ||
                subtype.equals("experience"))
            {
              new com.z1.handler.ModulesHandler().post().handle(uctx, subParts, req, resp);
            }
            break;
          }
          // c3/data/bx/<experience>/all?type=experience
          // c3/data/bx/segment/all?type=segment
          // c3/data/bx/signal/all?type=signal
          // c3/data/bx/actiontemplate/all?type=actiontemplate
          case all:
          {
            if (subtype.equals("segment") || 
                subtype.equals("signal") ||
                subtype.equals("experience") ||
                subtype.equals("successmetric") ||
                subtype.equals("actiontemplate"))
            {
              new com.z1.handler.ModulesHandler().post().handle(uctx, subParts, req, resp);
            }
            break;
          }
          // c3/data/bx/<experience>/unpublishModuleid?id=<appId>&force=<true|false>
          // c3/data/bx/actiontemplate/unpublishModuleid?id=<appId>&force=<true|false>
          case unpublishModule:
          {
            if (subtype.equals("experience") ||
                subtype.equals("actiontemplate") ||
                subtype.equals("segment"))
            {
              new com.z1.handler.ModulesHandler().post().handle(uctx, subParts, req, resp);
            }
            break;
          }
        }
      }
    };
  }
}
