package com.z1.handler;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.mapping.EventMapping;
import z1.c3.mapping.EventMappingInfo;
import z1.commons.Const;
import z1.commons.EventUtils;
import z1.domains.DomainHandler;

@SuppressWarnings("unchecked")
public class EventMappingHandler implements CommandHandlerFactory
{
  static final String _NULL_EVENT = Const.NULL_EVENT;
  private static final Map<String,Object> _NULL_EVENT_MAP;
  static
  {
      Map<String, Object> map = new HashMap<>();
      map.put("domainEvent", _NULL_EVENT);
      _NULL_EVENT_MAP = Collections.unmodifiableMap(map);
  }
  
  public enum GetCommand
  {
    all,
    eventMappingRefs
  }

  public enum PostCommand
  {
    add,
    delete,
    publish,
    rollback,
    enable,
    disable,
    addValidate
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        JsonMarshaller jm = new JsonMarshaller();
        if (pathParts.length > 0)
        {
          String cStr = pathParts[0];
          GetCommand command = GetCommand.valueOf(cStr);
         
          switch (command)
          {
            // c3/data/eventmapping/all
            case all:
            {
              if (z1.commons.Utils.isMarketPlace) return;
              EventMappingInfo em = EventMapping.getInstance().getNSMappingInfo(uctx);
              if (!em.doesConfigExist())
              {
                resp.getWriter().print("{}");
              }
              else
              {
                Map<String, Object> map = new HashMap<>(10);
                String payload = em.getPayload();
                String payload2 = em.getPayload2();
                if (payload != null && !payload.isEmpty())
                {
                  map.put("payload", payload);
                }
                else
                {
                  map.put("payload", jm.serialize(new ArrayList<>()));
                }

                if (payload2 != null && !payload2.isEmpty())
                {
                  List<Map<String,Object>> pl2List = jm.readAsObject(payload2, List.class);
                  if (pl2List != null && !pl2List.isEmpty())
                  {
                    // check if there's null marker
                    Map<String,Object> eventIdx0 = pl2List.get(0);
                    boolean isNullEventMap = (eventIdx0.containsKey("domainEvent") 
                        && eventIdx0.get("domainEvent").equals(_NULL_EVENT));
                    if (isNullEventMap)
                    {
                      map.put("payload2", jm.serialize(new ArrayList<>()));
                    }
                    else
                    {
                      map.put("payload2", payload2);
                    } 
                  }
                  else
                  {
                    map.put("payload2", jm.serialize(new ArrayList<>()));
                  }                  
                }
                else
                {
                  map.put("payload2", jm.serialize(new ArrayList<>()));
                }
                map.put("state", em.getState());
                resp.getWriter().print(jm.serialize(map));
              }

              break;
            }
            // c3/data/eventmapping/eventMappingRefs?domainevent=<domainEvent>&nsevent=<nsevent>
            case eventMappingRefs:
            {
              if (z1.commons.Utils.isMarketPlace) return;
              
              ResponseMessage msg = null;
              String domainEvent = req.getParameter("domainevent");
              String nsEvent = req.getParameter("nsevent");
              if (domainEvent == null || domainEvent.isEmpty()
                  || nsEvent == null || nsEvent.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing required parameter 'domainevent' or 'nsevent'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              Map<String, Object> refs = EventMappingInfo
                  .getEventMappingRefs(uctx, domainEvent, nsEvent, false);
              resp.getWriter().print(new JsonMarshaller().serialize(refs));
              break;
            }
            default:
              break;
          }
        }
        else
        {
          // new format, loading as list
          CustomConfig cc = CustomConfig.load(uctx, Type.eventMapping, true);

          if (cc == null)
          {
            resp.getWriter().print(jm.serialize(new ArrayList<>()));
          }
          else
          {
            String payload = cc.getPayload();
            if (payload == null || payload.isEmpty())
            {
              resp.getWriter().print(jm.serialize(new ArrayList<>()));
            }
            else
            {
              resp.getWriter().print(payload);
            }
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        JsonMarshaller jm = new JsonMarshaller();
        if (pathParts.length > 0)
        {
          String cStr = pathParts[0];
          PostCommand command = PostCommand.valueOf(cStr);
          switch (command)
          {
            // c3/data/eventmapping/add
            case add:
            {
              if (z1.commons.Utils.isMarketPlace) return;

              String pl = ServletUtil.getPayload(req);

              if (pl.isEmpty())
              {
                ResponseMessage msg = new ResponseMessage(uctx,
                    ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Empty request payload. Expecting "
                        + "event mapping "
                        + "payload to add/update.");
                resp.getWriter().print(msg);
                return;
              }

              String responseStr = EventUtils.addEventMapping(uctx,
                  jm.readAsObject(pl, List.class));

              if (responseStr != null)
              {
                resp.getWriter().print(
                    new ResponseMessage(uctx, ResponseMessage.Status.fail,
                        ResponseMessage.Type.requestProcessingFailed,
                        responseStr));
                return;
              }

              resp.getWriter().print(
                  new ResponseMessage(uctx, ResponseMessage.Status.success,
                      ResponseMessage.Type.requestProcessingDone,
                      "Done processing eventmapping/add api"));
              break;
            }
            // c3/data/eventmapping/delete
            case delete:
            {
              if (z1.commons.Utils.isMarketPlace) return;
              
              ResponseMessage msg = null;              
              String pl = ServletUtil.getPayload(req);
              if (pl == null || pl.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing or ill formatted request payload.");
                resp.getWriter().print(msg.toString());
                return;
              }
              List<String> eList = jm.readAsObject(pl, List.class);
              if (eList == null || eList.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Request payload is missing or empty!");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              EventMappingInfo em = EventMapping.getInstance().getNSMappingInfo(uctx);
              if (!em.doesConfigExist())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.resourceNotFound,
                    "No eventMapping config found in namespace '" + uctx.getNamespace() + "'.");
                resp.getWriter().print(msg.toString());
                return;
              }              
              
              String payload2 = em.getPayload2();
              List<Map<String, Object>> ccMapList = jm.readAsObject(payload2, List.class);
              if (ccMapList == null || ccMapList.isEmpty())
              {
                // check if there's payload
                String payload = em.getPayload();
                ccMapList = jm.readAsObject(payload, List.class);
                if (ccMapList == null || ccMapList.isEmpty())
                {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "Failed processing eventmapping/" + command.name() + 
                      " api. No mapping exists!");
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
              else if (ccMapList != null && !ccMapList.isEmpty())
              {
                // check if there's null marker
                Map<String,Object> eventIdx0 = ccMapList.get(0);
                boolean isNullEventMap = (eventIdx0.containsKey("domainEvent") 
                    && eventIdx0.get("domainEvent").equals(_NULL_EVENT));
                if (isNullEventMap)
                {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "Failed processing eventmapping/" + command.name() + 
                      " api. Previous change had emptied all mappings without publishing. " +
                      "Publish or rollback change first before proceeding.");
                  resp.getWriter().print(msg.toString());
                  return;
                }
              }
              
              List<Map<String, Object>> updatedMapList = new ArrayList<>();

              for (Map<String, Object> ccMap : ccMapList)
              {
                // No restriction when domain event contains enrichment code ZMOB-17899              
                String ccDomainEvent = (String) ccMap.get("domainEvent");
                if (eList.contains(ccDomainEvent))
                {
                  continue;
                }
                updatedMapList.add(ccMap);
              }
              if (updatedMapList.isEmpty())
              {
                // add a null event mapping marker
                updatedMapList.add(_NULL_EVENT_MAP);
              }
              em.setPayload2(jm.serialize(updatedMapList));
              em.save(); // save and maintain the previous state
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done processing eventmapping/" + command.name() + " api");
              resp.getWriter().print(msg.toString());
              break;
            }            
            // c3/data/eventmapping/rollback
            case rollback:
            {
              if (z1.commons.Utils.isMarketPlace) return;

              ResponseMessage msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done processing eventmapping/" + command.name() + " api"); 
              EventMappingInfo em = EventMapping.getInstance().getNSMappingInfo(uctx);
              if (!em.doesConfigExist())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.resourceNotFound,
                    "No eventMapping config found in namespace '" + uctx.getNamespace() + "'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              String payload2 = em.getPayload2();
              List<Object> ccMapList = jm.readAsObject(payload2, List.class);
              if (ccMapList != null)
              {
                if (!ccMapList.isEmpty())
                {
                  // empty payload2 and save as published.
                  em.setPayload2(jm.serialize(new ArrayList<>()));
                  em.save(true);
                }
                else
                {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "Failed processing eventmapping/" + command.name() + 
                      " api. There was no change to rollback!");
                }
              }
              else
              {
                // check if there's payload
                String payload = em.getPayload();
                ccMapList = jm.readAsObject(payload, List.class);
                if (ccMapList != null && !ccMapList.isEmpty())
                {
                  em.save(true); // save as published                
                }
                else
                {
                  msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                      ResponseMessage.Type.requestProcessingFailed,
                      "Failed processing eventmapping/" + command.name() + 
                      " api. No mapping exists!");
                }
              }

              resp.getWriter().print(msg.toString());
              break;
            }
            // c3/data/eventmapping/publish
            case publish:
            {
              if (z1.commons.Utils.isMarketPlace) return;

              ResponseMessage msg;
              String err = EventUtils.publishEventMapping(uctx);

              if (err != null)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed, err);
              }
              else
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "Done processing eventmapping/publish api");
              }

              resp.getWriter().print(msg);
              break;
            }
            // c3/data/eventmapping/enable
            case enable:
            {
              if (z1.commons.Utils.isMarketPlace) return;

              ResponseMessage msg = null;
              EventMappingInfo em = EventMapping.getInstance().getNSMappingInfo(uctx);
              if (!em.doesConfigExist())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.resourceNotFound,
                    "No eventMapping config found in namespace '" + uctx.getNamespace() + "'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              em.save(true); // save as published
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done processing eventmapping/" + command.name() + " api");
              resp.getWriter().print(msg.toString());
              break;
            }
            // c3/data/eventmapping/disable
            case disable:
            {
              if (z1.commons.Utils.isMarketPlace) return;

              ResponseMessage msg = null;
              EventMappingInfo em = EventMapping.getInstance().getNSMappingInfo(uctx);
              if (!em.doesConfigExist())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.resourceNotFound,
                    "No eventMapping config found in namespace '" + uctx.getNamespace() + "'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              em.save(false); // save as draft
              msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                  ResponseMessage.Type.requestProcessingDone,
                  "Done processing eventmapping/" + command.name() + " api");
              resp.getWriter().print(msg.toString());
              break;
            }
            // c3/data/eventmapping/addEventValidate?domainevent=<domainEvent>&nsevent=<nsevent>
            case addValidate:
            {
              if (z1.commons.Utils.isMarketPlace) return;
              
              ResponseMessage msg = null;
              String domainEvent = req.getParameter("domainevent");
              String nsEvent = req.getParameter("nsevent");
              if (domainEvent == null || domainEvent.isEmpty()
                  || nsEvent == null || nsEvent.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Missing required parameter 'domainevent' or 'nsevent'.");
                resp.getWriter().print(msg.toString());
                return;
              }
              
              List<String> conflicts = EventMappingInfo
                  .findMappedNSEventsWithConflictSource(uctx, domainEvent,
                      nsEvent);
              Map<String,Object> ret = new HashMap<>();
              ret.put("conflicts", conflicts);
              
              if (conflicts.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "No conflict!");
              }
              else
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "Event '" + nsEvent
                        + "' has source that's in conflicts with other events mapped to '"
                        + domainEvent + "'.");
              }
              ret.put("status", msg);
              resp.getWriter().print(jm.serialize(ret));
              break;
            }            
            default:
              break;
          }
        }
        else
        {
          // Storing as list of objects.
          String payload = ServletUtil.getPayload(req);
          if (payload != null && !payload.isEmpty())
          {
            CustomConfig cc = CustomConfig.load(uctx,
                CustomConfig.Type.eventMapping, true);
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, CustomConfig.Type.eventMapping);
            }
            cc.setPayload(payload);
            cc.save();
          }
        }
      }

    };
  }

}
