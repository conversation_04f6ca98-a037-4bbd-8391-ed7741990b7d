package com.z1.handler;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.util.Bytes;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobHandler;
import udichi.core.util.JsonMarshaller;
import udichi.gateway.apiservice.PipelineExecutor;
import z1.actions.sysrst.SysResetAction.SysResetActionType;
import z1.core.Context;
import z1.core.Profile;
import z1.core.profile.ProfileException;
import z1.core.profile.ProfileIndex;
import z1.core.ZTable;
import z1.core.Profile.Prefix;

public class ProfileOptJobHandler implements JobHandler
{

  public enum JobType
  {
    optout,
    optin;
  }

  @Override
  public void handle(Job job)
  {
    UContext uctx = job.getContext();
    ULogger logger = uctx.getLogger(getClass());

    String payload = job.getPayload();
    Map<String, Object> data = new JsonMarshaller().readAsMap(payload);
    String profileId = (String) data.get(z1.commons.Const.P_PROFILE_ID);
    String jobType = (String) data.get("jobtype");

    if (profileId == null)
    {
      logger.severe(String.format("Unable to %s profile. Profile ID is missing",
          jobType));
    }

    if (jobType.equals(JobType.optout.name()))
    {
      optOut(uctx, profileId);
    }
    else if (jobType.equals(JobType.optin.name()))
    {
      optIn(uctx, profileId);
    }

  }

  /**
   * Creates optout index entries and delete original index entries, then spin a
   * P4 job to cleanup profile, relation, activity data pertained to the
   * profile.
   * 
   * @param uctx
   * @param profileId
   */
  public void optOut(UContext uctx, String profileId)
  {
    Context ctx = Context.getInstance(uctx);
    ULogger logger = uctx.getLogger(getClass());

    Profile p = Profile.instance(ctx, profileId);
    // Revive the original index entry and delete optout index entries.
    List<String> handles = p.getMetaPropertyNames(Prefix.handle);
    List<String> newHandles = new java.util.ArrayList<>(handles.size());
    List<String> delHandles = new java.util.ArrayList<>(handles.size());
    List<String> secondaryKeyList = new java.util.ArrayList<String>();
    String secondaryIndexKey = new StringBuilder()
        .append(z1.core.Type.IndexType.CUSTOMKEY.id).append('\3').toString();

    for (String h : handles)
    {
      int index = h.indexOf(ProfileIndex.SEP2);
      if (index < 0) continue;

      String indexEntry = ctx.accountNum() + String.valueOf(ProfileIndex.SEP) + h;
      // Check in pindex if this entry exist and has the same profileId attached
      if (!isProfileMatching(ctx, indexEntry, p.getKeyValue())) continue;

      StringBuilder newIndexEntry = new StringBuilder(h);
      newIndexEntry.insert(index + 1, z1.commons.Const.OPTOUT_PROFILE_MARKER);

      newHandles.add(newIndexEntry.toString());
      delHandles.add(ctx.accountNum() + String.valueOf(ProfileIndex.SEP) + h);

      if (h.startsWith(secondaryIndexKey))
      {
        String[] secKey = h.substring(index + 1).split("\\|");
        secondaryKeyList.add(secKey[0]);
      }
    }

    try
    {
      ProfileIndex.addOptOutIndexes(ctx, p, delHandles, newHandles);
    }
    catch (IOException e)
    {
      logger.severe(
          "Error while adding the optOut entries in Pindex." + e.getMessage());
    }
    ProfileIndex.deleteAll(ctx, delHandles);

    p.addProperty(Profile.Fields.isOptIn.value, "false");
    p.flush();
    p.delProfileColumnData(ZTable.profile.cf_data, secondaryKeyList);

    // Need to delete data from other table
    Map<String, Object> item = new HashMap<>();
    item.put("ns", uctx.getNamespace());
    item.put("type", SysResetActionType.profile_reset.name());
    item.put(z1.commons.Const.P_PROFILE_ID, profileId);
    PipelineExecutor pe = PipelineExecutor.createActionExecutor(
        com.z1.handler.api.Const.SYSRESET_ACTION_NAME, uctx);
    pe.setParams(item);
    pe.setPriority(Priority.counter);
    pe.setAsyncExec(true);
    pe.run();
  }

  /**
   * Revives an opted-out profile by recreating the original index entries and
   * delete optout index entries
   * 
   * @param uctx
   * @param profileId
   */
  public void optIn(UContext uctx, String profileId)
  {
    Context ctx = Context.getInstance(uctx);
    ULogger logger = uctx.getLogger(getClass());

    Profile p = Profile.instance(ctx, profileId);
    // Create the index entry delete other index entries.
    List<String> handles = p.getMetaPropertyNames(Prefix.handle);
    List<String> newHandles = new java.util.ArrayList<>(handles.size());
    List<String> delHandles = new java.util.ArrayList<>(handles.size());
    for (String h : handles)
    {
      int index = h.indexOf(ProfileIndex.SEP2);
      if (index < 0) continue;
      StringBuilder newIndexEntry = new StringBuilder(h);
      newIndexEntry.insert(index + 1, z1.commons.Const.OPTOUT_PROFILE_MARKER);
      String indexEntry = ctx.accountNum() + String.valueOf(ProfileIndex.SEP)
          + newIndexEntry;
      // Check in pindex if this entry exist and has the same profileId attached
      if (!isProfileMatching(ctx, indexEntry, p.getKeyValue())) continue;

      delHandles.add(indexEntry);
      newHandles.add(h.toString());
    }

    try
    {
      ProfileIndex.addOptOutIndexes(ctx, p, delHandles, newHandles);
    }
    catch (IOException e)
    {
      logger.severe(
          "Error while adding the optOut entries in Pindex." + e.getMessage());
    }
    ProfileIndex.deleteAll(ctx, delHandles);

    p.addProperty(Profile.Fields.isOptIn.value, "true");
    p.flush();
  }

  /**
   * Check if the profileId matches with the given pindex entry.
   * 
   * @param ctx
   * @param indexKey
   * @param pid
   * @return
   */
  public boolean isProfileMatching(Context ctx, String indexKey, String pid)
  {
    boolean isMatch = false;
    try (ZTable table = ZTable.profileIndex.getTableForRead(ctx))
    {
      Get g = new Get(Bytes.toBytes(indexKey));
      org.apache.hadoop.hbase.client.Result res = table.get(g);
      if (res == null || res.isEmpty()) return isMatch;

      byte[] val = res.getValue(ZTable.profileIndex.cf_core,
          ProfileIndex.PINDEX_PROFILE);

      if (val != null && pid.equals(Bytes.toString(val)))
      {
        isMatch = true;
      }
      return isMatch;
    }
    catch (IOException e)
    {
      throw new ProfileException("Failed to extract record").forCause(e);
    }
  }

}
