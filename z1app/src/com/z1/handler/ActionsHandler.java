package com.z1.handler;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.AbstractSignal;
import z1.c3.Actions;
import z1.c3.ActivityDataHandler;
import z1.c3.signal.TimerEventSignal;
import z1.chat.ChatSubject;
import z1.commons.Const;
import z1.commons.def.TimeSelectorDef;
import z1.core.Context;
import z1.template.TemplateUtils;


public class ActionsHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all, id, aggregate,appInboxMessage

  }
  
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
          String cStr = pathParts[0];
          GetCommand command = GetCommand.valueOf(cStr);
          
        switch (command)
        {
          // c3/data/actions/all
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);

            // We'll get all goal definitions to send them back
            List<Actions> segs = Actions.loadAll(ctx);
            for (Actions s : segs)
            {
              if (s == null || TemplateUtils.isTemplateConfig(s.getId(), s.PREFIX))  
                continue;

              Map<String, Object> map = s.getDefAsMap();
              if (map != null)
              {
                map.put("id", s.getId());
                ret.add(map);
              }
            }

            String payload = new JsonMarshaller().serialize(ret);
            resp.getWriter().print(payload);

            return;
          }
          
          // c3/data/actions/aggregate?profileId=<profile-id>&tunit=<tunit>&tvalue=<tvalue>
          // aggregate actions per user
          case aggregate:
          {
            String profileId = req.getParameter("profileId");
            String unit = req.getParameter("tunit");
            String value = req.getParameter("tvalue");

            TimeSelectorDef timeDef = new TimeSelectorDef();
            timeDef.setUnit(unit);
            timeDef.setValue(Integer.parseInt(value));
            timeDef.setDate(Const.PAST_TIME);

            AbstractSignal sig = new TimerEventSignal(ctx);
            List<ActivityDataHandler.MatchedItem> signals = ActivityDataHandler
                .getMatchedActions(ctx, sig, profileId, timeDef);
            JsonMarshaller jm = new JsonMarshaller();
            resp.getWriter().print(jm.serialize(signals));
            return;
          }
          
          // c3/data/actions/id?id=<id>
          case id:
          {
              // This must be a request for a specific goal payload.
              String sId = req.getParameter("id");
              // get the goal payload
              Actions s = Actions.load(ctx, sId);
              String payload = new JsonMarshaller().serialize(s.getDef());
              resp.getWriter().print(payload);

              return;
          }


          case appInboxMessage:
          {
            String chatId = req.getParameter("chatId");
            Map<String, Object> res = new LinkedHashMap<>();
            JsonMarshaller jm = new JsonMarshaller();
            if (chatId==null || chatId.isEmpty())
            {
              res.put("message", "Action message not sent.");
              resp.getWriter().print(jm.serializeMap(res));
              return;
            }
            ChatSubject chatSubject = ChatSubject.findChatSubject(Context.getInstance(ctx), chatId);
            res.put("message", chatSubject.getValue(ChatSubject.Fields.msg_message));
            resp.getWriter().print(jm.serializeMap(res));
            return;
          }

          default:
          {
            return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/actions/create => payload has the definition
          // returns <segment id>
          case create:
          {
            // Create a segment by loading the payload from the request
            String payload = ServletUtil.getPayload(req);

            Actions s = Actions.create(ctx);
            s.setPayload(payload);
            s.save();
            String sid = s.getId();

            resp.getWriter().print(sid);
            return;
          }

          // c3/data/actions/update?sid=<segment-id> => payload has the definition
          case update:
          {
            String sid = req.getParameter("sid");
            String payload = ServletUtil.getPayload(req);

            Actions s = Actions.load(ctx, sid);
            s.setPayload(payload);
            s.save();

            return;
          }
          // c3/data/actions/delete?sid=<segment-id>
          case delete:
          {
            String sid = req.getParameter("sid");
            Actions.delete(ctx, sid);
            return;
          }
        }

      }
    };

  }

  // /////////////////////////////////////////////////////////////////


}
