package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.stream.EventStream;
import z1.stream.Pipeline;
import z1.stream.StreamQuery;
import z1.stream.def.PipelineDef;
import z1.template.TemplateUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StreamQueryHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    create,
    update,
    delete
  }

  private enum GetCommand
  {
    all, // All query definitions
    id, // name of the pipeline/query example: getTopKProductsByCategory
    query, // Returns data for a given query
    // values
    cachedump // Dumps data from a cache
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler()
    {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
                         final HttpServletRequest req, final HttpServletResponse resp)
              throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/streamQuery/all
          // get all definitions
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(10);
            List<CustomConfig> ccList = CustomConfig.forceLoadAll(uctx,
                    Type.streamQuery);
            JsonMarshaller jm = new JsonMarshaller();
            for (CustomConfig cc : ccList)
            {
              if (cc == null || TemplateUtils.isTemplateConfig(cc.getId(), Type.streamQuery.name()))
                continue;

              Map<String, Object> map = new HashMap<String, Object>();
              map.put(DefinitionItem.ID, cc.getId());
              map.put(DefinitionItem.Fields.name.name(), cc.getName());
              map.put(DefinitionItem.Fields.description.name(),
                      cc.getDescription());

              Map<String, Object> plMap = jm.readAsMap(cc.getPayload());
              map.put(DefinitionItem.Fields.payload.name(), plMap);

              ret.add(map);
            }

            resp.getWriter().print(jm.serialize(ret));
            return;
          }
          // c3/data/streamQuery/id?id=<id>
          // returns the definition of a stream query identified by the id
          case id:
          {
            String id = req.getParameter("id");

            CustomConfig cc = CustomConfig.load(uctx, id, Type.streamQuery, true);
            if (cc == null) return;

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> map = new HashMap<>();
            map.put(DefinitionItem.ID, cc.getId());
            map.put(DefinitionItem.Fields.name.name(), cc.getName());
            map.put(DefinitionItem.Fields.description.name(),
                    cc.getDescription());

            Map<String, Object> plMap = jm.readAsMap(cc.getPayload());
            map.put(DefinitionItem.Fields.payload.name(), plMap);

            resp.getWriter().print(jm.serialize(map));
            return;
          }

          // c3/data/streamQuery/cachedump?id=<stream-id>
          // returns the data for a given cache id
          case cachedump:
          {
            String id = req.getParameter("id");
            PipelineDef pd = new Pipeline(uctx).getDefinition(id);
            EventStream es = EventStream.forCurrentValues(uctx, pd);
            Map<String, List<Object>> val = es.dumpData();
            if (val.isEmpty()) return;

            resp.getWriter().print(new JsonMarshaller().serialize(val));
            return;
          }


          // c3/data/streamQuery/query?queryName=<id>
          // returns the data for a given stream query
          case query:
          {
            String queryName = req.getParameter("queryName");
            if (queryName == null || queryName.isEmpty()) return;
            // Get all event parmas, if any
            Enumeration<String> pNames = req.getParameterNames();
            Map<String, Object> reqParams = new java.util.HashMap<>(10);
            while (pNames.hasMoreElements())
            {
              String n = pNames.nextElement();
              if ("queryName".equalsIgnoreCase(n) || "namespace".equalsIgnoreCase(n)) continue;
              String v = req.getParameter(n);
              reqParams.put(n, v);
            }

            List<Object> dataSet = new StreamQuery(uctx, queryName).withParameters(reqParams)
                    .execute();
            Map<String, Object> map = new java.util.HashMap<>(2);
            map.put("items", dataSet);
            resp.getWriter().print(new JsonMarshaller().serialize(map));
            return;
          }

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler()
    {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
                         final HttpServletRequest req, final HttpServletResponse resp)
              throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/streamQuery/create => payload has the definition
          // returns <contextformula-id>
          case create:
          {
            String cap = ServletUtil.getPayload(req);
            String id = "";

            // If no payload is defined, we will simply return
            if (cap == null) return;

            JsonMarshaller jm = new JsonMarshaller();
            // create the context attribute object and save the payload
            Map<String, Object> map = jm.readAsMap(cap);
            String name = (String) map.get("name");
            CustomConfig cc = CustomConfig.create(uctx, Type.streamQuery, name);
            cc.setPayload(cap);
            cc.setName(name);
            cc.save();
            id = cc.getId();

            Map<String, Object> m = new java.util.HashMap<>();
            m.put("id", id);
            resp.getWriter().print(new JsonMarshaller().serializeMap(m));
            break;
          }
          case update:
          {
            // c3/data/streamQuery/update?id=<id>
            // update the existing conext attribute by passing the id
            String id = req.getParameter("id");

            String cap = ServletUtil.getPayload(req);
            CustomConfig cc = CustomConfig.load(uctx, id, Type.streamQuery, true);
            if (cc != null)
            {
              cc.setPayload(cap);
              cc.save();
            }
            break;
          }
          case delete:
          {
            // c3/data/streamQuery/delete?id=<id>
            String id = req.getParameter("id");
            if (CustomConfig.delete(uctx, id, Type.streamQuery))
            {
              ServletUtil.printSuccess(resp.getWriter());
            }

//            req.setAttribute("id", id);
//            req.setAttribute("type", CustomConfig.Type.streamQuery.name());
//            String[] subParts = new String[pathParts.length + 1];
//            subParts[0] = CustomConfig.Type.streamQuery.name();
//            subParts[1] = pathParts[0];
//            new BXHandler().post().handle(uctx, subParts, req, resp);
            break;
          }
        }

        // Notify to clear pipeline stream cache as well
        App.notifyClearCache(uctx.getNamespace(), Pipeline.C_KEY);
      }

    };
  }

  ///////////////////////////
  private void _fillEmptyTimeSeries(Calendar cal, Long endTime,
                                    String attribute, List<Map<String, Object>> retList,
                                    EventStream.ComputeWindow interval)
  {
    // Fill in the list with al zero and return
//    Long t = cal.getTimeInMillis();
//    while (t < endTime)
//    {
//      Map<String, Object> m = new java.util.HashMap<>(4);
//      m.put(EventStream.SystemDataType.currentTime.value, t);
//      m.put(attribute, 0);
//      retList.add(m);
//      t = TimeUtils.getNextTimeInMillis(cal, interval.value(),
//          interval.timeUnit());
//      cal.setTimeInMillis(t);
//    }

  }

}
