package com.z1.handler;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.IntStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.c3.CustomConfig;
import z1.commons.Utils;

public class IncidentalEventHandler implements CommandHandlerFactory
{
  private static JsonMarshaller jm = new JsonMarshaller();
  private static final String EVENT_NAME = "eventName";
  private static final String CLICK_COUNT = "count";
  private static final String ID = "id";

  public enum GetCommand
  {
    all,
    find
  }

  public enum PostCommand
  {
    add,
    update,
    delete
  }

  @Override
  @SuppressWarnings("unchecked")
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length > 0)
        {
          String cStr = pathParts[0];
          GetCommand path = GetCommand.valueOf(cStr);

          final CustomConfig cc = CustomConfig.load(uctx, CustomConfig.Type.incidentalEvent);
          final String payload = (cc != null) ? cc.getPayload() : null;
          if (payload == null)
            return;

          final List<Map<String, Object>> incidentalEventConfig = jm.readAsObject(payload, List.class);

          switch (path)
          {
            // c3/data/incidentalevent/all
            case all:
              resp.getWriter().print(jm.serialize(incidentalEventConfig));
              break;

            // c3/data/incidentalevent/find/?id=id
            case find: 
              final String id = req.getParameter(ID);
              if ((id == null) || (id.length() == 0))
              {
                // Missing parameter value
                _respondRequestFailMessage(uctx, resp,
                    String.format("Missing [%s] parameter.", ID));
                return;
              }

              final Map<String, Object> incidentalConfig = incidentalEventConfig.stream()
                  .filter(config -> config.get(ID).equals(id))
                  .findAny().orElse(new HashMap<>());

              resp.getWriter().print(jm.serialize(incidentalConfig));
              break;
            default:
              throw new Exception("Unknown REST command");
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        final String cStr = pathParts[0];
        final PostCommand path = PostCommand.valueOf(cStr);

        switch (path)
        {
          // c3/data/incidentalevent/add
          case add:
          {
            final String reqPayload = ServletUtil.getPayload(req);
            if ((reqPayload == null) || (reqPayload.length() == 0))
            {
              _respondRequestFailMessage(uctx, resp, "No payload provided in request.");
              return;
            }

            Map<String, Object> incidentalEventToAdd = jm.readAsMap(reqPayload);
            incidentalEventToAdd.put(ID, UUID.randomUUID().toString());

            CustomConfig incidentalEventConfig = CustomConfig.load(uctx, CustomConfig.Type.incidentalEvent);

            // No incidental event configuration in database so create it.
            if (incidentalEventConfig == null)
            {
              incidentalEventConfig = CustomConfig.create(uctx, CustomConfig.Type.incidentalEvent);
              List<Map<String, Object>> incidentalEvents = new ArrayList<>();
              incidentalEvents.add(incidentalEventToAdd);
              incidentalEventConfig.setPayload(jm.serialize(incidentalEvents));
              incidentalEventConfig.save();
              resp.getWriter().print(jm.serialize(incidentalEventToAdd));
              return;
            }

            final String incidentalEventConfigPayload = incidentalEventConfig.getPayload();
            if (incidentalEventConfigPayload == null)
            {
              // Incidental event configuration payload is null so add it now.
              List<Map<String, Object>> incidentalEvents = new ArrayList<>();
              incidentalEvents.add(incidentalEventToAdd);
              incidentalEventConfig.setPayload(jm.serialize(incidentalEvents));
              incidentalEventConfig.save();
              resp.getWriter().print(jm.serialize(incidentalEventToAdd));
              return;
            }
            
            final List<Map<String, Object>> incidentalEvents = jm.readAsObject(incidentalEventConfigPayload, List.class);
            final boolean eventAlreadyExists = incidentalEvents.stream()
                .anyMatch(config -> config.get(EVENT_NAME).equals(incidentalEventToAdd.get(EVENT_NAME)) &&
                    config.get(CLICK_COUNT).equals(incidentalEventToAdd.get(CLICK_COUNT)));
            if(eventAlreadyExists)
            {
              // Event name already exists so cannot add it.
              _respondProcessingFailMessage(uctx, resp, String.format("Request failed.  "
                  + "An entry for event name [%s] and count [%s] already exists.", 
                  incidentalEventToAdd.get(EVENT_NAME), incidentalEventToAdd.get(CLICK_COUNT)));
              return;
            }

            // Add requested configuration.
            incidentalEvents.add(incidentalEventToAdd);
            incidentalEventConfig.setPayload(jm.serialize(incidentalEvents));
            incidentalEventConfig.save();
            resp.getWriter().print(jm.serialize(incidentalEventToAdd));
            break;
          }

          // c3/data/incidentalevent/update
          case update:  
          {
            final String reqPayload = ServletUtil.getPayload(req);
            if ((reqPayload == null) || (reqPayload.length() == 0))
            {
              _respondRequestFailMessage(uctx, resp, "No payload provided in request.");
              return;
            }

            // Get the name from the payload
            Map<String, Object> incidentalEventToUpdate = jm.readAsMap(reqPayload);
            final String incidentalEventIdToUpdate = (String) incidentalEventToUpdate.get(ID);
            if(incidentalEventIdToUpdate == null)
            {
              // Missing parameter value
              _respondRequestFailMessage(uctx, resp,
                  String.format("Payload missing [%s] value.", ID));
              return;
            }

            final CustomConfig incidentalEventConfig = CustomConfig.load(uctx, CustomConfig.Type.incidentalEvent);

            // No configuration in database to update.
            if (incidentalEventConfig == null)
            {
              // Error no configuration to update.
              _respondRequestFailMessage(uctx, resp,
                  "No incidental event configuration to update.  Call add before update.");
              return;
            }

            final String incidentalEventConfigPayload = incidentalEventConfig.getPayload();
            if (incidentalEventConfigPayload == null)
            {
              // Error no configuration to update.
              _respondRequestFailMessage(uctx, resp,
                  "No incidental event configuration to update.  Call add before update.");
              return;
            }
            
            final List<Map<String, Object>> incidentalEvents = jm.readAsObject(incidentalEventConfigPayload, List.class);

            int entryIndex = IntStream.range(0, incidentalEvents.size())
                  .filter(idx -> incidentalEvents.get(idx).get(ID).equals(incidentalEventIdToUpdate))
                  .findFirst().orElse(-1);

            if(entryIndex >= 0)
            {
              incidentalEvents.set(entryIndex, incidentalEventToUpdate);
              incidentalEventConfig.setPayload(jm.serialize(incidentalEvents));
              incidentalEventConfig.save();
              _respondSuccessMessage(uctx, resp);
            }
            else
            {
              _respondProcessingFailMessage(uctx, resp, String.format("Request failed.  "
                  + "Entry id [%s] not found for updating.", incidentalEventIdToUpdate));
            }
            break;
          }

          // c3/data/incidentalevent/delete?id=id
          case delete:
            final String incidentalEventIdToDelete = req.getParameter(ID);
            if ((incidentalEventIdToDelete == null) || (incidentalEventIdToDelete.length() == 0))
            {
              // Missing parameter value
              _respondRequestFailMessage(uctx, resp,
                  String.format("Missing [%s] parameter.", ID));
              return;
            }

            final CustomConfig incidentalEventConfig = CustomConfig.load(uctx, CustomConfig.Type.incidentalEvent);

            if (incidentalEventConfig == null)
            {
              // Error no configuration to delete.
              _respondRequestFailMessage(uctx, resp,
                  "No incidental event configuration to update.  Call add before update.");
              return;
            }

            final String configPayload = incidentalEventConfig.getPayload();
            if (configPayload == null)
            {
              // Error no configuration to delete.
              _respondRequestFailMessage(uctx, resp,
                  "No incidental event configuration to update.  Call add before update.");
              return;
            }
            
            final List<Map<String, Object>> incidentalEvents = jm.readAsObject(configPayload, List.class);

            int entryIndex = IntStream.range(0, incidentalEvents.size())
                .filter(idx -> incidentalEvents.get(idx).get(ID).equals(incidentalEventIdToDelete))
                .findFirst().orElse(-1);

            if(entryIndex >= 0)
            {
              incidentalEvents.remove(entryIndex);
              incidentalEventConfig.setPayload(jm.serialize(incidentalEvents));
              incidentalEventConfig.save();
              _respondSuccessMessage(uctx, resp);
            }
            else
            {
              _respondProcessingFailMessage(uctx, resp, String.format("Request failed.  "
                  + "Entry [%s] not found for deletion.", incidentalEventIdToDelete));
            }
            break;

          default:
            break;
        }
      }
    };
  }

  private void _respondProcessingFailMessage(UContext uctx, final HttpServletResponse resp, final String message)
      throws IOException
  {
    ResponseMessage rmsg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
        ResponseMessage.Type.requestProcessingFailed, message);
    resp.getWriter().print(rmsg.toString());
  }

  private void _respondRequestFailMessage(UContext uctx, final HttpServletResponse resp, final String message)
      throws IOException
  {
    ResponseMessage rmsg = new ResponseMessage(uctx, ResponseMessage.Status.fail, 
            ResponseMessage.Type.invalidParams, message);
    resp.getWriter().print(rmsg.toString());
  }

  private void _respondSuccessMessage(UContext uctx, final HttpServletResponse resp)
      throws IOException
  {
    resp.getWriter().print(new ResponseMessage(uctx, ResponseMessage.Status.success,
        ResponseMessage.Type.requestProcessingDone).toString());
  }
}
