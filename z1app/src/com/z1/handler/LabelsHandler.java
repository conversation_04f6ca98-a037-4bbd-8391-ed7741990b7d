package com.z1.handler;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.Journey;
import z1.commons.Utils;
import z1.processing.JourneyRuntimeUtils;

/**
 * This class handles action labels (page and position) CRUD
 *
 */
public class LabelsHandler implements CommandHandlerFactory
{  
  // Supported post commands
  private enum PostCommand
  {
    add,
    delete
  }

  // Supported get commands
  private enum GetCommand
  {
    all
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);
        
        ResponseMessage msg = null;
        JsonMarshaller jm = new JsonMarshaller();

        switch (command)
        {
          // c3/data/labels/all?type=<page|position>
          case all:
          {
            String type = req.getParameter("type");
            if (type == null || type.isEmpty()
                || (!JourneyRuntimeUtils.ActionLabelType.page.name().equals(type) 
                    && !JourneyRuntimeUtils.ActionLabelType.position.name().equals(type)))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing or invalid required parameter 'type'. Expect either label type 'page' or 'position'.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            CustomConfig cc = CustomConfig.load(uctx, Type.actionLabel.name(), Type.actionLabel, false);
            
            if (cc == null || cc.getPayload() == null)
            {
              resp.getWriter().print(jm.serialize(Collections.emptyList()));
              break;
            }
            
            String pl = cc.getPayload();
            Map<String,Object> plMap = jm.readAsMap(pl);
            if (!plMap.containsKey(type))
            {
              resp.getWriter().print(jm.serialize(Collections.emptyList()));
              break;
            }
            
            List<String> labels = (List<String>) plMap.get(type);
            resp.getWriter().print(new JsonMarshaller().serialize(labels));
            
            break;
          }          
          default:
          {
            break;
          }

        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);
        
        ResponseMessage msg = null;
        JsonMarshaller jm = new JsonMarshaller();

        switch (command)
        {
          // c3/data/labels/add?type=<page|position>&name=<labelName>
          case add:
          {
            String type = req.getParameter("type");
            String name = req.getParameter("name");
            
            if (type == null || type.isEmpty()
                || (!JourneyRuntimeUtils.ActionLabelType.page.name().equals(type) 
                    && !JourneyRuntimeUtils.ActionLabelType.position.name().equals(type))
                || name == null || name.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing or invalid required parameter 'type' or parameter 'name'." +
                  "  Expect either label type 'page' or 'position' and a label name.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            Pattern pattern = Pattern.compile("^[0-9A-Za-z\\s-_]+$");
            Matcher matcher = pattern.matcher(name);
            if (!matcher.matches())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Invalid label provided! " + 
                  "Label can only contains alphanumeric characters, spaces, hyphens and underscores.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            CustomConfig cc = CustomConfig.load(uctx, Type.actionLabel.name(), Type.actionLabel, false);
            
            if (cc == null)
            {
              cc = CustomConfig.create(uctx, Type.actionLabel, Type.actionLabel.name());
            }
            
            String pl = cc.getPayload();
            Map<String,Object> plMap = (pl == null) ? new HashMap<>(2) : jm.readAsMap(pl);
            List<String> labels = new ArrayList<>();
            if (plMap.containsKey(type))
            {
              labels = (List<String>) plMap.get(type);
            }
            if (labels.contains(name))
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Label '" + name + "' already exists!");
              resp.getWriter().print(msg.toString());
              return;
            }
            labels.add(name);
            plMap.put(type, labels);
            cc.setPayload(jm.serialize(plMap));
            cc.save(true);
            break;
          }
          // c3/data/labels/delete?type=<page|position>&name=<labelName>
          case delete:
          {
            String type = req.getParameter("type");
            String name = req.getParameter("name");
            if (type == null || type.isEmpty()
                || (!JourneyRuntimeUtils.ActionLabelType.page.name().equals(type) 
                    && !JourneyRuntimeUtils.ActionLabelType.position.name().equals(type))
                || name == null || name.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Missing or invalid required parameter 'type' or parameter 'name'." +
                  "  Expect either label type 'page' or 'position' and a label name.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            CustomConfig cc = CustomConfig.load(uctx, Type.actionLabel.name(), Type.actionLabel, false);
            
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.requestProcessingFailed,
                "Failed processing labels/" + command.name() + 
                " api: " + type + " label '" + name + "' does not exist!");
            
            if (cc == null || cc.getPayload() == null)
            { 
              resp.getWriter().print(msg.toString());
              return;
            }
            
            String pl = cc.getPayload();
            Map<String,Object> plMap = jm.readAsMap(pl);
            if (!plMap.containsKey(type) 
                || ((List<String>) plMap.get(type)).isEmpty()
                || !((List<String>) plMap.get(type)).contains(name))
            {
              resp.getWriter().print(msg.toString());
              return;
            }
            
            List<String> labels = (List<String>) plMap.get(type);
            
            // check for any TI reference to this label            
            List<String> references = JourneyRuntimeUtils
                .getActionLabelReferences(uctx, type, name);
            
            if (!references.isEmpty())
            {
              String refStr = references.stream().collect(Collectors.joining(", "));
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.requestProcessingFailed,
                  "Failed processing labels/" + command.name() + 
                  " api: " + type + " label '" + name + "' is referenced in: "
                  + refStr);
              resp.getWriter().print(msg.toString());
              return;
            }
            
            labels.remove(name);
            plMap.put(type, labels);
            cc.setPayload(jm.serialize(plMap));
            cc.save(true);
            break;
          }
          default:
          {
            break;
          }          
        }
        App.notifyClearCache(uctx.getNamespace(), Type.actionLabel.name());
      }
    };
  }

}
