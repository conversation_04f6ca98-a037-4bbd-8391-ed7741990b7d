package com.z1.handler;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.application.def.Application.Artifact;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.gateway.defservice.DefinitionItem;
import udichi.gateway.defservice.DefinitionItem.State;
import z1.accountcontext.AccountContextWrapper;
import z1.accountcontext.def.AccountContextType;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.channel.ChannelDefWrapper;
import z1.channel.ChannelHandler;
import z1.channel.ChannelType;
import z1.channel.def.ChannelDef;
import z1.channel.def.CompanyType;
import z1.channel.def.KvpairDef;
import z1.commons.USession;
import z1.core.utils.Utils;
import z1.expression.ScriptObject;
import z1.template.ModuleVisitor;
import z1.template.TemplateArtifact;
import z1.template.TemplateConst;
import z1.template.TemplateConst.InstanceConfigType;
import z1.template.TemplateUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ChannelsHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    tps
  }

  private enum PostCommand
  {
    create,
    suspend,
    activate,
    update,
    delete,
    updateconfig,
    loadconfig,
    initfetch
  }

  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command = GetCommand.valueOf(cStr);

        
        switch (command)
        {
          // c3/data/channels/all
          case all:
          {
            List<Map<String, Object>> ret = new java.util.ArrayList<>(20);
            JsonMarshaller jm = new JsonMarshaller();

            // Send data for all channels created so far
            List<ChannelDefWrapper> defWrappers = ChannelDefWrapper.forceLoadAll(ctx,
                false);
            for (ChannelDefWrapper cdw : defWrappers)
            {
              // ZMOB-14959: Filter ChannelType.recurrence
              if (cdw == null
                  || TemplateUtils.isTemplateConfig(cdw.getId(),
                      ChannelDefWrapper.PREFIX)
                  || ChannelType.recurrence.equals(cdw.getType()))
              {
                continue;
              }

              if (ChannelType.none.equals(cdw.getType()))
              {
                // Old channel defs

                // The source payload is stored as a KV pair for the def
                ChannelDef cd = cdw.getDef();
                List<KvpairDef> kvs = cd.getParams();
                for (KvpairDef kv : kvs)
                {
                  if (RegistrationHandler.Fields.sourcePayload.name()
                      .equalsIgnoreCase(kv.getKey()))
                  {
                    // Get the value. This is a serialized json as a string
                    String payload = kv.getValue();
                    if (payload != null && !payload.isEmpty())
                    {
                      Map<String, Object> uit = jm.readAsMap(payload);
                      uit.put("channelId", cdw.getId());
                      uit.put("active", (cdw.getState() == State.published));
                      ret.add(uit);

                      break;
                    }
                  }
                }
              }
              else
              {
                String pl = cdw.getPayload();
                if ((pl != null) && (pl.length() > 0))
                {
                  Map<String, Object> item = jm.readAsMap(pl);
                  item.put(DefinitionItem.ID, cdw.getId());
                  item.put(DefinitionItem.Fields.state.name(),
                      cdw.getState().name());
                  ret.add(item);
                }
              }
            }

            resp.getWriter().print(jm.serialize(ret));
            break;
          }

          // c3/data/channels/tps
          // Returns the transaction number for each channel types
          case tps:
          {
//            PipelineDef pd = SystemPipelines.channelTps();
//            if (pd == null) return;
//
//            // Get all event parmas, if any
//            Map<String, Object> reqParams = new java.util.HashMap<>(10);
//            @SuppressWarnings("unchecked")
//            Enumeration<String> pNames = req.getParameterNames();
//            while (pNames.hasMoreElements())
//            {
//              String n = pNames.nextElement();
//              String v = req.getParameter(n);
//              reqParams.put(n, v);
//            }
//            EventStream es = EventStream.forCurrentValues(ctx, pd);
//            DataHolder val = es.getCurrentValue(reqParams);
//
//            if (val == null) return;
//
//            List<Map<String, Object>> data = val.data();
//            Map<String, Object> map = new java.util.HashMap<>(2);
//
//            for (Map<String, Object> d : data)
//            {
//              map.put((String) d.get("channel"), d.get("count"));
//            }
//
//            resp.getWriter().print(new JsonMarshaller().serialize(map));
            break;
          }

          default:
          {
            // noop
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/channels/create
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            Map<String, Object> map = new JsonMarshaller().readAsMap(payload);
            String name = (String) map.get(DefinitionItem.Fields.name.name());
            String description = (String) map
                .get(DefinitionItem.Fields.description.name());
            String type = (String) map.get("type");
            String subtype = (String) map.get("subtype");

            if (_isChannelExist(ctx, name, type, subtype))
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.channelCreateOrUpdateFailed,
                  "Name '" + name + "' already exists. Enter unique name.");
              resp.getWriter().print(msg.toString());
              return;
            }

            ChannelDefWrapper cdr = ChannelDefWrapper.create(ctx);
            cdr.setName(name);
            cdr.setDescription(description);
            cdr.setPayload(payload);
            cdr.setState(State.published);
            cdr.save();
            JsonMarshaller jm = new JsonMarshaller();
            map.put(DefinitionItem.ID, cdr.getId());
            map.put(DefinitionItem.Fields.state.name(), cdr.getState().name());
            resp.getWriter().print(jm.serializeMap(map));

            audit(type, subtype, ctx, cdr.getId(), name, Operations.create);

            return;
          }

          // c3/data/channels/update?id=<id>
          case update:
          {
            String id = req.getParameter("id");
            if (id == null) return;
                        
            String payload = ServletUtil.getPayload(req);
            if ((payload == null) || (payload.length() == 0)) return;

            ChannelDefWrapper cdr = ChannelDefWrapper.forceLoad(ctx, id);

            if (cdr == null) return;

            Map<String, Object> map = new JsonMarshaller().readAsMap(payload);
            String name = (String) map.get(DefinitionItem.Fields.name.name());
            String description = (String) map
                .get(DefinitionItem.Fields.description.name());

            String type = (String) map.get(ChannelHandler.Fields.type.name());
            String subtype = (String) map.get(ChannelHandler.Fields.subtype.name());
            if (!cdr.getName().equals(name)
                && _isChannelExist(ctx, name, type, subtype))
            {
              ResponseMessage msg = new ResponseMessage(ctx,
                  ResponseMessage.Status.fail,
                  ResponseMessage.Type.channelCreateOrUpdateFailed,
                  "Name '" + name + "' already exists. Enter unique name.");
              resp.getWriter().print(msg.toString());
              return;
            }
                        
            cdr.setName(name);
            cdr.setDescription(description);
            cdr.setPayload(payload);
            cdr.save();
            JsonMarshaller jm = new JsonMarshaller();
            map.put(DefinitionItem.ID, cdr.getId());
            map.put(DefinitionItem.Fields.state.name(), cdr.getState().name());
            resp.getWriter().print(jm.serializeMap(map));

            // Invalidate the compiled script to reload again 
            ScriptObject.invalidateCache(ctx,
                ScriptObject.ScriptType.channelScript, cdr.getId());
            App.notifyClearCache(ctx.getNamespace(), ChannelDefWrapper.PREFIX);

            audit(type, subtype, ctx, id, name, Operations.edit);
            return;
          }
          
          // c3/data/channels/initfetch?id=<id>
          case initfetch:
          {            
            String id = req.getParameter("id");
            if(id == null) return;
            
            String payload = ServletUtil.getPayload(req);
            
            // TODO - call back-end service
            
            final String serviceUrl = z1.commons.Utils.getPropertyValue("connector.gateway.url"); //"http://***********/process";
            USession sess = new USession(ctx);
            Map<String, String> headers = sess.getHeaderFields();
            headers.put("Content-Type", "application/json");
            sess.httpPost(serviceUrl, payload);
            
//            ResponseMessage msg = new ResponseMessage(ctx,
//                ResponseMessage.Status.success,
//                ResponseMessage.Type.channelCreateOrUpdateFailed,
//                "DataObj Received");
//            resp.getWriter().print(msg.toString());
            return;
          }
          
          // c3/data/channels/<suspend|activate>?chId=<channel-id>
          case suspend:
          case activate:
          {
            String channelId = req.getParameter("chId");
            String name = req.getParameter("chId");

            String type = "";
            String subtype = "";

            ChannelDefWrapper cdw = ChannelDefWrapper.forceLoad(ctx, channelId);
            if (cdw == null) return;
            Map<String, Object> map = new JsonMarshaller()
                .readAsMap(cdw.getPayload());
            name = (String) map.get(DefinitionItem.Fields.name.name());
            type = (String) map.get(ChannelHandler.Fields.type.name());
            subtype = (String) map.get(ChannelHandler.Fields.subtype.name());
            
            String payload2 = cdw.getPayload2();
            
            Map<String,Object> ret = TemplateUtils.getModuleConfigInstanceReferences(ctx, payload2);
            if (ret == null || !ret.containsKey("type")) return;
            TemplateConst.InstanceConfigType ict = TemplateConst.InstanceConfigType.valueOf((String) ret.get("type"));
            Set<String> refs = (Set<String>) ret.get("refs");

            ResponseMessage  msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                ResponseMessage.Type.requestProcessingDone,
                "Operation '" + command.name() + "' finished!");
            if (ict.equals(InstanceConfigType.primary))
            {             
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream().collect(Collectors.joining(", "));
                msg = new ResponseMessage(ctx, ResponseMessage.Status.success,
                    ResponseMessage.Type.requestProcessingDone,
                    "This channel is primary component of a module instance. Operation '"
                    + command.name() + 
                    "' finished and was also applied on its supportive components: "
                    + references);
              }
             
            }
            else if (ict.equals(InstanceConfigType.supportive))
            {
              if (refs != null && !refs.isEmpty())
              {
                String references = refs.stream().collect(Collectors.joining(", "));
                msg = new ResponseMessage(ctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.requestProcessingFailed,
                    "This channel is supportive config instance and still being referenced in: "
                    + references);
                resp.getWriter().print(msg.toString());
                return;
              }

            }


            Artifact art = new Artifact();
            art.setSubtype(TemplateArtifact.Subtype.channel.name());
            art.setId(channelId);
            TemplateArtifact ta = new TemplateArtifact(ctx, null, art, null,
                null, TemplateArtifact.Subtype.channel.name(), null);
            
            ModuleVisitor.VisitingOperation mvOp = null;
            Operations auditOp = null;
            if (command.equals(PostCommand.suspend))
            {
              mvOp = ModuleVisitor.VisitingOperation.suspend;
              auditOp = Operations.suspend;
            }
            else if (command.equals(PostCommand.activate))
            {
              mvOp = ModuleVisitor.VisitingOperation.resume;
              auditOp = Operations.resume;
            }

            ModuleVisitor mv = new ModuleVisitor(ctx, null, mvOp);
            mv.visit(ta);

            audit(type, subtype, ctx, channelId, name, auditOp);
            resp.getWriter().print(msg.toString());
            break;
          }
          
          // c3/data/channels/updateconfig?chId=<channel-id>
          case updateconfig:
          {
            String channelId = req.getParameter("chId");
            String name = req.getParameter("chId");
            
            String type = "";
            String subtype = "";
            Map<String, Object> data = ServletUtil.loadPayloadAsDataMap(req);
            ChannelDefWrapper cdw = ChannelDefWrapper.forceLoad(ctx, channelId);
            if (cdw != null)
            {
              Map<String, Object> map = new JsonMarshaller()
                  .readAsMap(cdw.getPayload());
              name = (String) map.get(DefinitionItem.Fields.name.name());
              type = (String) map.get(ChannelHandler.Fields.type.name());
              subtype = (String) map.get(ChannelHandler.Fields.subtype.name());
            }
            ChannelDef cd = cdw.getDef();
            CompanyType ct = cd.getCompany();
            AccountContextWrapper acw = AccountContextWrapper.load(ctx);
            AccountContextType act = acw.getDef();
            List<z1.accountcontext.def.CompanyType> cts = act.getCompany();
            int size = cts.size() - 1;
            for (int i = size; i >= 0; i--)
            {
              z1.accountcontext.def.CompanyType c = cts.get(i);
              if (c.getName().equalsIgnoreCase(ct.getName()))
              {
                c.getAliases().clear();
                c.getBrands().clear();
                c.getProducts().clear();
                c.getTopics().clear();
                String[] aliases = ((String) data.get("aliases")).split(",");
                String[] brands = ((String) data.get("brands")).split(",");
                String[] products = ((String) data.get("products")).split(",");
                String[] topics = ((String) data.get("topics")).split(",");
                HashMap<String, Boolean> aliasestemp = new HashMap<>();
                for (int j = 0; j < aliases.length; j++)
                {
                  if (!aliasestemp.containsKey(aliases[j].toLowerCase().trim()))
                  {
                    aliasestemp.put(aliases[j].toLowerCase().trim(), true);
                    c.getAliases().add(aliases[j].toLowerCase().trim());
                  }
                }
                for (int j = 0; j < brands.length; j++)
                {
                  if (!c.getBrands().contains(brands[j]))
                    c.getBrands().add(brands[j]);
                }
                for (int j = 0; j < products.length; j++)
                {
                  if (!c.getProducts().contains(products[j]))
                    c.getProducts().add(products[j]);
                }
                for (int j = 0; j < topics.length; j++)
                {
                  if (!c.getTopics().contains(topics[j]))
                    c.getTopics().add(topics[j]);
                }
                break;
              }
            }

            JsonMarshaller jm = new JsonMarshaller();
            acw.setPayload(jm.serialize(act));
            acw.save();

            audit(type, subtype, ctx, channelId, name, Operations.updateconfig);
          }
          
          // c3/data/channels/loadconfig?chId=<channel-id>
          case loadconfig:
          {
            String channelId = req.getParameter("chId");
            String name = req.getParameter("chId");

            String type = "";
            String subtype = "";
            ChannelDefWrapper cdw = ChannelDefWrapper.forceLoad(ctx, channelId);
            if (cdw != null)
            {
              Map<String, Object> map = new JsonMarshaller()
                  .readAsMap(cdw.getPayload());
              name = (String) map.get(DefinitionItem.Fields.name.name());
              type = (String) map.get("type");
              subtype = (String) map.get("subtype");
            }
            ChannelDef cd = cdw.getDef();
            CompanyType ct = cd.getCompany();
            AccountContextWrapper acw = AccountContextWrapper.load(ctx);
            AccountContextType act = acw.getDef();
            List<z1.accountcontext.def.CompanyType> cts = act.getCompany();
            z1.accountcontext.def.CompanyType channelInfoToReturn = null;

            int size = cts.size() - 1;
            for (int i = size; i >= 0; i--)
            {
              z1.accountcontext.def.CompanyType c = cts.get(i);
              if (c.getName().equalsIgnoreCase(ct.getName()))
              {
                channelInfoToReturn = c;
                break;
              }
            }
            HashMap<String, Object> hm = new HashMap<>();
            if (channelInfoToReturn != null)
            {
              hm.put("aliases", Utils.convertListToCommaSeperatedString(
                  channelInfoToReturn.getAliases()));
              hm.put("brands", Utils.convertListToCommaSeperatedString(
                  channelInfoToReturn.getBrands()));
              hm.put("products", Utils.convertListToCommaSeperatedString(
                  channelInfoToReturn.getProducts()));
              hm.put("topics", Utils.convertListToCommaSeperatedString(
                  channelInfoToReturn.getTopics()));
            }

            resp.getWriter().print(new JsonMarshaller().serialize(hm));

            audit(type, subtype, ctx, channelId, name, Operations.loadconfig);

            return;

          }
          
          // c3/data/channels/delete?chId=<channel-id>
          case delete:
          {
            String channelId = req.getParameter("chId");
            req.setAttribute("id", channelId);
            req.setAttribute("type", ChannelDefWrapper.PREFIX);
            String[] subParts = new String[pathParts.length + 1];      
            subParts[0] = ChannelDefWrapper.PREFIX;
            subParts[1] = pathParts[0];
            new BXHandler().post().handle(ctx, subParts, req, resp);
            break;            
          }
        }

      }

      // //////////////////////////////////////////////////////////////////
//      private void createChannelUrls(List<UrlInfoType> channelUrls,
//          Map<String, Object> channelUrlsMap)
//      {
//        UrlInfoType u = new UrlInfoType();
//        u.setAccessToken((String) channelUrlsMap.get("accessToken"));
//        u.setAlias((String) channelUrlsMap.get("alias"));
//        u.setChannelType((String) channelUrlsMap.get("channelType"));
//        u.setDescription((String) channelUrlsMap.get("description"));
//        u.setId((String) channelUrlsMap.get("id"));
//        u.setImageUrl((String) channelUrlsMap.get("imageUrl"));
//        u.setIsCompetitor((String) channelUrlsMap.get("isCompetitor"));
//        if (channelUrlsMap.get("likes") instanceof Integer)
//        {
//          u.setLikes(Integer.toString((Integer) channelUrlsMap.get("likes")));
//        }
//        else
//        {
//          u.setLikes((String) channelUrlsMap.get("likes"));
//        }
//        u.setName((String) channelUrlsMap.get("name"));
//        if (channelUrlsMap.get("ptat") instanceof Integer)
//        {
//          u.setPtat(Integer.toString((Integer) channelUrlsMap.get("ptat")));
//        }
//        else
//        {
//          u.setPtat((String) channelUrlsMap.get("ptat"));
//        }
//        u.setUrl((String) channelUrlsMap.get("url"));
//        u.setIndustry((String) channelUrlsMap.get("industry"));
//        channelUrls.add(u);
//
//      }

      // ....................................................................
//      private void _updateAccountContextForNewChannel(UContext ctx,
//          String payload, final HttpServletResponse resp) throws IOException
//      {
//        JsonMarshaller j = new JsonMarshaller();
//
//        Map<String, Object> channelPayload = j.readAsMap(payload);
//        List<Map<String, Object>> channelUrlsMaps = (List<Map<String, Object>>) channelPayload
//            .get("channelUrls");
//
//        List<UrlInfoType> channelUrls = new ArrayList<UrlInfoType>();
//        for (Map<String, Object> channelUrlsMap : channelUrlsMaps)
//        {
//          {
//            createChannelUrls(channelUrls, channelUrlsMap);
//          }
//        }
//
//        RegistrationHandler.createChannel(ctx, channelUrls, "false");
//
//        // after creating a new channel we add them to the Account context.
//        AccountContextWrapper acw = AccountContextWrapper.load(ctx);
//        if (acw == null)
//        {
//          resp.getWriter().print("{\"result\":\"Channel Creation Failed.\"}");
//          return;
//        }
//        AccountContextType act = acw.getDef();
//        if (act == null)
//        {
//          resp.getWriter().print("{\"result\":\"Channel Creation Failed.\"}");
//          return;
//        }
//        for (int i = 0; i < channelUrls.size(); i++)
//        {
//          UrlInfoType competitor = channelUrls.get(i);
//          // set competitor info
//          z1.accountcontext.def.CompanyType ctt = RegistrationHandler
//              .createCompanyType(competitor);
//          if (ctt != null) act.getCompany().add(ctt);
//        }
//
//        acw.setPayload(j.serialize(act));
//        acw.save();
//      }

      // ....................................................................
      private void audit(String type, String subtype, UContext ctx, String id,
          String name, Operations operation)
      {
        if (type != null)
        {
          switch (type)
          {
            case "service":
            {
              ArtifactAudit.newInstance(ctx, ItemTypes.microservice,
                  id, name, operation).save();
              break;
            }
            case "push":
            {
              if (!(subtype != null && (subtype.contains("chatbot")
                  || subtype.contains("__z1_et__") || subtype.contains("genericwebhookchannel"))))
              {
                ArtifactAudit.newInstance(ctx, ItemTypes.webhook, id,
                    name, operation).save();
              }
              break;
            }
            case "pull":
            {
              ArtifactAudit.newInstance(ctx, ItemTypes.batchcollector,
                  id, name, operation).save();
              break;
            }
            default:
            {
              break;
            }
          }
        }
      }
      /**
       * Checks channel existence for given name for a specific type and subtype
       * @param ctx
       * @param name
       * @param type
       * @param subType
       * @return
       */
      private boolean _isChannelExist(UContext ctx, String name,String type, String subType)
      {
        List<ChannelDefWrapper> defWrappers = ChannelDefWrapper.forceLoadAll(ctx,
            true);
        JsonMarshaller jm = new JsonMarshaller();
        for (ChannelDefWrapper cdw : defWrappers)
        {
          Map<String, Object> map = jm.readAsMap(cdw.getPayload());

          String nameLocal = (String) map
              .get(DefinitionItem.Fields.name.name());
          String typeLocal = (String) map.get("type");
          String subtypeLocal = (String) map.get("subtype");
          if (typeLocal.equals(type) && nameLocal.equalsIgnoreCase(name))
          {
            if (subType == null && subtypeLocal == null || (subType != null
                && subtypeLocal != null && subType.equals(subtypeLocal)))
            {
              return true;
            }

          }
        }
        return false;
      }

    };
  }

}
