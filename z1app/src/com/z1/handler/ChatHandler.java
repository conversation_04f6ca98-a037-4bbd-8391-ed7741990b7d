package com.z1.handler;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.data.Result;
import udichi.core.queue.Job;
import udichi.core.queue.Job.Priority;
import udichi.core.queue.JobQueue;
import udichi.core.queue.UnsupportedJobException;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.actions.AbstractMobileAction;
import z1.actions.AbstractMobileAction.MsgAttr;
import z1.chat.ChatConnection;
import z1.chat.ChatLog;
import z1.chat.ChatLog.Fields;
import z1.chat.ChatMediator;
import z1.chat.ChatSubject;
import z1.commons.Utils;
import z1.core.Context;
import z1.core.Profile;
import z1.core.Type;
import z1.core.Type.IndexType;
import z1.core.profile.ProfileService;
import z1.core.utils.ChatLogIterator;
import z1.core.utils.ChatSubjectIterator;
import z1.core.utils.TimeUtils;

public class ChatHandler implements CommandHandlerFactory
{
  private enum Command
  {
    // customer
    initiateChat,
    inbox,
    inboxCount,
    delete, // customer wants to deletes a chat subject
    end, // agent ends chat
    markAllRead, // customer wants to mark all chats as read.
    // agent and customer
    postFeedback, // agents asks for feedback, customer sends response to
                  // feedback

    // agent
    agentInbox, // agents needs list of subjects (the ones assigned to this
                // agent and all unassigned subjects)
    agentSelectsCustomer, // agent selects customer to chat with
    chatSession, // agent requests chat history of selected customer
    agentAvailable,
    agentUnavailable,
    post,
    resolve,
    release,
    reopen,

    // system
    chatSubjectResponse,
    create,
    update

  }

  private static final Boolean disableInfoLog = Boolean.parseBoolean(
      z1.commons.Utils.getPropertyValue("disable.detail.info.log"));

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");
        Command type = Command.valueOf(pathParts[0]);

        long startTime = System.currentTimeMillis();

        try
        {
          switch (type)
          {
            case agentInbox:
            {
              String agentId = req.getParameter("agentId");

              Context ctx = Context.getInstance(uctx);
              List<InboxResponse> inbox = new ArrayList<>();

              // 1. Get all chat subjects assigned to this agent.
              Result<ChatConnection> result = ChatConnection
                  .getChatConnectionForState(uctx, agentId,
                      ChatConnection.State.active);
              if (result != null)
              {
                Iterator<ChatConnection> itr = result.getData().iterator();
                while (itr.hasNext())
                {
                  ChatConnection chatConnection = itr.next();
                  String chatId = (String) chatConnection.getValues()
                      .get(ChatConnection.Fields.id.name());
                  String profileId = (String) chatConnection.getValues()
                      .get(ChatConnection.Fields.profileId.name());
                  int unread = (int) chatConnection.getValues()
                      .get(ChatConnection.Fields.unreadCustomerMessages.name());
                  String time = (String) chatConnection.getValues()
                      .get(ChatConnection.Fields.time.name());

                  // get other subject details from the chat connection
                  ChatSubject chatSubject = ChatSubject.findChatSubject(ctx,
                      chatId);
                  if (chatSubject == null || chatSubject.isClosed()
                      || chatSubject.isDeleted() || chatSubject.hasExpired())
                  {
                    continue;
                  }

                  InboxResponse subject = new InboxResponse();
                  subject.setCustomerName(
                      chatSubject.getValue(ChatSubject.Fields.customerName));
                  subject.setSubject(
                      chatSubject.getValue(ChatSubject.Fields.subject));
                  subject
                      .setTopic(chatSubject.getValue(ChatSubject.Fields.topic));
                  subject.setId(chatId);
                  subject.setProfileId(profileId);
                  subject.setLastUpdateTime(time);
                  subject.setUnread(unread);
                  subject.setFilter("assigned");
                  inbox.add(subject);
                }
              }

              // 2. Get all unassigned chat subjects.
              result = ChatConnection.getChatConnectionForState(uctx,
                  ChatConnection.State.waiting);
              if (result != null)
              {
                Iterator<ChatConnection> itr = result.getData().iterator();
                while (itr.hasNext())
                {
                  ChatConnection chatConnection = itr.next();
                  String chatId = (String) chatConnection.getValues()
                      .get(ChatConnection.Fields.id.name());
                  int unread = (int) chatConnection.getValues()
                      .get(ChatConnection.Fields.unreadCustomerMessages.name());
                  String time = (String) chatConnection.getValues()
                      .get(ChatConnection.Fields.time.name());
                  String profileId = (String) chatConnection.getValues()
                      .get(ChatConnection.Fields.profileId.name());

                  ChatSubject chatSubject = ChatSubject.findChatSubject(ctx,
                      chatId);
                  // get other subject details from the chat connection
                  if (chatSubject == null || chatSubject.isClosed()
                      || chatSubject.isDeleted() || chatSubject.hasExpired())
                  {
                    continue;
                  }

                  InboxResponse subject = new InboxResponse();
                  subject.setCustomerName(
                      chatSubject.getValue(ChatSubject.Fields.customerName));
                  subject.setSubject(
                      chatSubject.getValue(ChatSubject.Fields.subject));
                  subject
                      .setTopic(chatSubject.getValue(ChatSubject.Fields.topic));
                  subject.setId(chatId);
                  subject.setProfileId(profileId);
                  subject.setLastUpdateTime(time);
                  subject.setUnread(unread);
                  subject.setFilter("unassigned");
                  inbox.add(subject);
                }
              }

              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(jm.serialize(inbox));
              return;
            }

            case inbox:
            {

              Context ctx = Context.getInstance(uctx);
              String profileId = req.getParameter("profileId");
              JsonMarshaller jm = new JsonMarshaller();

              // Check if profile is unique optOut profile
              if (profileId != null
                  && profileId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
              {
                ServletHandlerResponse response = new ServletHandlerResponse();
                response.setStatus("failure");
                response.setReason("optOut profile.");
                resp.getWriter().print(jm.serialize(response));
                return;
              }

              if (profileId == null)
              {
                // Check if we have customer ID sent
                String custId = req.getParameter("id");
                if (custId != null)
                {
                  Profile p = new ProfileService(ctx).findAProfile(custId,
                      IndexType.CUSTOMERID);
                  if (p != null)
                  {
                    profileId = p.getKeyValue();
                  }
                }
              }

              if (profileId == null)
              {
                ServletHandlerResponse response = new ServletHandlerResponse();
                response.setStatus("failure");
                response.setReason("Invalid profile.");
                resp.getWriter().print(jm.serialize(response));
                return;
              }

              String client = req.getParameter("client");
              if (client == null) client = "customer"; // Assumes request was
                                                       // from
                                                       // a customer and not
                                                       // agent

              List<InboxResponse> inbox = new ArrayList<>();
              List<InboxResponse> readInbox = new ArrayList<>();
              List<InboxResponse> unreadInbox = new ArrayList<>();

              // attempt to get n rows after this number. if limit=5, after = 10
              // try to get rows 11-15
              String after = req.getParameter("after");
              // attempt to get n rows before this number. if limit=5, before =
              // 11 try to get rows 5-10
              String before = req.getParameter("before");
              String limit_s = req.getParameter("limit");

              int limit = ChatConnection.getChatSubjectRetrieveMax();
              if (limit_s != null)
              {
                limit = Integer.parseInt(limit_s);
              }

              boolean pagination = false;

              int start = 1;
              ChatSubjectIterator itr = null;
              if (after != null)
              {
                try
                {
                  start = Integer.parseInt(after) + 1;
                  itr = ChatSubject.findNextChatSubjects(ctx, profileId, start,
                      limit);
                  pagination = true;
                }
                catch (NumberFormatException e)
                {
                  ServletHandlerResponse response = new ServletHandlerResponse();
                  response.setStatus("failure");
                  response.setReason(
                      "Invalid after argument. It should be a number");
                  resp.getWriter().print(jm.serialize(response));
                  return;
                }
              }
              else if (before != null)
              {
                try
                {
                  start = Integer.parseInt(before) - 1;
                  itr = ChatSubject.findPreviousChatSubjects(ctx, profileId,
                      start, limit);
                  pagination = true;
                }
                catch (NumberFormatException e)
                {
                  ServletHandlerResponse response = new ServletHandlerResponse();
                  response.setStatus("failure");
                  response.setReason(
                      "Invalid before argument. It should be a number");
                  resp.getWriter().print(jm.serialize(response));
                  return;
                }
              }
              else
              {
                itr = ChatSubject.findNextChatSubjects(ctx, profileId, limit); // start
                                                                               // from
                                                                               // 1
              }

              int rows = 0;
              int valid = 0;
              while (itr.hasNext())
              {
                if (pagination)
                {
                  if (rows >= limit) break;
                }
                else
                {
                  if (valid >= limit) break;
                }

                ChatSubject chatSubject = itr.next();
                rows++;

                if (chatSubject == null || chatSubject.isDeleted()
                    || chatSubject.hasExpired())
                {
                  continue;
                }
                valid++;

                // get other subject details from the chat connection
                String chatId = chatSubject.getKeyValue();

                // log the count of unread messages for this subject
                String systemUnread = chatSubject
                    .getValue(ChatSubject.Fields.systemUnread);
                int unread = 0;
                if (systemUnread != null)
                {
                  unread = Integer.parseInt(systemUnread);
                }

                String time = ChatSubject
                    .getTimeFromKey(chatSubject.getKeyValue());
                // time this
                // message was
                // created
                time = String.valueOf(new TimeUtils().parseTime(time)); // time
                                                                        // in
                                                                        // ms

                ArrayList<Chat> al = new ArrayList<>();

                if (ChatConnection.isChatEnableForAgent())
                {
                  ChatConnection chatConnection = ChatConnection.load(uctx,
                      chatId);
                  if (chatConnection != null)
                  {
                    unread += (int) chatConnection.getValues()
                        .get(ChatConnection.Fields.unreadAgentMessages.name());
                    time = (String) chatConnection.getValues()
                        .get(ChatConnection.Fields.time.name());
                  }
                  // Retrieve the first chat message (along with the chat
                  // subject)
                  // and send this as part of the InboxResponse as well
                  ChatLogIterator chatLogItr = null;
                  int nDays = 180; // chat messages for last 180 days. TODO:
                                   // this will need pagination too
                  chatLogItr = ChatLog.findChatLogsForNDays(ctx, chatId, nDays);

                  while (chatLogItr.hasNext())
                  {
                    ChatLog chatLog = chatLogItr.next();
                    // String id = chatLog.getKeyValue();
                    String msg = chatLog.getValue(Fields.data);
                    String authorType = chatLog.getValue(Fields.sender);
                    String msgType = chatLog.getValue(ChatLog.Fields.type);

                    // if the device is requesting this, filter this message if
                    // it
                    // was meant for agent only
                    if (!client.equalsIgnoreCase("agent")
                        && msgType.equals(ChatLog.Type.agentInfo.name())) // device
                    {
                      continue;
                    }

                    String author = "";
                    if (authorType.equals(ChatLog.AuthorType.customer.name()))
                    {
                      author = chatLog.getValue(ChatLog.Fields.customerName);
                      if (author == null || author.isEmpty())
                      {
                        author = "Customer";
                      }
                    }
                    else if (authorType.equals(ChatLog.AuthorType.agent.name()))
                    {
                      author = "Agent";
                    }
                    else if (authorType
                        .equals(ChatLog.AuthorType.system.name()))
                    {
                      author = "System";
                    }
                    else
                    // ERROR
                    {
                      continue;
                    }

                    String z1dateFormat = chatLog.getTime();
                    long timeInMills = new TimeUtils().parseTime(z1dateFormat); // Convert
                    // this to
                    // time in
                    // milliseconds
                    String date = String.valueOf(timeInMills);
                    Chat ct = new Chat(chatId, author, authorType, date, msg,
                        msgType);

                    al.add(ct);

                    if (al.size() > 1)
                    {
                      break; // because we only want to send the first message
                    }
                  }
                }
                else
                {
                  String ttl = chatSubject
                      .getValue(ChatSubject.Fields.msg_title);
                  String authorType = chatSubject
                      .getValue(ChatSubject.Fields.msg_sender);
                  String msgType = chatSubject
                      .getValue(ChatSubject.Fields.msg_type);
                  String date = chatSubject.getValue(ChatSubject.Fields.time);
                  al.add(new Chat(chatId, "System", authorType, date, ttl,
                      msgType));
                  String msg = chatSubject
                      .getValue(ChatSubject.Fields.msg_message);
                  al.add(new Chat(chatId, "System", authorType, date, msg,
                      msgType));
                }

                InboxResponse subject = new InboxResponse();
                if (ChatConnection.isChatEnableForAgent())
                {
                  subject.setCustomerName(
                      chatSubject.getValue(ChatSubject.Fields.customerName));
                  subject
                      .setTopic(chatSubject.getValue(ChatSubject.Fields.topic));
                }

                subject.setSubject(
                    chatSubject.getValue(ChatSubject.Fields.subject));

                subject.setId(chatId);
                subject.setLastUpdateTime(time);
                subject.setChat(chatSubject.getValue(ChatSubject.Fields.chat));
                subject.setUnread(unread);

                if (!al.isEmpty() && (al.size() > 1)) // if there is a chat
                                                      // message for this
                                                      // subject
                                                      // set this here
                {
                  Chat chat = al.get(1);
                  subject.setMessage(chat.getData());
                }

                if (unread > 0)
                {
                  unreadInbox.add(subject);
                }
                else
                {
                  readInbox.add(subject);
                }
              }

              InboxResponse.InboxTimeComparator comp = new InboxResponse.InboxTimeComparator();
              Collections.sort(unreadInbox, comp);
              Collections.sort(readInbox, comp);

              inbox.addAll(unreadInbox);
              inbox.addAll(readInbox);

              Map<String, Object> result = new HashMap<>();
              Map<String, Object> paging = new HashMap<>();
              result.put("data", inbox);
              result.put("paging", paging);

              String payload = null;
              if (!pagination)
              {
                payload = jm.serialize(inbox); // backward compatible
              }
              else
              {
                // don't send next url if no data found
                String url = req.getRequestURL() + "?profileId="
                    + req.getParameter("profileId");
                int bef = 0;
                int aft = 0;
                String nextUrl = null;
                String prevUrl = null;

                if (before != null) // was on page n+1, coming to page n
                {
                  aft = Integer.parseInt(before) - 1;
                  bef = aft - rows + 1;

                  if (rows > 0 && bef > 1)
                  {
                    prevUrl = url + "&limit=" + limit + "&before=" + bef;
                  }
                  nextUrl = url + "&limit=" + limit + "&after=" + aft;
                }
                else // was on page n-1, coming to page n
                {
                  bef = Integer.parseInt(after) + 1;
                  aft = bef + rows - 1;

                  if (rows > 0 && itr.hasNext())
                  {
                    nextUrl = url + "&limit=" + limit + "&after=" + aft;
                  }
                  prevUrl = url + "&limit=" + limit + "&before=" + bef;
                }

                if (nextUrl != null) paging.put("next", nextUrl);
                if (prevUrl != null) paging.put("previous", prevUrl);

                payload = jm.serialize(result);
              }
              resp.getWriter().print(payload);

              return;
            }

            case inboxCount: // inbox count for the device
            {
              Context ctx = Context.getInstance(uctx);
              String profileId = req.getParameter("profileId");

              //Check if profile is unique optOut profile
              if (profileId != null
                  && profileId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
              {
                return;
              }

              if (profileId == null)
              {
                String deviceId = req.getParameter("deviceId");
                ProfileService ps = new ProfileService(ctx);
                Profile p = ps.findAProfile(deviceId, Type.IndexType.DEVICE);
                if (p == null)
                {
                  return;
                }
                profileId = p.getKeyValue();
              }

              int totalUnread = 0; // unread chat subjects
              int totalRead = 0; // read chat subjects
              int valid = 0;

              int limit = 100; // only look for most recent 100 messages if not
                               // specified
              String limit_s = req.getParameter("limit");
              if (limit_s != null)
              {
                limit = Integer.parseInt(limit_s);
              }

              ChatSubjectIterator itr = ChatSubject.findNextChatSubjects(ctx,
                  profileId, limit);
              while (itr.hasNext())
              {
                ChatSubject chatSubject = itr.next();
                if (chatSubject == null || chatSubject.isDeleted()
                    || chatSubject.hasExpired())
                {
                  continue;
                }
                valid++;

                // get other subject details from the chat connection
                String chatId = chatSubject.getKeyValue();

                // log the count of unread messages for this subject
                String systemUnread = chatSubject
                    .getValue(ChatSubject.Fields.systemUnread);
                int unread = 0;
                if (systemUnread != null)
                {
                  unread = Integer.parseInt(systemUnread);
                }
                if (ChatConnection.isChatEnableForAgent())
                {
                  ChatConnection chatConnection = ChatConnection.load(uctx,
                      chatId);
                  if (chatConnection != null)
                  {
                    unread += (int) chatConnection.getValues()
                        .get(ChatConnection.Fields.unreadAgentMessages.name());
                  }
              
                }
                if (unread == 0)
                {
                  totalRead++;
                }

                unread = unread > 0 ? 1 : 0; // just keep a count of 1 per chat
                                             // subject
                totalUnread += unread;
              }

              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> map = new HashMap<>();
              map.put("total", valid);
              map.put("unread", totalUnread);
              map.put("count", totalUnread);
              map.put("read", totalRead);
              resp.getWriter().print(jm.serialize(map));
              return;
            }

            case chatSession:
            {
              String chatId = req.getParameter("id");
              String client = req.getParameter("client");

              Context ctx = Context.getInstance(uctx);

              // Make sure this conversation is not already deleted
              ChatSubject chatSubject = ChatSubject.findChatSubject(ctx,
                  chatId);
              if (chatSubject == null || chatSubject.isDeleted()
                  || chatSubject.hasExpired())
              {
                resp.getWriter().print("[]");
                return;
              }

              // if agent closed the case dont show it to the agent (but
              // customer
              // still sees it)
              if (!client.equals("customer") && chatSubject.isClosed())
              {
                resp.getWriter().print("[]");
                return;
              }

              String feedback = chatSubject
                  .getValue(ChatSubject.Fields.feedback);
              ArrayList<Chat> al = new ArrayList<>();
              if (ChatConnection.isChatEnableForAgent())
              {
                int limit = 100;
                ChatLogIterator itr = ChatLog.findChatLogsForNDays(ctx, chatId,
                    limit);

                Chat feedbackChat = null; // this is a special case. we want to
                                          // show
                                          // only the last feedback chatLog not
                                          // the
                                          // prev ones
                while (itr.hasNext())
                {
                  ChatLog chatLog = itr.next();
                  // String id = chatLog.getKeyValue();
                  String msg = chatLog.getValue(Fields.data);
                  String authorType = chatLog.getValue(Fields.sender);
                  String msgType = chatLog.getValue(ChatLog.Fields.type);

                  // if the device is requesting this, filter this message if it
                  // was
                  // meant for agent only
                  if (!client.equalsIgnoreCase("agent")
                      && msgType.equals(ChatLog.Type.agentInfo.name())) // device
                  {
                    continue;
                  }

                  String author = "";
                  if (authorType.equals(ChatLog.AuthorType.customer.name()))
                  {
                    author = chatLog.getValue(ChatLog.Fields.customerName);
                    if (author == null || author.isEmpty())
                    {
                      author = "Customer";
                    }
                  }
                  else if (authorType.equals(ChatLog.AuthorType.agent.name()))
                  {
                    author = "Agent";
                  }
                  else if (authorType.equals(ChatLog.AuthorType.system.name()))
                  {
                    author = "System";
                  }
                  else
                  // ERROR
                  {
                    continue;
                  }

                  String z1dateFormat = chatLog.getTime();
                  long timeInMills = new TimeUtils().parseTime(z1dateFormat); // Convert
                  // this to
                  // time in
                  // milliseconds
                  String date = String.valueOf(timeInMills);
                  Chat ct = new Chat(chatId, author, authorType, date, msg,
                      msgType);

                  if (client.equals("customer"))
                  {
                    // if message type was feedback, check if customer has
                    // already
                    // responded to this feedback.
                    // if they have then do not show this message.
                    if (feedback != null && msgType != null
                        && msgType.equals(ChatSubject.Fields.feedback.name()))
                    {
                      if (feedback.isEmpty()) // if the customer has not
                                              // responded
                                              // to feedback
                      {
                        feedbackChat = ct;
                      }
                      continue; // customer has already responded to this
                                // feedback,
                                // dont show it on the device
                    }
                  }
                  al.add(ct);
                }
                if (feedbackChat != null)
                {
                  al.add(feedbackChat);
                }
              }
              else
              {
                String ttl = chatSubject.getValue(ChatSubject.Fields.msg_title);
                String authorType = chatSubject
                    .getValue(ChatSubject.Fields.msg_sender);
                String msgType = chatSubject
                    .getValue(ChatSubject.Fields.msg_type);
                String date = chatSubject.getValue(ChatSubject.Fields.time);
                al.add(
                    new Chat(chatId, "System", authorType, date, ttl, msgType));
                String msg = chatSubject
                    .getValue(ChatSubject.Fields.msg_message);
                al.add(
                    new Chat(chatId, "System", authorType, date, msg, msgType));
                int systemUnread = Integer.parseInt(
                    chatSubject.getValue(ChatSubject.Fields.systemUnread));
                if (client.equals("customer"))
                {
                  chatSubject.setValue(ChatSubject.Fields.systemUnread, 0);
                  chatSubject.flush();

                  String actionId = chatSubject
                      .getValue(ChatSubject.Fields.actionId);
                  // Mark interacted only once when unread flag is 1
                  if (systemUnread == 1 && actionId != null
                      && !actionId.isEmpty())
                  {
                    Map<String, Object> info = new HashMap<>();
                    info.put(MsgAttr.actionId.name(), actionId);
                    info.put(MsgAttr.actionState.name(), "interacted");
                    try
                    {
                      JobQueue jQ = JobQueue.getInstance(
                          "ActionResponseProcessor",
                          z1.actions.ActionResponseProcessor.class);
                      Job job = jQ.createJob("ActionResponseProcessor", uctx);
                      job.setPayload(new JsonMarshaller().serialize(info));
                      job.setPriority(Priority.low);
                      jQ.submit(job);
                    }
                    catch (UnsupportedJobException e)
                    {
                      uctx.getLogger(ChatHandler.class).severe(
                          "Unable to set interacted metric for appbox message:"
                              + e.getMessage());
                    }
                  }
                }
              }

              JsonMarshaller jm = new JsonMarshaller();
              resp.getWriter().print(jm.serialize(al));

              if (ChatConnection.isChatEnableForAgent())
              {
                // for the chat subjects served to client, set the messages
                // unread
                // to 0
                for (Chat c : al)
                {
                  ChatConnection chatConnection = ChatConnection.load(uctx,
                      c.id);
                  if (chatConnection != null)
                  {
                    if (client.equals("customer"))
                    {
                      // customer read messages for this chat subject
                      chatConnection.getValues().put(
                          ChatConnection.Fields.unreadAgentMessages.name(), 0);
                      chatConnection.save();

                    }
                    else if (client.equals("agent"))
                    {
                      // agent read messages for this chat subject
                      String state = (String) chatConnection.getValues()
                          .get(Fields.state.name());

                      // for unassigned chats do not mark this as read.
                      if (!state.equals(ChatConnection.State.waiting.name()))
                      {
                        chatConnection.getValues().put(
                            ChatConnection.Fields.unreadCustomerMessages.name(),
                            0);
                        chatConnection.save();
                      }
                    }
                  }
                  else // if no chat connection entry, assume a system message
                       // exists for this
                  {
                    if (client.equals("customer"))
                    {
                      chatSubject.setValue(ChatSubject.Fields.systemUnread, 0);
                      chatSubject.flush();
                    }

                  }
                }
              }
              return;
            }
            default:
              break;
          }
        }
        finally
        {
          if (!disableInfoLog)
          {
            long timeTaken = System.currentTimeMillis() - startTime;
            uctx.getLogger(getClass()).info(
                String.format("[ApBx(GET)] %s -> %d ms", type.name(), timeTaken));
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        resp.setContentType("text/plain");

        UContext uCtx = UContext.getInstance(req);
        Context ctx = Context.getInstance(uCtx);

        Command command = Command.valueOf(pathParts[0]);
        long startTime = System.currentTimeMillis();

        try
        {
          switch (command)
          {
            // A client (customer client side will always initiate a chat)
            // connects
            // to
            // server.
            // this process creates a record in the chat connection table in
            // mongo,
            // which will be
            // deleted when the chat concludes
            case initiateChat:
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              
              String profileId = (String) m
                  .get(ChatConnection.Fields.profileId.name());
              
              //Check if profile is unique optOut profile
              if (profileId != null
                  && profileId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
              {
                return;
              }
              
              // customer joins chat
              ChatMediator cm = new ChatMediator();
              Map<String, Object> z1profMap = (Map<String, Object>) m
                  .get(ChatSubject.Fields.z1_profile.name());
              String z1prof = new JsonMarshaller().serialize(z1profMap);
              String subject = (String) m
                  .get(ChatSubject.Fields.subject.name());
              String topic = (String) m.get(ChatSubject.Fields.topic.name());
              String customerToken = (String) m
                  .get(ChatConnection.Fields.customerToken.name());
              String customerName = ""; // customer name is optional
              if (m.containsKey(ChatLog.Fields.customerName.name()))
              {
                customerName = (String) m
                    .get(ChatLog.Fields.customerName.name());
              }

              Map<String, Object> inputs = new HashMap<>();
              inputs.put(ChatSubject.Fields.z1_profile.name(), z1prof);
              inputs.put(ChatConnection.Fields.profileId.name(), profileId);
              inputs.put(ChatSubject.Fields.subject.name(), subject);
              inputs.put(ChatSubject.Fields.topic.name(), topic);
              inputs.put(ChatLog.Fields.customerName.name(), customerName);
              inputs.put(ChatConnection.Fields.customerToken.name(),
                  customerToken);

              // Creates a chat connection for agent and customer to chat.
              ChatMediator.ConnectionData cd = cm.customerInitiatesNewChat(uCtx,
                  inputs);

              // This call is used to create the response for the servlet call.
              InitiateChatResponse icr = new InitiateChatResponse(
                  cd.getChatId());
              resp.getWriter().print(jm.serialize(icr));

              return;
            }

            case postFeedback:
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              ChatMediator cm = new ChatMediator();

              try
              {
                Map<String, Object> inputs = new HashMap<>();
                String id = (String) m.get(ChatConnection.Fields.id.name());
                if (id == null)
                {
                  uCtx.getLogger(getClass())
                      .severe("Feedback posted without a chat id");
                  return;
                }
                inputs.put(ChatConnection.Fields.id.name(), id);
                inputs.put(ChatLog.Fields.data.name(),
                    m.get(ChatLog.Fields.data.name()));

                // if customer responded to feedback request
                if (ChatLog.AuthorType.customer.name().equalsIgnoreCase(
                    (String) m.get(ChatLog.Fields.sender.name())))
                {
                  // Send message to agent
                  ChatConnection chatConnection = ChatConnection.load(uctx, id);
                  if (chatConnection == null)
                  {
                    // chat record not found. this case was closed by the agent
                    // or
                    // deleted by the customer?
                    uctx.getLogger(this.getClass())
                        .log("Agent may have already closed this conversation:"
                            + id);
                  }
                  else
                  {
                    cm.customerRespondsToFeedbackRequest(uCtx, inputs);
                  }
                }
                // if agent is asking for feedback
                else if (ChatLog.AuthorType.agent.name().equalsIgnoreCase(
                    (String) m.get(ChatLog.Fields.sender.name())))
                {
                  inputs.put(ChatConnection.Fields.agentId.name(),
                      m.get(ChatConnection.Fields.agentId.name()));
                  cm.agentRequestsForFeedback(uCtx, inputs);
                }

              }
              catch (UException e)
              {
                ServletHandlerResponse response = new ServletHandlerResponse();
                response.setStatus("failure");
                response.setReason(e.getMessage());
                resp.getWriter().print(jm.serialize(response));
              }

              return;
            }

            // When a chat post comes from either customer or agent.
            case post:
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              ChatMediator cm = new ChatMediator();

              try
              {
                Map<String, Object> inputs = new HashMap<>();
                String id = (String) m.get(ChatConnection.Fields.id.name());
                if (id == null)
                {
                  uCtx.getLogger(getClass())
                      .severe("Chat post without a chat id.");
                  return;
                }
                inputs.put(ChatConnection.Fields.id.name(), id);
                inputs.put(ChatLog.Fields.type.name(),
                    m.get(ChatLog.Fields.type.name()));
                inputs.put(ChatLog.Fields.data.name(),
                    m.get(ChatLog.Fields.data.name()));

                // if chat is from the customer
                if (ChatLog.AuthorType.customer.name().equalsIgnoreCase(
                    (String) m.get(ChatLog.Fields.sender.name())))
                {
                  inputs.put(ChatConnection.Fields.customerToken.name(),
                      m.get(ChatConnection.Fields.customerToken.name()));
                  inputs.put(ChatConnection.Fields.customerName.name(),
                      m.get(ChatConnection.Fields.customerName.name()));

                  // Send message to agent
                  ChatConnection chatConnection = ChatConnection.load(uctx, id);
                  if (chatConnection == null)
                  {
                    // chat record not found. this case was closed by the agent
                    // or
                    // deleted by the customer?
                    uctx.getLogger(this.getClass()).log(
                        "Attempt to reopen this case. Case was closed by the agent or deleted by the customer?:"
                            + id);
                    cm.customerReopensChat(uCtx, inputs);
                  }
                  else
                  {
                    // customers message for the agent
                    cm.customerSendsMessage(uCtx, inputs);

                    // if this message is not assigned to an agent then
                    // broadcast
                    // to all agents
                    String state = (String) chatConnection.getValues()
                        .get(Fields.state.name());
                    if (state.equals(ChatConnection.State.waiting.name()))
                    {
                      cm.notifyCustomerMessageToAllAgents(uctx, inputs,
                          AbstractMobileAction.ChatAction.data);
                    }
                  }
                }
                // if chat is from the agent
                else if (ChatLog.AuthorType.agent.name().equalsIgnoreCase(
                    (String) m.get(ChatLog.Fields.sender.name())))
                {
                  inputs.put(ChatConnection.Fields.agentId.name(),
                      m.get(ChatConnection.Fields.agentId.name()));
                  inputs.put(ChatConnection.Fields.agentToken.name(),
                      m.get(ChatConnection.Fields.agentToken.name()));
                  cm.agentSendsMessage(uCtx, inputs);
                }

              }
              catch (UException e)
              {
                ServletHandlerResponse response = new ServletHandlerResponse();
                response.setStatus("failure");
                response.setReason(e.getMessage());
                resp.getWriter().print(jm.serialize(response));
              }

              return;
            }
            case agentAvailable:
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              String agentId = (String) m
                  .get(ChatConnection.Fields.agentId.name());
              String agentToken = (String) m
                  .get(ChatConnection.Fields.agentToken.name());

              ChatMediator cm = new ChatMediator();
              agentToken = cm.agentAvailable(uCtx, agentId, agentToken);
              resp.getWriter().print("{\"agentToken\":\"" + agentToken + "\"}");

              return;
            }
            case agentUnavailable:
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              String agentId = (String) m
                  .get(ChatConnection.Fields.agentId.name());

              ChatMediator cm = new ChatMediator();
              cm.agentUnavailable(uCtx, agentId);
              resp.getWriter().print("{\"Result\":\" Success\"}");

              return;
            }
            // When an agent picks up a chat,
            // a request comes to backend to establish the communication
            // channel.
            case agentSelectsCustomer:
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              ChatMediator cm = new ChatMediator();
              String agentId = (String) m
                  .get(ChatConnection.Fields.agentId.name());
              String agentToken = (String) m
                  .get(ChatConnection.Fields.agentToken.name());
              String id = (String) m.get(ChatConnection.Fields.id.name());

              // finds the chat connection with the specified id.
              // updates the agent id and token in the chat connection table.
              ServletHandlerResponse response = new ServletHandlerResponse();
              try
              {
                cm.agentSelectsCustomer(uCtx, id, agentId, agentToken);
                response.setStatus("success");
              }
              catch (UException e)
              {
                response.setStatus("failure");
                response.setReason(e.getMessage());
              }
              resp.getWriter().print(jm.serialize(response));

              return;
            }

            case end: // c3/api/chat/close (agent closes chat)
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              String chatId = (String) m.get(ChatConnection.Fields.id.name());
              ServletHandlerResponse response = new ServletHandlerResponse();
              try
              {
                ChatMediator cm = new ChatMediator();
                cm.agentEndsChat(uctx, chatId);
              }
              catch (UException e)
              {
                response.setStatus("success");
                response.setReason(e.getMessage());
              }
              resp.getWriter().print(jm.serialize(response));
              return;
            }

            case delete: // c3/api/chat/delete (customer deletes chat)
            {
              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              String chatId = (String) m.get(ChatConnection.Fields.id.name());
              ServletHandlerResponse response = new ServletHandlerResponse();
              try
              {
                ChatMediator cm = new ChatMediator();
                cm.customerDeletesChat(uctx, chatId);
              }
              catch (UException e)
              {
                response.setStatus("success");
                response.setReason(e.getMessage());
              }
              resp.getWriter().print(jm.serialize(response));
              return;
            }
            case markAllRead:
            {

              String payload = ServletUtil.getPayload(req);
              JsonMarshaller jm = new JsonMarshaller();
              Map<String, Object> m = jm.readAsMap(payload);
              ServletHandlerResponse response = new ServletHandlerResponse();

              try
              {
                String profileId = (String) m
                    .get(ChatConnection.Fields.profileId.name());
                
                //Check if profile is unique optOut profile
                if (profileId != null
                    && profileId.equalsIgnoreCase(Profile.OPTOUT_PROFILE_ID))
                {
                  return;
                }
                
                if (profileId == null)
                {
                  String deviceId = (String) m.get("deviceId");
                  ProfileService ps = new ProfileService(ctx);
                  Profile p = ps.findAProfile(deviceId, Type.IndexType.DEVICE);
                  if (p == null)
                  {
                    uCtx.getLogger(getClass())
                        .severe("markAllRead without profileId.");
                    return;
                  }
                  profileId = p.getKeyValue();
                }

                int limit = 100; // only look for most recent 100 messages if
                                 // not
                                 // specified
                String limit_s = (String) m.get("limit");
                if (limit_s != null)
                {
                  limit = Integer.parseInt(limit_s);
                }

                ChatSubjectIterator itr = ChatSubject.findNextChatSubjects(ctx,
                    profileId, limit);
                while (itr.hasNext())
                {
                  ChatSubject chatSubject = itr.next();
                  if (chatSubject == null || chatSubject.isDeleted()
                      || chatSubject.hasExpired())
                  {
                    continue;
                  }

                  // get other subject details from the chat connection
                  String chatId = chatSubject.getKeyValue();
                  ChatConnection chatConnection = ChatConnection.load(uctx,
                      chatId);
                  if (chatConnection != null)
                  {
                    // if unreadAgentMessage is not available skip
                    if (!chatConnection.getValues().containsKey(
                        ChatConnection.Fields.unreadAgentMessages.name()))
                    {
                      continue;
                    }
                    // if unread is 1 then make it 0
                    if ((int) chatConnection.getValues().get(
                        ChatConnection.Fields.unreadAgentMessages.name()) > 0)
                    {
                      chatConnection.getValues().put(
                          ChatConnection.Fields.unreadAgentMessages.name(), 0);
                      chatConnection.save();
                    }
                  }
                  else // if no chat connection entry, assume a system message
                  // exists for this
                  {
                    if (chatSubject
                        .getValue(ChatSubject.Fields.systemUnread) != null
                        && Integer.parseInt(chatSubject
                            .getValue(ChatSubject.Fields.systemUnread)) > 0)
                    {
                      chatSubject.setValue(ChatSubject.Fields.systemUnread, 0);
                      chatSubject.flush();
                    }
                  }
                }

              }
              catch (UException e)
              {
                response.setStatus("success");
                response.setReason(e.getMessage());
              }
              resp.getWriter().print(jm.serialize(response));

              return;
            }
            /*
             * System response to a new chat subject is set in the UI. Customer
             * sees this response when a new chat subject is created.
             */
            case chatSubjectResponse:
            {
              String crudOp = pathParts[1];
              // c3/data/chatSubjectResponse/create => payload has the
              // definition
              switch (crudOp)
              {
                /*
                 * case create: { // Create a channel by loading the payload
                 * from the request String payload =
                 * ServletUtil.getPayload(req);
                 * 
                 * CustomConfig chatResponse = CustomConfig.create(ctx);
                 * chatResponse.setPayload(payload); chatResponse.save();
                 * 
                 * Map<String, Object> map = new java.util.HashMap<>();
                 * map.put(DefinitionItem.ID, chatResponse.getId());
                 * 
                 * resp.getWriter().print(new
                 * JsonMarshaller().serializeMap(map)); return; } //
                 * c3/data/chatSubjectResponse/update/<id> => payload has the
                 * definition case update: { String id = pathParts[2]; String
                 * payload = ServletUtil.getPayload(req);
                 * 
                 * CustomConfig chatResponse = CustomConfig.load(ctx, id);
                 * chatResponse.setPayload(payload); chatResponse.save();
                 * 
                 * return; } // c3/data/chatSubjectResponse/delete/<id> case
                 * delete: { String id = pathParts[2]; CustomConfig.delete(ctx,
                 * id); return; }
                 */   
                default:
                  break;
              }
              break;
            }
            default:
              break;
          }
        }
        finally
        {
          if (!disableInfoLog)
          {
            long timeTaken = System.currentTimeMillis() - startTime;
            uctx.getLogger(getClass()).info(
                String.format("[ApBx(POST)] %s -> %d ms", command.name(), timeTaken));
          }
        }
      }

    };
  }

  private class InitiateChatResponse
  {
    private String _id;
    List<Data> data = new ArrayList<Data>();

    InitiateChatResponse(String id)
    {
      _id = id;
    }

    public List<Data> getData()
    {
      return data;
    }

    public String getId()
    {
      return _id;
    }

    public void setId(String id)
    {
      this._id = id;
    }
  }

  private class Data
  {
    private String _id;
    private String _msg;
    private String _sender;
    private String _timeStamp;

    Data(String id, String msg, String sender, String type, String timeStamp)
    {
      setId(id);
      setMsg(msg);
      setSender(sender);
      setTimeStamp(timeStamp);
    }

    public String getId()
    {
      return _id;
    }

    public void setId(String _id)
    {
      this._id = _id;
    }

    public String getMsg()
    {
      return _msg;
    }

    public void setMsg(String _msg)
    {
      this._msg = _msg;
    }

    public String getSender()
    {
      return _sender;
    }

    public void setSender(String _sender)
    {
      this._sender = _sender;
    }

    public String getTimeStamp()
    {
      return _timeStamp;
    }

    public void setTimeStamp(String _timeStamp)
    {
      this._timeStamp = _timeStamp;
    }
  }

  private class ChatConnectionData
  {
    private String id;
    private String subject;
    private String customerName;
    private String deviceModel;
    private String deviceOS;

    public ChatConnectionData(String id, String topic, String customerName)
    {
      this.id = id;
      this.subject = topic;
      this.customerName = customerName;
      this.deviceModel = deviceModel;
      this.deviceOS = deviceOS;
    }

    public String getId()
    {
      return id;
    }

    public void setId(String id)
    {
      this.id = id;
    }

    public String getSubject()
    {
      return subject;
    }

    public void setSubject(String subject)
    {
      this.subject = subject;
    }

    public String getCustomerName()
    {
      return customerName;
    }

    public void setCustomerName(String customerName)
    {
      this.customerName = customerName;
    }

    public String getDeviceModel()
    {
      return deviceModel;
    }

    public void setDeviceModel(String deviceModel)
    {
      this.deviceModel = deviceModel;
    }

    public String getDeviceOS()
    {
      return deviceOS;
    }

    public void setDeviceOS(String deviceOS)
    {
      this.deviceOS = deviceOS;
    }
  }

  private class Chat
  {
    String id = "";
    String sender = "";
    String authorType = "";
    String profileUrl = "";
    String avatar = "";
    String time = "";
    String data = "";
    String type = "";

    public Chat(String id, String author, String authorType, String date,
        String msg, String type)
    {
      super();
      this.id = id;
      this.sender = author;
      this.authorType = authorType;
      this.time = date;
      this.data = msg;
      this.type = type; // type of chat message (not the content type)
    }

    public String getType()
    {
      return type;
    }

    public void setType(String type)
    {
      this.type = type;
    }

    public String getId()
    {
      return id;
    }

    public void setId(String id)
    {
      this.id = id;
    }

    public String getSender()
    {
      return sender;
    }

    public void setSender(String sender)
    {
      this.sender = sender;
    }

    public String getAuthorType()
    {
      return authorType;
    }

    public void setAuthorType(String authorType)
    {
      this.authorType = authorType;
    }

    public String getProfileUrl()
    {
      return profileUrl;
    }

    public void setProfileUrl(String profileUrl)
    {
      this.profileUrl = profileUrl;
    }

    public String getAvatar()
    {
      return avatar;
    }

    public void setAvatar(String avatar)
    {
      this.avatar = avatar;
    }

    public String getTime()
    {
      return time;
    }

    public void setTime(String time)
    {
      this.time = time;
    }

    public String getData()
    {
      return data;
    }

    public void setData(String data)
    {
      this.data = data;
    }

  }

  private static class InboxResponse
  {
    public String getCustomerName()
    {
      return customerName;
    }

    public void setProfileId(String profileId)
    {
      this.profileId = profileId;
    }

    public void setFilter(String assigned)
    {
      this.filter = assigned;
    }

    public void setCustomerName(String customerName)
    {
      this.customerName = customerName;
    }

    public String getSubject()
    {
      return subject;
    }

    public void setSubject(String subject)
    {
      this.subject = subject;
    }

    public String getTopic()
    {
      return topic;
    }

    public void setTopic(String topic)
    {
      this.topic = topic;
    }

    public String getLastUpdateTime()
    {
      return lastUpdateTime;
    }

    public void setLastUpdateTime(String lastUpdateTime)
    {
      this.lastUpdateTime = lastUpdateTime;
    }

    public String getProfileId()
    {
      return profileId;
    }

    public String getId()
    {
      return id;
    }

    public void setId(String id)
    {
      this.id = id;
    }

    public int getUnread()
    {
      return unread;
    }

    public void setUnread(int unread)
    {
      this.unread = unread;
    }

    public String getFilter()
    {
      return this.filter;
    }

    public String getChat()
    {
      return chat;
    }

    // 1st message in this conversation
    public String getMessage()
    {
      return message;
    }

    public void setChat(String chat)
    {
      this.chat = chat;
    }

    public void setMessage(String message)
    {
      this.message = message;
    }

    String customerName = "";
    String subject;
    String topic;
    String lastUpdateTime;
    String id;
    String filter;
    int unread;
    String profileId;
    String chat;
    String message;

    public static class InboxTimeComparator implements Comparator<InboxResponse>
    {
      @Override
      public int compare(InboxResponse obj1, InboxResponse obj2)
      {
        return obj2.lastUpdateTime.compareTo(obj1.lastUpdateTime);
      }
    }

  }

}
