package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.data.DataObject;
import udichi.core.data.Result;
import udichi.core.util.JsonMarshaller;
import udichi.gateway.defservice.DefinitionItem;

/**
 * handles servlet end point for predictive models.
 */
public class PModelHandler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        if (command == GetCommand.all)
        {
          // We will call the .../ml/pipeline/all 
          MLHandler mh = new MLHandler();
          String parts[] = {"pipeline", "all"};
          mh.get().handle(uctx, parts, req, resp);
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    // TODO Auto-generated method stub
    return null;
  }

  
}
