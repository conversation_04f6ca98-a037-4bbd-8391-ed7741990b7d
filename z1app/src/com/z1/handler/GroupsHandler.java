package com.z1.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.ServletUtil;
import z1.commons.Utils;

public class Groups<PERSON><PERSON>ler implements CommandHandlerFactory
{
  private enum GetCommand
  {
    all,
    id
  }
  
  
  private enum PostCommand
  {
    create,
    update,
    delete
  }
  
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
          // c3/data/groups/<group-id>
          String cStr = pathParts[0];
          GetCommand command = GetCommand.valueOf(cStr);         

        switch (command)
        {
          // c3/data/groups/all => Sends all groups
          case all:
          {

            resp.getWriter().print("["
              + "{"
                  + "\"name\": \"analyst\","
                  + " \"id\": \"_g_1\","
                  + "\"roles\": ["
                    + "\"admin\","
                    + "\"developer\""
                  + "],"
                  + "\"acl\": {"
                  + "\"any\": [],"
                  + "\"none\": [],"
                  + "\"campaign\": [\"view\", \"edit\", \"delete\", \"publish\"],"
                  + "\"segment\": []"
                  + "}"
              + "},"
              + "{"
                + "\"name\": \"developer\","
                + " \"id\": \"_g_2\","
                + "\"roles\": ["
                  + "\"admin\","
                  + "\"developer\""
                + "],"
                + "\"acl\": {"
                + "\"any\": [],"
                + "\"none\": [],"
                + "\"campaign\": [\"view\", \"edit\", \"delete\", \"publish\"],"
                + "\"segment\": []"
                + "}"
              + "}"
              +"]");

            return;
          }
          // c3/data/groups/id?id=<group-id>
          case id:
          {
              // This must be a request for a specific goal payload.
              String sId = req.getParameter("id");              
              // get the goal payload
              //GroupPart s = GroupPart.load(ctx, sId);
              //String payload = new JsonMarshaller().serialize(s.getDef());
              //resp.getWriter().print(payload);

              return;
          }
        }

      }
    };

  }

  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/groups/create => payload has the definition
          // returns <group id>
          case create:
          {
            // Create a group by loading the payload from the request
            String payload = ServletUtil.getPayload(req);

//            GroupPart s = GroupPart.create(ctx);
//            s.setPayload(payload);
//            s.save();
//            String sid = s.getId();
//
//            resp.getWriter().print("{ \"id\": \""+sid+"\" }");
            break;
          }
          // c3/data/groups/update/<group-id> => payload has the definition
          case update:
          {
            String sid = pathParts[1];
//            String payload = ServletUtil.getPayload(req);
//
//            GroupPart s = GroupPart.load(ctx, sid);
//            s.setPayload(payload);
//            s.save();

            break;
          }
          // c3/data/groups/delete/<group-id>
          case delete:
          {
            String sid = pathParts[1];
//            GroupPart.delete(ctx, sid);
            break;
          }
        }
        
//        App.notifyClearCache(ctx.getNamespace(), GroupPart.PREFIX);

      }
    };

  }

  // /////////////////////////////////////////////////////////////////


}
