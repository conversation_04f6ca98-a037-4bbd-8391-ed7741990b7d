 package com.z1.handler;

 import com.z1.CommandHandler;
 import com.z1.CommandHandlerFactory;
 import udichi.core.UContext;
 import udichi.core.data.Result;
 import udichi.core.util.JsonMarshaller;
 import udichi.core.util.ServletUtil;
 import z1.c3.Journey.ExeMode;
 import z1.stats.DeviceSegmentStatsProcessor;
 import z1.stats.EventStatsProcessor;
 import z1.stats.ExperienceDailyStatsRecorder;
 import z1.stats.ROIPricing;
 import z1.stats.ROIStatsProcessor;
 import z1.stats.StatsProcessor;
 import z1.stats.metric.AnalyticMetric;
 import z1.stats.metric.AnalyticMetric.TimeUnit;
 import z1.stats.metric.TimeRequest;

 import javax.servlet.http.HttpServletRequest;
 import javax.servlet.http.HttpServletResponse;
 import java.util.ArrayList;
 import java.util.HashMap;
 import java.util.HashSet;
 import java.util.List;
 import java.util.Map;
 import java.util.Set;

/**
 * Handles the custom audience REST apis.
 */
public class StatsHandler implements CommandHandlerFactory
{
  public static final String SEP = "|";

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() 
    {
      @Override
      public void handle(UContext ctx, String[] pathParts,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        String subCmd = pathParts[0];
        
        //c3/data/insights/stats/actionsPricing
        if("actionsPricing".equalsIgnoreCase(subCmd))
        {
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          ROIStatsProcessor p = new ROIStatsProcessor(uctx);
          String payload = p.getPricingForAllActions();
          resp.getWriter().print(payload);  
          return;
        }
        
        String subCmd1 = pathParts[1];
        
        int nDays = -1;
        String startDate = null;
        String endDate = null;
        if ("queryDays".equalsIgnoreCase(subCmd1))
        {
          // get the number of days
          String days = req.getParameter("days");
          if (days == null) return;
          nDays = Integer.parseInt(days);
        }
        else if("queryTimeRange".equalsIgnoreCase(subCmd1))
        {
          startDate = req.getParameter("startDate");
          if (startDate == null)
          {
            startDate = req.getParameter("f");
          }
          if (startDate == null) return;
          
          endDate = req.getParameter("endDate");
          if (endDate == null)
          {
            endDate = req.getParameter("t");
          }
          if (endDate == null) return;
         
          if(startDate.compareTo(endDate)>0) return;      
        }
        
        //c3/data/insights/stats/totalStats/queryDays?days="<n>"
        if("totalStats".equalsIgnoreCase(subCmd))
        {
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          StatsProcessor b = new StatsProcessor(uctx);
          String payload = b.totalStats(nDays, startDate, endDate);
          resp.getWriter().print(payload);  
          return;
        }
        
        // get the topk topics
        int topK=-1;
        String top = req.getParameter("topK");
        if (top != null)
        {
          topK = Integer.parseInt(top);
        }
        else
        {
          topK = 3; //top 3 goals or segments
        }
        
        //c3/data/insights/stats/actions/queryDays?days="<n>"
        if("actions".equalsIgnoreCase(subCmd))
        {
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          StatsProcessor b = new StatsProcessor(uctx);
          String payload = b.actionStatsPayload(nDays,startDate,endDate,topK);        
          resp.getWriter().print(payload);          
        }
        //c3/data/insights/stats/actionGoal/queryDays?days="<n>"&actionType<msg/chat/kb>&topK=3
        //c3/data/insights/stats/actionGoal/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"  
        else if("actionGoal".equalsIgnoreCase(subCmd))
        {      
          String actionType = req.getParameter("actionType");
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          StatsProcessor b = new StatsProcessor(uctx);
          String payload = b.goalActionStatsPayload(nDays,startDate,endDate,topK,actionType);        
          resp.getWriter().print(payload);
        }
        else if("actionSegment".equalsIgnoreCase(subCmd)) 
        {           
          String actionType = req.getParameter("actionType");
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          StatsProcessor b = new StatsProcessor(uctx);
          String payload = b.goalSegmentStatsPayload(nDays,startDate,endDate,topK,actionType);
          resp.getWriter().print(payload);     
        }
        else if("events".equalsIgnoreCase(subCmd)) 
        {
          String eventType = req.getParameter("eventType");
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          EventStatsProcessor b = new EventStatsProcessor(uctx);
          String payload = b.eventStats(nDays,startDate,endDate,topK,eventType);
          resp.getWriter().print(payload);      
        }
        //c3/data/insights/stats/eventsDeviceSegment/queryDays?days="<n>"&include="os,.."&exclude=".."&eventType=".." //all events if no event type 
        //c3/data/insights/stats/eventsDeviceSegment/queryDays?days="<n>"&exclude="..,.." //all if no include is specified, except the ones in exclude        
        else if("eventsDeviceSegment".equalsIgnoreCase(subCmd)) 
        {
          String segIncludeCSV = req.getParameter("include");
          String segExcludeCSV = req.getParameter("exclude");
          String eventType = req.getParameter("eventType");
          Set<String> includes = new HashSet<String>();
          Set<String> excludes = new HashSet<String>();
          if(segIncludeCSV!=null) for(String s:segIncludeCSV.split(",")) includes.add(s);
          if(segExcludeCSV!=null) for(String s:segExcludeCSV.split(",")) excludes.add(s);
       
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());      
          DeviceSegmentStatsProcessor b = new DeviceSegmentStatsProcessor(uctx);
          String payload = b.eventStats(nDays,startDate,endDate,topK,eventType,includes,excludes);
          resp.getWriter().print(payload);      
        }
        //c3/data/insights/stats/roi/queryDays?days="<n>" (all actions)
        //c3/data/insights/stats/roi/queryDays?days="<n>"&actionType="&topK=2
        else if("roi".equalsIgnoreCase(subCmd)) 
        {
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());                
          ROIStatsProcessor b = new ROIStatsProcessor(uctx);
          String payload = b.roiStats(nDays,startDate,endDate,topK);
          resp.getWriter().print(payload);          
        }
        //c3/data/insights/stats/roiGoal/queryDays?days="<n>" (all actions)
        //c3/data/insights/stats/roiGoal/queryDays?days="<n>"&actionType="&topK=2
        else if("roiGoal".equalsIgnoreCase(subCmd))
        {
          String actionType = req.getParameter("actionType");
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());                
          ROIStatsProcessor b = new ROIStatsProcessor(uctx);
          String payload = b.roiGoalStats(nDays,startDate,endDate,topK,actionType);          
       
          resp.getWriter().print(payload);        
        }
        //c3/data/insights/stats/roiSegment/queryDays?days="<n>" (all actions)
        //c3/data/insights/stats/roiSegment/queryDays?days="<n>"&actionType="type"&topK=2
        else if("roiSegment".equalsIgnoreCase(subCmd))
        {
          String actionType = req.getParameter("actionType");
          UContext uctx = UContext.getInstance().setNamespace(ctx.getNamespace());                
          ROIStatsProcessor b = new ROIStatsProcessor(uctx);
          String payload = b.roiSegmentStats(nDays,startDate,endDate,topK,actionType);           
          resp.getWriter().print(payload);           
        }
        // c3/data/insights/stats/actionsOverviewSummary/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("actionsOverviewSummary".equalsIgnoreCase(subCmd))
        {
          List<Map<String, Object>> items = ExperienceDailyStatsRecorder
              .getActionsOverviewSummary(ctx, startDate, endDate,
                  ExeMode.live);
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/compareActionsOverviewByDay/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("compareActionsOverviewByDay".equalsIgnoreCase(subCmd))
        {
          boolean excludePreviousPeriod = Boolean.parseBoolean(
              req.getParameter("excludePrev"));
          List<Map<String, Object>> items = ExperienceDailyStatsRecorder
              .getActionsOverviewByDayComparedToPreviousTimePeriod(ctx,
                  startDate, endDate, ExeMode.live, excludePreviousPeriod);
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/eventsOverviewSummary/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("eventsOverviewSummary".equalsIgnoreCase(subCmd))
        {
          EventStatsProcessor eventStatsProcessor = new EventStatsProcessor(
              ctx);
          List<Map<String, Object>> items = eventStatsProcessor
              .getEventsOverviewSummary(startDate, endDate);
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/compareEventsOverviewByDay/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("compareEventsOverviewByDay".equalsIgnoreCase(subCmd))
        {
          boolean excludePreviousPeriod = Boolean.parseBoolean(
              req.getParameter("excludePrev"));
          EventStatsProcessor eventStatsProcessor = new EventStatsProcessor(
              ctx);
          List<Map<String, Object>> items = eventStatsProcessor
              .getEventsOverviewByDayComparedToPreviousTimePeriod(startDate,
                  endDate, excludePreviousPeriod);
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/uniqueUsersOverviewSummary/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("uniqueUsersOverviewSummary".equalsIgnoreCase(subCmd))
        {
          TimeRequest timeRequest = new TimeRequest(TimeUnit.day, startDate, endDate, false);
          Double average = AnalyticMetric.getAverage(ctx, AnalyticMetric.Type.uniqueUsers, timeRequest);
          Long roundedAverage = Math.round(average);
          List<Map<String, Object>> items = new ArrayList<>(1);
          Map<String, Object> averageMap = new HashMap<>(1);
          averageMap.put("avgUniqueUsers", roundedAverage);
          items.add(averageMap);

          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/compareUniqueUsersOverviewByDay/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("compareUniqueUsersOverviewByDay".equalsIgnoreCase(subCmd))
        {
          boolean excludePreviousPeriod = Boolean.parseBoolean(
              req.getParameter("excludePrev"));
          boolean includePreviousTimePeriod = !excludePreviousPeriod;
          TimeRequest timeRequest = new TimeRequest(TimeUnit.day, startDate, endDate, includePreviousTimePeriod);
          List<Map<String, Object>> items = AnalyticMetric.getStats(ctx, AnalyticMetric.Type.uniqueUsers, timeRequest);
          
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
       // c3/data/insights/stats/conversionsOverviewSummary/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("conversionsOverviewSummary".equalsIgnoreCase(subCmd))
        {
          TimeRequest timeRequest = new TimeRequest(TimeUnit.day, startDate, endDate, false);
          Double average = AnalyticMetric.getAverage(ctx, AnalyticMetric.Type.conversions, timeRequest);
          Long roundedAverage = Math.round(average);
          List<Map<String, Object>> items = new ArrayList<>(1);
          Map<String, Object> averageMap = new HashMap<>(1);
          averageMap.put("avgConversions", roundedAverage);
          items.add(averageMap);

          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/compareConversionsOverviewByDay/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("compareConversionsOverviewByDay".equalsIgnoreCase(subCmd))
        {
          boolean excludePreviousPeriod = Boolean.parseBoolean(
              req.getParameter("excludePrev"));
          boolean includePreviousTimePeriod = !excludePreviousPeriod;
          TimeRequest timeRequest = new TimeRequest(TimeUnit.day, startDate, endDate, includePreviousTimePeriod);
          List<Map<String, Object>> items = AnalyticMetric.getStats(ctx, AnalyticMetric.Type.conversions, timeRequest);
          
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/revenuePerVisitorOverviewSummary/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("revenuePerVisitorOverviewSummary".equalsIgnoreCase(subCmd))
        {
          TimeRequest timeRequest = new TimeRequest(TimeUnit.day, startDate, endDate, false);
          Double average = AnalyticMetric.getAverage(ctx, AnalyticMetric.Type.revenuePerVisitor, timeRequest);
          // TODO trim and round the average for rpv?
          List<Map<String, Object>> items = new ArrayList<>(1);
          Map<String, Object> averageMap = new HashMap<>(1);
          averageMap.put("avgRevenuePerVisitor", average);
          items.add(averageMap);

          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
        // c3/data/insights/stats/compareRevenuePerVisitorOverviewByDay/queryTimeRange?startDate="<YYYYMMdd>"&endDate="<YYYYMMdd>"
        else if ("compareRevenuePerVisitorOverviewByDay".equalsIgnoreCase(subCmd))
        {
          boolean excludePreviousPeriod = Boolean.parseBoolean(
              req.getParameter("excludePrev"));
          boolean includePreviousTimePeriod = !excludePreviousPeriod;
          TimeRequest timeRequest = new TimeRequest(TimeUnit.day, startDate, endDate, includePreviousTimePeriod);
          List<Map<String, Object>> items = AnalyticMetric.getStats(ctx, AnalyticMetric.Type.revenuePerVisitor, timeRequest);
          
          Map<String, Object> result = new HashMap<>();
          result.put("identifier", "id");
          result.put("items", items);
          JsonMarshaller jm = new JsonMarshaller();
          resp.getWriter().print(jm.serialize(result));
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext ctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        if (pathParts.length == 0) return;
        String subCmd = pathParts[0];

        // Changes the assignment of a journey item to other journey bucket(s)
        // c3/data/insights/stats/actionsPricing
        if ("actionsPricing".equalsIgnoreCase(subCmd))
        {
          Map<String, Object> data = ServletUtil.loadPayloadAsDataMap(req);
          List<Map<String,Object>> actionPriceList = (List<Map<String,Object>>) data.get("data");
          for(Map<String,Object> map: actionPriceList)
          {
            String action = (String) map.get("name");
            String topPrice = (String) map.get("topPrice");
            String bottomPrice = (String) map.get("bottomPrice");
            
            Result<ROIPricing> result = ROIPricing.getPricingForGroup(ctx, action);
            ROIPricing pricing = null;
            if(result.isNull())
            {
              //new record
              pricing = ROIPricing.newInstance(ctx);
            }
            else
            {
              pricing = result.getData().iterator().next();
               
            }
            pricing.getValues().put(ROIPricing.Fields.action.name(),action);
            pricing.getValues().put(ROIPricing.Fields.topPrice.name(),topPrice);
            pricing.getValues().put(ROIPricing.Fields.bottomPrice.name(),bottomPrice);
            pricing.save();           
          }
        }       
      }
    };
  }
}


