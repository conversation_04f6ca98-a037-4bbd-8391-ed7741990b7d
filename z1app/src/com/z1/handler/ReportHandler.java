package com.z1.handler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;

import udichi.core.UContext;
import udichi.core.util.JsonMarshaller;
import z1.singletonservices.EmailReportsService;
import z1.singletonservices.EmailReportsService.Report;

public class ReportHandler implements CommandHandlerFactory
{
  public enum GetCommand
  {
    emailReport
  }

  public enum PostCommand
  {
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(UContext ctx, String[] pathElemets,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        JsonMarshaller jm = new JsonMarshaller();
        if (pathElemets.length > 0)
        {
          String cStr = pathElemets[0];
          GetCommand command = GetCommand.valueOf(cStr);
          switch (command)
          {
            // c3/data/report/emailReport
            case emailReport:
            {
              // This should be a report defined in EmailReportsService.
              String emailReport = req.getParameter("report");
              Report report = null;
              try
              {
                report = Report.valueOf(emailReport);
              }
              catch (IllegalArgumentException ex)
              {
                resp.getWriter().print("Invalid report");
                return;
              }
              // Set next run time to 0 so it will trigger sending out the
              // report
              // the next time the server checks when the report should run.
              EmailReportsService.getInstance().setNextRunTime(ctx, report, 0L);
              resp.getWriter().print("success");
              return;
            }
            default:
            {
              break;
            }
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(UContext ctx, String[] pathElemets,
          HttpServletRequest req, HttpServletResponse resp) throws Exception
      {
        if (z1.commons.Utils.isMarketPlace)
        {
          return;
        }

        JsonMarshaller jm = new JsonMarshaller();
        if (pathElemets.length > 0)
        {
          String cStr = pathElemets[0];
          PostCommand command = PostCommand.valueOf(cStr);
          switch (command)
          {
            // No post endpoints
          }
        }
      }
    };
  }

}
