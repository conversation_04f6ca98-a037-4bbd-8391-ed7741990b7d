package com.z1.handler;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.queue.Job;
import udichi.core.queue.JobQueue;
import udichi.core.util.JsonMarshaller;
import z1.commons.Normalizer;
import z1.imagescanner.ImageUploadWorker;
import z1.kb.ContentStore;
import z1.kb.KBDocStatus;
import z1.kb.KBDocumentRetreiver;
import z1.kb.KBProcessor;
import z1.kb.KBResponse;
import z1.kb.ZineoneStoreHandler;

public class ImageDataHandler implements CommandHandlerFactory
{
  private enum Command
  {
    renameImage,
    multipleUpload,
    singleUpload
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String contentId = pathParts[0];
        if (uctx.getNamespace() == null)
        {
          uctx.setNamespace(req.getParameter("namespace"));
        }
        KBResponse response;
        try 
        {
          response = KBDocumentRetreiver.getContent(uctx, contentId);
        }
        catch (Exception e)
        {
          resp.getWriter()
              .print(errorMessage(uctx, ResponseMessage.Type.resourceNotFound));
          return;
        }
        if (!"image".equals(response.getContentType().name()))
        {
          resp.getWriter()
              .print(errorMessage(uctx, ResponseMessage.Type.docIsNotImage));
          return;
        }
        else
        {
          byte[] content = response.getImageData();
          resp.setContentType("image/" + response.getSubType());
          resp.setContentLength(content.length);
          resp.getOutputStream().write(content);
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        Command command = (pathParts != null && pathParts.length > 0)
            ? Command.valueOf(pathParts[0])
            : Command.singleUpload;
        switch (command)
        {
          case renameImage:
          {
            String newName = req.getParameter("newName");
            String subType = getSubType(newName);
            if (subType == null)
            {
              resp.getWriter().print(
                  errorMessage(uctx, ResponseMessage.Type.invalidImageSubtype));
              return;
            }
            String contentId = pathParts[1];
            KBProcessor proc = new KBProcessor();
            proc.renameImage(uctx, contentId, newName);
            break;
          }
          case multipleUpload:
          {
            if (!ServletFileUpload.isMultipartContent(req))
            {
              resp.getWriter()
                  .print("Invalid Request: must be multipart request.");
              return;
            }
            List<FileItem> items = new ServletFileUpload(
                new DiskFileItemFactory()).parseRequest(req);
            Map<String, Object> uploadResult = new HashMap<>();
            for (FileItem item : items)
            {
              String name = item.getFieldName();
              byte[] imageContent = item.get();
              try
              {
                String upload = checkAndUploadImage(imageContent, name, uctx,
                    req, true);
                uploadResult.put(name, upload);
              }
              catch (Exception e)
              {
                uploadResult.put(name,
                    "Upload Failed with exception: " + e.getMessage());
              }
            }
            resp.getWriter()
                .print(new JsonMarshaller().serialize(uploadResult));
            break;
          }
          case singleUpload:
          {
            List<FileItem> items = new ServletFileUpload(
                new DiskFileItemFactory()).parseRequest(req);
            String name = "";
            byte[] imageContent = null;
            for (FileItem item : items)
            {
              String fldName = item.getFieldName();
              imageContent = fldName.equals("file") ? item.get() : imageContent;
              name = fldName.equals("name") ? item.getString() : name;
            }
            String response = checkAndUploadImage(imageContent, name, uctx, req,
                false);
            resp.getWriter().print(response);
            break;
          }
          default:
            break;
        }
      }

      /**
       * Logic for handling one image file item
       * 
       * @param imageContent
       * @param name
       * @param uctx
       * @param req
       * @param isMultipleUpload
       * @return
       * @throws Exception
       */
      public String checkAndUploadImage(byte[] imageContent, String name,
          final UContext uctx, final HttpServletRequest req,
          boolean isMultipleUpload) throws Exception
      {
        String logotype = req.getParameter("logotype");
        String subType = getSubType(name);
        String checkImageResult = checkImage(imageContent, subType, uctx);
        if (!checkImageResult.equals("success"))
        {
          return checkImageResult;
        }
        KBResponse rsp = processingImageMetaData(name, subType, logotype,
            imageContent, uctx, req, isMultipleUpload);
        if (rsp == null)
        {
          return errorMessage(uctx, ResponseMessage.Type.resourceNotFound);
        }
        String verifyImage = req.getParameter("verify");
        if (verifyImage == null || verifyImage.equals("true"))
        {
          JobQueue jQ = JobQueue.getInstance("ImageUploadWorkerJob",
              ImageUploadWorker.class);

          Job job = jQ.createJob("ImageUploadWorkerJob", uctx);
          job.setPriority(Job.Priority.medium);
          Map<String, Object> payload = new HashMap<>();
          rsp.setImageData(null);
          payload.put("metadata", new JsonMarshaller().serialize(rsp));
          job.setPayload(new JsonMarshaller().serializeMap(payload));
          jQ.submit(job);
        }
        rsp.setImageData(null);
        return new JsonMarshaller().serialize(rsp);
      }

      /**
       * Currently check if it exceed 1MB, or it has a valid extention
       * 
       * @param imageContent
       * @param subType
       * @param uctx
       * @return
       */
      private String checkImage(byte[] imageContent, String subType,
          final UContext uctx)
      {
        if (imageContent.length > 1 * 1024 * 1024L)
        {
          return errorMessage(uctx, ResponseMessage.Type.invalidImageSize);
        }

        if (subType == null)
        {
          return errorMessage(uctx, ResponseMessage.Type.invalidImageSubtype);
        }

        switch (subType)
        {
          case "jpeg":
          case "png":
          case "gif":
          case "tiff":
          case "svg+xml":
          case "bmp":
          case "webp":
            return "success";
          default:
        }

        return errorMessage(uctx, ResponseMessage.Type.invalidImageSubtype);
      }

      /**
       * Prepare the image metadata in Mongo
       * 
       * @param name
       * @param subType
       * @param logotype
       * @param imageContent
       * @param uctx
       * @param req
       * @param isMultipleUpload
       * @return
       * @throws Exception
       */
      private KBResponse processingImageMetaData(String name, String subType,
          String logotype, byte[] imageContent, final UContext uctx,
          final HttpServletRequest req, boolean isMultipleUpload)
          throws Exception
      {
        String imageName = req.getParameter("name"); // if image name provided
                                                     // use it for single upload
                                                     // case
        name = Normalizer.normalizeImageName(name);
        if (!isMultipleUpload && imageName != null)
        {
          name = imageName;
        }
        if (logotype != null)
        {
          name = logotype + "_" + name;
        }
        String repo = req.getParameter("repo"); // if image is part of event
                                                // repo
        String desc = req.getParameter("desc");
        if (desc == null) desc = "";
        String url = getImageUrl(req, logotype);
        KBProcessor proc = new KBProcessor();
        Map<String, Object> map = new HashMap<>();
        List<String> arr = new ArrayList<>();
        arr.add("_z1_image");
        map.put(ContentStore.Fields.title.name(), name);
        map.put(ContentStore.Fields.desc.name(), desc);
        map.put(ContentStore.Fields.contentType.name(), "image");
        map.put(ContentStore.Fields.subType.name(), subType);
        map.put(ContentStore.Fields.tags.name(), arr);
        map.put(ContentStore.Fields.status.name(), "draft");
        map.put(ContentStore.Fields.cdnURL.name(), url);
        map.put(ContentStore.Fields.contentStats.name(),
            imageStats(imageContent, uctx));
        map.put(ContentStore.Fields.image.name(), imageContent);
        if (repo != null) map.put("repo", repo);
        if (logotype != null
            && ("pushlogo".equals(logotype) || "nslogo".equals(logotype)))
        {
          map.put("id", "_z1_" + logotype);
        }
        String id = proc.createOrUpdateImage(uctx, map);
        ZineoneStoreHandler zh = new ZineoneStoreHandler();
        KBResponse rsp = zh.getContent(uctx, id);
        if (rsp == null)
        {
          return null;
        }
        rsp.setImageData(imageContent);
        rsp.setStatus(KBDocStatus.draft);
        return rsp;
      }
    };
  }

  String getSubType(String imageName)
  {
    int splitPos = imageName.lastIndexOf(".");
    if (splitPos <= 0) return null;
    String ext = imageName.substring(splitPos + 1).toLowerCase();
    switch (ext)
    {
      case "jpg":
        return "jpeg";
      case "svgz":
      case "svg":
        return "svg+xml";
      default:
        return ext;
    }
  }

  String errorMessage(UContext ctx, ResponseMessage.Type type)
  {
    return new ResponseMessage(ctx, ResponseMessage.Status.fail, type)
        .toString();
  }

  /**
   * Return the image stats: height, width and byte size.
   * 
   * @param imageContent
   * @return
   */
  Map<String, Integer> imageStats(byte[] imageContent, UContext uctx)
  {
    Map<String, Integer> res = new HashMap<>();
    try
    {
      res.put("byteSize", imageContent.length);
      BufferedImage image = ImageIO
          .read(new ByteArrayInputStream(imageContent));
      res.put("imageHeight", image.getHeight());
      res.put("imageWidth", image.getWidth());
    }
    catch (Exception e)
    {
      ULogger logger = uctx.getLogger(getClass());
      logger.warning("Exception thrown when trying to get image stats: "+e.getMessage());
    }
    return res;
  }

  /**
   * The old logic for generate image URL.IF the image is for logo, this URL
   * will be kept, otherwise it will be replaced once the image metadata is set
   * to publish state.
   * 
   * @param req
   * @param logotype
   * @return
   */
  String getImageUrl(final HttpServletRequest req, String logotype)
  {
    String url = "";
    String proto = "https";
    String reqURL = req.getRequestURL().toString();
    int index = reqURL.lastIndexOf("/c3/data/");
    if (index > 0)
    {
      String host = reqURL.substring(0, index);
      int i2 = host.indexOf("://");
      if (i2 > 0)
      {
        host = host.substring(i2);
      }
      url = proto + host + "/c3/api/v1/images/_z1_" + logotype;
    }
    return url;
  }
}
