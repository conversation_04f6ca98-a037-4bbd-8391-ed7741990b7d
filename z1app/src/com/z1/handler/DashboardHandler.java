package com.z1.handler;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.CommandHandler;
import com.z1.CommandHandlerFactory;
import com.z1.Utils.ResponseMessage;

import udichi.core.App;
import udichi.core.ArtifactType;
import udichi.core.UContext;
import udichi.core.data.Result;
import udichi.core.system.User;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.workspace.WorkspaceService;
import udichi.gateway.AppServiceFacade;
import udichi.gateway.defservice.DefinitionItem;
import z1.commons.Utils;

public class DashboardHandler implements CommandHandlerFactory
{

  // Supported post commands
  private enum PostCommand
  {
    publish,
    update,
    create
  }

  private enum GetCommand
  {
    all, // All dashboard definitions
    ootb,
    shared
  }

  @Override
  public CommandHandler get()
  {
    return new CommandHandler() {
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        GetCommand command;
        command = GetCommand.valueOf(cStr);

        switch (command)
        {
          // c3/data/dashboard/all?wid=<workspace-id>
          case all:
          {
            String wId = req.getParameter("wid");
            if (wId == null)
            {
              resp.getWriter().print("[]");
              return;
            }
            
            Result<DefinitionItem> items = DefinitionItem.getAllItems(uctx, ArtifactType.dashboard);
            
            List<Map<String, Object>> dsList = new java.util.ArrayList<>(10);  
            
            for (DefinitionItem di : items.getData())
            {
              Map<String, Object> ds = new java.util.HashMap<>();
              
              String id = (String) di.getValues().get("id");
              String prefix = wId.concat("/");
              // remove z1default/ or any other wId from the id and assign it as id
              if (id.startsWith(prefix))
              {
                id = id.substring(prefix.length());
              }
              Long lastUpdatedTime = (Long) di.getValues().get("lastUpdated");
              String lastUpdatedBy = (String) di.getValues().get("lastUpdatedBy");
              String description = (String) di.getValues().get("description");
              String state = (String) di.getValues().get("state");
              
              ds.put("id", id);
              ds.put("state", state);
              ds.put("description", description);
              ds.put("lastUpdatedBy", lastUpdatedBy);
              ds.put("lastUpdatedTime", lastUpdatedTime);
              
              dsList.add(ds);
            }
            if (!dsList.isEmpty())
            {
              String payload = new JsonMarshaller().serialize(dsList);
              resp.getWriter().print(payload);
            }
            else
            {
              resp.getWriter().print("[]");
            }
            
            break;
          }
          case ootb:
          {
            List<DefinitionItem> importedList = null;
            AppServiceFacade asf = new AppServiceFacade(uctx);
            importedList = asf.getApplicationComponents(ArtifactType.dashboard);
            
            if (importedList != null && !importedList.isEmpty())
            {
              String payload = new JsonMarshaller().serialize(importedList);
              resp.getWriter().print(payload);
            }
            else
            {
              resp.getWriter().print("[]");
            }
            break;
          }
          case shared:
          {
            List<DefinitionItem> importedList = new java.util.ArrayList<>(20);
            // We'll load the dashboards created on c3 as well
            Result<DefinitionItem> res = DefinitionItem.getAllItems(uctx,
                ArtifactType.dashboard, new DefinitionItem.LoadOptions()
                    .includePayload(true).includePublishedOnly(true));
            if (!res.isNull())
            {
              for (DefinitionItem item : res.getData())
              {
                // Construct the name from the id
                String name = (String) item.getValues().get("name");
                if (name == null)
                {
                  String[] sParts = item.getId().split("/");
                  name = (sParts.length == 2) ? sParts[1] : sParts[0];
                  item.getValues().put("name", name);
                }
                importedList.add(item);
              }
            }

            if (!importedList.isEmpty())
            {
              String payload = new JsonMarshaller().serialize(importedList);
              resp.getWriter().print(payload);
            }
            else
            {
              resp.getWriter().print("[]");
            }
          }
        }
      }
    };
  }

  @Override
  public CommandHandler post()
  {
    return new CommandHandler() {
      @SuppressWarnings("unchecked")
      @Override
      public void handle(final UContext uctx, final String[] pathParts,
          final HttpServletRequest req, final HttpServletResponse resp)
          throws Exception
      {
        String cStr = pathParts[0];
        PostCommand command = PostCommand.valueOf(cStr);

        switch (command)
        {
          case publish:
          {
            // Get the payload
            String payload = ServletUtil.getPayload(req);
            if (payload == null) return;
            Map<String, Object> map = new JsonMarshaller().readAsMap(payload);
            String id = (String) map.get("id");
            if (id != null)
            {
              DefinitionItem di = DefinitionItem.getInstance(uctx,
                  ArtifactType.dashboard, id);
              if (di != null)
              {
                di.getValues().put(DefinitionItem.Fields.state.name(),
                    DefinitionItem.State.published.name());
                di.save();
                App.notifyClearCache(uctx.getNamespace(), ArtifactType.dashboard.name());
              }
            }
            break;
          }
          // c3/data/dashboard/update?id=<id> where id = workspace + "/" + dashboard name (ex: z1default/MyDashboard)
          case update:
          {
            String payload = ServletUtil.getPayload(req);
            String id = req.getParameter("id");
            
            ResponseMessage msg = null;

            if (payload == null || payload.isEmpty() || id == null || id.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, null,
                  "Request payload or id is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }

            DefinitionItem di = DefinitionItem.getInstance(uctx,
                ArtifactType.dashboard, id);
            if (di == null)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, null,
                  "Id is not found.");
              resp.getWriter().print(msg.toString());
              return;
            }

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> sentData = jm.readAsMap(payload);
            Map<String, Object> data = (Map<String, Object>) sentData.get("dashboard");
            Map<String, Object> propData = (Map<String, Object>) sentData.get("properties");
            
            String pl = jm.serializeMap(data);
            String prop = (propData != null) ? jm.serializeMap(propData) : null;

            String desc = "";
            if (data.get(DefinitionItem.Fields.description.name()) != null)
            {
              desc = (String) data
                  .get(DefinitionItem.Fields.description.name());
            }
            di.getValues().put(DefinitionItem.Fields.description.name(),
                  desc);
            di.getValues().put(DefinitionItem.Fields.payload.name(), pl);
            if (prop != null) di.getValues().put(DefinitionItem.Fields.properties.name(), prop);
            di.setState(DefinitionItem.State.draft);
            di.save();
            
            // Store the dash board ref/description to the workspace
            String[] parts = id.split("/");
            WorkspaceService ws = new WorkspaceService(uctx);
            String wId = parts[0];
            String dbId = parts[1];              
            DefinitionItem wObj = ws.getWorkspace(wId);            
            ws.saveDashboard(wObj, dbId, desc);

            App.notifyClearCache(uctx.getNamespace(), ArtifactType.dashboard.name());

            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                null, "Upgraded dashboard successfully for " + id);
            resp.getWriter().print(msg.toString());
            break;
          }
          // c3/data/dashboard/create?id=<id> where id = workspace + "/" + dashboard name (ex: z1default/MyDashboard)
          case create:
          {
            String payload = ServletUtil.getPayload(req);
            String did = req.getParameter("id");

            ResponseMessage msg = null;
            if (payload == null || payload.isEmpty() || did == null || did.isEmpty())
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, null,
                  "Request payload or id is missing.");
              resp.getWriter().print(msg.toString());
              return;
            }

            String[] parts = did.split("/");
            if (parts.length != 2)
            {
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                  ResponseMessage.Type.invalidParams,
                  "Invalid dashboard id in request.");
              resp.getWriter().print(msg.toString());
              return;
            }
            
            DefinitionItem di = DefinitionItem.getInstance(uctx,
                ArtifactType.dashboard, did);
            if (di != null)
            {
              String name = parts.length > 1 ? parts[1] : did;
              msg = new ResponseMessage(uctx, ResponseMessage.Status.fail, 
                  ResponseMessage.Type.dashboardCreateFailed,
                  "A dashboard with the name '" + name + "' already exists.");
              resp.getWriter().print(msg.toString());
              return;
            }

            JsonMarshaller jm = new JsonMarshaller();
            Map<String, Object> sentData = jm.readAsMap(payload);
            Map<String, Object> data = (Map<String, Object>) sentData.get("dashboard");
            Map<String, Object> propData = (Map<String, Object>) sentData.get("properties");
            
            String pl = jm.serializeMap(data);
            String prop = (propData != null) ? jm.serializeMap(propData) : null;

            di = DefinitionItem.newInstance(uctx, did,
                ArtifactType.dashboard);
            String desc = (String) data
                .get(DefinitionItem.Fields.description.name());
            if (desc != null)
              di.getValues().put(DefinitionItem.Fields.description.name(),
                  desc);

            User user = uctx.getUser();
            if (user != null)
              di.getValues().put(DefinitionItem.Fields.owner.name(),
                  user.getId());
            di.getValues().put(DefinitionItem.Fields.payload.name(), pl);
            if (prop != null) di.getValues().put(DefinitionItem.Fields.properties.name(), prop);
            di.save();

            // Now store the data sheet ref to the workspace
            WorkspaceService ws = new WorkspaceService(uctx);
            String wId = parts[0];
            String id = parts[1];
            DefinitionItem wObj = ws.getWorkspace(wId);
            ws.saveDashboard(wObj, id, desc);
            App.notifyClearCache(uctx.getNamespace(), ArtifactType.dashboard.name());

            msg = new ResponseMessage(uctx, ResponseMessage.Status.success,
                null, "Create dashboard successfully for " + id);
            resp.getWriter().print(msg.toString());
            break;
          }

          default:
            break;
        }
      }

    };
  }

  ///////////////////////////

}
