package com.z1.payments;

import java.math.BigDecimal;
import java.util.Calendar;

import com.braintreegateway.BraintreeGateway;
import com.braintreegateway.ClientTokenRequest;
import com.braintreegateway.Customer;
import com.braintreegateway.CustomerRequest;
import com.braintreegateway.Environment;
import com.braintreegateway.Result;
import com.braintreegateway.Subscription;
import com.braintreegateway.Transaction;
import com.braintreegateway.TransactionRequest;
import com.braintreegateway.Subscription.DurationUnit;
import com.braintreegateway.Subscription.Status;
import com.braintreegateway.SubscriptionRequest;

/*
 * There are 3 factors that come together when setting up recurring billing.
 *
 *	1. The Plan
 *	A template for subscriptions, from which they will inherit billing cycle, price, and other attributes.
 *	You can create multiple Plans, e.g. Gold, Silver, and Bronze, to be used for different levels of service.
 *	
 *	2. A Customer
 *	Includes personal details and credit card information (number and expiry date are required).
 *	A Payment Method is a customer’s credit card, which optionally includes a billing address.
 *	
 *	3. Subscriptions
 *	Payment Methods and Plans are associated to form subscriptions.
 *
 */
public class BraintreeProcessor {

	private static BraintreeGateway gateway = new BraintreeGateway(
			Environment.SANDBOX,
			"jpf432xg2ptnt7h2",
			"4dh5gvks5qvpwpg4",
			"f0746e4945ef9ac73e5b1c5fed96c3af"
			);
	
	//create clientToken which is passed to custom javascript
	public String createClientToken() {
		String clientToken = gateway.clientToken().generate();
		return clientToken;
	}
	
	//create clientToken for existing customerId in Braintree 
	public String createClientToken(String customerId) {
		ClientTokenRequest clientTokenRequest = new ClientTokenRequest()
	    	.customerId(customerId);
		String clientToken = gateway.clientToken().generate(clientTokenRequest);
		return clientToken;
	}
	
	//create customer in Braintree with info from js client including card verification
	//card info is sent directly to Braintree by js client and associated nonce is used 
	//here.
	public void createCustomerWithCreditCard() {
		CustomerRequest request = new CustomerRequest()
		.firstName("Fred")
		.lastName("Jones")
		.creditCard()
		.paymentMethodNonce("nonce-received-from-the-client")
		.options()
		.verifyCard(true)
		.done()
		.done();

	    //.company("Jones Co.")
	    //.email("<EMAIL>")
	    //.fax("************")
	    //.phone("************")
	    //.website("http://example.com");
	    
		Result<Customer> result = gateway.customer().create(request);

		//error checking 
		if (result.isSuccess()) {
			// true			
			String id = result.getTarget().getId();
		}

		//return
		return;
	}
	
	public void changeCreditCard() {
		CustomerRequest request = new CustomerRequest()
		.creditCard()
		.paymentMethodNonce("nonce-received-from-the-client")
		.options()
		.verifyCard(true)
		.done()
		.done();

		Result<Customer> result = gateway.customer().update("the_customer_id", request);
	}

	public static String createSubscription(String planId, String paymentMethodToken) {
		SubscriptionRequest request = new SubscriptionRequest()
		.paymentMethodToken(paymentMethodToken)
		.planId(planId);

		Result<Subscription> result = gateway.subscription().create(request);
		
		//return Id of created subscription
		return result.getTarget().getId();
	}

	public boolean cancelSubscription(String subscriptionId) {
		Result<Subscription> result = gateway.subscription().cancel(subscriptionId);
		
		//check return
		if (result.isSuccess()) {
			// true			
			return true;
		}
		return false;
	}

	public void getSubscriptionDetails(String subscriptionId) {
		Result<Subscription> result = gateway.subscription().cancel(subscriptionId);
		
		//check return
		if (result.isSuccess()) {
			// true			
			Subscription sub = result.getSubscription();
			
			String planId = sub.getPlanId();
			Status status = sub.getStatus();
			int trialPeriod = sub.getTrialDuration();
			DurationUnit trialUnit = sub.getTrialDurationUnit();
			
			Calendar billingStart = sub.getBillingPeriodStartDate();
			Calendar billingEnd = sub.getBillingPeriodEndDate();
			Calendar billingFirst = sub.getFirstBillingDate();
			Calendar lastModified = sub.getUpdatedAt();
			
		}
		
		
	}
	
  public static void createTransaction(String nonce)
  {
    //TransactionRequest request = new TransactionRequest().customerId("manish").paymentMethodToken("fbqkwb").amount(new BigDecimal("9.99")).options();
    
    SubscriptionRequest request = new SubscriptionRequest()
    .paymentMethodToken("fbqkwb")
    .planId("test_plan_1")
    .price(new BigDecimal("12.00"));

    Result<Subscription> result = gateway.subscription().create(request);

    //Result<Transaction> result = gateway.transaction().sale(request);
  }
  
  public static void createCustomerwithCreditCardInfo(String nonce)
  {
    /*TransactionRequest request = new TransactionRequest()
    .amount(new BigDecimal("10.99"))
    .paymentMethodNonce(nonce)
    .options()
        .submitForSettlement(true)
        .done();

    Result<Transaction> result = gateway.transaction().sale(request);*/
    
    
    CustomerRequest request = new CustomerRequest()
    .firstName("Fred")
    .lastName("Jones")
    .paymentMethodNonce(nonce);

	Result<Customer> result = gateway.customer().create(request);

	System.out.println("customer created "+result.isSuccess());
	// true

	Customer customer = result.getTarget();
	customer.getId();
	// e.g. 160923

	customer.getCreditCards().get(0).getToken();
    
    System.out.println(result);
  }
	
}
