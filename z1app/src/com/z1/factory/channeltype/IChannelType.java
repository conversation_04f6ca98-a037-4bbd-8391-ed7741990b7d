package com.z1.factory.channeltype;

import java.util.List;
import java.util.Map;

import z1.channel.def.CredentialsDef;

import com.z1.registration.def.UrlInfoType;

public interface IChannelType
{
    public String getAccessToken();
    public String getFeedHandler();
    public List<Map<String,Object>> getChannelInfoByCompanyName(Map<String, Object> channelInput);
    public List<Map<String,Object>> getChannelInfoByUrl(Map<String, Object> channelInput);
    public void setCredentialDef(UrlInfoType channelUrl, CredentialsDef cd);
    
}
