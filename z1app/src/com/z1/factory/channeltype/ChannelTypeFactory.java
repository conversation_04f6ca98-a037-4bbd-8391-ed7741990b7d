package com.z1.factory.channeltype;

public class ChannelTypeFactory
{
  
  private enum ChannelType
  {
    facebook(new Facebook()),
    googleplay(new GooglePlay()),
    applestore(new AppleStore());
    
    ChannelType(IChannelType channelType)
    {
      this.channelType = channelType;
    }
    
    final IChannelType channelType;
  }
  public static IChannelType getChannel(String channelStr)
  {
    
    return ChannelType.valueOf(channelStr.toLowerCase()).channelType;
    
  }

}
