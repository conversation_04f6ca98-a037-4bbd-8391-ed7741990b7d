package com.z1.analytics.formatters;

import z1.analytics.QueryResults;
import z1.analytics.query.def.AnalyticQuery;
import z1.analytics.query.def.Metric;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Flatten the analytic query data-points into separate key-pairs and
 * value-pairs whilst keeping the axis values intact.
 *
 * Example:
 *    if INPUT: [{
 *        ...,
 *        "values": [{
 *            "date": "20230101", // axis
 *            "label": "A", // axis,
 *            "revenue": 10, // data-point,
 *            "discount": 20, // data-point
 *        }],
 *    }]
 *    then OUTPUT: [{
 *        ...,
 *        "values": [{
 *            "date": "20230101",
 *            "label": "A",
 *            "metricName": "revenue", // data-point name
 *            "metricValue": 10 // data-point value
 *        }, {
 *            "date": "20230101",
 *            "label": "A",
 *            "metricName": "discount", // data-point name
 *            "metricValue": 20 // data-point value
 *        }]
 *    }]
 */
public class MetricKeyValueFormatter
    extends AbstractFormatter<List<QueryResults>>
{
  /**
   * @see AbstractFormatter#format(List, List)
   * @param queryResultsList
   *          Final response from the analytic query processors.
   * @param analyticQueries
   *          External query requests.
   * @return Formatted response.
   */
  @Nullable
  @Override
  public List<QueryResults> format(
      @Nonnull final List<QueryResults> queryResultsList,
      @Nonnull final List<AnalyticQuery> analyticQueries)
  {
    final List<Set<String>> metricNamesSets = analyticQueries.stream()
        .map(analyticQuery -> Optional.ofNullable(analyticQuery.getMetrics())
            .map(metrics -> metrics.stream().map(Metric::getName)
                .collect(Collectors.toSet()))
            .orElse(new HashSet<>()))
        .collect(Collectors.toList());

    IntStream.range(0, queryResultsList.size()).forEach(index -> {
      final Set<String> metricNames = Optional
          .ofNullable(metricNamesSets.get(index)).orElse(new HashSet<>());

      final QueryResults queryResults = queryResultsList.get(index);
      final List<Map<String, Object>> values = queryResults.getValues();

      final List<Map<String, Object>> newValues = new ArrayList<>();

      values.forEach(value -> {
        final Map<String, Object> constMap = value.entrySet().stream()
            .filter(entry -> !metricNames.contains(entry.getKey()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (constMap.size() == value.size())
        {
          newValues.add(constMap);
          return;
        }

        value.entrySet().stream()
            .filter(entry -> metricNames.contains(entry.getKey()))
            .forEach(entry -> {
              final Map<String, Object> newValue = new HashMap<>(constMap);
              newValue.put("metricName", entry.getKey());
              newValue.put("metricValue", entry.getValue());

              newValues.add(newValue);
            });
      });

      queryResults.setValues(newValues);
    });

    return queryResultsList;
  }
}
