package com.z1.analytics.formatters;

import z1.analytics.QueryResults;
import z1.analytics.query.def.AnalyticQuery;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;

/**
 * Analytic external APIs often need the response data to be formatted in a
 * specific way. This class acts as a abstract parent of all general purpose
 * formatters.
 * 
 * @param <T>
 *          Response type generic for the formatted response
 */
public abstract class AbstractFormatter<T>
{
  /**
   * Format the given `queryResultsList` based on the `analyticQueries`
   * provided.
   * 
   * @param queryResultsList
   *          Final response from the analytic query processors.
   * @param analyticQueries
   *          External query requests.
   * @return Formatted external API response
   */
  @Nullable
  public abstract T format(@Nonnull final List<QueryResults> queryResultsList,
      @Nonnull final List<AnalyticQuery> analyticQueries);
}
