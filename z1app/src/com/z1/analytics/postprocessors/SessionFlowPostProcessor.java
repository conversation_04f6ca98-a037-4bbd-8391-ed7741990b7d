package com.z1.analytics.postprocessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import z1.analytics.QueryResults;
import z1.c3.Journey;
import z1.channel.ChannelType;
import z1.commons.Const;
import z1.ml.ModelInfo;

public class SessionFlowPostProcessor implements IQueryResultPostProcessor
{

  protected static final String CONVERSION_METRIC_NAME = "totalFlowConversions";
  protected static final String SESSION_METRIC_NAME = "totalFlowSessions";
  protected static final String REVENUE_METRIC_NAME = "totalFlowRevenue";
  protected static final String ORDERCOUNT_METRIC_NAME = "totalFlowOrderCount";
  protected static final String JOURNEY_IDLIST_METRIC_NAME = "journeyIdList";
  protected static final String PREDICTION_LABEL_METRIC_NAME = "predictionLabel";
  protected static final String CHANNEL_METRIC_NAME = "channel";
  protected static final String OUTCOME_METRIC_NAME = "converted";

  @Override
  public void execute(UContext uctx,
      HttpServletRequest httpServletRequest, List<QueryResults> results)
  {
    Map<String, Object> desktopLabels = ModelInfo.getRangeDetails(uctx,
        "desktop_EPPV2");
    Map<String, Object> mobileLabels = ModelInfo.getRangeDetails(uctx,
        "mobile_EPPV2");
    
    if (results.size() != 1)
    {
      ULogger logger = uctx.getLogger(getClass());
      logger.error("Expected 1 Query Result but got " + results.size());
      return;
    }
    QueryResults queryResults = results.get(0);

    for (Map<String, Object> obj : queryResults.getValues())
    {
      // massage journeyID list
      if (obj.get(JOURNEY_IDLIST_METRIC_NAME) != null)
      {
        // get the value for the journey Idlist
        String journeyVal = Journey.getJourneyNameFromIdList(uctx,
            (String) obj.get(JOURNEY_IDLIST_METRIC_NAME));
        if (journeyVal.equals(Journey.experienceValues.Unknown.name()))
        {
          obj.put(JOURNEY_IDLIST_METRIC_NAME,
              (Object) Journey.experienceValues.Unknown.name());
        }
        else if(obj.get(JOURNEY_IDLIST_METRIC_NAME).equals("noExperience"))
        {
          obj.put(JOURNEY_IDLIST_METRIC_NAME, "No Experience");
        }
        else
        {
          obj.put(JOURNEY_IDLIST_METRIC_NAME, journeyVal);
        }
      }
      // massage epp label
      if (obj.get(PREDICTION_LABEL_METRIC_NAME) != null
          && !(obj.get(PREDICTION_LABEL_METRIC_NAME).equals(Const.NS))
          && (desktopLabels != null || mobileLabels != null))
      {
        // channel is available
        if (obj.get(CHANNEL_METRIC_NAME) != null)
        {
          // case the channel was desktop web, fetch data from the desktopWeb
          // EPPV2 model
          if ((obj.get(CHANNEL_METRIC_NAME).equals("desktopWeb")
              || (obj.get(CHANNEL_METRIC_NAME).equals("push")))
              && desktopLabels != null)
          {
            if (!(obj.get(PREDICTION_LABEL_METRIC_NAME)
                .equals((Object) Const.NS)))
            {
              Map<String, Object> labelData = (Map<String, Object>) desktopLabels
                  .get((String) obj.get(PREDICTION_LABEL_METRIC_NAME));
              if (labelData != null)
              {
                Object displayName =  labelData.get(Const.DISPLAY_NAME);
                if (displayName != null)
                {
                  obj.put(PREDICTION_LABEL_METRIC_NAME, displayName);
                }
              }
            }
          }
          // case the channel is not desktop web, use mobile EPPV2 model to
          // fetch
          else if (mobileLabels != null)
          {
            Map<String, Object> labelData = (Map<String, Object>)mobileLabels
                .get(obj.get(PREDICTION_LABEL_METRIC_NAME));
            if (labelData != null)
            {
              Object displayName = labelData.get(Const.DISPLAY_NAME);
              if (displayName != null)
              {
                obj.put(PREDICTION_LABEL_METRIC_NAME, displayName);
              }
            }
          }
        }
      }
      if(obj.get(CHANNEL_METRIC_NAME) != null)
      {
          // once the mapping of the epp labels is done, we will map the channel
          // names to their appropriate name
          String channel = (String) obj.get(CHANNEL_METRIC_NAME);
          if (channel.equalsIgnoreCase(ChannelType.mobileIOS.name()) || channel.equalsIgnoreCase(ChannelType.mobileAndroid.name())
                || channel.equalsIgnoreCase(ChannelType.tabletIOS.name()) || channel.equalsIgnoreCase(ChannelType.tabletAndroid.name()))
          {
            channel = "nativeApp";
          }
          obj.put(CHANNEL_METRIC_NAME, getChannelDisplayName(channel));
      }
    }
    results.get(0).setValues(this.optimizeResponse(results.get(0).getValues()));
  }

  /**
   * map the BE response for channel to a display Name
   *
   * @param channel
   * @return
   */
  private static String getChannelDisplayName(String channel)
  {
    String name;
    switch (channel)
    {
      case "nativeApp":
        name = "Native App";
        break;
      case "desktopWeb":
        name = "Desktop Web";
        break;
      case "DesktopWeb":
        name = "Desktop Web";
        break;
      case "tabletWeb":
        name = "Tablet Web";
        break;
      case "mobileWeb":
        name = "Mobile Web";
        break;
      default:
        name = "Other";
    }
    return name;
  }

  /**
   * Optimizes the given list of results by aggregating them based on specific
   * properties.
   *
   * The method takes a list of results, where each result is represented as a
   * Map containing various metrics. It aggregates the results by generating a
   * unique key based on specific properties (journeyIdList, channel, converted,
   * predictionLabel), and then combining the corresponding metric values for
   * the same key. The aggregation is performed using a HashMap to efficiently
   * merge the data. The optimized results are returned as a List of Maps, where
   * each map represents an aggregated result with combined metrics.
   *
   * @param queryResultsValues
   *          The list of results to be optimized, represented as a List of
   *          Maps.
   * @return The optimized list of results, where each aggregated result is
   *         represented as a Map containing the combined metrics.
   */
  public List<Map<String, Object>> optimizeResponse(
      List<Map<String, Object>> queryResultsValues)
  {
    Map<String, Map<String, Object>> aggregatedMap = new HashMap<>();

    for (Map<String, Object> responseItem : queryResultsValues)
    {
      String journeyIdList = (String) responseItem
          .get(JOURNEY_IDLIST_METRIC_NAME);
      String channel = (String) responseItem.get(CHANNEL_METRIC_NAME);
      String converted = (String) responseItem.get(OUTCOME_METRIC_NAME);
      String predictionLabel = (String) responseItem
          .get(PREDICTION_LABEL_METRIC_NAME);

      // Generate a unique key by combining the properties
      String key = journeyIdList + "_" + channel + "_" + converted + "_"
          + predictionLabel;

      // If the key already exists, aggregate the values; otherwise, create a
      // new
      // entry
      if (aggregatedMap.containsKey(key))
      {
        Map<String, Object> aggregatedItem = aggregatedMap.get(key);
        aggregatedItem.put(ORDERCOUNT_METRIC_NAME,
            ((BigDecimal) aggregatedItem.get(ORDERCOUNT_METRIC_NAME))
                .add((BigDecimal) responseItem.get(ORDERCOUNT_METRIC_NAME)));
        aggregatedItem.put(CONVERSION_METRIC_NAME,
            ((BigDecimal) aggregatedItem.get(CONVERSION_METRIC_NAME))
                .add((BigDecimal) responseItem.get(CONVERSION_METRIC_NAME)));
        aggregatedItem.put(SESSION_METRIC_NAME,
            ((BigDecimal) aggregatedItem.get(SESSION_METRIC_NAME))
                .add((BigDecimal) responseItem.get(SESSION_METRIC_NAME)));
        aggregatedItem.put(REVENUE_METRIC_NAME,
            ((BigDecimal) aggregatedItem.get(REVENUE_METRIC_NAME))
                .add((BigDecimal) responseItem.get(REVENUE_METRIC_NAME)));
      }
      else
      {
        Map<String, Object> aggregatedItem = new HashMap<>();
        aggregatedItem.put(JOURNEY_IDLIST_METRIC_NAME, journeyIdList);
        aggregatedItem.put(CHANNEL_METRIC_NAME, channel);
        aggregatedItem.put(OUTCOME_METRIC_NAME, converted);
        aggregatedItem.put(PREDICTION_LABEL_METRIC_NAME, predictionLabel);
        aggregatedItem.put(ORDERCOUNT_METRIC_NAME,
            responseItem.get(ORDERCOUNT_METRIC_NAME));
        aggregatedItem.put(CONVERSION_METRIC_NAME,
            responseItem.get(CONVERSION_METRIC_NAME));
        aggregatedItem.put(SESSION_METRIC_NAME,
            responseItem.get(SESSION_METRIC_NAME));
        aggregatedItem.put(REVENUE_METRIC_NAME,
            responseItem.get(REVENUE_METRIC_NAME));
        aggregatedMap.put(key, aggregatedItem);
      }
    }
    List<Map<String, Object>> optimizedResponse = new ArrayList<>();
    optimizedResponse.addAll(aggregatedMap.values());

    return optimizedResponse;
  }

}