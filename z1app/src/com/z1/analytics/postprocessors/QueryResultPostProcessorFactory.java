 package com.z1.analytics.postprocessors;

/**
 * Factory to create a post processor for query results from a specific named
 * query.
 */
public final class QueryResultPostProcessorFactory
{
  private QueryResultPostProcessorFactory()
  {
  }

  /**
   * Returns an instance of a query result post processor for a specific named
   * query.
   * 
   * @param analyticQueryId
   * @return
   */
  public static IQueryResultPostProcessor getInstance(String analyticQueryId)
  {
    switch (analyticQueryId.toLowerCase())
    {
      case "sessionscorebinsv2":
        return new SessionScoreBinsPostProcessor();
      case "sessionflowsankeyv2":
        return new SessionFlowPostProcessor();
      case "te_channel_actionall_liftv2":
      case "te_channel_filterv2":
      case "te_identity_actionall_liftv2":
      case "te_identity_filterv2":
        return new RemoveLiftMetricsFromControlGroupRecords();
      case "commandcenter":
        return new CommandCenterPostProcessor();
      case "significance_status_total_sessionsv2":
        return new StatisticalSignificanceConversion();
      default:
        return null;
    }
  }
}
