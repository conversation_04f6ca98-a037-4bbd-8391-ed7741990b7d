package com.z1.analytics.postprocessors;

import java.util.Arrays;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import z1.analytics.QueryResults;
import z1.commons.Const;

/**
 * Removes the lift metrics from control group records.
 */
public class RemoveLiftMetricsFromControlGroupRecords
    implements IQueryResultPostProcessor
{
  List<String> liftMetricsToRemove = Arrays.asList("conversionRateLift",
      "incrementalRevenue", "incrementalRevenueAdjusted", "rpvAdjustedLift",
      "rpvLift", "aovAdjustedLift", "aovLift");

  @Override
  public void execute(UContext uctx, HttpServletRequest httpServletRequest,
      List<QueryResults> results)
  {
    for (QueryResults queryResults : results)
    {
      List<Map<String, Object>> queryResultsValues = queryResults.getValues();
      if (queryResultsValues != null)
      {
        ListIterator<Map<String, Object>> listIterator = queryResultsValues
            .listIterator();
        while (listIterator.hasNext())
        {
          Map<String, Object> record = listIterator.next();
          String actionName = (String) record.get("actionName");
          if (actionName != null
              && actionName.equals(Const.Z1_CONTROL_GROUP_ACTION_NAME))
          {
            for (String metricNameToRemove : liftMetricsToRemove)
            {
              record.remove(metricNameToRemove);
            }
          }
        }
      }
    }
  }
}