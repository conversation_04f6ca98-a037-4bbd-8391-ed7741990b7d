package com.z1.analytics.postprocessors;

public final class QueryResultPostProcessorFactoryDeprecated
{
  private QueryResultPostProcessorFactoryDeprecated()
  {
  }
  
  public static IQueryResultPostProcessorDeprecated getInstance(String analyticQueryId)
  {
    switch(analyticQueryId.toLowerCase())
    {
      case "sessionscorebins":
        return new SessionScoreBinsPostProcessorDeprecated();
      case "sessionflowsankey":
        return new SessionFlowPostProcessorDeprecated();
      default:
        return null;
    }
  }
}
