package com.z1.analytics.postprocessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

import udichi.core.util.Utils;
import z1.c3.BizOutcome;
import z1.core.utils.TimeUtils;
import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import z1.analytics.QueryResults;
import z1.analytics.cubes.DataByJidAllocationGrpCommandCenterCube;
import z1.commons.Const;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.text.ParseException;
import java.util.Set;
import java.util.stream.Collectors;

public class CommandCenterPostProcessor implements IQueryResultPostProcessor
{
  public static final Set<String> SKIP_JIDS = new HashSet<>(
      Arrays.asList("", "noexperience", "none", Const.Z1_TARGET_GROUP_ACTION_NAME));
  // Keys for the response that the UI understands
  private static final String TOTAL_REVENUE_ANNUALIZED_LIFT = "annualizedTotalRevenueLift";
  private static final String TOTAL_REVENUE = "totalRevenue";
  private static final String TOTAL_REVENUE_LIFT = "totalRevenueLift";
  private static final String TOTAL_SENT_SESSIONS = "totalSentSessions";
  private static final String TOTAL_SENT_SESSIONS_LIFT = "totalSentSessionsLift";
  private static final String AVG_DISCOUNT_RATE = "avgDiscountRate";
  private static final String AVG_DISCOUNT_RATE_LIFT = "avgDiscountRateLift";
  private static final String CONVERSION_RATE = "conversionRate";
  private static final String CONVERSION_RATE_LIFT = "conversionRateLift";
  private static final String TOTAL_DISCOUNT = "totalDiscount";
  private static final String CHART_DATA = "chartData";
  private static final String ANNUAL_REVENUE = "annualRevenue";
  private static final String DESIRED_INC_REV = "desiredIncrementalRevenue";
  private static final String ACTUAL_INC_REV = "actualIncrementalRevenue";
  private static final String TOTAL_ORDER_BEFORE_DISCOUNT = "totalOrderBeforeDiscount";

  private static final String SEGMENT_ID = "segmentId";
  private static final String TOTAL_SESSIONS = "totalSessions";
  private static final String TOTAL_SESSIONS_LIFT = "totalSessionsLift";
  private static final String TOTAL_EXPERIENCES = "totalExperiences";
  private static final String REVENUE_PER_VISITOR = "revenuePerVisitor";

  private static final String JOURNEY_ID = "journeyId";

  // Key that we are retreiving from biz outcome config
  private static final String CONVERSION_RATE_IMPROVEMENT = "conversionRateImprovement";
  private static final String CURRENT_CONVERSION_RATE = "currentConversionRate";
  private static final String INCENTIVE_BUDGET = "incentiveBudget";
  private static final String AUDIENCES = "audiences";

  private static final String GROUP_ALLOCATION = DataByJidAllocationGrpCommandCenterCube.Axis.GROUP_ALLOCATION
      .getId();

  private static final String BIZ_OUTCOME_SCHEMA;
  static
  {
    BIZ_OUTCOME_SCHEMA = Utils.loadFileResource("META-INF/bizoutcome.json");
  }

  @Override
  public void execute(UContext uctx, HttpServletRequest httpServletRequest,
      List<QueryResults> results)
  {
    ULogger logger = uctx.getLogger(getClass());
    List<Map<String, Object>> config = null;
    try
    {
      config = CommandCenterPostProcessor.getBizOutcomeConfig(uctx);
    }
    catch (Exception e)
    {
      logger.log("Failed to retrieve Biz Outcome Config", e);
    }

    // Hold the results from "commandCenterHistoricDataset" for
    // further processing
    List<Map<String, Object>> historyResults = new ArrayList<>();

    // Hold the results from "commandCenterFromDataset" for
    // further processing
    List<Map<String, Object>> fromResults = new ArrayList<>();

    // Fetch `historyResults` and `fromResults` for further
    // processing
    for (final QueryResults qr : results)
    {
      final String dataSetName = qr.getDatasetName();
      switch (dataSetName)
      {
        case "commandCenterHistoricDataset":
          historyResults = qr.getValues();
          break;

        case "commandCenterFromDataset":
          fromResults = qr.getValues();
          break;
      }
    }

    // This object to hold data from beginning until the beginning of this
    // period
    // And measure percentage difference: how much did it increased
    Map<String, Object> historMap = handleHistoricData(historyResults, fromResults);

    // This object will hold the conversion rate and the avg discount for the
    // current period.
    Map<String, Object> periodMap = new HashMap<>();

    // This List will hold the charts data, cumulative, and dated
    List<Map<String, Object>> chartList = new ArrayList<>();

    List<Map<String, Object>> historicAudiences = new ArrayList<>();
    List<Map<String, Object>> currentAudiences = new ArrayList<>();

    for (QueryResults qr : results)
    {
      String dataSetName = qr.getDatasetName();
      switch (dataSetName)
      {
        case "commandCenterPeriodDataset":
          periodMap = handlePeriodData(qr.getValues());
          break;

        case "historicAudiences":
          historicAudiences = qr.getValues();
          break;

        case "currentAudiences":
          currentAudiences = qr.getValues();
          break;

        case "commandCenterSparkDataset":
          chartList = handleChartData(historMap, qr.getValues());
          break;
      }
    }

    // The revenue generated in the current period
    BigDecimal totalRevenue = getBigDecimalValue(periodMap.get(TOTAL_REVENUE));

    // Revenue lift is calculated based on the biz outcome RevenueLift =
    // (Revenue generated for the period / Revenue generated prior) x 100
    BigDecimal totalRevenueLift = calculateLift(
        getBigDecimalValue(historMap.get(TOTAL_REVENUE)),
        getBigDecimalValue(periodMap.get(TOTAL_REVENUE)));

    // Annualized Revenue = Revenue for the period / number of days in period *
    // 365.25
    BigDecimal annualizedRevenue = calculateAnnualizedRevenue(totalRevenue,
        httpServletRequest, logger);

    // Annualized Revenue lift = Annualized Revenue - actual revenue / actual
    // revenue * 100
    BigDecimal annualRevenue = getBigDecimalValue(
        getPropertyFromConfig(ANNUAL_REVENUE, config));

    BigDecimal totalSessionsLift = null;
    // Total Sessions = Total sessions that were in target for this period
    BigDecimal totalSessions = getBigDecimalValue(
        periodMap.get(TOTAL_SENT_SESSIONS));

    // Total Session lift =(Total Targeted Sessions in this period / Total
    // Sessions targeted in the past ) x 100
    totalSessionsLift = calculateLift(
        getBigDecimalValue(historMap.get(TOTAL_SENT_SESSIONS)), totalSessions);

    // Revenue Per Visitor (RPV) = Total Revenue / Total Sessions
    BigDecimal revenuePerVisitor = BigDecimal.ZERO;
    if(totalRevenue != null && totalSessions != null && !totalSessions.equals(BigDecimal.ZERO))
    {
      revenuePerVisitor = totalRevenue.divide(totalSessions, 2, RoundingMode.HALF_UP);
    }

    // Number of sessions in target group that were converted
    BigDecimal cvrTarget = getBigDecimalValue(
        periodMap.get(CONVERSION_RATE + "_TG"));

    // Number of sessions that were not influenced that were converted
    BigDecimal cvrSiteWide = getBigDecimalValue(
        periodMap.get(CONVERSION_RATE + "_CG"));

    // Avg discount rate =( Total Discount / Total Order Values Before Discount
    // ) * 100
    BigDecimal avgDiscountRateTG = calculateAvgDiscountRate(
        getBigDecimalValue(periodMap.get(TOTAL_DISCOUNT + "_TG")),
        getBigDecimalValue(periodMap.get(TOTAL_ORDER_BEFORE_DISCOUNT + "_TG")));

    BigDecimal avgDiscountRateSiteWide = calculateAvgDiscountRate(
        getBigDecimalValue(periodMap.get(TOTAL_DISCOUNT + "_CG")),
        getBigDecimalValue(periodMap.get(TOTAL_ORDER_BEFORE_DISCOUNT + "_CG")));

    BigDecimal conversionRateImprovement = getBigDecimalValue(
        (getPropertyFromConfig(CONVERSION_RATE_IMPROVEMENT, config)));

    BigDecimal currentIncentiveBugetPerc = getBigDecimalValue(
        (getPropertyFromConfig(INCENTIVE_BUDGET, config)));

    BigDecimal currentCvr = getBigDecimalValue(
        (getPropertyFromConfig(CURRENT_CONVERSION_RATE, config)));

    // Budget for incentives = Actual Annual revenue * Incentive to Spend (%)
    BigDecimal sessionAiIncentiveBudget = null;
    if (annualRevenue != null && currentIncentiveBugetPerc != null)
    {
      sessionAiIncentiveBudget = annualRevenue
          .multiply(currentIncentiveBugetPerc)
          .divide(BigDecimal.valueOf(100))
          .setScale(2, RoundingMode.HALF_UP);
    }

    // This is the goal set to increment the revenue
    // Desired Incremental Revenue based on biz outcome config = CVR Improvement
    // - Current Cvr;
    BigDecimal desiredIncRev = getDesiredIncrementalRevenue(
        conversionRateImprovement, currentCvr);

    // Actuanl Incremental Revenue = (Annualized Revenue - Actual Revenue) /
    // Actual Revenue * 100
    BigDecimal actualIncRev = getActualIncrementalRevenue(annualRevenue,
        annualizedRevenue);

    // Incremental Revenue lift = Actual Incremental Revneue / Desired
    // Incremental Revenue * 100
    BigDecimal incRevLift = getIncRevLit(desiredIncRev, actualIncRev);

    final List<Map<String, Object>> audiences = handleAudiences(
        historicAudiences, currentAudiences);

    // Combine metrics into a map
    Map<String, Object> resultMetrics = new HashMap<>();
    resultMetrics.put(TOTAL_REVENUE, totalRevenue);
    resultMetrics.put(TOTAL_REVENUE_LIFT, totalRevenueLift);
    if (totalSessions != null)
    {
      resultMetrics.put(TOTAL_SENT_SESSIONS, totalSessions.intValue());
    }
    else
    {
      resultMetrics.put(TOTAL_SENT_SESSIONS, 0);
    }

    resultMetrics.put(TOTAL_SENT_SESSIONS_LIFT, totalSessionsLift);
    resultMetrics.put(AVG_DISCOUNT_RATE, avgDiscountRateTG);
    resultMetrics.put(AVG_DISCOUNT_RATE_LIFT, avgDiscountRateSiteWide);
    resultMetrics.put(CONVERSION_RATE, cvrTarget);
    resultMetrics.put(CONVERSION_RATE_LIFT, cvrSiteWide);
    resultMetrics.put(TOTAL_DISCOUNT, periodMap.get(TOTAL_DISCOUNT + "_TG"));

    resultMetrics.put(INCENTIVE_BUDGET, sessionAiIncentiveBudget);
    resultMetrics.put(DESIRED_INC_REV, desiredIncRev);
    resultMetrics.put(ACTUAL_INC_REV, actualIncRev);
    resultMetrics.put(TOTAL_REVENUE_ANNUALIZED_LIFT, incRevLift);
    resultMetrics.put(CHART_DATA, chartList);
    resultMetrics.put(AUDIENCES, audiences);
    resultMetrics.put(REVENUE_PER_VISITOR, revenuePerVisitor);

    // Replace results with new map
    results.clear();
    QueryResults newResult = new QueryResults();
    newResult.setValues(Arrays.asList(resultMetrics));
    results.add(newResult);
  }

  /**
   * Gets the totalSessions and the totalRevenue up until the 'from' - 1 date.
   * The response of the query should return 1 value for totalSessions and 1 value
   * for totalRevenue
   *
   * @param historyResults Historic results will 'from' date
   * @param historyResults Historic results only for 'from' date
   * @return
   */
  private Map<String, Object> handleHistoricData(
      final List<Map<String, Object>> historyResults,
      final List<Map<String, Object>> fromResults)
  {
    final Map<String, Object> output = new HashMap<>();
    output.put(TOTAL_REVENUE, BigDecimal.ZERO);
    output.put(TOTAL_SENT_SESSIONS, BigDecimal.ZERO);

    // extracting `history` results
    if (!historyResults.isEmpty())
    {
      final Map<String, Object> result = historyResults.get(0);
      output.compute(TOTAL_REVENUE, (k, v) ->
              result.getOrDefault(TOTAL_REVENUE, BigDecimal.ZERO));
      output.compute(TOTAL_SENT_SESSIONS, (k, v) ->
              result.getOrDefault(TOTAL_SENT_SESSIONS, BigDecimal.ZERO));
    }

    // extracting `from` results and subtracting from historic
    if (!fromResults.isEmpty())
    {
      final Map<String, Object> result = fromResults.get(0);
      output.compute(TOTAL_REVENUE, (k, v) ->
              ((BigDecimal) v).subtract(
                      (BigDecimal) result.getOrDefault(TOTAL_REVENUE, BigDecimal.ZERO)));
      output.compute(TOTAL_SENT_SESSIONS, (k, v) ->
              ((BigDecimal) v).subtract(
                      (BigDecimal) result.getOrDefault(TOTAL_SENT_SESSIONS, BigDecimal.ZERO)));
    }

    return output;
  }

  /**
   * Returns a map with mapped values for the current period for Target Group
   * and Control Group Values include = Revenue, Sessions, Discount, CVR, Order
   * Value Before Discount
   * 
   * @param vList
   * @return
   */
  private Map<String, Object> handlePeriodData(List<Map<String, Object>> vList)
  {
    Map<String, Object> output = new HashMap<>();
    for (Map<String, Object> item : vList)
    {
      String groupAllocation = (String) item.get(GROUP_ALLOCATION);

      if (Const.Z1_TARGET_GROUP_ACTION_NAME.equals(groupAllocation)
          || Const.Z1_CONTROL_GROUP_ACTION_NAME.equals(groupAllocation))
      {
        for (Map.Entry<String, Object> entry : item.entrySet())
        {
          String key = entry.getKey();
          Object value = entry.getValue();

          if (value != null)
          {
            switch (key)
            {
              case TOTAL_REVENUE:
              case TOTAL_SENT_SESSIONS:
              {
                if (Const.Z1_TARGET_GROUP_ACTION_NAME.equals(groupAllocation))
                {
                  output.put(key, value);
                }
                break;
              }
              case TOTAL_DISCOUNT:
              case CONVERSION_RATE:
              case TOTAL_ORDER_BEFORE_DISCOUNT:
              {
                final String _k = Const.Z1_TARGET_GROUP_ACTION_NAME.equals(
                    groupAllocation) ? key + "_TG" : key + "_CG";
                output.put(_k, value);
                break;
              }
              default:
              {
                break;
              }
            }
          }
        }
      }
    }

    return output;
  }

  /**
   * This data is cumulative. The starting value should be the sum of Revenue up
   * until the start of this period And should keep aggregating for Sessions
   * that were part of target group
   * 
   * @param historyMap
   * @param vList
   * @return List<Map<String, Object>>
   */
  private List<Map<String, Object>> handleChartData(
      Map<String, Object> historyMap, List<Map<String, Object>> vList)
  {
    List<Map<String, Object>> cumulativeData = new ArrayList<>();

    // Initialize cumulative values with historyMap data
    BigDecimal cumulativeSessions = (BigDecimal) historyMap
        .getOrDefault(TOTAL_SENT_SESSIONS, BigDecimal.ZERO);
    BigDecimal cumulativeRevenue = (BigDecimal) historyMap
        .getOrDefault(TOTAL_REVENUE, BigDecimal.ZERO);

    for (Map<String, Object> dataPoint : vList)
    {

      BigDecimal totalSentSessions = getBigDecimalValue(
          dataPoint.get(TOTAL_SENT_SESSIONS)) != null
              ? getBigDecimalValue(dataPoint.get(TOTAL_SENT_SESSIONS))
              : BigDecimal.ZERO;
      BigDecimal totalRevenue = getBigDecimalValue(
          dataPoint.get(TOTAL_REVENUE)) != null
              ? getBigDecimalValue(dataPoint.get(TOTAL_REVENUE))
              : BigDecimal.ZERO;

      String date = (String) dataPoint.get("date");

      // Accumulate values starting from historMap initial values
      cumulativeSessions = cumulativeSessions.add(totalSentSessions);
      cumulativeRevenue = cumulativeRevenue.add(totalRevenue);

      // Prepare cumulative data for this point
      Map<String, Object> cumulativePoint = new HashMap<>();
      cumulativePoint.put("date", date);
      cumulativePoint.put(TOTAL_SENT_SESSIONS, cumulativeSessions);
      cumulativePoint.put(TOTAL_REVENUE, cumulativeRevenue);

      // Add to the cumulative data list
      cumulativeData.add(cumulativePoint);
    }

    return cumulativeData;
  }

  /**
   * Retrieves a property value from the configuration list by key.
   *
   * @param key
   *          The key of the property to retrieve.
   * @param config
   *          The configuration list.
   * @return The property value, or null if not found or if config is null.
   */
  private Object getPropertyFromConfig(String key,
      List<Map<String, Object>> config)
  {
    if (config == null)
    {
      return null;
    }
    for (Map<String, Object> item : config)
    {
      if (item.get("key").equals(key))
      {
        return item.get("value");
      }
    }
    return null;
  }

  /**
   * Calculates the total lift based on two metrics input.
   *
   * @param historicValue
   *          The historic data value.
   * @param periodValue
   *          The period data value.
   * @return The total revenue lift as a BigDecimal, or null if inputs are
   *         invalid.
   */
  private BigDecimal calculateLift(BigDecimal historicValue,
      BigDecimal periodValue)
  {
    // If either value is null then we can't calculate the lift
    if (historicValue == null || periodValue == null)
    {
      return null;
    }
    // The period selected is the initial period
    if (BigDecimal.ZERO.compareTo(historicValue) == 0
        && BigDecimal.ZERO.compareTo(periodValue) != 0)
    {
      return BigDecimal.valueOf(100);
    }

    // If we enter here then both historicValue and periodValue is zero
    if (BigDecimal.ZERO.compareTo(historicValue) == 0)
    {
      return null;
    }

    BigDecimal lift = periodValue
        .divide(historicValue, 10, RoundingMode.HALF_UP)
        .multiply(BigDecimal.valueOf(100));
    return lift.setScale(2, RoundingMode.HALF_UP); // Rounds to 2 decimal
                                                   // places

  }

  /**
   * Calculates the average discount rate.
   *
   * @param totalDiscount
   *          The total discount amount.
   * @param totalOrdrAmntBeforeDiscount
   *          The total order amount before discount.
   * @return The average discount rate as a percentage, or null if the total
   *         order amount is zero.
   */
  private static BigDecimal calculateAvgDiscountRate(BigDecimal totalDiscount,
      BigDecimal totalOrdrAmntBeforeDiscount)
  {
    if (totalOrdrAmntBeforeDiscount == null || totalDiscount == null
        || totalOrdrAmntBeforeDiscount.compareTo(BigDecimal.ZERO) == 0)
    {
      return null; // return null for valid input
    }

    BigDecimal avgDiscountRate = totalDiscount
        .divide(totalOrdrAmntBeforeDiscount, 10, RoundingMode.DOWN)
        .multiply(BigDecimal.valueOf(100));

    return avgDiscountRate.setScale(2, RoundingMode.DOWN);
  }

  /**
   * Casts and retrieves values from Objects to BigDecimal
   */

  public static BigDecimal getBigDecimalValue(Object value)
  {
    if (value instanceof Number)
    {
      return BigDecimal.valueOf(((Number) value).doubleValue());
    }
    else if (value instanceof String)
    {
      return new BigDecimal((String) value);
    }
    return BigDecimal.ZERO;
  }

  private static String getParameter(HttpServletRequest request,
      String... possibleNames)
  {
    for (String name : possibleNames)
    {
      String value = request.getParameter(name);
      if (value != null && !value.isEmpty())
      {
        return value;
      }
    }
    return null; // Return null if none of the parameters are present
  }

  /**
   * Calculates the desired inc rev desired inc Rev = CVR Improvement - Current
   * Cvr;
   * 
   * @param cvrImprov
   * @param currentCvr
   * @return BigDecimal
   */
  private BigDecimal getDesiredIncrementalRevenue(BigDecimal cvrImprov,
      BigDecimal currentCvr)
  {
    if (cvrImprov == null || currentCvr == null)
    {
      return null;
    }

    // Calculate the desired incremental Rev
    BigDecimal desiredIncRev = cvrImprov.subtract(currentCvr);
    return desiredIncRev.setScale(2, RoundingMode.HALF_UP);

  }

  /**
   * Calculates the actual inc rev Actual inc Rev = (Annualized Revenue - Annual
   * Revenue) / Annual Revenue * 100;
   * 
   * @param annualRev
   * @param annualizedRev
   * @return BigDecimal
   */
  private BigDecimal getActualIncrementalRevenue(BigDecimal annualRev,
      BigDecimal annualizedRev)
  {
    if (annualRev == null || annualizedRev == null
        || annualRev.compareTo(BigDecimal.ZERO) == 0)
      return null;
    BigDecimal result = annualizedRev.subtract(annualRev)
        .divide(annualRev, 4, RoundingMode.HALF_UP)
        .multiply(BigDecimal.valueOf(100));
    return result.setScale(2, RoundingMode.HALF_UP);
  }

  private static BigDecimal getIncRevLit(BigDecimal desiredIncRev,
      BigDecimal actualIncRev)
  {
    if (desiredIncRev == null || actualIncRev == null || desiredIncRev.compareTo(BigDecimal.ZERO) == 0) return null;

    return actualIncRev.divide(desiredIncRev, 10, RoundingMode.HALF_UP)
        .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);

  }

  private static BigDecimal calculateAnnualizedRevenue(BigDecimal totalRevenue,
      HttpServletRequest req, ULogger logger)
  {

    // For annualized revenue, we need the dates to know the duration
    // Ann Revenue = Revenue for current period / the period in years
    // Eg. For 30 days, rev was 100 thus 100 Ann revenue = 100 / 30 days x
    // 365.25 = 1217.5
    // Using 365.25 is more precise to account for leap years
    String toDate = (getParameter(req, "t", "to", "toDate") != null)
        ? getParameter(req, "t", "to", "toDate")
        : "********";
    String fromDate = (getParameter(req, "f", "from", "fromDate") != null)
        ? getParameter(req, "f", "from", "fromDate")
        : new SimpleDateFormat("yyyyMMdd").format(new Date());

    TimeUtils timeUtils = new TimeUtils();

    Long differenceInDays = 0L;
    try
    {
      differenceInDays = timeUtils.diffTwoDates(TimeUtils.TimeUnit.parse("day"),
          fromDate, toDate);
    }
    catch (ParseException ex)
    {
      logger.error("Failed to parse the dates", ex);
      return null;
    }

    // Check for null or zero values
    if (totalRevenue == null || differenceInDays == null
        || differenceInDays < 0L)
    {
      return null; // Return null if inputs are invalid
    }

    // adding the one day in the difference as ******** to
    // ******** is actually 7 days and not 6 days
    differenceInDays++;

    // Convert differenceInDays to BigDecimal for accuaracy
    BigDecimal days = BigDecimal.valueOf(differenceInDays);

    // Calculate annualized revenue using BigDecimal for precision
    BigDecimal annualizedRevenue = totalRevenue
        .divide(days, 10, RoundingMode.HALF_UP)
        .multiply(BigDecimal.valueOf(365.25));

    // Round the result to 2 decimal places
    return annualizedRevenue.setScale(2, RoundingMode.HALF_UP);
  }

  /**
   * Parse the analytic query data in the format easiest for the UI. The format
   * for `historicAudiences` will be as follows: [{ "segmentId": "segment id 1"
   * "totalSessions": 1 }, ... ] The format for `currentAudiences` will be as
   * follows: [{ "segmentId": "segment id 1", "journeyId": "journey id 1",
   * "totalSessions": 1 }, ... ]
   * 
   * @param historicAudiences
   *          History data from 2020-01-01 to query end date
   * @param currentAudiences
   *          Current data from query start date to query end date
   * @return Parsed audiences data
   */
  private List<Map<String, Object>> handleAudiences(
      List<Map<String, Object>> historicAudiences,
      final List<Map<String, Object>> currentAudiences)
  {
    // Group historicAudience as { SEGMENT_ID : TOTAL_SESSIONS }
    Map<String, Long> historicAudienceData = historicAudiences.stream().collect(
        Collectors.groupingBy(m -> (String) m.get(SEGMENT_ID),
            Collectors.summingLong(
                m -> Const.Z1_TARGET_GROUP_ACTION_NAME.equals(m.get(JOURNEY_ID)) ?
                    getBigDecimalValue(m.get(TOTAL_SESSIONS)).longValue() : 0L)));

    // Process currentAudience by grouping all objects by segmentid and then
    // computing values.
    return currentAudiences.stream()
        .collect(Collectors.groupingBy(m -> m.get(SEGMENT_ID)))
        .entrySet()
        .stream()
        .map(e -> {
          String segId = (String) e.getKey();
          List<Map<String, Object>> metricData = e.getValue();

          long totalSessions = 0;
          final Set<Object> uniqueJids = new HashSet<>();
          for (Map<String, Object> data : metricData)
          {
            String jid = (String) data.get(JOURNEY_ID);

            // Calculate exp count only from valid jids
            if (!SKIP_JIDS.contains(jid))
            {
              uniqueJids.add(jid);
            }

            // totalSessions is value from z1TG object
            if (Const.Z1_TARGET_GROUP_ACTION_NAME.equals(jid))
            {
              totalSessions += getBigDecimalValue(
                  data.get(TOTAL_SESSIONS)).longValue();
            }
          }

          // (definition as per product requirement)
          // Considering (query start date) = ${f} and (query end date) = ${t},
          // historic data contains data in date range [2020-01-01, ${t}].
          // Thus, to get data in date range [2020-01-01, ${f}] we need to
          // do the following to the data:
          // [2020-01-01, ${t}] - [${f}, ${t}].
          // Also, "totalSessionsLift" means the following done to "totalSessions":
          //    [${f}, ${t}] - [2020-01-01, ${f}]
          // =  [${f}, ${t}] - ([2020-01-01, ${t}] - [${f}, ${t}])
          // = 2[${f}, ${t}] - [2020-01-01, ${t}]
          long histTotalSessions = historicAudienceData.getOrDefault(segId, 0L);
          long lift = histTotalSessions > 0 ? 2 * totalSessions - histTotalSessions : 0;

          Map<String, Object> row = new HashMap<>(4);
          row.put(SEGMENT_ID, segId);
          row.put(TOTAL_SESSIONS, totalSessions);
          row.put(TOTAL_EXPERIENCES, uniqueJids.size());
          row.put(TOTAL_SESSIONS_LIFT, lift);

          return row;
        })
        .collect(Collectors.toList());
  }

  /**
   * Returns the business outcome config or null if empty
   */
  private static List<Map<String, Object>> getBizOutcomeConfig(UContext uctx)
  {
    if (BIZ_OUTCOME_SCHEMA == null || BIZ_OUTCOME_SCHEMA.isEmpty())
    {
      return null;
    }

    // load config from mongo (format: schema)
    return BizOutcome.getConfig(uctx, BIZ_OUTCOME_SCHEMA);
  }
}
