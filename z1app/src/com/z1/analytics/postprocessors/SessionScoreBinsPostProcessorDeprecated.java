package com.z1.analytics.postprocessors;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import z1.commons.Const;
import z1.ml.ModelInfo;
import z1.ml.def.ModelInfoDef;

public class SessionScoreBinsPostProcessorDeprecated implements IQueryResultPostProcessorDeprecated
{
  protected static final String QUERY_RESULT_VALUES_KEY = "values";
  protected static final String TOTAL_SESSIONS_KEY = "totalSessions";
  protected static final String PERCENT_SESSIONS_KEY = "percentOfSessions";
  protected static final String TOTAL_SUCCESSES_KEY = "totalSuccesses";
  protected static final String SCORE_BIN_KEY = "scoreBin";
  protected static final String SCORE_START_KEY = "scoreStart";
  protected static final String SCORE_END_KEY = "scoreEnd";
  protected static final String SUCCESS_RATE_KEY = "successRate";
  protected static final int NUMBER_OF_SCORE_BINS = 100;
  protected static final BigDecimal SCORE_BIN_SIZE = BigDecimal.valueOf(0.01);

  @Override
  public List<Map<String, Object>> process(UContext uctx,
      HttpServletRequest httpServletRequest, List<Map<String, Object>> results)
  {
    List<Map<String, Object>> res = new ArrayList<Map<String, Object>>();
    ULogger logger = uctx.getLogger(this.getClass());
    if (results.size() != 1)
    {
      logger.warning(
          "SessionScoreBinsPostProcessor expected to receive only 1 result, but got "
              + results.size());
      return res;
    }
    // making a copy for results
    res.addAll(results);

    Map<String, Object> queryResult = res.get(0);
    List<Map<String, Object>> values = (List<Map<String, Object>>) queryResult
        .get(QUERY_RESULT_VALUES_KEY);

    // In this case we want no data to show so we do not process any further.
    if (values.isEmpty())
    {
      return res;
    }

    // Sort results by score bin and get total sessions
    Long totalSessions = 0L;
    Map<Integer, Map<String, Object>> scoreBinMap = new HashMap<>(
        NUMBER_OF_SCORE_BINS);
    for (Map<String, Object> scoreBin : values)
    {
      Integer scoreBinValue;
      try
      {
        scoreBinValue = NumberFormat.getInstance()
            .parse((String) scoreBin.get(SCORE_BIN_KEY)).intValue();
      }
      catch (ParseException e)
      {
        logger.warning(
            "Unable to parse score bin value: " + scoreBin.get(SCORE_BIN_KEY),
            e);
        continue;
      }
      Long scoreBinTotalSessions = (Long) scoreBin.get(TOTAL_SESSIONS_KEY);
      if (scoreBinTotalSessions != null)
      {
        totalSessions += scoreBinTotalSessions;
      }
      scoreBinMap.put(scoreBinValue, scoreBin);
    }

    List<Map<String, Object>> newValues = new ArrayList<>(NUMBER_OF_SCORE_BINS);
    for (int i = 1; i <= NUMBER_OF_SCORE_BINS; i++)
    {
      Map<String, Object> scoreBin = scoreBinMap.get(i);
      if (scoreBin == null)
      {
        scoreBin = new HashMap<>(3);
        scoreBin.put(TOTAL_SESSIONS_KEY, 0L);
        scoreBin.put(TOTAL_SUCCESSES_KEY, 0L);
        scoreBin.put(SCORE_BIN_KEY, String.valueOf(i));
      }

      Map<String, Object> processedScoreBin = processRecord(logger, scoreBin,
          totalSessions);
      newValues.add(processedScoreBin);
    }

    queryResult.put(QUERY_RESULT_VALUES_KEY, newValues);

    // Add the score labels to the response.
    String channel = httpServletRequest.getParameter(ModelInfo.CHANNEL);
    String type = httpServletRequest.getParameter(ModelInfo.TYPE);

    if ((type != null) && (channel != null))
    {
      boolean isCandidate = false;
      // The UI supports EPP but only knows about EPPV2.
      // If EPPV2 is requested, we need to also check for other EPPs so just pass in EPP
      if (type.equals("EPPV2"))
      {
        type = ModelInfo.EPP;
      }
      else if(type.endsWith(ModelInfo.MODEL_ID_CANDIDATE_POSTFIX))
      {
        isCandidate = true;
        type = type.substring(0, type.length() - ModelInfo.MODEL_ID_CANDIDATE_POSTFIX.length());
      }
      // Retrieve model name for the provided channel and type.
      ModelInfoDef modelDef = ModelInfo.getModelForChannelAndType(uctx, channel,
          type);
      if (modelDef != null)
      {
        // Retrieve score label for the model, or the
        // default labels for the model's type.
        String modelName = modelDef.getName();
        String pipelineName = modelDef.getChampion();
        if (isCandidate)
        {
          pipelineName = modelDef.getCandidate();
        }
        String payload =  ModelInfo.
            getCustomOrDefaultScoreLabels(uctx, modelName, pipelineName,false);
        if (payload != null)
        {
          Map<String, Object> labels = Const.jsonMarshaller.readAsMap(payload);
          queryResult.put("channel", channel);
          queryResult.put("model", modelName);
          queryResult.put("labelRanges", labels);
        }
      }
    }

    return res;
  }

  /**
   * Results a record with totalSessions, percentOfSessions, scoreStart,
   * scoreEnd, successRate, scoreBin
   * 
   * @param logger
   * @param scoreBin
   * @param totalSessions
   * @return
   */
  protected Map<String, Object> processRecord(ULogger logger, Map<String, Object> scoreBin,
      Long totalSessions)
  {
    Map<String, Object> newResult = new HashMap<>(6);
    Long totalSessionsForScoreBin = (Long) scoreBin.get(TOTAL_SESSIONS_KEY);
    if (totalSessionsForScoreBin == null)
    {
      totalSessionsForScoreBin = 0L;
    }
    newResult.put(TOTAL_SESSIONS_KEY, totalSessionsForScoreBin);

    Double percentSessionsForScoreBin = totalSessions != 0
        ? ((totalSessionsForScoreBin.doubleValue() / totalSessions) * 100)
        : 0.0;
    newResult.put(PERCENT_SESSIONS_KEY, percentSessionsForScoreBin);

    Integer scoreBinValue;
    try
    {
      scoreBinValue = NumberFormat.getInstance()
          .parse((String) scoreBin.get(SCORE_BIN_KEY)).intValue();
    }
    catch (ParseException e)
    {
      logger.warning(
          "Unable to parse score bin value: " + scoreBin.get(SCORE_BIN_KEY),
          e);
      return Collections.emptyMap();
    }
    newResult.put(SCORE_BIN_KEY, scoreBinValue);

    BigDecimal scoreEnd = BigDecimal.valueOf(scoreBinValue)
        .multiply(SCORE_BIN_SIZE);
    newResult.put(SCORE_END_KEY, scoreEnd);

    BigDecimal scoreStart = scoreEnd.subtract(SCORE_BIN_SIZE);
    newResult.put(SCORE_START_KEY, scoreStart);

    Long totalSuccesses = (Long) scoreBin.get(TOTAL_SUCCESSES_KEY);
    if (totalSuccesses == null)
    {
      totalSuccesses = 0L;
    }
    newResult.put(TOTAL_SUCCESSES_KEY, totalSuccesses);
    
    Double successRate = totalSessionsForScoreBin != 0
        ? ((totalSuccesses.doubleValue() / totalSessionsForScoreBin) * 100)
        : 0;
    newResult.put(SUCCESS_RATE_KEY, successRate);

    return newResult;
  }
}
