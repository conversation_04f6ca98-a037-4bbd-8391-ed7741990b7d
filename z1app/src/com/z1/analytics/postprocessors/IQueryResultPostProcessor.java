package com.z1.analytics.postprocessors;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import z1.analytics.QueryResults;

/**
 * A class that will post process query results from a named query.
 */
public interface IQueryResultPostProcessor
{
  /**
   * Post processes the query results from a named query.
   * 
   * @param uctx
   * @param httpServletRequest
   * @param results
   */
  public void execute(UContext uctx,
      HttpServletRequest httpServletRequest, List<QueryResults> results);
}