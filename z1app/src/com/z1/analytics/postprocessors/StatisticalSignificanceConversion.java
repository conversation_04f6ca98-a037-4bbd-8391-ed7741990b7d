package com.z1.analytics.postprocessors;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.math3.stat.inference.ChiSquareTest;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import z1.analytics.AnalyticService;
import z1.analytics.QueryResults;
import z1.analytics.query.def.AnalyticQuery;
import z1.c3.CustomConfig;
import z1.c3.Journey;
import z1.c3.Journey.Type;
import z1.commons.Const;
import z1.core.utils.TimeUtils;

/**
 * This class represents a statistical significance report generator for a given journey.
 */
public class StatisticalSignificanceConversion implements IQueryResultPostProcessor
{
  public final static String ANALYTIC_QUERY_ID = "significance_status_total_sessionsV2";

  private long totalSessionWithPurchaseTG = 0;
  private long totalSessionWithNoPurchaseTG = 0;
  private long totalSessionWithPurchaseCG = 0;
  private long totalSessionWithNoPurchaseCG = 0;
  private long totalSessionTG = 0;
  private long totalSessionCG = 0;
  private final Map<String, Map<String, Object>> actionData = new HashMap<>();
  private String journeyId = null;

  /**
   * Handles the Triggered Experience Summary request for a given journey ID.
   *
   * @param uctx
   *     The UContext object for the request.
   * @param jId
   *     The journey ID for which the significance request is made.
   * @return A map containing the significance levels and other relevant information. Returns null
   * if the journey does not exist.
   */
  public Map<String, Object> handleTESummaryRequest(UContext uctx, String jId)
  {
    ULogger logger = uctx.getLogger(getClass());
    try
    {
      Journey journey = Journey.loadDef(uctx, jId, Type.campaign);
      if (journey == null)
      {
        if (logger.canLog())
        {
          logger.log("could not load journey from database for jId: " + jId);
        }
        return null;
      }

      // Execute queries to retrieve data for calculating significance levels
      List<QueryResults> cubeResults = this.loadDataFromCube(uctx, jId);
      if (cubeResults == null || cubeResults.isEmpty())
      {
        if (logger.canLog())
        {
          logger.log("received no/empty data from cube for jId: " + jId);
        }
        return null;
      }

      IQueryResultPostProcessor queryResultPostProcessor = QueryResultPostProcessorFactory
          .getInstance(ANALYTIC_QUERY_ID);

      queryResultPostProcessor.execute(uctx, null, cubeResults);

      if (!cubeResults.isEmpty() && !cubeResults.get(0).getValues().isEmpty())
      {
        return cubeResults.get(0).getValues().get(0);
      }
      return null;
    }
    catch (IOException e)
    {
      throw new RuntimeException(e);
    }
  }

  public List<QueryResults> loadDataFromCube(UContext uctx, String jId) throws IOException
  {
    CustomConfig cc = CustomConfig.load(uctx, ANALYTIC_QUERY_ID,
        CustomConfig.Type.analyticQuery, true);
    if (cc == null) return null;

    String payload = cc.getPayload();
    Pattern p = Pattern.compile(Pattern.quote("${journeyId}"));
    payload = p.matcher(payload).replaceAll(jId);
    ObjectMapper mapper = App.getInstance().getJsonMapper();
    List<AnalyticQuery> analyticQueries =
        mapper.readValue(payload, new TypeReference<List<AnalyticQuery>>()
        {
        });

    AnalyticService analyticService = new AnalyticService(uctx);
    List<QueryResults> queryResultsList = new ArrayList<>();
    for (AnalyticQuery analyticQuery : analyticQueries)
    {
      QueryResults results = analyticService.query(analyticQuery, true);
      queryResultsList.add(results);
    }
    return queryResultsList;
  }


  /**
   * Processes the data obtained from the different queries. It calculates the total sessions with
   * purchase, total sessions with no purchase, and other relevant metrics for each action.
   *
   * @param cubeResults
   *     the result map from the cube queries.
   */
  @SuppressWarnings("unchecked")
  private void processData(List<QueryResults> cubeResults)
  {
    for (QueryResults queryResult : cubeResults)
    {
      switch (queryResult.getDatasetName())
      {
        case "totalSessions":
        {
          processSessions(queryResult.getValues());
          break;
        }
        case "totalSessionsWithChannel":
        {
          processSessionsWithAttributes(queryResult.getValues(), "channels", "channel");
          break;
        }
        case "totalSessionsWithIdentity":
        {
          processSessionsWithAttributes(queryResult.getValues(), "identities", "visitIden");
          break;
        }
      }
    }
  }

  /**
   * Helper method to processes the total sessions.
   *
   * @param sessions
   *     the list of session maps containing purchase data.
   */
  private void processSessions(List<Map<String, Object>> sessions)
  {
    for (Map<String, Object> map : sessions)
    {
      String actionName = map.getOrDefault("actionName", "").toString().trim();
      if (actionName.isEmpty()) continue;
      if (this.journeyId == null)
      {
        this.journeyId = (String) map.get("journeyId");
      }

      long totalSessionsWithPurchase =
          Long.parseLong(map.getOrDefault("totalConvertedSession", "0").toString());
      long totalSentSessionsCount =
          Long.parseLong(map.getOrDefault("totalSentSessions", "0").toString());
      long totalNotSentSessionsCount =
          Long.parseLong(map.getOrDefault("totalNotSentSessions", "0").toString());
      long totalSessionsCount = totalSentSessionsCount + totalNotSentSessionsCount;

      if (actionName.equals("z1TG"))
      {
        this.totalSessionWithPurchaseTG = totalSessionsWithPurchase;
        this.totalSessionTG = totalSessionsCount;
        this.totalSessionWithNoPurchaseTG = totalSessionsCount - totalSessionWithPurchaseTG;

      } else if (actionName.equals("z1Control"))
      {
        this.totalSessionWithPurchaseCG = totalSessionsWithPurchase;
        this.totalSessionCG = totalSessionsCount;
        this.totalSessionWithNoPurchaseCG = totalSessionsCount - totalSessionWithPurchaseCG;

      } else
      {
        Map<String, Object> data = actionData.getOrDefault(actionName, new HashMap<>());
        data.put("totalSessionWithPurchase", totalSessionsWithPurchase);
        data.put("totalSessionWithNoPurchase", totalSessionsCount - totalSessionsWithPurchase);
        actionData.put(actionName, data);
      }
    }
  }

  /**
   * Helper method to process sessions with attributes (channels or identities).
   *
   * @param sessions
   *     the list of session maps containing attribute data.
   * @param attributeType
   *     the type of attribute (e.g., "channels" or "identities").
   * @param attributeName
   *     the name of the attribute in the map (e.g., "channel" or "visitIden").
   */
  private void processSessionsWithAttributes(List<Map<String, Object>> sessions, String attributeType, String attributeName)
  {
    for (Map<String, Object> map : sessions)
    {
      String actionName = map.getOrDefault("actionName", "").toString().trim();
      if (actionName.isEmpty() || !actionData.containsKey(actionName) || actionName.equals(
          "Z1TG") || actionName.equals("z1Control"))
      {
        continue;
      }

      ArrayList<HashMap<String, Object>> attributeList =
          (ArrayList<HashMap<String, Object>>) actionData.get(actionName)
              .getOrDefault(attributeType, new ArrayList<HashMap<String, Object>>());

      HashMap<String, Object> attributeData = new HashMap<>();
      attributeData.put("name", map.get(attributeName));
      long totalConvertedSession =
          Long.parseLong(map.getOrDefault("totalConvertedSession", "0").toString());
      long totalSentSessionsCount =
          Long.parseLong(map.getOrDefault("totalSentSessions", "0").toString());
      long totalNotSentSessionsCount =
          Long.parseLong(map.getOrDefault("totalNotSentSessions", "0").toString());
      long totalSessionsCount = totalSentSessionsCount + totalNotSentSessionsCount;


      attributeData.put("totalSessionWithPurchase", totalConvertedSession);
      attributeData.put("totalSessionWithNoPurchase", totalSessionsCount - totalConvertedSession);
      attributeList.add(attributeData);
      actionData.get(actionName).put(attributeType, attributeList);
    }
  }

  /**
   * This method builds the payload for the statistical significance report.
   *
   * @param uctx
   *     The UContext object for the request.
   * @return A map containing the overview and significance levels.
   */
  private Map<String, Object> buildPayload(UContext uctx)
  {
    Map<String, Object> payload = new HashMap<>();

    payload.put("overview", createOverview(uctx));
    payload.put("significanceLevels", getSignificanceLevels());

    return payload;
  }

  /**
   * This method creates an overview of the journey.
   *
   * @param uctx
   *     The UContext object for the request.
   * @return A map containing the overview details.
   */
  private Map<String, Object> createOverview(UContext uctx)
  {
    Map<String, Object> overview = new HashMap<>();
    Journey journey = Journey.loadDef(uctx, this.journeyId, Type.campaign);
    overview.put("journeyId", journey.getId());

    String startDateFormatted = "";
    long durationDays = 0;

    long startDate = getStartDateOfJourney(journey);
    if (startDate > 0L)
    {
      TimeUtils timeUtils = new TimeUtils();
      startDateFormatted = TimeUtils.getFormatLocalTime(startDate, "dd/MM/yyyy");
      long currentTime = timeUtils.getTimeMillis();
      int millisInADay = 1000 * 60 * 60 * 24;
      durationDays = (currentTime - startDate) / millisInADay;
    }

    overview.put("durationDays", durationDays);
    overview.put("startDate", startDateFormatted); // format: dd/mm/yyyy
    overview.put("totalSessions", totalSessionCG + totalSessionTG);
    overview.put("metric", "Conversion");

    BigDecimal teSignificance =
        this.getSignificanceStatus(totalSessionWithPurchaseTG, totalSessionWithNoPurchaseTG,
            totalSessionWithPurchaseCG, totalSessionWithNoPurchaseCG);

    overview.put("statisticalSignificance", teSignificance);

    boolean isSignificant =
        teSignificance != null && teSignificance.doubleValue() >= Const.SIGNIFICANCE_THRESHOLD;

    overview.put("isSignificant", isSignificant);

    return overview;
  }

  /**
   * This method calculates the start date of the journey.
   *
   * @param journey
   *     The journey object for which the start date needs to be calculated.
   * @return The start date of the journey in milliseconds. If the journey or its definition item is
   * null, it returns 0.
   */
  private Long getStartDateOfJourney(Journey journey)
  {
    if (journey == null || journey.getDefItem() == null) return 0L;
    Map<String, Object> values = journey.getDefItem().getValues();
    String timeStamp = values.getOrDefault("timestamp", 0L).toString();
    return Long.parseLong(timeStamp);
  }

  /**
   * This method calculates the significance levels for each action in the journey.
   *
   * @return A map containing the significance levels for each action.
   */
  private Map<String, Object> getSignificanceLevels()
  {
    Map<String, Object> significanceLevels = initializeSignificanceLevels();

    for (Map.Entry<String, Map<String, Object>> entry : actionData.entrySet())
    {
      Long totalSessionWithPurchaseForAction =
          (Long) entry.getValue().getOrDefault("totalSessionWithPurchase", 0L);
      Long totalSessionWithNoPurchaseForAction =
          (Long) entry.getValue().getOrDefault("totalSessionWithNoPurchase", 0L);
      BigDecimal significance = this.getSignificanceStatus(totalSessionWithPurchaseForAction,
          totalSessionWithNoPurchaseForAction, totalSessionWithPurchaseCG,
          totalSessionWithNoPurchaseCG);

      HashMap<String, Object> actionPayload = new HashMap<>();
      actionPayload.put("name", entry.getKey());
      actionPayload.put("significanceLevel", significance);

      List<Object> channelsPayload = getChannelsFromPayload(
          (ArrayList<HashMap<String, Object>>) entry.getValue().get("channels"));
      actionPayload.put("channels", channelsPayload);

      List<Object> identitiesPayload = getIdentitiesFromPayload(
          (ArrayList<HashMap<String, Object>>) entry.getValue().get("identities"));
      actionPayload.put("identities", identitiesPayload);

      addDataWithRightIdentity(significanceLevels, actionPayload, significance);
    }
    return significanceLevels;
  }

  /**
   * Initializes the significance levels map with empty actions map for each level.
   *
   * @return A map containing the significance levels and their corresponding empty actions map.
   */
  private Map<String, Object> initializeSignificanceLevels()
  {
    Map<String, Object> significanceLevels = new HashMap<>();
    significanceLevels.put("inProgress", getEmptyActionsMap());

    significanceLevels.put("low", getEmptyActionsMap());
    significanceLevels.put("medium", getEmptyActionsMap());
    significanceLevels.put("mediumHigh", getEmptyActionsMap());
    significanceLevels.put("high", getEmptyActionsMap());
    significanceLevels.put("veryHigh", getEmptyActionsMap());
    return significanceLevels;
  }

  /**
   * This method initializes an empty actions map with an empty list of actions.
   *
   * @return A map containing a key "actions" with an empty list as its value.
   */
  private Map<String, Object> getEmptyActionsMap()
  {
    HashMap<String, Object> objectHashMap = new HashMap<>();
    objectHashMap.put("actions", new ArrayList<>());
    return objectHashMap;
  }

  /**
   * This method adds the action payload to the right significance level in the significance levels
   * map.
   *
   * @param significanceLevels
   *     The map containing the significance levels and their corresponding actions.
   * @param actionPayload
   *     The map representing the action payload to be added.
   * @param significance
   *     The significance value of the action.
   */
  private void addDataWithRightIdentity(Map<String, Object> significanceLevels, HashMap<String, Object> actionPayload, BigDecimal significance)
  {
    double val = significance.doubleValue();
    String level;
    if (val >= 99.9D)
    {
      level = "veryHigh";
    } else if (val >= 99D)
    {
      level = "high";
    } else if (val >= 98D)
    {
      level = "mediumHigh";
    } else if (val >= 95D)
    {
      level = "medium";
    } else if (val >= Const.SIGNIFICANCE_THRESHOLD)
    {
      level = "low";
    } else
    {
      level = "inProgress";
    }

    HashMap<String, ArrayList<Object>> actions =
        (HashMap<String, ArrayList<Object>>) significanceLevels.get(level);

    ArrayList<Object> actionsList = actions.get("actions");
    actionsList.add(actionPayload);
    significanceLevels.put(level, actions);
  }

  /**
   * This method processes the identities data from the payload and calculates the significance
   * level.
   *
   * @param identities
   *     The list of identities data from the payload.
   * @return A list of maps, each containing the name and significance level of an identity.
   */
  private List<Object> getIdentitiesFromPayload(ArrayList<HashMap<String, Object>> identities)
  {
    List<Object> identitiesPayload = new ArrayList<>();
    for (Map<String, Object> data : identities)
    {
      HashMap<String, Object> payload = new HashMap<>();
      Long totalSessionWithPurchase = (Long) data.get("totalSessionWithPurchase");
      Long totalSessionWithNoPurchase = (Long) data.get("totalSessionWithNoPurchase");
      BigDecimal significance =
          this.getSignificanceStatus(totalSessionWithPurchase, totalSessionWithNoPurchase,
              totalSessionWithPurchaseCG, totalSessionWithNoPurchaseCG);
      payload.put("name", data.get("name"));
      payload.put("significanceLevel", significance);
      identitiesPayload.add(payload);
    }
    return identitiesPayload;
  }

  /**
   * This method processes the channels data from the payload and calculates the significance
   * level.
   *
   * @param channels
   *     The list of channels data from the payload.
   * @return A list of maps, each containing the name and significance level of a channel.
   */
  private List<Object> getChannelsFromPayload(ArrayList<HashMap<String, Object>> channels)
  {
    List<Object> channelsPayload = new ArrayList<>();
    for (Map<String, Object> data : channels)
    {
      HashMap<String, Object> payload = new HashMap<>();
      Long totalSessionWithPurchase = (Long) data.get("totalSessionWithPurchase");
      Long totalSessionWithNoPurchase = (Long) data.get("totalSessionWithNoPurchase");
      BigDecimal significance =
          this.getSignificanceStatus(totalSessionWithPurchase, totalSessionWithNoPurchase,
              totalSessionWithPurchaseCG, totalSessionWithNoPurchaseCG);
      payload.put("name", data.get("name"));
      payload.put("significanceLevel", significance);
      channelsPayload.add(payload);
    }
    return channelsPayload;
  }

  @Override
  public void execute(UContext uctx, HttpServletRequest httpServletRequest,
                      List<QueryResults> results)
  {
    // Process the retrieved data to calculate significance levels
    processData(results);

    // Build and return the payload containing the significance levels and other relevant information
    Map<String, Object> payload = buildPayload(uctx);
    results.clear();
    QueryResults queryResults = new QueryResults();
    queryResults.setValues(Arrays.asList(payload));
    results.add(queryResults);
  }

  /**
   * Calculates the significance status between two categories using chi-square test.
   *
   * @param a
   *     The count of observations in the first category of the first group.
   * @param b
   *     The count of observations in the second category of the first group.
   * @param c
   *     The count of observations in the first category of the second group.
   * @param d
   *     The count of observations in the second category of the second group.
   * @return A BigDecimal representing the significance status (100 - p-value) rounded to 2 decimal
   * places. If the p-value is NaN, it returns 0.
   */
  public BigDecimal getSignificanceStatus(long a, long b, long c, long d)
  {
    ChiSquareTest chiSquareTest = new ChiSquareTest();
    long[][] data = new long[][]{{a, b}, {c, d}};
    double pValue = chiSquareTest.chiSquareTest(data);
    return Double.isNaN(pValue) ? BigDecimal.ZERO :
        BigDecimal.valueOf((1 - pValue) * 100).setScale(2, RoundingMode.HALF_UP);
  }
}