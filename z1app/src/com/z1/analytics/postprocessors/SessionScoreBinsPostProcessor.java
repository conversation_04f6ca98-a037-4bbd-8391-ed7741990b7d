package com.z1.analytics.postprocessors;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import z1.analytics.QueryResults;
import z1.commons.Const;
import z1.ml.ModelInfo;
import z1.ml.def.ModelInfoDef;

public class SessionScoreBinsPostProcessor implements IQueryResultPostProcessor
{
  protected static final String TOTAL_SESSIONS_KEY = "totalSessions";
  protected static final String PERCENT_SESSIONS_KEY = "percentOfSessions";
  protected static final String TOTAL_SUCCESSES_KEY = "totalSuccesses";
  protected static final String SCORE_BIN_KEY = "scoreBin";
  protected static final String SCORE_START_KEY = "scoreStart";
  protected static final String SCORE_END_KEY = "scoreEnd";
  protected static final String SUCCESS_RATE_KEY = "successRate";
  protected static final int NUMBER_OF_SCORE_BINS = 100;
  protected static final BigDecimal SCORE_BIN_SIZE = BigDecimal.valueOf(0.01);

  @Override
  public void execute(UContext uctx,
      HttpServletRequest httpServletRequest, List<QueryResults> results)
  {
    ULogger logger = uctx.getLogger(this.getClass());
    if (results.size() != 1)
    {
      logger.warning(
          "SessionScoreBinsPostProcessor expected to receive only 1 result, but got "
              + results.size());
      return;
    }
    QueryResults queryResults = results.get(0);
    List<Map<String, Object>> values = queryResults.getValues();

    // In this case we want no data to show so we do not process any further.
    if (values.isEmpty())
    {
      return;
    }

    // Sort results by score bin and get total sessions
    BigDecimal totalSessions = BigDecimal.ZERO;
    Map<Integer, Map<String, Object>> scoreBinMap = new HashMap<>(
        NUMBER_OF_SCORE_BINS);
    for (Map<String, Object> scoreBin : values)
    {
      Integer scoreBinValue;
      try
      {
        scoreBinValue = NumberFormat.getInstance()
            .parse((String) scoreBin.get(SCORE_BIN_KEY)).intValue();
      }
      catch (ParseException e)
      {
        logger.warning(
            "Unable to parse score bin value: " + scoreBin.get(SCORE_BIN_KEY),
            e);
        continue;
      }
      BigDecimal scoreBinTotalSessions = (BigDecimal) scoreBin.get(TOTAL_SESSIONS_KEY);
      if (scoreBinTotalSessions != null)
      {
        totalSessions = totalSessions.add(scoreBinTotalSessions);
      }
      scoreBinMap.put(scoreBinValue, scoreBin);
    }

    List<Map<String, Object>> newValues = new ArrayList<>(NUMBER_OF_SCORE_BINS);
    for (int i = 1; i <= NUMBER_OF_SCORE_BINS; i++)
    {
      Map<String, Object> scoreBin = scoreBinMap.get(i);
      if (scoreBin == null)
      {
        scoreBin = new HashMap<>(3);
        scoreBin.put(TOTAL_SESSIONS_KEY, BigDecimal.ZERO);
        scoreBin.put(TOTAL_SUCCESSES_KEY, BigDecimal.ZERO);
        scoreBin.put(SCORE_BIN_KEY, String.valueOf(i));
      }

      Map<String, Object> processedScoreBin = processRecord(logger, scoreBin,
          totalSessions);
      newValues.add(processedScoreBin);
    }

    queryResults.setValues(newValues);

    // Add the score labels to the response.
    String channel = httpServletRequest.getParameter(ModelInfo.CHANNEL);
    String type = httpServletRequest.getParameter(ModelInfo.TYPE);

    if ((type != null) && (channel != null))
    {
      boolean isCandidate = false;
      // The UI supports EPP but only knows about EPPV2.
      // If EPPV2 is requested, we need to also check for other EPPs so just pass in EPP
      if (type.equals("EPPV2"))
      {
        type = ModelInfo.EPP;
      }
      else if(type.endsWith(ModelInfo.MODEL_ID_CANDIDATE_POSTFIX))
      {
        isCandidate = true;
        type = type.substring(0, type.length() - ModelInfo.MODEL_ID_CANDIDATE_POSTFIX.length());
      }
      // Retrieve model name for the provided channel and type.
      ModelInfoDef modelDef = ModelInfo.getModelForChannelAndType(uctx, channel,
          type);
      if (modelDef != null)
      {
        // Retrieve score label for the model, or the
        // default labels for the model's type.
        String modelName = modelDef.getName();
        String pipelineName = modelDef.getChampion();
        if (isCandidate)
        {
          pipelineName = modelDef.getCandidate();
        }
        String payload =  ModelInfo.
            getCustomOrDefaultScoreLabels(uctx, modelName, pipelineName,false);
        if (payload != null)
        {
          Map<String, Object> labels = Const.jsonMarshaller.readAsMap(payload);
          queryResults.putMetadata("channel", channel);
          queryResults.putMetadata("model", modelName);
          queryResults.putMetadata("labelRanges", labels);
        }
      }
    }
  }

  /**
   * Results a record with totalSessions, percentOfSessions, scoreStart,
   * scoreEnd, successRate, scoreBin
   * 
   * @param logger
   * @param scoreBin
   * @param totalSessions
   * @return
   */
  protected Map<String, Object> processRecord(ULogger logger, Map<String, Object> scoreBin,
      BigDecimal totalSessions)
  {
    Map<String, Object> newResult = new HashMap<>(6);
    BigDecimal totalSessionsForScoreBin = (BigDecimal) scoreBin.get(TOTAL_SESSIONS_KEY);
    if (totalSessionsForScoreBin == null)
    {
      totalSessionsForScoreBin = BigDecimal.ZERO;
    }
    newResult.put(TOTAL_SESSIONS_KEY, totalSessionsForScoreBin);

    BigDecimal percentSessionsForScoreBin = totalSessions != BigDecimal.ZERO
        ? totalSessionsForScoreBin
            .divide(totalSessions, 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100L))
        : BigDecimal.ZERO;
    percentSessionsForScoreBin = new BigDecimal(
        String.format("%.2f", percentSessionsForScoreBin));
    newResult.put(PERCENT_SESSIONS_KEY, percentSessionsForScoreBin);

    Integer scoreBinValue;
    try
    {
      scoreBinValue = NumberFormat.getInstance()
          .parse((String) scoreBin.get(SCORE_BIN_KEY)).intValue();
    }
    catch (ParseException e)
    {
      logger.warning(
          "Unable to parse score bin value: " + scoreBin.get(SCORE_BIN_KEY),
          e);
      return Collections.emptyMap();
    }
    newResult.put(SCORE_BIN_KEY, scoreBinValue);

    BigDecimal scoreEnd = BigDecimal.valueOf(scoreBinValue)
        .multiply(SCORE_BIN_SIZE);
    newResult.put(SCORE_END_KEY, scoreEnd);

    BigDecimal scoreStart = scoreEnd.subtract(SCORE_BIN_SIZE);
    newResult.put(SCORE_START_KEY, scoreStart);

    BigDecimal totalSuccesses = (BigDecimal) scoreBin.get(TOTAL_SUCCESSES_KEY);
    if (totalSuccesses == null)
    {
      totalSuccesses = BigDecimal.ZERO;
    }
    newResult.put(TOTAL_SUCCESSES_KEY, totalSuccesses);
    
    BigDecimal successRate = totalSessionsForScoreBin != BigDecimal.ZERO
        ? totalSuccesses
            .divide(totalSessionsForScoreBin, 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100L))
        : BigDecimal.ZERO;
    successRate = new BigDecimal(String.format("%.2f", successRate));
    newResult.put(SUCCESS_RATE_KEY, successRate);

    return newResult;
  }
}
