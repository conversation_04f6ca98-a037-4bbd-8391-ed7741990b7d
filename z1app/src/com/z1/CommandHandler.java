/**
 * AbstractCommandHandler.java
 * Copyright 2012 by Udichi.com.,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of Udichi Inc. 
 */
package com.z1;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import udichi.core.UContext;

/**
 * Handles a REST command. The servlets create specific handlers for commands
 * derived from the REST request signature.
 */
public interface CommandHandler
{
  /**
   * Handles a command.
   * 
   * @param ctx
   *          Current runtime context.
   * @param pathElemets
   *          Elements of the request path.
   * @param req
   *          Request object.
   * @param resp
   *          Response object.
   * @throws Exception
   *           If failed to handle.
   */
  public void handle(UContext ctx, String[] pathElemets,
      HttpServletRequest req, HttpServletResponse resp) throws Exception;

}
