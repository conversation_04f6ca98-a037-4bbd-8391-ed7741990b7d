
package com.z1.registration.def;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.z1.registration.def package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.z1.registration.def
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CustomType }
     * 
     */
    public CustomType createCustomType() {
        return new CustomType();
    }

    /**
     * Create an instance of {@link RegistrationType }
     * 
     */
    public RegistrationType createRegistrationType() {
        return new RegistrationType();
    }

    /**
     * Create an instance of {@link UrlInfoType }
     * 
     */
    public UrlInfoType createUrlInfoType() {
        return new UrlInfoType();
    }

    /**
     * Create an instance of {@link CompetitorType }
     * 
     */
    public CompetitorType createCompetitorType() {
        return new CompetitorType();
    }

    /**
     * Create an instance of {@link CustomType.Nv }
     * 
     */
    public CustomType.Nv createCustomTypeNv() {
        return new CustomType.Nv();
    }

}
