package com.z1;

import org.apache.http.entity.ContentType;
import udichi.core.util.JsonMarshaller;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@SuppressWarnings("serial")
public class ErrorServlet extends HttpServlet
{

  public void doGet(HttpServletRequest request, HttpServletResponse response)
      throws IOException
  {
    // Retrieve the exception or error details
    Throwable throwable = (Throwable) request
        .getAttribute("javax.servlet.error.exception");
    Integer statusCode = (Integer) request
        .getAttribute("javax.servlet.error.status_code");
    String message = (String) request
        .getAttribute("javax.servlet.error.message");

    String originURI = (String) request
        .getAttribute("javax.servlet.forward.request_uri");

    z1.commons.Utils.printInfo(String.format(
        "Error Servlet for path:%s and message: %s", originURI, message));
    z1.commons.Utils.showStackTraceIfEnable(throwable, null);

    Map<String, Object> resp = new HashMap<>();
    resp.put("status", statusCode);
    resp.put("message", "An error occurred.");

    response.setContentType(ContentType.APPLICATION_JSON.getMimeType());
    response.getWriter().write(new JsonMarshaller().serialize(resp));

  }

  public void doPost(HttpServletRequest request, HttpServletResponse response)
          throws IOException
  {
    // Retrieve the exception or error details
    Throwable throwable = (Throwable) request
            .getAttribute("javax.servlet.error.exception");
    Integer statusCode = (Integer) request
            .getAttribute("javax.servlet.error.status_code");
    String message = (String) request
            .getAttribute("javax.servlet.error.message");

    String originURI = (String) request
            .getAttribute("javax.servlet.forward.request_uri");

    z1.commons.Utils.printInfo(String.format(
            "Error Servlet for path:%s and message: %s", originURI, message));
    z1.commons.Utils.showStackTraceIfEnable(throwable, null);

    Map<String, Object> resp = new HashMap<>();
    resp.put("status", statusCode);
    resp.put("message", "An error occurred.");

    response.setContentType(ContentType.APPLICATION_JSON.getMimeType());
    response.getWriter().write(new JsonMarshaller().serialize(resp));

  }

}

