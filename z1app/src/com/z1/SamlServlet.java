package com.z1;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import z1.account.Z1Account;
import z1.account.Z1AccountService;
import z1.sso.saml.SamlAuth;

@SuppressWarnings("serial")
public class SamlServlet extends HttpServlet
{
  private enum GetCommand
  {
    login
  }

  private final static String URL_PATH_SPLITTER = "/";
  private final static String LOGIN_PARAM_NAMESPACE = "namespace";
  
  private final static String RESPONSE_IDP_SSO_URL_PROPERTY = "url";

  /**
   * The response will be an empty object if an error occurs. This is to hide
   * any unnecessary information to anyone trying to access the system. The UI
   * will handle displaying a generic message in this case.
   */
  public void doGet(HttpServletRequest request, HttpServletResponse response)
      throws IOException
  {
    UContext ctx = UContext.getInstance(request);
    ULogger logger = ctx.getLogger(SamlServlet.class);

    try
    {
      String path = request.getPathInfo();
      String[] pathParts = path.split(URL_PATH_SPLITTER);
      GetCommand cmd = GetCommand.valueOf(pathParts[1]);
      switch (cmd)
      {
        case login:
        {
          JsonMarshaller jm = new JsonMarshaller();
          String namespace = request.getParameter(LOGIN_PARAM_NAMESPACE);
          if (StringUtils.isEmpty(namespace))
          {
            String message = "Request is missing a value for the namespace parameter";
            logger.severe(message);
            response.getWriter().write(jm.serialize(Collections.emptyMap()));
            return;
          }
          ctx.setNamespace(namespace);
          if (logger.canLog())
          {
            logger.log(String.format(
                "Attempting to get account information for namespace '%s'.",
                namespace));
          }
          Z1AccountService accountService = new Z1AccountService(ctx);
          Z1Account account = accountService.getAccount(namespace);
          if (account == null)
          {
            String message = String
                .format("No account found for namespace '%s'.", namespace);
            if (logger.canLog())
            {
              logger.log(message);
            }
            response.getWriter().write(jm.serialize(Collections.emptyMap()));
            return;
          }

          if (account.getProperties().getIsSsoEnabled())
          {
            SamlAuth samlAuth = new SamlAuth(ctx, account, request, response);
            String idpSsoUrl = samlAuth.initiateLogin();
            Map<String, Object> responseObject = new HashMap<>(1);
            responseObject.put(RESPONSE_IDP_SSO_URL_PROPERTY, idpSsoUrl);
            response.getWriter().write(jm.serialize(responseObject));
          }
          else
          {
            String message = String
                .format("SSO is not enabled for namespace '%s'.", namespace);
            if (logger.canLog())
            {
              logger.log(message);
            }
            response.getWriter().write(jm.serialize(Collections.emptyMap()));
            return;
          }
        }
      }
    }
    catch (Exception ex)
    {
      z1.commons.Utils.showStackTraceIfEnable(ex, null);
      if (logger.canLog())
      {
        logger.log(ex.getLocalizedMessage());
      }
      JsonMarshaller jm = new JsonMarshaller();
      response.getWriter().write(jm.serialize(Collections.emptyMap()));
    }
  }
}
