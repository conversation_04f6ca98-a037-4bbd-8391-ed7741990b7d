/**
 * Copyright 2011 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.C3ApiServlet.InvalidApiKeyException;
import com.z1.Utils.ApiUtils;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.ServletUtil;
import z1.commons.Const;

/**
 * Implements REST support for public/api/* commands.
 */
@SuppressWarnings("serial")
public class PublicApiServlet extends HttpServlet
{
  public static final String HEADER_APIKEY = "apikey";

  private enum Command
  {
    v1
  }

  @Override
  protected void doOptions(HttpServletRequest req, HttpServletResponse resp)
      throws ServletException, IOException
  {
    // Set the header to support CORS (x-origin requests)
    resp.setHeader("Access-Control-Allow-Origin", "*");
    resp.setHeader("Access-Control-Allow-Headers", "apikey, accesstoken");
    resp.setHeader("Timing-Allow-Origin", "*");

    super.doOptions(req, resp);
  }

  // ..................................................
  // Gets
  @Override
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/plain");
    resp.setCharacterEncoding("UTF-8");
    resp.setHeader("Timing-Allow-Origin", "*");
    
    UContext ctx = UContext.getInstance(req, true);
    ULogger logger = ctx.getLogger(getClass());
    
    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/data/<artifact type>/*
      // So we are expecting at least 2 parts after splitting, the first one
      // being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }
      
      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }
      
      if (!ApiUtils.checkAccess(req, ctx, subParts) ) {
        throw new InvalidApiKeyException();
      }

      // Set the header to support CORS (x-origin requests)
      resp.setHeader("Access-Control-Allow-Origin", "*");
      resp.setHeader("Access-Control-Allow-Headers", "apikey, accesstoken");

      Command type = Command.valueOf(pathParts[1]);
      switch (type)
      {
      // c3/api/v1/*
        case v1:
        {
          new com.z1.handler.publicApi.V1Handler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
      }

    }
    catch (InvalidApiKeyException ie)
    {
      logger.log("Invalid api key");

      resp.sendError(401);
      return;
    }
    catch (Throwable e)
    {
      if (e.getMessage().equals(z1.commons.Const.INVALID_ACCESS_TOKEN))
      {
        logger.log(Const.INVALID_ACCESS_TOKEN);
        resp.sendError(401);
        return;
      }

      resp.getWriter().print("{");
      ServletUtil.printFailure(resp.getWriter(), e.getMessage());
      resp.getWriter().print("}");
    }

  }

  // ..................................................
  @Override
  public void doPut(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    // Handle the same way we handle POST
    doPost(req, resp);
  }

  
  // ..................................................
  @Override
  public void doPost(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("text/plain");
    resp.setCharacterEncoding("UTF-8");
    resp.setHeader("Timing-Allow-Origin", "*");
    
    UContext ctx = UContext.getInstance(req, true);
    ULogger logger = ctx.getLogger(getClass());
    
    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/data/<artifact type>/*
      // So we are expecting 5 parts after splitting, the first one being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }
      
      if (!ApiUtils.checkAccess(req, ctx, subParts) ) {
        throw new InvalidApiKeyException();
      }

      // Set the header to support CORS (x-origin requests)
      resp.setHeader("Access-Control-Allow-Origin", "*");
      resp.setHeader("Access-Control-Allow-Headers", "apikey, accesstoken");

      Command type = Command.valueOf(pathParts[1]);
      switch (type)
      {
      // c3/api/v1/*
        case v1:
        {
          new com.z1.handler.publicApi.V1Handler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
      }

    }
    catch (InvalidApiKeyException ie)
    {
      logger.log("Invalid api key");

      resp.sendError(401);
      return;
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);

      if (e.getMessage().equals(z1.commons.Const.INVALID_ACCESS_TOKEN))
      {
        logger.log(Const.INVALID_ACCESS_TOKEN);
        resp.sendError(401);
        return;
      }
      
      resp.getWriter().print("{");
      ServletUtil.printFailure(resp.getWriter(), e.getMessage());
      resp.getWriter().print("}");
    }

  }


}
