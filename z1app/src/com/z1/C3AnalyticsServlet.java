/**
 * Copyright 2011 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.z1.analytics.formatters.AbstractFormatter;
import com.z1.analytics.formatters.MetricKeyValueFormatter;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;

import com.z1.Utils.ApiUtils;
import com.z1.Utils.ResponseMessage;
import com.z1.analytics.postprocessors.IQueryResultPostProcessor;
import com.z1.analytics.postprocessors.IQueryResultPostProcessorDeprecated;
import com.z1.analytics.postprocessors.QueryResultPostProcessorFactory;
import com.z1.analytics.postprocessors.QueryResultPostProcessorFactoryDeprecated;

import udichi.core.App;
import udichi.core.UContext;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import z1.analytics.AnalyticService;
import z1.analytics.QueryOptimizer;
import z1.analytics.QueryResults;
import z1.analytics.commons.AnalyticsUtils;
import z1.analytics.interactionops.InteractionOpsSubsystem;
import z1.analytics.datalake.queryservices.QueryExecutor;
import z1.analytics.query.AnalyticQueryBuilder;
import z1.analytics.query.def.AnalyticQuery;
import z1.analytics.query.def.AnalyticQueryDef;
import z1.analytics.query.def.MetricQueryDef;
import z1.c3.CustomConfig;
import z1.commons.Const;
import z1.commons.macros.DateMacro;

/**
 * Launches endpoint implementations to retrieve metrics from Analytic
 * Subsystems
 */
@SuppressWarnings("serial")
public class C3AnalyticsServlet extends HttpServlet
{
  private enum GetCommand
  {
    query,
    querySankey,
    queryAggr,
    querySankeyFlow
  }

  private enum PostCommand
  {
    query
  }

  /** Enum listing all the available API response formatters. */
  private enum FormatterOption
  {
    metricKeyValue(new MetricKeyValueFormatter());

    /** Internal formatter reference. */
    @Nonnull
    private final AbstractFormatter<?> abstractFormatter;

    /**
     * Initialize the formatter enum.
     * 
     * @param abstractFormatter
     *          Internal formatter reference.
     */
    FormatterOption(@Nonnull final AbstractFormatter<?> abstractFormatter)
    {
      this.abstractFormatter = abstractFormatter;
    }

    /**
     * Get formatter enum from the provided formatter name.
     * 
     * @param param
     *          Formatter name supplied.
     * @return Formatter enum if a valid name is provided else {@code null}
     */
    @Nullable
    public static FormatterOption fromName(@Nullable final String param)
    {
      return Arrays.stream(values()).filter(value -> value.name().equals(param))
          .findFirst().orElse(null);
    }
  }

  private final static String NEW_ANALYTIC_SERVICE_FEATURE_FLAG = "qns";

  // the flag to be used by the external API caller as a query param to avail
  // the required formatter
  private final static String FORMATTER_FLAG = "formatter";

  // ..................................................
  // Gets
  @SuppressWarnings({ "unchecked" })
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("application/json");
    resp.setCharacterEncoding("UTF-8");

    UContext uctx = UContext.getInstance(req);

    try
    {
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }

      GetCommand cmd = GetCommand.valueOf(pathParts[1]);

      ResponseMessage msg = null;
      JsonMarshaller jm = new JsonMarshaller();

      switch (cmd)
      {
        // c3/analytics/query?id=<analyticQueryConfigId>&f=20210501&t=20210505&tu=day
        // Used by charts (except sankey)
        // param "id": mandatory, ID of analyticQuery config that holds the
        // query payload
        // param "f": optional, specify to replace any "fromDate": ${param} in
        // analyticQuery payload
        // param "t": optional, specify to replace any "toDate":${param} in
        // analyticQuery payload
        // param "tu": optional, specify to replace any "timeUnit":${param} in
        // analyticQuery payload
        // Query result will be structured in the new analytic query response
        // format
        case query:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams,
                "Missing required parameter -- analyticQuery config 'id'.");
            resp.getWriter().print(msg.toString());
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, id,
              CustomConfig.Type.analyticQuery, true);
          if (cc == null)
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams, String
                    .format("AnalyticQuery config id '%s' doesn't exist.", id));
            resp.getWriter().print(msg.toString());
            return;
          }

          // Fetch query string for possible parameters for substitution
          String queryString = req.getQueryString();
          String[] parts = queryString.split("&");
          Map<String, String> params = new HashMap<>();
          Arrays.stream(parts).forEach(part -> {
            String[] keyParts = part.split("=");
            String key = keyParts[0];
            if (!key.equals("id") && keyParts.length > 1)
            {
              String value = keyParts[1];
              params.put(key, value);
              // requests will pass in jid/journeyId but in the named queries
              // the param is journeyId so add this param to set the value.
              if (key.equals("jid"))
              {
                params.put("journeyId", value);
              }
            }
          });
          if (req.getParameter("tu") == null) params.put("tu", "day");
          String queryPayload = cc.getPayload();

          // Substitute parameters if any
          if (!params.isEmpty())
          {
            for (Entry<String, String> param : params.entrySet())
            {
              Pattern p = Pattern
                  .compile(Pattern.quote("${" + param.getKey() + "}"));
              Matcher m = p.matcher(queryPayload);
              queryPayload = m.replaceAll(param.getValue());
            }
          }

          boolean tabular = false;
          String tabularStr = req.getParameter("tabular");
          if (tabularStr != null && !tabularStr.isEmpty())
          {
            tabular = Boolean.parseBoolean(tabularStr);
          }

          boolean joinOnGroupBy = true;
          String joinOnGroupByStr = req.getParameter("joinOnGroupBy");
          if (joinOnGroupByStr != null && !joinOnGroupByStr.isEmpty())
          {
            joinOnGroupBy = Boolean.parseBoolean(joinOnGroupByStr);
          }

          boolean useNewAnalyticService = Boolean.parseBoolean(
              req.getParameter(NEW_ANALYTIC_SERVICE_FEATURE_FLAG));

          if (!useNewAnalyticService)
          {
            AnalyticQueryDef qDef = new AnalyticQueryBuilder()
                .loadFromPayload(queryPayload).getQuery();
            List<MetricQueryDef> mqDefs = qDef.getMetricQuery();
            DateMacro dm = new DateMacro(uctx);
            mqDefs.forEach(mqDef -> {
              String fromDate = mqDef.getFromDate();
              if (fromDate != null && fromDate.contains("${")) // date macro is
                                                               // used, resolve
                                                               // it
              {
                mqDef.setFromDate(
                    dm.compute(fromDate, java.util.Collections.emptyMap()));
              }
              String toDate = mqDef.getToDate();
              if (toDate != null && toDate.contains("${")) // date macro is
                                                           // used,
                                                           // resolve it.
              {
                mqDef.setToDate(
                    dm.compute(toDate, java.util.Collections.emptyMap()));
              }
            });

            List<Map<String, Object>> ret = new QueryOptimizer(uctx, tabular)
                .addAnalyticQuery(qDef).execute(true);

            // ZMOB-19077: post processing of the query result
            // assuming all timeUnits in the query list are the same
            List<Map<String, Object>> payloadMap = new JsonMarshaller()
                .readAsObject(queryPayload, List.class);
            if (payloadMap == null || payloadMap.isEmpty()) break;
            Map<String, Object> samplePayload = payloadMap.get(0);
            String timeUnit = (String) samplePayload.getOrDefault("timeUnit",
                "day"), fromDate = (String) samplePayload.get("fromDate"),
                toDate = (String) samplePayload.get("toDate"),
                metric = (String) samplePayload.get("metric");
            if (timeUnit.equals("hour"))
            {
              ret = ApiUtils.postProcessingQueryResultPerHour(ret, fromDate,
                  toDate, params.get("period"), metric);
            }

            IQueryResultPostProcessorDeprecated queryResultPostProcessor = QueryResultPostProcessorFactoryDeprecated
                .getInstance(id);
            if (queryResultPostProcessor != null)
            {
              ret = queryResultPostProcessor.process(uctx, req, ret);
            }

            resp.getWriter().print(jm.serialize(ret));
            break;
          }
          else
          {
            ObjectMapper mapper = App.getInstance().getJsonMapper();
            List<AnalyticQuery> analyticQueries = mapper.readValue(queryPayload,
                new TypeReference<List<AnalyticQuery>>() {
                });

            AnalyticService analyticService = new AnalyticService(uctx);
            List<QueryResults> queryResultsList = new ArrayList<>();
            for (AnalyticQuery analyticQuery : analyticQueries)
            {
              DateMacro dm = new DateMacro(uctx);
              String fromDate = analyticQuery.getFromDate();
              if (fromDate != null && fromDate.contains("${")) // date macro is
                                                               // used, resolve
                                                               // it
              {
                analyticQuery.setFromDate(
                    dm.compute(fromDate, java.util.Collections.emptyMap()));
              }
              String toDate = analyticQuery.getToDate();
              if (toDate != null && toDate.contains("${")) // date macro is
                                                           // used,
                                                           // resolve it.
              {
                analyticQuery.setToDate(
                    dm.compute(toDate, java.util.Collections.emptyMap()));
              }
              QueryResults analyticQueryResult = analyticService
                  .query(analyticQuery, true);
              queryResultsList.add(analyticQueryResult);
            }

            IQueryResultPostProcessor queryResultPostProcessor = QueryResultPostProcessorFactory
                .getInstance(id);
            if (queryResultPostProcessor != null)
            {
              queryResultPostProcessor.execute(uctx, req, queryResultsList);
            }

            Object response = queryResultsList;

            final FormatterOption formatterOption = FormatterOption
                .fromName(req.getParameter(FORMATTER_FLAG));
            if (formatterOption != null)
            {
              response = formatterOption.abstractFormatter
                  .format(queryResultsList, analyticQueries);
            }
            else if (tabular)
            {
              List<Map<String, Object>> tabularResults = new ArrayList<>();

              for (QueryResults queryResults : queryResultsList)
              {
                List<Map<String, Object>> queryResultsValues = queryResults
                    .getValues();
                if (queryResultsValues != null)
                {
                  tabularResults.addAll(queryResults.getValues());
                }
              }
              if (joinOnGroupBy)
              {
                // the queries should all have the same group by so use the
                // first.
                List<String> groupBy = analyticQueries.get(0).getGroupBy();
                tabularResults = AnalyticsUtils
                    .joinResultOnGroupBy(tabularResults, groupBy);
              }

              response = tabularResults;
            }

            resp.getWriter().print(jm.serialize(response));
          }
          break;
        }

        case queryAggr:
        {
          String id = req.getParameter("id");

          if (id == null || id.isEmpty())
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams,
                "Missing required parameter -- analyticQuery config 'id'.");
            resp.getWriter().print(msg.toString());
            return;
          }

          CustomConfig cc = CustomConfig.load(uctx, id,
              CustomConfig.Type.analyticQuery, true);
          if (cc == null)
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams,
                String.format("AnalyticQuery config id '%s' doesn't exist.",
                    id));
            resp.getWriter().print(msg.toString());
            return;
          }

          // Fetch query string for possible parameters for substitution
          String queryString = req.getQueryString();
          String[] parts = queryString.split("&");
          Map<String, String> params = new HashMap<>();
          Arrays.stream(parts).forEach(part -> {
            String[] keyParts = part.split("=");
            String key = keyParts[0];
            if (!key.equals("id") && keyParts.length > 1)
            {
              String value = keyParts[1];
              params.put(key, value);
              // requests will pass in jid/journeyId but in the named queries
              // the param is journeyId so add this param to set the value.
              if (key.equals("jid"))
              {
                params.put("journeyId", value);
              }
            }
          });
          if (req.getParameter("tu") == null)
            params.put("tu", "day");

          String queryPayload = cc.getPayload();

          // Substitute parameters if any
          if (!params.isEmpty())
          {
            for (Entry<String, String> param : params.entrySet())
            {
              Pattern p = Pattern.compile(
                  Pattern.quote("${" + param.getKey() + "}"));
              Matcher m = p.matcher(queryPayload);
              queryPayload = m.replaceAll(param.getValue());
            }
          }

          ObjectMapper mapper = App.getInstance().getJsonMapper();
          List<AnalyticQuery> analyticQueries = mapper.readValue(queryPayload,
              new TypeReference<List<AnalyticQuery>>() {});

          analyticQueries.forEach(analyticQuery -> {
            DateMacro dm = new DateMacro(uctx);
            String fromDate = analyticQuery.getFromDate();
            if (fromDate != null && fromDate.contains("${"))
            {
              analyticQuery.setFromDate(
                  dm.compute(fromDate, Collections.emptyMap()));
            }
            String toDate = analyticQuery.getToDate();
            if (toDate != null && toDate.contains("${"))
            {
              analyticQuery.setToDate(
                  dm.compute(toDate, Collections.emptyMap()));
            }
          });

          QueryExecutor qe = new QueryExecutor(uctx);

          if (analyticQueries.size() > 1)
          {
            Map<String, Object> res = qe.executeQueries(analyticQueries, Collections.emptyMap());
            resp.getWriter().print(Const.jsonMarshaller.serialize(res));
          }
          else
          {
            resp.getWriter().print(Const.jsonMarshaller.serialize(qe.executeQuery(analyticQueries.get(0), Collections.emptyMap())));
          }
          return;
        }

        // c3/analytics/querySankey?jId=<journeyId>&changeLogId=<changeLogId>
        case querySankey:
        {
          String jId = req.getParameter("jId");
          String changeLogId = req.getParameter("changeLogId");

          if (jId == null || jId.isEmpty() || changeLogId == null
              || changeLogId.isEmpty())
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams,
                "Missing or empty parameter 'jid' or 'logid'.");
            resp.getWriter().print(msg.toString());
            return;
          }

          List<Map<String, Object>> analyticQueryMap = new ArrayList<>();
          List<String> metricNames = Arrays.asList(
              InteractionOpsSubsystem.MetricName.totalReached.name(),
              InteractionOpsSubsystem.MetricName.totalInteracted.name(),
              InteractionOpsSubsystem.MetricName.totalDismissed.name(),
              InteractionOpsSubsystem.MetricName.totalNotSent.name(),
              InteractionOpsSubsystem.MetricName.totalSent.name(),
              InteractionOpsSubsystem.MetricName.totalQualified.name(),
              InteractionOpsSubsystem.MetricName.totalNotSentReason.name());

          String filterAttr = "attr";
          String filterOp = "op";
          String filterVal = "val";

          for (String metric : metricNames)
          {
            Map<String, Object> map = new HashMap<>();

            map.put("subsystem", "interactionOps");
            map.put("metric", metric);
            map.put("datasetName", metric);
            map.put("timeUnit", "cumulative");
            map.put("aggregateAs", "SUM");
            // groupBy
            List<String> groupBy = Arrays.asList("date");
            map.put("groupBy", groupBy);

            // filter
            List<Map<String, Object>> filterList = new ArrayList<>();
            // Suppressed Sent is totalQualified when exeMode=silent
            if (metric.equals(
                InteractionOpsSubsystem.MetricName.totalQualified.name()))
            {
              filterList.add(createSourceTargetValueObject(filterAttr,
                  "exeMode", filterOp, "=", filterVal, "silent"));
            }
            else
            { // all other metrics filter without control group
              // Appbox reached metric is captured by BE and not SDK
              String op = "!=";
              if (metric.equals(
                  InteractionOpsSubsystem.MetricName.totalNotSentReason.name()))
              {// totalNotSentReason is use to find NotSentForCG
                op = "=";
              }
              filterList.add(createSourceTargetValueObject(filterAttr,
                  "actionName", filterOp, op, filterVal, "z1Control"));
            }
            // filter
            filterList.add(createSourceTargetValueObject(filterAttr,
                "journeyId", filterOp, "=", filterVal, jId));
            if (!changeLogId.equals("*"))
            {
              filterList.add(createSourceTargetValueObject(filterAttr,
                  "changeLogId", filterOp, "=", filterVal, changeLogId));
            }

            map.put("filter", filterList);
            analyticQueryMap.add(map);
          }

          // Build the payload
          AnalyticQueryDef qDef = new AnalyticQueryBuilder()
              .loadFromPayload(new JsonMarshaller().serialize(analyticQueryMap))
              .getQuery();
          List<Map<String, Object>> ret = new QueryOptimizer(uctx, true)
              .addAnalyticQuery(qDef).execute();

          Map<String, Object> res = getSankeyResponse(ret);
          resp.getWriter().print(jm.serialize(res));

          break;
        }

        case querySankeyFlow:
        {
          boolean useNewAnalyticService = Boolean.parseBoolean(
              req.getParameter(NEW_ANALYTIC_SERVICE_FEATURE_FLAG));

          final String id = useNewAnalyticService ? "sessionFlowSankeyV2"
              : "sessionFlowSankey";
          CustomConfig cc = CustomConfig.load(uctx, id,
              CustomConfig.Type.analyticQuery, true);

          // retrieve the payload
          String queryPayload = cc.getPayload();
          if (req.getParameter("fromDate") == null
              || req.getParameter("toDate") == null)
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams,
                "Date range for the query isn't specified");
            resp.getWriter().print(msg.toString());
            return;
          }
          // substitute the fromDate and toDate in the payload with the params
          Pattern p1 = Pattern.compile(Pattern.quote("${fromDate}"));
          Matcher m1 = p1.matcher(queryPayload);
          queryPayload = m1.replaceAll(req.getParameter("fromDate"));
          Pattern p2 = Pattern.compile(Pattern.quote("${toDate}"));
          Matcher m2 = p2.matcher(queryPayload);
          queryPayload = m2.replaceAll(req.getParameter("toDate"));

          if (!useNewAnalyticService)
          {
            // Build the payload
            AnalyticQueryDef qDef = new AnalyticQueryBuilder()
                .loadFromPayload(queryPayload).getQuery();

            List<Map<String, Object>> ret = new QueryOptimizer(uctx, true)
                .addAnalyticQuery(qDef).execute();
            List<Map<String, Object>> response = new ArrayList<>();
            IQueryResultPostProcessorDeprecated queryResultPostProcessor = QueryResultPostProcessorFactoryDeprecated
                .getInstance(id);
            if (queryResultPostProcessor != null)
            {
              response = queryResultPostProcessor.process(uctx, req, ret);
            }
            if (response.size() > 0 && response.get(0) != null)
            {
              resp.getWriter().print(jm.serialize(response));
            }
            else
            {
              resp.getWriter()
                  .print(jm.serialize(new HashMap<String, Object>()));
            }
          }
          else
          {
            ObjectMapper mapper = App.getInstance().getJsonMapper();
            List<AnalyticQuery> analyticQueries = mapper.readValue(queryPayload,
                new TypeReference<List<AnalyticQuery>>() {
                });

            AnalyticService analyticService = new AnalyticService(uctx);
            List<QueryResults> queryResultsList = new ArrayList<>();
            if (analyticQueries.size() != 1)
            {
              ULogger logger = uctx.getLogger(getClass());
              logger.error("querySankeyFlow expected 1 Query Result but got "
                  + analyticQueries.size());
              resp.getWriter().print(jm.serialize(Collections.emptyList()));
              break;
            }
            AnalyticQuery analyticQuery = analyticQueries.get(0);
            DateMacro dm = new DateMacro(uctx);
            String fromDate = analyticQuery.getFromDate();
            if (fromDate != null && fromDate.contains("${")) // date macro is
                                                             // used, resolve it
            {
              analyticQuery.setFromDate(
                  dm.compute(fromDate, java.util.Collections.emptyMap()));
            }
            String toDate = analyticQuery.getToDate();
            if (toDate != null && toDate.contains("${")) // date macro is used,
                                                         // resolve it.
            {
              analyticQuery.setToDate(
                  dm.compute(toDate, java.util.Collections.emptyMap()));
            }
            // Remove group by values that are null/empty or have not been
            // replaced
            analyticQuery.getGroupBy().removeIf(groupBy -> groupBy == null
                || groupBy.isEmpty() || groupBy.startsWith("${"));
            QueryResults analyticQueryResult = analyticService
                .query(analyticQuery, true);
            queryResultsList.add(analyticQueryResult);

            IQueryResultPostProcessor queryResultPostProcessor = QueryResultPostProcessorFactory
                .getInstance(id);
            if (queryResultPostProcessor != null)
            {
              queryResultPostProcessor.execute(uctx, req, queryResultsList);
            }

            if (queryResultsList.size() != 1)
            {
              // log error expected 1 query results
            }
            List<Map<String, Object>> tabularResults = new ArrayList<>();
            tabularResults.addAll(queryResultsList.get(0).getValues());

            resp.getWriter().print(jm.serialize(tabularResults));
          }

          break;
        }
      }
    }
    catch (IllegalAccessError e)
    {
      resp.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
    }
    catch (IllegalArgumentException e)
    {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND);
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);

      ULogger logger = uctx.getLogger(getClass());
      if (logger.canLog())
      {
        logger.log(e.getMessage());
      }

      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), "Server side error.");
      resp.getWriter().println("}");
    }

  }

  /**
   * Create the filter object from the given source and target values
   * 
   * @param sourceName
   * @param sourceValue
   * @param targetName
   * @param targetValue
   * @return
   */
  private Map<String, Object> createSourceTargetObject(String sourceName,
      String sourceValue, String targetName, String targetValue)
  {
    Map<String, Object> map = new HashMap<>(2);
    map.put(targetName, targetValue);
    map.put(sourceName, sourceValue);
    return map;
  }

  /**
   * Create the map of the source & target object from the given values
   *
   * @param sourceName
   * @param sourceValue
   * @param targetName
   * @param targetValue
   * @param valueName
   * @param value
   * @return
   */
  private Map<String, Object> createSourceTargetValueObject(String sourceName,
      Object sourceValue, String targetName, String targetValue,
      String valueName, Object value)
  {
    Map<String, Object> map = new HashMap<>(3);

    map.put(valueName, value);
    map.put(targetName, targetValue);
    map.put(sourceName, sourceValue);

    return map;
  }

  /**
   * Create the sankey chart response structure
   * 
   * @param ret
   * @return
   */
  private Map<String, Object> getSankeyResponse(List<Map<String, Object>> ret)
  {
    long totalReached = 0;
    long totalInteracted = 0;
    long totalDismissed = 0;
    long totalNotSent = 0;
    long totalNotSentReason = 0;
    long totalSent = 0;
    long totalSuppressedSent = 0;
    for (Map<String, Object> map : ret)
    {
      if (map.get("date") != null && !map.get("date").toString().isEmpty())
      {
        if (map.get(
            InteractionOpsSubsystem.MetricName.totalReached.name()) != null)
          totalReached += (long) map
              .get(InteractionOpsSubsystem.MetricName.totalReached.name());
        if (map.get(
            InteractionOpsSubsystem.MetricName.totalInteracted.name()) != null)
          totalInteracted += (long) map
              .get(InteractionOpsSubsystem.MetricName.totalInteracted.name());
        if (map.get(
            InteractionOpsSubsystem.MetricName.totalDismissed.name()) != null)
          totalDismissed += (long) map
              .get(InteractionOpsSubsystem.MetricName.totalDismissed.name());
        if (map.get(
            InteractionOpsSubsystem.MetricName.totalNotSent.name()) != null)
          totalNotSent += (long) map
              .get(InteractionOpsSubsystem.MetricName.totalNotSent.name());
        if (map
            .get(InteractionOpsSubsystem.MetricName.totalSent.name()) != null)
          totalSent += (long) map
              .get(InteractionOpsSubsystem.MetricName.totalSent.name());
        if (map.get(
            InteractionOpsSubsystem.MetricName.totalQualified.name()) != null)
          totalSuppressedSent += (long) map
              .get(InteractionOpsSubsystem.MetricName.totalQualified.name());
        if (map.get(InteractionOpsSubsystem.MetricName.totalNotSentReason
            .name()) != null)
          totalNotSentReason += (long) map.get(
              InteractionOpsSubsystem.MetricName.totalNotSentReason.name());
      }
    }

    // Create links
    List<Map<String, Object>> links = new ArrayList<>();
    String source = "source";
    String target = "target";
    String value = "value";

    links.add(createSourceTargetValueObject(source, "targetActions", target,
        "actionsTargeted", value, totalSent));
    links.add(createSourceTargetValueObject(source, "targetActions", target,
        "actionsNotTargetedCG", value, totalNotSentReason));
    links.add(createSourceTargetValueObject(source, "targetActions", target,
        "actionsNotTargetedOtherReasons", value, totalNotSent));
    links.add(createSourceTargetValueObject(source, "actionsTargeted", target,
        "actionsReached", value, totalReached));
    links.add(createSourceTargetValueObject(source, "actionsReached", target,
        "actionsInteracted", value, totalInteracted));
    links.add(createSourceTargetValueObject(source, "actionsReached", target,
        "dismissedActions", value, totalDismissed));
    links.add(createSourceTargetValueObject(source, "actionsInteracted", target,
        "interactedAndConverted", value, 0));
    links.add(createSourceTargetValueObject(source, "dismissedActions", target,
        "dismissedYetConverted", value, 0));
    links.add(createSourceTargetValueObject(source, "actionsReached", target,
        "reachedAndConverted", value, 0));
    links.add(createSourceTargetValueObject(source, "targetActions", target,
        "actionsSuppressed", value, totalSuppressedSent));

    // Create nodes
    String nodeSourceName = "displayName";
    String nodeTargetName = "name";
    List<Map<String, Object>> nodes = new ArrayList<>();
    nodes.add(createSourceTargetObject(nodeSourceName,
        "Total Actions to Target", nodeTargetName, "targetActions"));
    nodes.add(createSourceTargetObject(nodeSourceName, "Actions Targeted",
        nodeTargetName, "actionsTargeted"));
    nodes.add(createSourceTargetObject(nodeSourceName,
        "Actions Not Targeted Control Group", nodeTargetName,
        "actionsNotTargetedCG"));
    nodes.add(createSourceTargetObject(nodeSourceName,
        "Actions Not Targeted Other Reasons", nodeTargetName,
        "actionsNotTargetedOtherReasons"));
    nodes.add(createSourceTargetObject(nodeSourceName, "Actions Reached",
        nodeTargetName, "actionsReached"));
    nodes.add(createSourceTargetObject(nodeSourceName, "Actions Interacted",
        nodeTargetName, "actionsInteracted"));
    nodes.add(createSourceTargetObject(nodeSourceName, "Actions Dismissed",
        nodeTargetName, "dismissedActions"));
    nodes.add(createSourceTargetObject(nodeSourceName,
        "Interacted and Converted", nodeTargetName, "interactedAndConverted"));
    nodes.add(createSourceTargetObject(nodeSourceName,
        "Dismissed Yet Converted", nodeTargetName, "dismissedYetConverted"));
    nodes.add(createSourceTargetObject(nodeSourceName, "Reached and Converted",
        nodeTargetName, "reachedAndConverted"));
    nodes.add(createSourceTargetObject(nodeSourceName, "Sent Suppressed",
        nodeTargetName, "actionsSuppressed"));

    Map<String, Object> map = new HashMap<>();
    map.put("nodes", nodes);
    map.put("links", links);

    return map;
  }

  // ..................................................
  public void doPost(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("application/json");
    resp.setCharacterEncoding("UTF-8");

    UContext uctx = UContext.getInstance(req);

    try
    {
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }

      ResponseMessage msg = null;
      JsonMarshaller jm = new JsonMarshaller();

      PostCommand cmd = PostCommand.valueOf(pathParts[1]);
      switch (cmd)
      {
        // c3/analytics/query?tabular=<true|false>&joinOnGroupBy=<true|false>
        // Param “tabular”: Optional. Default is “false”:
        // - When set to “true” will return result “raw” from cube query.
        // Tabular display of multiple metrics should set this param to “true”.
        // - If omit or set to “false”: query result structure will be exactly
        // same
        // as for GET c3/analytics/query
        // Param “joinOnGroupBy”: Optional. Used when tabular is "true" only.
        // Default is "true"
        // - When set to “false”: query result is raw structure of cube query's
        // result.
        // - If omit or set to “true: Request Metrics will be validated for
        // having the
        // same groupBy axes. If validation passed, raw cube query's result will
        // be
        // merged where groupBy axis values are matched.
        // NOTE: tabular display of multiple Journey metrics should use this
        // POST query endpoint
        // with tabular=true to get query result back suitable for tabular
        // display
        case query:
        {
          String payload = ServletUtil.getPayload(req);
          if (payload == null || payload.isEmpty())
          {
            msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                ResponseMessage.Type.invalidParams,
                "Missing or empty request payload for query.");
            resp.getWriter().print(msg.toString());
            return;
          }

          boolean tabular = Boolean.parseBoolean(req.getParameter("tabular"));

          boolean joinOnGroupBy = true;
          String joinOnGroupByStr = req.getParameter("joinOnGroupBy");
          if (joinOnGroupByStr != null && !joinOnGroupByStr.isEmpty())
          {
            joinOnGroupBy = Boolean.parseBoolean(joinOnGroupByStr);
          }

          boolean useNewAnalyticService = Boolean.parseBoolean(
              req.getParameter(NEW_ANALYTIC_SERVICE_FEATURE_FLAG));

          if (!useNewAnalyticService)
          {
            AnalyticQueryDef qDef = new AnalyticQueryBuilder()
                .loadFromPayload(payload).getQuery();

            if (tabular && joinOnGroupBy)
            {
              // Check #1:
              // If tabular=true, then request must have a valid groupBy.
              List<String> gb = qDef.getMetricQuery().get(0).getGroupBy();
              if (gb == null || gb.isEmpty())
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "'groupBy' array is invalid or missing in request payload for QSP tabular=true");
                resp.getWriter().print(msg.toString());
                return;
              }

              // Check #2:
              // Verify groupBy for multiMetric is same
              boolean validJoinOnGroupBy = AnalyticsUtils
                  .validateJoinOnGroupBy(qDef);
              if (!validJoinOnGroupBy)
              {
                msg = new ResponseMessage(uctx, ResponseMessage.Status.fail,
                    ResponseMessage.Type.invalidParams,
                    "Cannot join Query Result on groupBy. "
                        + "Some metrics in the query are either null or do not have same groupBy axes!");
                resp.getWriter().print(msg.toString());
                return;
              }
            }

            List<Map<String, Object>> qResult = new QueryOptimizer(uctx,
                tabular).addAnalyticQuery(qDef).execute();

            if (tabular && joinOnGroupBy)
            {
              List<String> groupBy = qDef.getMetricQuery().get(0).getGroupBy();
              List<Map<String, Object>> joinedQResult = AnalyticsUtils
                  .joinResultOnGroupBy(qResult, groupBy);
              resp.getWriter().print(jm.serialize(joinedQResult));
            }
            else
            {
              resp.getWriter().print(jm.serialize(qResult));
            }
            break;
          }
          else
          {
            ObjectMapper mapper = App.getInstance().getJsonMapper();
            List<AnalyticQuery> analyticQueries = mapper.readValue(payload,
                new TypeReference<List<AnalyticQuery>>() {
                });

            AnalyticService analyticService = new AnalyticService(uctx);
            List<QueryResults> queryResultsList = new ArrayList<>();
            for (AnalyticQuery analyticQuery : analyticQueries)
            {
              QueryResults analyticQueryResult = analyticService
                  .query(analyticQuery, false);
              queryResultsList.add(analyticQueryResult);
            }

            Object response = queryResultsList;

            final FormatterOption formatterOption = FormatterOption
                .fromName(req.getParameter(FORMATTER_FLAG));
            if (formatterOption != null)
            {
              response = formatterOption.abstractFormatter
                  .format(queryResultsList, analyticQueries);
            }
            else if (tabular)
            {
              List<Map<String, Object>> tabularResults = new ArrayList<>();

              for (QueryResults queryResults : queryResultsList)
              {
                List<Map<String, Object>> queryResultValues = queryResults
                    .getValues();
                if (queryResultValues != null)
                {
                  tabularResults.addAll(queryResultValues);
                }
              }
              if (joinOnGroupBy)
              {
                // the queries should all have the same group by so use the
                // first.
                List<String> groupBy = analyticQueries.get(0).getGroupBy();
                tabularResults = AnalyticsUtils
                    .joinResultOnGroupBy(tabularResults, groupBy);
              }

              response = tabularResults;
            }

            resp.getWriter().print(jm.serialize(response));
          }
          break;
        }
      }
    }
    catch (IllegalAccessError e)
    {
      resp.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
    }
    catch (IllegalArgumentException e)
    {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND);
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);

      ULogger logger = uctx.getLogger(getClass());
      if (logger.canLog())
      {
        logger.log(e.getMessage());
      }

      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), "Server side error.");
      resp.getWriter().println("}");
    }
  }
}
