package com.z1.postprocessors;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

import udichi.core.UContext;
import udichi.core.analytic.aggregation.QueryPostProcessor;
import z1.kb.KBDocumentRetreiver;
import z1.kb.KBResponse;
import z1.kb.KBResponse.ContentTuple;

/**
 * Selects Top N items from the query result set. The processor expects the following parameters. 
 * <li>
 * dataPoint: The id of the data point that indicates the values for top N.
 * </li>
 * <li>
 * topN: N of Top N.
 * </li> 
 *  
 *
 */
public class SelectTopNProcessor extends QueryPostProcessor
{
  private final String P_DP = "dataPoint";
  private final String P_TOP_N = "topN";
  private static final int MAX_TITLE_WORDS = 10;
  
  @Override
  public void process(UContext ctx, List<Map<String, Object>> retData,
      Map<String, String> params)
  {
    String idName = params.get(P_DP);// Get the param name of the content id
    if (idName == null) return;
    String topNStr = params.get(P_TOP_N);
    if (topNStr == null) return;
    Integer topN = Integer.parseInt(topNStr);
    
    // If we have less items than the topN, return
    if (topN > retData.size()) return;
    
    // We will create a sorted map based on the type of the id
    java.util.TreeMap<Number, List<Map<String, Object>>> sortedNumMap = 
        new java.util.TreeMap<>((o1, o2) -> o2.intValue() - o1.intValue());
    
    java.util.TreeMap<String, List<Map<String, Object>>> sortedStrMap = 
        new java.util.TreeMap<>((s1, s2) -> s2.compareTo(s1));
    
    boolean isNum = true;
    boolean isFirst = true;
    for (Map<String, Object> m : retData)
    {
      Object val = m.get(idName);
      if (val != null)
      {
        // Get the value
        if (isFirst)
        {
          isNum = (val instanceof Number);
          isFirst = false;
        }
        
        if (isNum)
        {
          Number key = (Number)val;
          List<Map<String, Object>> list = sortedNumMap.get(key);
          if (list == null)
          {
            list = new java.util.ArrayList<>(10);
            sortedNumMap.put(key, list);
          }
          //sortedNumMap.put(key, m);
          list.add(m);
        }
        else
        {
          String key = (String)val;
          List<Map<String, Object>> list = sortedStrMap.get(key);
          if (list == null)
          {
            list = new java.util.ArrayList<>(10);
            sortedStrMap.put(key, list);
          }
          //sortedStrMap.put((String)val, m);
          list.add(m);                   
        }        
      }
    }
    
    retData.clear();
    // We'll now find the top N    
    if (isNum)
    {
      int cnt = 1;
      for (Map.Entry<Number, List<Map<String, Object>>> entry : sortedNumMap.entrySet())
      {
        if (cnt > topN) break;
        List<Map<String, Object>> list = entry.getValue();
        for (Map<String, Object> m : list)
        {
          retData.add(m);
          cnt++;
          if (cnt > topN) break;
        }
      }
    }
    else
    {
      int cnt = 1;
      for (Map.Entry<String, List<Map<String, Object>>> entry : sortedStrMap.entrySet())
      {
        if (cnt > topN) break;
        List<Map<String, Object>> list = entry.getValue();
        for (Map<String, Object> m : list)
        {
          retData.add(m);
          cnt++;
          if (cnt > topN) break;
        }
      }
    }
    
  }
}
