package com.z1.postprocessors;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.analytic.aggregation.QueryPostProcessor;
import z1.core.utils.DateFormatter;
import z1.core.utils.DateFormatter.DateFormat;

/**
 * Reads a date data from the given result set and converts to a given display format.
 *
 */
public class DateFormatterPostProcessor extends QueryPostProcessor
{
  private final String P_DATEID = "id";
  private final String P_READFORMAT = "read";
  private final String P_WRITEFORMAT = "write";
  
  @Override
  public void process(UContext ctx, List<Map<String, Object>> retData,
      Map<String, String> params)
  {
    // Get the param name of the goal id
    String idName = params.get(P_DATEID);
    if (idName == null) return;
    
    String rFormat = params.get(P_READFORMAT);
    String wFormat = params.get(P_WRITEFORMAT);
    if (rFormat == null || wFormat == null) return;
    
    //sort by date
    Collections.sort(retData,new DateComparator(idName));
    
    for (Map<String, Object> m : retData)
    {
      try
      {
       // Convert the date to the specified format
        String date = (String)m.get(idName);
        date = DateFormatter.convert(DateFormat.valueOf(rFormat),DateFormat.valueOf(wFormat), date);    
        m.put(idName, date);
      }
      catch (UException e)
      {
        ctx.getLogger(this.getClass()).severe(e.getLocalizedMessage());
      }     
    }
    
    
  }
  
  public static class DateComparator implements Comparator<Map<String, Object>>
  {
    private String idName;
    public DateComparator(String idName)
    {
      this.idName = idName;
    }

    @Override
    public int compare(Map<String, Object> m1, Map<String, Object> m2)
    {
      String date1 = (String)m1.get(idName);
      String date2 = (String)m2.get(idName);
      
      return date1.compareTo(date2);
    }
    
  }



}
