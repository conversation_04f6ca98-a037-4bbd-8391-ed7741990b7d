/**
 * Post processors process cube query result as a plug-in to udichi cube roll-up query infrastructures.
 * Udichi chart declarative definition also takes the same as it constructs a cube query behind the scene to fetch data for
 * the chart. 
 * <p>
 * The post processors must implement {@link udichi.core.analytic.aggregation.QueryPostProcessor} class.
 * <p>  
 * Below is an example of a cube query. <br>
 * <pre>
 {
  "axis": [
    {
      "id": "Goal IDs", 
      "src": "goalId/${goalId}",
      "hint":[
        {
          "type": "postprocessor",
          "name":"class",
          "value":"com.z1.queryprocessors.GoalQueryProcessor"
        },
        {
          "type": "postprocessor",
          "name":"params",
          "value":"id=Goal IDs"
        }
      ]
    }
  ], 
  ...
  }
 * </pre> 
 * 
 * <p>
 * A chat meta definition is something similar as below.    
 * <pre>   
 {
  "axes": [
    {
      "id": "Goal IDs", 
      "label": "Your Goals",
      "props": {}, 
      "src": "goalId/${goalId}",
      "hint":[
        {
          "type": "postprocessor",
          "name":"class",
          "value":"com.z1.queryprocessors.GoalQueryProcessor"
        },
        {
          "type": "postprocessor",
          "name":"params",
          "value":"id=Goal IDs"
        }
      ]
    }
  ], 
  "control": {
    "chartType": "Columns", 
    "props": {
      "_custom": true, 
      "standalone": true
    }
  }, 
  "cube": {
    "description": null, 
    "id": "cube", 
    "ref": "GoalStatBuilder:cube", 
    "solution": "GoalStatBuilder", 
    "type": "cube"
  }, 
  "dataPoints": [
    {
      "label": "Customers",
      "id": "My Customers", 
      "method": "udc.system.aggregation:sum", 
      "props": {}, 
      "src": "customers"
    }, 
    {
      "label": "Signals",
      "id": "My Signals", 
      "method": "udc.system.aggregation:sum", 
      "props": {}, 
      "src": "signals"
    }, 
    {
      "id": "My Actions", 
      "method": "udc.system.aggregation:sum", 
      "props": {}, 
      "src": "actions"
    }
  ], 
  "description": "", 
  "filters": []
  }
  </pre>

 */
package com.z1.postprocessors;

