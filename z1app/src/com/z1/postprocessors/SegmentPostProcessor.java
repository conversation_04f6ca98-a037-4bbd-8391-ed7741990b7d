package com.z1.postprocessors;

import java.util.List;
import java.util.Map;

import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.analytic.aggregation.QueryPostProcessor;
import z1.c3.Segment;
import z1.kb.KBDocumentRetreiver;
import z1.kb.KBResponse;
import z1.kb.KBResponse.ContentTuple;

/**
 * Replaces segment ID with the segment name. If the title is greater than
 * MAX_TITLE_WORDS it terminates it with "..." If not document is found for this
 * id, removes it from the set
 *
 */
public class SegmentPostProcessor extends QueryPostProcessor
{
  private final String P_ID = "id";
  private static final int MAX_TITLE_WORDS = 10;

  @Override
  public void process(UContext ctx, List<Map<String, Object>> retData,
      Map<String, String> params)
  {
    List<Map<String, Object>> newList = new java.util.ArrayList<>(20);

    String idName = params.get(P_ID);// Get the param name of the content id
    if (idName == null) return;

    for (Map<String, Object> m : retData)
    {
      Segment s = null;             
      String id = (String) m.get(idName);
      if (id != null)
      {
        try
        {
          // Load the content
          s = Segment.load(ctx, id);
          if (s != null)
          {
            // Substitute the id with name
            String title = s.getDef().getName();
            if (title == null) continue;

            // if title is too long, trim it
            String[] words = title.split("\\s+");
            StringBuilder sb = new StringBuilder();
            if (words.length > MAX_TITLE_WORDS)
            {
              for (String w : words)
              {
                sb.append(w).append("\\s");
              }
              sb.append("...");
              title = sb.toString();
            }
            m.put(idName, title);
          }
        }
        catch (UException e)
        {
          // The system will throw this exception if the doc is not present
          // We'll ignore that and won't add that doc.

          // No-Op
        }
      }

      if (s != null)
      {
        // If no content is found, no point showing that data
        newList.add(m);
      }

    }

    retData.clear();
    retData.addAll(newList);
  }
}
