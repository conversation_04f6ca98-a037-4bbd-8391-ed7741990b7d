package com.z1.postprocessors;

import java.util.List;
import java.util.Map;

import udichi.core.UContext;
import udichi.core.analytic.aggregation.QueryPostProcessor;
import z1.c3.Goal;

/**
 * Post processes the goal related query results to substitute goal ID with goal names.
 *
 */
public class GoalQueryProcessor extends QueryPostProcessor
{
  private final String P_ID = "id";
  
  @Override
  public void process(UContext ctx, List<Map<String, Object>> retData,
      Map<String, String> params)
  {
    List<Map<String, Object>> newList = new java.util.ArrayList<>(20);
    // Get the param name of the goal id
    String idName = params.get(P_ID);
    if (idName == null) return;
    
    for (Map<String, Object> m : retData)
    {
      Goal g = null;
      String id = (String) m.get(idName);
      if (id != null)
      {
        // Load the goal object from this id
        g = Goal.load(ctx, id);
        if (g != null)
        {
          // Substitute the id with name
          String name = g.getName();
          m.put(idName, name);
        }
      }
      
      if (g != null)
      {
        // If no goal is found, no point showing that data
        newList.add(m);
      }
    }
    
    retData.clear();
    retData.addAll(newList);
  }

}
