package com.z1.postprocessors;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import udichi.core.UContext;
import udichi.core.analytic.aggregation.QueryPostProcessor;

/**
 * Sorts the result set based on a given field value. The processor expects the
 * following parameters. <li>
 * dataPoint: The id of the data point that indicates the values for top N.</li>
 *
 */
public class SortProcessor extends QueryPostProcessor
{
  private final String P_DP = "dataPoint";
  private final String P_ASC = "ascending";

  @Override
  public void process(UContext ctx, List<Map<String, Object>> retData,
      Map<String, String> params)
  {
    final String idName = params.get(P_DP);// Get the param name of the content
                                           // id
    if (idName == null) return;

    String strAsc = params.get(P_ASC);
    final boolean isAsc = 
        (strAsc != null) ? Boolean.parseBoolean(strAsc) : true;

    // Sort the collection
    Collections.sort(retData, new Comparator<Map<String, Object>>() {

      private boolean isNum = true;
      private boolean isFirst = true;

      @Override
      public int compare(Map<String, Object> o1, Map<String, Object> o2)
      {
        Object val1 = o1.get(idName);
        Object val2 = o2.get(idName);
        
        if (val1 == null && val2 == null) return 0;
        
        if (val1 == null) return -1;
        
        if (val2 == null) return 1;

        if (isFirst)
        {
          isNum = (val1 instanceof Number);
          isFirst = false;
        }

        if (isNum)
        {
          if (isAsc)
          {
            return Long.compare((Long)val2, (Long)val1);
          }
          else
          {
            return Long.compare((Long)val1, (Long)val2);
          }
        }
        else
        {
          if (isAsc)
          {
            return ((String) val1).compareToIgnoreCase((String) val2);
          }
          else
          {
            return ((String) val2).compareToIgnoreCase((String) val1);
          }
        }

      }
    });

  }
}
