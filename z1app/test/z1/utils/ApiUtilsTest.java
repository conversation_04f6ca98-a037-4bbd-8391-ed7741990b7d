package z1.utils;

import com.z1.Utils.ApiUtils;
import org.junit.Assert;
import org.junit.Test;
import udichi.core.util.JsonMarshaller;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

import static org.junit.Assert.*;

public class ApiUtilsTest
{

  @Test
  public void isSdkBlackListedOrObsoleteIsValid()
  {

    try
    {
      modifyTheApiUtilsField("CERTIFIED_SDK_VERSION", "4.202.0");

      boolean isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android",
          "4.202.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202.0.1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202.1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.210.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.212.4.5");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("html5",
          "4.211.0.1-beta1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.211.TPR711");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios",
          "4.211.TPR711-beta1");
      assertFalse(isBlackList);

      // MIN_SDK_VERSION is not set yet. Certified version will still apply
      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0.1");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.201");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("html5", "4.201.0");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.201.0.3");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android",
          "4.201.0.3-beta3");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "3.3.1");
      assertTrue(isBlackList);

      // Setting the MIN_SDK_VERSION similar to CERTIFIED_SDK_VERSION
      modifyTheApiUtilsField("MIN_SDK_VERSION", "4.202.0");

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202.0.1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.202.1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.210.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.212.4.5");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("html5",
          "4.211.0.1-beta1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.211.TPR711");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios",
          "4.211.TPR711-beta1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0.1");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.201");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("html5", "4.201.0");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.201.0.3");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android",
          "4.201.0.3-beta3");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "3.3.1");
      assertTrue(isBlackList);

      // Set the sdk.ver.min less than certified version
      modifyTheApiUtilsField("MIN_SDK_VERSION", "4.192.0");
      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0.1");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.201");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("html5", "4.201.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.201.0.3");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android",
          "4.201.0.3-beta3");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "4.192.0");
      assertFalse(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.191.0");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.191.0.5");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("ios", "4.191.2.3");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "3.3.1");
      assertTrue(isBlackList);

      // Set the sdk.ver.min less than certified version
      modifyTheApiUtilsField("MIN_SDK_VERSION", "4.192.0");

      // Set the sdk.ver.min less than certified version
      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "3.3.1");
      assertTrue(isBlackList);

      isBlackList = ApiUtils.isSdkBlackListedOrObsolete("android", "3.3.3");
      assertTrue(isBlackList);

    }
    catch (Exception e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);
      fail("Expected no exception.");

    }
  }

  private static void modifyTheApiUtilsField(String fieldName, String value)
      throws Exception
  {

    Field origField = ApiUtils.class.getDeclaredField(fieldName);
    origField.setAccessible(true);
    Field modifiersField = origField.getClass().getDeclaredField("modifiers");
    modifiersField.setAccessible(true);
    modifiersField.setInt(origField,
        origField.getModifiers() & ~Modifier.FINAL);
    origField.set(null, value);
  }

  @Test
  public void testPostProcessingQueryResultPerHour(){
    String samplePayload = sampleC3QueryResult();
    List<Map<String, Object>> analyticQueryMap = new JsonMarshaller().readAsObject(samplePayload, List.class);
    analyticQueryMap = ApiUtils.postProcessingQueryResultPerHour(analyticQueryMap,
              "20220831", "20220906", "7", "totalSessions" );
    String actual = new JsonMarshaller().serialize(analyticQueryMap);
    Assert.assertTrue(expectedProcessedC3QueryResult().equals(actual));
  }

  private String sampleC3QueryResult(){
    return "[{\"metric\": \"totalSessions\",\"values\": [{\"date\": \"2022082916\",\"totalSessions\": 2},{\"date\": \"2022083000\",\"totalSessions\": 2}],\"datasetName\": \"lastWeekData\",\"subsystem\": \"session\"},{\"metric\": \"totalSessions\",\"values\": [{\"date\": \"2022090105\",\"totalSessions\": 2},{\"date\": \"2022090207\",\"totalSessions\": 3}],\"datasetName\": \"thisWeekData\",\"subsystem\": \"session\"}]";
  }

  private String expectedProcessedC3QueryResult(){
    return "[{\"metric\": \"totalSessions\",\"values\": [{\"date\": \"2022082916\",\"totalSessions\": 2},{\"date\": \"2022083000\",\"totalSessions\": 2},{\"date\": \"2022082505\",\"totalSessions\": 0},{\"date\": \"2022082607\",\"totalSessions\": 0}],\"datasetName\": \"lastWeekData\",\"subsystem\": \"session\"},{\"metric\": \"totalSessions\",\"values\": [{\"date\": \"2022090105\",\"totalSessions\": 2},{\"date\": \"2022090207\",\"totalSessions\": 3},{\"date\": \"2022090516\",\"totalSessions\": 0},{\"date\": \"2022090600\",\"totalSessions\": 0}],\"datasetName\": \"thisWeekData\",\"subsystem\": \"session\"}]";
  }
}
