package com.z1.handler.api;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import udichi.core.UContext;
import udichi.core.log.ULogger;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ConnectResponseBuilderTest
{

  private static final String NS = "unit_test_ns";
  UContext ctx = UContext.getInstance().setNamespace(NS);
  @Mock
  private HttpServletRequest req;

  @Before
  public void setUp() throws Exception
  {
    req = mock(HttpServletRequest.class);
  }

  @Test
  public void testGetConnectBuild() throws Exception
  {

    UContext uctx = mock(UContext.class);
    ULogger ulogger = mock(ULogger.class);
    Mockito.when(uctx.getLogger(any(Class.class))).thenReturn(ulogger);

    String custId = "sampleId";
    // Setting up mock request
    when(req.getRequestURL())
        .thenReturn(new StringBuffer("http://localhost:8888"));
    when(req.getQueryString()).thenReturn(
        "deviceId=testDeviceId&os=html5&devicetype=desktop&z1SDKVersion=*********&key=encKey_Test");
    when(req.getParameterNames()).thenReturn(Collections.enumeration(
        Arrays.asList("deviceId", "os", "devicetype", "z1SDKVersion", "key")));

    // Mock parameter values
    when(req.getParameter("deviceId")).thenReturn("testDeviceId");
    when(req.getParameter("os")).thenReturn("html5");
    when(req.getParameter("devicetype")).thenReturn("desktop");
    when(req.getParameter("z1SDKVersion")).thenReturn("*********");
    when(req.getParameter("key")).thenReturn("encKey_Test");

    ConnectResponseBuilder classObjUnderTest = new ConnectResponseBuilder();
    Method method = ConnectResponseBuilder.class.getDeclaredMethod(
        "_getConnectBuild", UContext.class, HttpServletRequest.class);
    method.setAccessible(true);
    // Invoke private method
    Map<String, Object> connectParams = (Map<String, Object>) method
        .invoke(classObjUnderTest, ctx, req);

    // Asserts to verify the contents of connectParams map
    assertTrue((Boolean) connectParams.get("loadConfig") == Boolean.FALSE);

    assertEquals("testDeviceId", connectParams.get("deviceId"));
    assertEquals("html5", connectParams.get("os"));
    assertEquals("desktop", connectParams.get("devicetype"));
    assertEquals("*********", connectParams.get("z1SDKVersion"));
    Map<String, Object> otherKeys = (Map<String, Object>) connectParams
        .get("otherKeys");
    assertEquals(1, otherKeys.size());
    assertEquals("encKey_Test", otherKeys.get("key"));
    assertNull("tts should not be in the map", connectParams.get("tts"));

  }

  @Test
  public void testGetConnectBuildWithTTSAndLoadConfig() throws Exception
  {

    UContext uctx = mock(UContext.class);
    ULogger ulogger = mock(ULogger.class);
    Mockito.when(uctx.getLogger(any(Class.class))).thenReturn(ulogger);

    String custId = "sampleId";
    // Setting up mock request
    when(req.getRequestURL())
        .thenReturn(new StringBuffer("http://localhost:8888"));
    when(req.getQueryString()).thenReturn(
        "deviceId=testDeviceId&os=html5&devicetype=desktop&z1SDKVersion=*********&key=encKey_Test&tts=1650000&loadConfig");
    when(req.getParameterNames())
        .thenReturn(Collections.enumeration(Arrays.asList("deviceId", "os",
            "devicetype", "z1SDKVersion", "key", "tts", "loadConfig")));

    // Mock parameter values
    when(req.getParameter("deviceId")).thenReturn("testDeviceId");
    when(req.getParameter("os")).thenReturn("html5");
    when(req.getParameter("devicetype")).thenReturn("desktop");
    when(req.getParameter("z1SDKVersion")).thenReturn("*********");
    when(req.getParameter("key")).thenReturn("encKey_Test");
    when(req.getParameter("tts")).thenReturn("1650000");

    ConnectResponseBuilder classObjUnderTest = new ConnectResponseBuilder();
    Method method = ConnectResponseBuilder.class.getDeclaredMethod(
        "_getConnectBuild", UContext.class, HttpServletRequest.class);
    method.setAccessible(true);
    // Invoke private method
    Map<String, Object> connectParams = (Map<String, Object>) method
        .invoke(classObjUnderTest, ctx, req);

    Map<String, Object> otherKeys = (Map<String, Object>) connectParams
        .get("otherKeys");
    assertEquals(1, otherKeys.size());
    assertEquals("encKey_Test", otherKeys.get("key"));
    assertTrue((Boolean) connectParams.get("loadConfig") == Boolean.TRUE);
    assertTrue("1650000".equals(connectParams.get("tts")));

  }

  @Test
  public void testPostConnectBuild() throws Exception
  {

    UContext uctx = mock(UContext.class);
    ULogger ulogger = mock(ULogger.class);
    Mockito.when(uctx.getLogger(any(Class.class))).thenReturn(ulogger);

    String custId = "sampleId";
    // Setting up mock request
    when(req.getRequestURL())
        .thenReturn(new StringBuffer("http://localhost:8888"));
    // Mock payload
    Map<String, Object> mockPayload = new HashMap<>();
    mockPayload.put("deviceId", "testDeviceId");
    mockPayload.put("os", "html5");
    mockPayload.put("devicetype", "desktop");
    mockPayload.put("z1SDKVersion", "*********");
    mockPayload.put("key", "encKey_Test");

    ConnectResponseBuilder classObjUnderTest = new ConnectResponseBuilder();
    Method method = ConnectResponseBuilder.class.getDeclaredMethod(
        "_postConnectBuild", UContext.class, HttpServletRequest.class,
        Map.class);
    method.setAccessible(true);
    // Invoke private method
    Map<String, Object> connectParams = (Map<String, Object>) method
        .invoke(classObjUnderTest, ctx, req, mockPayload);

    // Asserts to verify the contents of connectParams map
    assertTrue((Boolean) connectParams.get("loadConfig") == Boolean.FALSE);

    assertEquals("testDeviceId", connectParams.get("deviceId"));
    assertEquals("html5", connectParams.get("os"));
    assertEquals("desktop", connectParams.get("devicetype"));
    assertEquals("*********", connectParams.get("z1SDKVersion"));
    Map<String, Object> otherKeys = (Map<String, Object>) connectParams
        .get("otherKeys");
    assertEquals(1, otherKeys.size());
    assertEquals("encKey_Test", otherKeys.get("key"));
    assertNull("tts should not be in the map", connectParams.get("tts"));

  }

  @Test
  public void testPostConnectBuildWithTTSAndLoadConfig() throws Exception
  {

    UContext uctx = mock(UContext.class);
    ULogger ulogger = mock(ULogger.class);
    Mockito.when(uctx.getLogger(any(Class.class))).thenReturn(ulogger);

    String custId = "sampleId";
    // Setting up mock request
    when(req.getRequestURL())
        .thenReturn(new StringBuffer("http://localhost:8888"));

    // Mock payload
    Map<String, Object> mockPayload = new HashMap<>();
    mockPayload.put("deviceId", "testDeviceId");
    mockPayload.put("os", "html5");
    mockPayload.put("devicetype", "desktop");
    mockPayload.put("z1SDKVersion", "*********");
    mockPayload.put("key", "encKey_Test");
    mockPayload.put("tts", 1650000);
    mockPayload.put("loadConfig", true);

    ConnectResponseBuilder classObjUnderTest = new ConnectResponseBuilder();
    Method method = ConnectResponseBuilder.class.getDeclaredMethod(
        "_postConnectBuild", UContext.class, HttpServletRequest.class,
        Map.class);
    method.setAccessible(true);
    // Invoke private method
    Map<String, Object> connectParams = (Map<String, Object>) method
        .invoke(classObjUnderTest, ctx, req, mockPayload);

    Map<String, Object> otherKeys = (Map<String, Object>) connectParams
        .get("otherKeys");
    assertEquals(1, otherKeys.size());
    assertEquals("encKey_Test", otherKeys.get("key"));
    assertTrue((Boolean) connectParams.get("loadConfig") == Boolean.TRUE);
    assertTrue("1650000".equals(connectParams.get("tts")));

  }

  @Test
  public void testPostConnectBuildWithTTSAndLoadConfigAsStringValue()
      throws Exception
  {

    UContext uctx = mock(UContext.class);
    ULogger ulogger = mock(ULogger.class);
    Mockito.when(uctx.getLogger(any(Class.class))).thenReturn(ulogger);

    String custId = "sampleId";
    // Setting up mock request
    when(req.getRequestURL())
        .thenReturn(new StringBuffer("http://localhost:8888"));

    // Mock payload
    Map<String, Object> mockPayload = new HashMap<>();
    mockPayload.put("deviceId", "testDeviceId");
    mockPayload.put("os", "html5");
    mockPayload.put("devicetype", "desktop");
    mockPayload.put("z1SDKVersion", "*********");
    mockPayload.put("key", "encKey_Test");
    mockPayload.put("tts", "1650000");
    mockPayload.put("loadConfig", "true");

    ConnectResponseBuilder classObjUnderTest = new ConnectResponseBuilder();
    Method method = ConnectResponseBuilder.class.getDeclaredMethod(
        "_postConnectBuild", UContext.class, HttpServletRequest.class,
        Map.class);
    method.setAccessible(true);
    // Invoke private method
    Map<String, Object> connectParams = (Map<String, Object>) method
        .invoke(classObjUnderTest, ctx, req, mockPayload);

    Map<String, Object> otherKeys = (Map<String, Object>) connectParams
        .get("otherKeys");
    assertEquals(1, otherKeys.size());
    assertEquals("encKey_Test", otherKeys.get("key"));
    assertTrue((Boolean) connectParams.get("loadConfig") == Boolean.TRUE);
    assertTrue("1650000".equals(connectParams.get("tts")));

  }

}
