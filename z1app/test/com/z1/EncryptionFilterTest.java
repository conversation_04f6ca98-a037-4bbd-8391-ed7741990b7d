package com.z1;

import org.apache.http.HttpHeaders;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import udichi.core.UContext;
import z1.c3.SystemConfig;
import z1.commons.Const;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.MessageFormat;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class EncryptionFilterTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private FilterChain filterChain;

    private EncryptionFilter encryptionFilter;

    private static final String[] uploadEndpoints = new String[] {
            "/profile/upload", "/entity/upload", "/segments/upload",
            "/sslCertUpload/create", "/pushconfig" };
    @Before
    public void setup()
    {
        MockitoAnnotations.openMocks(this);
        encryptionFilter = new EncryptionFilter();
    }

    @Test
    public void testDoFilter_WithRightHeaderString() throws Exception {
        UContext uctx = UContext.getInstance().setNamespace("namespace_com");
        try (MockedStatic<SystemConfig> systemConfig = Mockito
                .mockStatic(SystemConfig.class))
        {
            String headerString = MessageFormat.format(
                    "Cross-Origin-Embedder-Policy{0}require-corp" +
                            "{1}Clear-Site-Data{0}",
                    Const.colonSeparator,
                    Const.valSeparator
            );
            systemConfig.when(
                            () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
                    .thenReturn(headerString);
            int time = 1;
            when(request.getPathInfo()).thenReturn("/eventflow");
            when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);
            encryptionFilter.doFilter(request, response, filterChain);
            verify(filterChain, times(time++)).doFilter(request, response);


            when(request.getPathInfo()).thenReturn("/images");
            encryptionFilter.doFilter(request, response, filterChain);
            verify(filterChain, times(time++)).doFilter(request, response);

            for (String path: uploadEndpoints)
            {
                when(request.getPathInfo()).thenReturn(path);
                encryptionFilter.doFilter(request, response, filterChain);
                verify(filterChain, times(time++)).doFilter(request, response);
            }

            when(request.getHeader(HttpHeaders.USER_AGENT)).thenReturn("PostmanRuntimeuser");
            when(request.getParameter("qa")).thenReturn("true");
            encryptionFilter.doFilter(request, response, filterChain);
            verify(filterChain, times(time++)).doFilter(request, response);

            when(request.getHeader(HttpHeaders.USER_AGENT)).thenReturn("PostmanRuntimeuser");
            when(request.getParameter("qa")).thenReturn("false");
            EncryptionFilter.HtmlServletRequestModifier htmlServletRequestModifier =
                    mock(EncryptionFilter.HtmlServletRequestModifier.class);
            PowerMockito.whenNew(EncryptionFilter.HtmlServletRequestModifier.class)
                    .withArguments(request).thenReturn(htmlServletRequestModifier);
            EncryptionFilter.HtmlServletResponseModifier responseModifier =
                    mock(EncryptionFilter.HtmlServletResponseModifier.class);
            PowerMockito.whenNew(EncryptionFilter.HtmlServletResponseModifier.class)
                    .withArguments(response).thenReturn(responseModifier);
            encryptionFilter.doFilter(request, response, filterChain);
            verify(filterChain, times(time++)).doFilter(any(), any());
        }
    }

    @Test
    public void testDoFilter_WithWrongHeaderString() throws IOException, ServletException
    {
        UContext uctx = UContext.getInstance().setNamespace("namespace_com");
        try (MockedStatic<SystemConfig> systemConfig = Mockito
                .mockStatic(SystemConfig.class)) {
            String headerString = MessageFormat.format(
                    "Cross-Origin-Embedder-Policy{0}require-corp" +
                            "{1}Clear-Site-Data{0}",
                    Const.colonSeparator,
                    Const.valSeparator
            );
            systemConfig.when(
                            () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
                    .thenReturn(headerString);
            systemConfig.when(
                    ()->SystemConfig.getBooleanValue(uctx,
                            "z1.security.end2EndEncryption", false)
            ).thenReturn(true);
            when(request.getPathInfo()).thenReturn("systemconfig/etesec");
            when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);
            try {
                encryptionFilter.doFilter(request, response, filterChain);
            }
            catch (Exception e)
            {
                z1.commons.Utils.printInfo(e.getMessage());
            }
            verify(filterChain, times(0)).doFilter(request, response);
        }
    }
}
