package com.z1.analytics.postprocessors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.junit.Test;
import org.mockito.Mockito;

import udichi.core.UContext;
import udichi.core.log.ULogger;
import z1.analytics.QueryResults;
import z1.analytics.sessionops.SessionOpsSubsystem;

public class SessionScoreBinsPostProcessorTest
{
  @Test
  public void testExecute_nonOneResult()
  {
    UContext uctx = Mockito.mock(UContext.class);
    ULogger logger = Mockito.mock(ULogger.class);
    Mockito.when(uctx.getLogger(SessionScoreBinsPostProcessor.class))
        .thenReturn(logger);
    Mockito.when(logger.canLog()).thenReturn(Boolean.TRUE);
    HttpServletRequest request = mock(HttpServletRequest.class);

    List<QueryResults> results = new ArrayList<>();
    SessionScoreBinsPostProcessor postProcessor = new SessionScoreBinsPostProcessor();
    postProcessor.execute(uctx, request, results);
    verify(logger, times(1)).warning(
        "SessionScoreBinsPostProcessor expected to receive only 1 result, but got 0");

    results.add(new QueryResults());
    results.add(new QueryResults());
    postProcessor.execute(uctx, request, results);
    verify(logger, times(1)).warning(
        "SessionScoreBinsPostProcessor expected to receive only 1 result, but got 2");
  }

  @Test
  public void testExecute_noDataResult()
  {
    UContext uctx = Mockito.mock(UContext.class);
    ULogger logger = Mockito.mock(ULogger.class);
    Mockito.when(uctx.getLogger(SessionScoreBinsPostProcessor.class))
        .thenReturn(logger);
    Mockito.when(logger.canLog()).thenReturn(Boolean.TRUE);
    HttpServletRequest request = mock(HttpServletRequest.class);

    List<QueryResults> results = new ArrayList<>();
    QueryResults result = new QueryResults();
    result.setDatasetName("ds1");
    List<Map<String, Object>> values = new ArrayList<>();
    result.setValues(values);
    results.add(result);
    SessionScoreBinsPostProcessor postProcessor = new SessionScoreBinsPostProcessor();
    postProcessor.execute(uctx, request, results);
    assertTrue((results.get(0).getValues()).isEmpty());
  }

  @Test
  public void testExecute_withResult()
  {
    UContext uctx = Mockito.mock(UContext.class);
    ULogger logger = Mockito.mock(ULogger.class);
    Mockito.when(uctx.getLogger(SessionScoreBinsPostProcessor.class))
        .thenReturn(logger);
    Mockito.when(logger.canLog()).thenReturn(Boolean.TRUE);
    HttpServletRequest request = mock(HttpServletRequest.class);

    List<QueryResults> results = new ArrayList<>();
    QueryResults result = new QueryResults();
    result.setDatasetName("ds1");
    List<Map<String, Object>> values = new ArrayList<>();
    Map<String, Object> value1 = new HashMap<>();
    value1.put(SessionOpsSubsystem.MetricName.totalSessions.name(), BigDecimal.valueOf(34L));
    value1.put(SessionOpsSubsystem.MetricName.totalSuccesses.name(), BigDecimal.valueOf(7L));
    value1.put("scoreBin", "3");
    values.add(value1);
    Map<String, Object> value2 = new HashMap<>();
    value2.put(SessionOpsSubsystem.MetricName.totalSessions.name(), BigDecimal.valueOf(4L));
    value2.put("scoreBin", "5");
    values.add(value2);
    Map<String, Object> value3 = new HashMap<>();
    value3.put(SessionOpsSubsystem.MetricName.totalSessions.name(), BigDecimal.valueOf(3L));
    value3.put(SessionOpsSubsystem.MetricName.totalSuccesses.name(), BigDecimal.valueOf(3L));
    value3.put("scoreBin", "7");
    values.add(value3);
    result.setValues(values);
    results.add(result);
    SessionScoreBinsPostProcessor postProcessor = new SessionScoreBinsPostProcessor();
    postProcessor.execute(uctx, request, results);
    List<Map<String, Object>> postProcessedValues = (List<Map<String, Object>>) results
        .get(0).getValues();
    assertEquals(100, postProcessedValues.size());

    // The values should be in this order
    validatePostProcessedValues(postProcessedValues, 0L, 1, 0L, 0.0, 0.0, 0.0, 0.01);
    validatePostProcessedValues(postProcessedValues, 0L, 2, 0L, 0.0, 0.0, 0.01, 0.02);
    validatePostProcessedValues(postProcessedValues, 34L, 3, 7L, 20.59, 82.93, 0.02, 0.03);
    validatePostProcessedValues(postProcessedValues, 0L, 4, 0L, 0.0, 0.0, 0.03, 0.04);
    validatePostProcessedValues(postProcessedValues, 4L, 5, 0L, 0.0, 9.76, 0.04, 0.05);
    validatePostProcessedValues(postProcessedValues, 0L, 6, 0L, 0.0, 0.0, 0.05, 0.06);
    validatePostProcessedValues(postProcessedValues, 3L, 7, 3L, 100.0, 7.32, 0.06, 0.07);
    validatePostProcessedValues(postProcessedValues, 0L, 8, 0L, 0.0, 0.0, 0.07, 0.08);
    validatePostProcessedValues(postProcessedValues, 0L, 9, 0L, 0.0, 0.0, 0.08, 0.09);
    validatePostProcessedValues(postProcessedValues, 0L, 10, 0L, 0.0, 0.0, 0.09, 0.1);
    validatePostProcessedValues(postProcessedValues, 0L, 11, 0L, 0.0, 0.0, 0.1, 0.11);
    validatePostProcessedValues(postProcessedValues, 0L, 12, 0L, 0.0, 0.0, 0.11, 0.12);
    validatePostProcessedValues(postProcessedValues, 0L, 13, 0L, 0.0, 0.0, 0.12, 0.13);
    validatePostProcessedValues(postProcessedValues, 0L, 14, 0L, 0.0, 0.0, 0.13, 0.14);
    validatePostProcessedValues(postProcessedValues, 0L, 15, 0L, 0.0, 0.0, 0.14, 0.15);
    validatePostProcessedValues(postProcessedValues, 0L, 16, 0L, 0.0, 0.0, 0.15, 0.16);
    validatePostProcessedValues(postProcessedValues, 0L, 17, 0L, 0.0, 0.0, 0.16, 0.17);
    validatePostProcessedValues(postProcessedValues, 0L, 18, 0L, 0.0, 0.0, 0.17, 0.18);
    validatePostProcessedValues(postProcessedValues, 0L, 19, 0L, 0.0, 0.0, 0.18, 0.19);
    validatePostProcessedValues(postProcessedValues, 0L, 20, 0L, 0.0, 0.0, 0.19, 0.2);
    validatePostProcessedValues(postProcessedValues, 0L, 21, 0L, 0.0, 0.0, 0.2, 0.21);
    validatePostProcessedValues(postProcessedValues, 0L, 22, 0L, 0.0, 0.0, 0.21, 0.22);
    validatePostProcessedValues(postProcessedValues, 0L, 23, 0L, 0.0, 0.0, 0.22, 0.23);
    validatePostProcessedValues(postProcessedValues, 0L, 24, 0L, 0.0, 0.0, 0.23, 0.24);
    validatePostProcessedValues(postProcessedValues, 0L, 25, 0L, 0.0, 0.0, 0.24, 0.25);
    validatePostProcessedValues(postProcessedValues, 0L, 26, 0L, 0.0, 0.0, 0.25, 0.26);
    validatePostProcessedValues(postProcessedValues, 0L, 27, 0L, 0.0, 0.0, 0.26, 0.27);
    validatePostProcessedValues(postProcessedValues, 0L, 28, 0L, 0.0, 0.0, 0.27, 0.28);
    validatePostProcessedValues(postProcessedValues, 0L, 29, 0L, 0.0, 0.0, 0.28, 0.29);
    validatePostProcessedValues(postProcessedValues, 0L, 30, 0L, 0.0, 0.0, 0.29, 0.3);
    validatePostProcessedValues(postProcessedValues, 0L, 31, 0L, 0.0, 0.0, 0.3, 0.31);
    validatePostProcessedValues(postProcessedValues, 0L, 32, 0L, 0.0, 0.0, 0.31, 0.32);
    validatePostProcessedValues(postProcessedValues, 0L, 33, 0L, 0.0, 0.0, 0.32, 0.33);
    validatePostProcessedValues(postProcessedValues, 0L, 34, 0L, 0.0, 0.0, 0.33, 0.34);
    validatePostProcessedValues(postProcessedValues, 0L, 35, 0L, 0.0, 0.0, 0.34, 0.35);
    validatePostProcessedValues(postProcessedValues, 0L, 36, 0L, 0.0, 0.0, 0.35, 0.36);
    validatePostProcessedValues(postProcessedValues, 0L, 37, 0L, 0.0, 0.0, 0.36, 0.37);
    validatePostProcessedValues(postProcessedValues, 0L, 38, 0L, 0.0, 0.0, 0.37, 0.38);
    validatePostProcessedValues(postProcessedValues, 0L, 39, 0L, 0.0, 0.0, 0.38, 0.39);
    validatePostProcessedValues(postProcessedValues, 0L, 40, 0L, 0.0, 0.0, 0.39, 0.4);
    validatePostProcessedValues(postProcessedValues, 0L, 41, 0L, 0.0, 0.0, 0.4, 0.41);
    validatePostProcessedValues(postProcessedValues, 0L, 42, 0L, 0.0, 0.0, 0.41, 0.42);
    validatePostProcessedValues(postProcessedValues, 0L, 43, 0L, 0.0, 0.0, 0.42, 0.43);
    validatePostProcessedValues(postProcessedValues, 0L, 44, 0L, 0.0, 0.0, 0.43, 0.44);
    validatePostProcessedValues(postProcessedValues, 0L, 45, 0L, 0.0, 0.0, 0.44, 0.45);
    validatePostProcessedValues(postProcessedValues, 0L, 46, 0L, 0.0, 0.0, 0.45, 0.46);
    validatePostProcessedValues(postProcessedValues, 0L, 47, 0L, 0.0, 0.0, 0.46, 0.47);
    validatePostProcessedValues(postProcessedValues, 0L, 48, 0L, 0.0, 0.0, 0.47, 0.48);
    validatePostProcessedValues(postProcessedValues, 0L, 49, 0L, 0.0, 0.0, 0.48, 0.49);
    validatePostProcessedValues(postProcessedValues, 0L, 50, 0L, 0.0, 0.0, 0.49, 0.5);
    validatePostProcessedValues(postProcessedValues, 0L, 51, 0L, 0.0, 0.0, 0.5, 0.51);
    validatePostProcessedValues(postProcessedValues, 0L, 52, 0L, 0.0, 0.0, 0.51, 0.52);
    validatePostProcessedValues(postProcessedValues, 0L, 53, 0L, 0.0, 0.0, 0.52, 0.53);
    validatePostProcessedValues(postProcessedValues, 0L, 54, 0L, 0.0, 0.0, 0.53, 0.54);
    validatePostProcessedValues(postProcessedValues, 0L, 55, 0L, 0.0, 0.0, 0.54, 0.55);
    validatePostProcessedValues(postProcessedValues, 0L, 56, 0L, 0.0, 0.0, 0.55, 0.56);
    validatePostProcessedValues(postProcessedValues, 0L, 57, 0L, 0.0, 0.0, 0.56, 0.57);
    validatePostProcessedValues(postProcessedValues, 0L, 58, 0L, 0.0, 0.0, 0.57, 0.58);
    validatePostProcessedValues(postProcessedValues, 0L, 59, 0L, 0.0, 0.0, 0.58, 0.59);
    validatePostProcessedValues(postProcessedValues, 0L, 60, 0L, 0.0, 0.0, 0.59, 0.6);
    validatePostProcessedValues(postProcessedValues, 0L, 61, 0L, 0.0, 0.0, 0.6, 0.61);
    validatePostProcessedValues(postProcessedValues, 0L, 62, 0L, 0.0, 0.0, 0.61, 0.62);
    validatePostProcessedValues(postProcessedValues, 0L, 63, 0L, 0.0, 0.0, 0.62, 0.63);
    validatePostProcessedValues(postProcessedValues, 0L, 64, 0L, 0.0, 0.0, 0.63, 0.64);
    validatePostProcessedValues(postProcessedValues, 0L, 65, 0L, 0.0, 0.0, 0.64, 0.65);
    validatePostProcessedValues(postProcessedValues, 0L, 66, 0L, 0.0, 0.0, 0.65, 0.66);
    validatePostProcessedValues(postProcessedValues, 0L, 67, 0L, 0.0, 0.0, 0.66, 0.67);
    validatePostProcessedValues(postProcessedValues, 0L, 68, 0L, 0.0, 0.0, 0.67, 0.68);
    validatePostProcessedValues(postProcessedValues, 0L, 69, 0L, 0.0, 0.0, 0.68, 0.69);
    validatePostProcessedValues(postProcessedValues, 0L, 70, 0L, 0.0, 0.0, 0.69, 0.7);
    validatePostProcessedValues(postProcessedValues, 0L, 71, 0L, 0.0, 0.0, 0.7, 0.71);
    validatePostProcessedValues(postProcessedValues, 0L, 72, 0L, 0.0, 0.0, 0.71, 0.72);
    validatePostProcessedValues(postProcessedValues, 0L, 73, 0L, 0.0, 0.0, 0.72, 0.73);
    validatePostProcessedValues(postProcessedValues, 0L, 74, 0L, 0.0, 0.0, 0.73, 0.74);
    validatePostProcessedValues(postProcessedValues, 0L, 75, 0L, 0.0, 0.0, 0.74, 0.75);
    validatePostProcessedValues(postProcessedValues, 0L, 76, 0L, 0.0, 0.0, 0.75, 0.76);
    validatePostProcessedValues(postProcessedValues, 0L, 77, 0L, 0.0, 0.0, 0.76, 0.77);
    validatePostProcessedValues(postProcessedValues, 0L, 78, 0L, 0.0, 0.0, 0.77, 0.78);
    validatePostProcessedValues(postProcessedValues, 0L, 79, 0L, 0.0, 0.0, 0.78, 0.79);
    validatePostProcessedValues(postProcessedValues, 0L, 80, 0L, 0.0, 0.0, 0.79, 0.8);
    validatePostProcessedValues(postProcessedValues, 0L, 81, 0L, 0.0, 0.0, 0.8, 0.81);
    validatePostProcessedValues(postProcessedValues, 0L, 82, 0L, 0.0, 0.0, 0.81, 0.82);
    validatePostProcessedValues(postProcessedValues, 0L, 83, 0L, 0.0, 0.0, 0.82, 0.83);
    validatePostProcessedValues(postProcessedValues, 0L, 84, 0L, 0.0, 0.0, 0.83, 0.84);
    validatePostProcessedValues(postProcessedValues, 0L, 85, 0L, 0.0, 0.0, 0.84, 0.85);
    validatePostProcessedValues(postProcessedValues, 0L, 86, 0L, 0.0, 0.0, 0.85, 0.86);
    validatePostProcessedValues(postProcessedValues, 0L, 87, 0L, 0.0, 0.0, 0.86, 0.87);
    validatePostProcessedValues(postProcessedValues, 0L, 88, 0L, 0.0, 0.0, 0.87, 0.88);
    validatePostProcessedValues(postProcessedValues, 0L, 89, 0L, 0.0, 0.0, 0.88, 0.89);
    validatePostProcessedValues(postProcessedValues, 0L, 90, 0L, 0.0, 0.0, 0.89, 0.9);
    validatePostProcessedValues(postProcessedValues, 0L, 91, 0L, 0.0, 0.0, 0.9, 0.91);
    validatePostProcessedValues(postProcessedValues, 0L, 92, 0L, 0.0, 0.0, 0.91, 0.92);
    validatePostProcessedValues(postProcessedValues, 0L, 93, 0L, 0.0, 0.0, 0.92, 0.93);
    validatePostProcessedValues(postProcessedValues, 0L, 94, 0L, 0.0, 0.0, 0.93, 0.94);
    validatePostProcessedValues(postProcessedValues, 0L, 95, 0L, 0.0, 0.0, 0.94, 0.95);
    validatePostProcessedValues(postProcessedValues, 0L, 96, 0L, 0.0, 0.0, 0.95, 0.96);
    validatePostProcessedValues(postProcessedValues, 0L, 97, 0L, 0.0, 0.0, 0.96, 0.97);
    validatePostProcessedValues(postProcessedValues, 0L, 98, 0L, 0.0, 0.0, 0.97, 0.98);
    validatePostProcessedValues(postProcessedValues, 0L, 99, 0L, 0.0, 0.0, 0.98, 0.99);
    validatePostProcessedValues(postProcessedValues, 0L, 100, 0L, 0.0, 0.0, 0.99, 1.0);
  }

  private void validatePostProcessedValues(List<Map<String, Object>> values,
      Long expectedTotalSessions, Integer expectedScoreBin,
      Long expectedTotalSuccesses, Double expectedSuccessRate,
      Double expectedPercentOfSessions, Double expectedScoreStart,
      Double expectedScoreEnd)
  {
    Map<String, Object> value = values.get(expectedScoreBin - 1);
    assertEquals(7, value.size());
    assertEquals(
        "Did not get expected total session for score bin " + expectedScoreBin,
        BigDecimal.valueOf(expectedTotalSessions),
        value.get(SessionScoreBinsPostProcessor.TOTAL_SESSIONS_KEY));
    assertEquals(
        "Did not get expected total successes for score bin " + expectedScoreBin,
        BigDecimal.valueOf(expectedTotalSuccesses),
        value.get(SessionScoreBinsPostProcessor.TOTAL_SUCCESSES_KEY));
    assertEquals(
        "Did not get expected score bin for score bin " + expectedScoreBin,
        expectedScoreBin,
        value.get(SessionScoreBinsPostProcessor.SCORE_BIN_KEY));
    String actualExpectedSuccessRate = BigDecimal.valueOf(expectedSuccessRate).toString();
    if (expectedSuccessRate == 0.0)
    {
      actualExpectedSuccessRate = "0.00";
    }
    else if (expectedSuccessRate == 100.0)
    {
      actualExpectedSuccessRate = "100.00";
    }
    assertEquals(
        "Did not get expected success rate for score bin " + expectedScoreBin,
        actualExpectedSuccessRate,
        value.get(SessionScoreBinsPostProcessor.SUCCESS_RATE_KEY).toString());
    String actualPercentOfSessions = expectedPercentOfSessions == 0.0 ? "0.00" : BigDecimal.valueOf(expectedPercentOfSessions).toString();
    assertEquals(
        "Did not get expected percent of sessions for score bin " + expectedScoreBin,
        actualPercentOfSessions,
        value.get(SessionScoreBinsPostProcessor.PERCENT_SESSIONS_KEY).toString());
    assertEquals(
        "Did not get expected score start for score bin " + expectedScoreBin, 0,
        BigDecimal.valueOf(expectedScoreStart).compareTo((BigDecimal) value
            .get(SessionScoreBinsPostProcessor.SCORE_START_KEY)));
    assertEquals(
        "Did not get expected score end for score bin " + expectedScoreBin, 0,
        BigDecimal.valueOf(expectedScoreEnd).compareTo((BigDecimal) 
            value.get(SessionScoreBinsPostProcessor.SCORE_END_KEY)));
  }
  
  @Test
  public void testProcessRecord()
  {
    UContext uctx = Mockito.mock(UContext.class);
    ULogger logger = Mockito.mock(ULogger.class);
    Mockito.when(uctx.getLogger(SessionScoreBinsPostProcessor.class))
        .thenReturn(logger);
    Mockito.when(logger.canLog()).thenReturn(Boolean.TRUE);

    Map<String, Object> scoreBin = new HashMap<>();
    scoreBin.put(SessionOpsSubsystem.MetricName.totalSessions.name(), BigDecimal.valueOf(34L));
    scoreBin.put(SessionOpsSubsystem.MetricName.totalSuccesses.name(), BigDecimal.valueOf(7L));
    scoreBin.put("scoreBin", "3");
    SessionScoreBinsPostProcessor postProcessor = new SessionScoreBinsPostProcessor();
    Map<String, Object> processedRecord = postProcessor.processRecord(logger, scoreBin, BigDecimal.valueOf(100L));
    assertEquals(7, processedRecord.size());
    assertEquals(BigDecimal.valueOf(34L), processedRecord.get(SessionScoreBinsPostProcessor.TOTAL_SESSIONS_KEY));
    assertEquals(BigDecimal.valueOf(7L), processedRecord.get(SessionScoreBinsPostProcessor.TOTAL_SUCCESSES_KEY));
    assertEquals("34.00", processedRecord.get(SessionScoreBinsPostProcessor.PERCENT_SESSIONS_KEY).toString());
    assertEquals(3, processedRecord.get(SessionScoreBinsPostProcessor.SCORE_BIN_KEY));
    assertEquals(0, BigDecimal.valueOf(0.02).compareTo((BigDecimal) processedRecord.get(SessionScoreBinsPostProcessor.SCORE_START_KEY)));
    assertEquals(0, BigDecimal.valueOf(0.03).compareTo((BigDecimal) processedRecord.get(SessionScoreBinsPostProcessor.SCORE_END_KEY)));
    assertEquals("20.59", processedRecord.get(SessionScoreBinsPostProcessor.SUCCESS_RATE_KEY).toString());
    
    scoreBin = new HashMap<>();
    scoreBin.put(SessionOpsSubsystem.MetricName.totalSessions.name(), BigDecimal.ZERO);
    scoreBin.put(SessionOpsSubsystem.MetricName.totalSuccesses.name(), BigDecimal.ZERO);
    scoreBin.put("scoreBin", "3");
    postProcessor = new SessionScoreBinsPostProcessor();
    processedRecord = postProcessor.processRecord(logger, scoreBin, BigDecimal.ZERO);
    assertEquals(7, processedRecord.size());
    assertEquals(BigDecimal.ZERO, processedRecord.get(SessionScoreBinsPostProcessor.TOTAL_SESSIONS_KEY));
    assertEquals("0.00", processedRecord.get(SessionScoreBinsPostProcessor.PERCENT_SESSIONS_KEY).toString());
    assertEquals(BigDecimal.ZERO, processedRecord.get(SessionScoreBinsPostProcessor.TOTAL_SUCCESSES_KEY));
    assertEquals(3, processedRecord.get(SessionScoreBinsPostProcessor.SCORE_BIN_KEY));
    assertEquals(0, BigDecimal.valueOf(0.02).compareTo((BigDecimal) processedRecord.get(SessionScoreBinsPostProcessor.SCORE_START_KEY)));
    assertEquals(0, BigDecimal.valueOf(0.03).compareTo((BigDecimal) processedRecord.get(SessionScoreBinsPostProcessor.SCORE_END_KEY)));
    assertEquals("0.00", processedRecord.get(SessionScoreBinsPostProcessor.SUCCESS_RATE_KEY).toString());
    
    scoreBin = new HashMap<>();
    scoreBin.put(SessionOpsSubsystem.MetricName.totalSuccesses.name(), BigDecimal.ZERO);
    scoreBin.put("scoreBin", "3");
    postProcessor = new SessionScoreBinsPostProcessor();
    processedRecord = postProcessor.processRecord(logger, scoreBin, BigDecimal.ZERO);
    assertEquals(7, processedRecord.size());
    assertEquals(BigDecimal.ZERO, processedRecord.get(SessionScoreBinsPostProcessor.TOTAL_SESSIONS_KEY));
    assertEquals("0.00", processedRecord.get(SessionScoreBinsPostProcessor.PERCENT_SESSIONS_KEY).toString());
    assertEquals(BigDecimal.ZERO, processedRecord.get(SessionScoreBinsPostProcessor.TOTAL_SUCCESSES_KEY));
    assertEquals(3, processedRecord.get(SessionScoreBinsPostProcessor.SCORE_BIN_KEY));
    assertEquals(0, BigDecimal.valueOf(0.02).compareTo((BigDecimal) processedRecord.get(SessionScoreBinsPostProcessor.SCORE_START_KEY)));
    assertEquals(0, BigDecimal.valueOf(0.03).compareTo((BigDecimal) processedRecord.get(SessionScoreBinsPostProcessor.SCORE_END_KEY)));
    assertEquals("0.00", processedRecord.get(SessionScoreBinsPostProcessor.SUCCESS_RATE_KEY).toString());
  }
}
