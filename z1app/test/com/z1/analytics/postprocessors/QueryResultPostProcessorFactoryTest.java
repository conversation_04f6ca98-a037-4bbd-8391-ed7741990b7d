package com.z1.analytics.postprocessors;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

public class QueryResultPostProcessorFactoryTest
{
  @Test
  public void testGetInstance_sessionScoreBins()
  {
    IQueryResultPostProcessor postProcessor = QueryResultPostProcessorFactory
        .getInstance("sessionScoreBinsV2");
    assertTrue(postProcessor instanceof SessionScoreBinsPostProcessor);
  }

  @Test
  public void testGetInstance_anyOtherQuery()
  {
    IQueryResultPostProcessor postProcessor = QueryResultPostProcessorFactory
        .getInstance("queryWithNoPostProcessor");
    assertNull(postProcessor);
  }
}
