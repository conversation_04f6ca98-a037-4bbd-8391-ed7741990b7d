package com.z1;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import udichi.core.UContext;
import z1.c3.SystemConfig;
import z1.commons.Const;

import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.MessageFormat;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SecurityHeaderFilterTest
{

  @Mock
  private HttpServletRequest request;

  @Mock
  private HttpServletResponse response;

  @Mock
  private FilterChain filterChain;

  private SecurityHeadersFilter filter;

  private static final int NO_OF_DEFAULT_HEADERS = 7;

  @Before
  public void setup()
  {
    MockitoAnnotations.openMocks(this);
    filter = new SecurityHeadersFilter();
  }

  @Test
  public void testDoFilter_HeaderInRightFormat()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy{0}require-corp" +
                      "{1}Clear-Site-Data{0}\"cache\",\"cookies\",\"storage\"" +
                      "{1}Cross-Origin-Opener-Policy{0}same-origin-allow-popups" +
                      "{1}X-Permitted-Cross-Domain-Policies{0}all" +
                      "{1}Cross-Origin-Resource-Policy{0}cross-origin" +
                      "{1}Referrer-Policy{0}unsafe-url",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Cross-Origin-Embedder-Policy"),
              eq("require-corp"));
      verify(response).setHeader(eq("Clear-Site-Data"),
              eq("\"cache\",\"cookies\",\"storage\""));
      verify(response).setHeader(eq("Cross-Origin-Opener-Policy"),
              eq("same-origin-allow-popups"));
      verify(response).setHeader(eq("X-Permitted-Cross-Domain-Policies"),
              eq("all"));
      verify(response).setHeader(eq("Cross-Origin-Resource-Policy"),
              eq("cross-origin"));
      verify(response).setHeader(eq("Referrer-Policy"), eq("unsafe-url"));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 6)).setHeader(Mockito.anyString(),
              Mockito.anyString());

      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderStringNotWithRightSeparator()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy{0}require-corp" +
                      "{0}Clear-Site-Data{0}\"cache\",\"cookies\",\"storage\"",
              Const.colonSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS)).setHeader(Mockito.anyString(),
              Mockito.anyString());
      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderStringNotWithRightKeyValFirstSeparator()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy;require-corp" +
                      "{0}Clear-Site-Data{1}\"cache\",\"cookies\",\"storage\"",
              Const.valSeparator,
              Const.colonSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Clear-Site-Data"),
              eq("\"cache\",\"cookies\",\"storage\""));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 1)).setHeader(Mockito.anyString(),
              Mockito.anyString());

      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderStringNotWithRightKeyValOtherSeparator()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy{0}require-corp" +
                      "{1}Clear-Site-Data;\"cache\",\"cookies\",\"storage\"",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Cross-Origin-Embedder-Policy"),
              eq("require-corp"));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 1)).setHeader(Mockito.anyString(),
              Mockito.anyString());
      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderStringNotWithValidKeyFormat()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy{0}require-corp" +
                      "{1}{0}\"cache\",\"cookies\",\"storage\"",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Cross-Origin-Embedder-Policy"),
              eq("require-corp"));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 1)).setHeader(Mockito.anyString(),
              Mockito.anyString());
      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderStringNotWithValidValueFormat()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy{0}require-corp" +
                      "{1}Clear-Site-Data{0}",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Cross-Origin-Embedder-Policy"),
              eq("require-corp"));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 1)).setHeader(Mockito.anyString(),
              Mockito.anyString());
      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderWithSpaces()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy {0} require-corp " +
                      "{1}Clear-Site-Data {0} \"cache\",\"cookies\",\"storage\" ",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Cross-Origin-Embedder-Policy"),
              eq("require-corp"));
      verify(response).setHeader(eq("Clear-Site-Data"),
              eq("\"cache\",\"cookies\",\"storage\""));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 2)).setHeader(Mockito.anyString(),
              Mockito.anyString());
      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderWithUnSupportedKeyValueChar()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin:Embedder-Policy {0} require-corp " +
                      "{1}Clear-Site-Data {0} cache:cookies:storage ",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS)).setHeader(Mockito.anyString(),
              Mockito.anyString());

      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderMultipleEntriesWithSameKeyDifferentValues()
          throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Cross-Origin-Embedder-Policy{0}require-corp" +
                      "{1}Cross-Origin-Embedder-Policy{0}all",
              Const.colonSeparator,
              Const.valSeparator
      );

      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      verify(response).setHeader(eq("Cross-Origin-Embedder-Policy"),
              eq("all"));
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 2)).setHeader(Mockito.anyString(),
              Mockito.anyString());

      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderNotSet() throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = "";
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      _verifyDefaultResponseHeader();
      // 7 times for the default response headers
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS)).setHeader(Mockito.anyString(),
              Mockito.anyString());

      verify(filterChain).doFilter(request, response);
    }

  }

  @Test
  public void testDoFilter_HeaderOverride() throws ServletException, IOException
  {

    UContext uctx = UContext.getInstance().setNamespace("namespace_com");
    try (MockedStatic<SystemConfig> systemConfig = Mockito
            .mockStatic(SystemConfig.class))
    {
      String headerString = MessageFormat.format(
              "Pragma{0}must-revalidate" +
                      "{1}Cache-Control{0}no-cache",
              Const.colonSeparator,
              Const.valSeparator
      );
      systemConfig.when(
                      () -> SystemConfig.getStringValue(uctx, "z1.response.headers", ""))
              .thenReturn(headerString);

      when(request.getPathInfo()).thenReturn("/v1/sample");
      when(request.getHeader(anyString())).thenReturn(null);
      when(request.getAttribute("_udc_ctx_")).thenReturn(uctx);

      filter.doFilter(request, response, filterChain);

      // Verify the response headers
      verify(response).setHeader(SecurityHeadersFilter.HEADER_X_XSS_PROTECTION,
              "1; mode=block");
      verify(response).setHeader(
              SecurityHeadersFilter.HEADER_STRICT_TRANSPORT_SEC,
              "max-age=31536000; includeSubDomains");
      verify(response).setHeader(
              SecurityHeadersFilter.HEADER_X_CONTENT_TYPE_OPT, "nosniff");
      verify(response).setHeader(SecurityHeadersFilter.HEADER_X_Frame_OPT,
              "DENY");
      verify(response).setHeader(eq(SecurityHeadersFilter.HEADER_CACHE_CONTROL),
              eq("no-cache"));
      verify(response).setHeader(eq(SecurityHeadersFilter.HEADER_PRAGMA),
              eq("must-revalidate"));
      // 7 times for the response headers added via code and additional 2 for
      // override
      verify(response, Mockito.times(NO_OF_DEFAULT_HEADERS + 2)).setHeader(Mockito.anyString(),
              Mockito.anyString());

      verify(filterChain).doFilter(request, response);
    }

  }

  private void _verifyDefaultResponseHeader()
  {
    verify(response).setHeader(SecurityHeadersFilter.HEADER_X_XSS_PROTECTION,
            "1; mode=block");
    verify(response).setHeader(
            SecurityHeadersFilter.HEADER_STRICT_TRANSPORT_SEC,
            "max-age=31536000; includeSubDomains");
    verify(response).setHeader(SecurityHeadersFilter.HEADER_X_CONTENT_TYPE_OPT,
            "nosniff");
    verify(response).setHeader(SecurityHeadersFilter.HEADER_X_Frame_OPT,
            "DENY");
    verify(response).setHeader(SecurityHeadersFilter.HEADER_CACHE_CONTROL,
            "no-store");
    verify(response).setHeader(SecurityHeadersFilter.HEADER_PRAGMA, "no-cache");

  }

  @Test
  public void testInit() throws ServletException
  {
    FilterConfig filterConfig = mock(FilterConfig.class);
    filter.init(filterConfig);

  }

  @Test
  public void testDestroy()
  {
    filter.destroy();
  }

}
