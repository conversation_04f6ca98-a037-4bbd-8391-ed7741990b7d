#####################################
# Session AI Properties
#####################################

#Required
#installationType=Development
#installationType=Production

#-----------------------------------
# Zookeeper Connection
#-----------------------------------
zkServer=dev.zineone.com
zkPort=2181

analytics.zkServer=dev.zineone.com
analytics.zkPort=2181

#----------------------------------------------------
# Kerberos identity settings
#----------------------------------------------------
kerberos.principal=<EMAIL>
kerberos.keytab=config/zineone.keytab

#-----------------------------------------
# DataMonitor Sink
#-----------------------------------------
# >>> no need to add leading '/'
slack.default.webhook=T8HNAHG9F/B037F6HF950/jWJYqpa0aSx8YOK84ovyC1RB
datamonitor.disabled=true

#----------------------------------------------
#Email Configuration
#-----------------------------------------------
#Required Fields
#valid values for mail.type are smtp or sendgrid
mail.type=smtp
mail.fromEmail=<EMAIL>

#Optional Fields
mail.fromName=Session AI
mail.replyTo=<EMAIL>

#SendGrid Required Fields
mail.sendgrid.apiKey=

#SMTP Required Fields
mail.smtp.username=<EMAIL>
mail.smtp.password=emluZXFhMTIz
mail.smtp.host=smtp.gmail.com
#port = 465 for ssl / 587 for tls
mail.smtp.port=587
mail.smtp.auth=true
#valid values for mail.smtp.security are ssl or tls
mail.smtp.security=tls
#Required only if mail.smtp.security is set to ssl
mail.smtp.socketFactory.port=465
mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
#-------------------------------------------------

#-------------------------------------------
#Registration email procedure for production
#-------------------------------------------

#flip this switch to true to start sending email to user directly with activation info.
registration.send.email.to.client=false  
email.registeration.internal=<EMAIL>
email.to.zineone.subject=New user requesting invite to join <domain=${domain}>
email.to.user.subject=Your request to sign up. 
#-------------------------------------------

industryList=Select Industry,Insurance,Gaming,Telecom,Banking,Manufacturing,Technology,Other
apps=udc.system.core.ZineOne Processing,udc.system.core.ZineOne Profiles,udc.system.core.common,udc.system.core.CommonOOTB,udc.system.core.EmailProcessing,udc.system.core.TriggerOOTB:1.0,udc.system.core.ActionTemplateOOTB:1.0
blacklist.email.domain=example.com,1and1.com,adelphia.net,att.yahoo.com,aol.com,bellatlantic.net,bellsouth.net,bluelight.net,gobrainstorm.net,cableone.net,charter.net,comcast.net,compaq.net,compuserve.com,concentric.net,cox.net,central.cox.net,east.cox.net,west.cox.net,directnic.com,registerapi.com,earthlink.net,frontiernet.net,godaddy.com,gmail.com,hughes.net,airmail.net,juno.com,lycos.com,mac.com,mail.com,megapathdsl.net,mchsi.com,mindspring.com,mpowercom.net,msn.com,netscape.com,netzero.net,emailworx.net,pacifier.com,peoplepc.com,pipeline.com,rediffmailpro.com,rr.com,yahoo.com,seanet.com,sonic.net,speakeasy.net,sprintpcs.com,sprynet.com,starpower.net,verizon.net,wildblue.net,windstream.net,mail.yahoo.com,ameritech.net,att.net,flash.net,nvbell.net,pacbell.net,prodigy.net,sbcglobal.net,snet.net,swbell.net,wans.net,comcast.net,verizon.net, gte.net, bellatlantic.net,aol.com,verizon.net, gte.net, bellatlantic.net,charter.net,qwest.net,optimum.net,netzero.com,netzero.net,embarq.com,windstream.net,mchsi.com,centurylink.net,CenturyTel.net,CLDS.net,CoastalNow.net,Cochill.net,CSWNet.com,eMadisonRiver.com,eMadisonRiver.net,EMBARQmail.com,GallatinRiver.net,Grics.net,GulfTel.com,MadisonRiver.biz,MebTel.net,frontier.com,frontiernet.net,citlink.net,newnorth.net,gvni.com,epix.net,hughes.net,insightbb.com,clearwire.net,localnet.com,fuse.net,surewest.net,gci.net,alaska.net,acsalaska.net,atuonline.net,mosquitonet.com,wildak.net,xyz.net,aemail4u.com,mail.com,canada.com,caramail.com,care2.com,catholic.org,centralpets.com,computermail.net,dcemail.com,doramail.com,ecologyfund.com,e-mailanywhere.com,easypeasy.com,eboxmail.net,email.com,emailaccount.com,emailaccounts4free.com,emailchoice.com,emailyou.co.uk,emailx.net,eudorawebmail.com,everyone.net,execs2k.com,fastmail.fm,fastermail.com,firstname.com,flashmail.com,fresnomail.com,e-garfield.com,gawab.com,glay.org,gmail.com,gmx.net,Go.com,go2now.com,gotgeekmail.com,graffiti.net,Graffiti.net,sanriotown.com,hotmail.com,hushmail.com,inbox.com,mail.com,joymail.com,junglemate.com,killer.ac.gs,kittymail.com,kukamail.com,lycos.com,mail2world.com,mailcity.com,maktoob.com,meowmail.com,myownemail.com,mypersonalemail.com,myplace.com,myrealbox.com,myway.com,netscape.com,nopeddlers.com,nz11.com,operamail.com,outgun.com,parsmail.com,rediff.com,sitewarp.com,snowboard.ch,www.surfy.net,swissmail.net,ultimateemail.com,ulmail.net
blacklist.error.message=Please use your company email address to sign up. 

##---------------------------------------------------
# Image scanning and verification parameters
# Configuration can be acquired from SRE
#----------------------------------------------------

#----------------------------------------------------
# To enable C3 Help in the portal, point it to ZineOne
#---------------------------------------------------------
#z1.apikey=<production api key>

#----------------------------------------------------
# Cube Query settings
#----------------------------------------------------
z1cube.localcache.ttl=900
z1cube.localcache.max=100000
z1cube.fastdistcache.ttl=900
z1cube.slowdistcache.ttl=14400
z1cube.throttle.max=30
z1cube.throttle.enable=true

#----------------------------------------------------
# Stream Query settings
#----------------------------------------------------
z1stream.localcache.ttl=900
z1stream.localcache.max=100000
z1stream.distcache.ttl=900
z1stream.throttle.max=300
z1stream.throttle.enable=true
disable.execute.system.pipelines.onEvent=false

#------------------------------
event.payload.max=2000000
event.param.size.max=2000

#----------------------------------------------------
# Activity Query settings
#----------------------------------------------------
activity.query.maxdayrange=90

#----------------------------------------------------
# Batch & Segment Evaluation Settings
#----------------------------------------------------
si.batch.timeout.mins=60
segment.batch.timeout.mins=30
segment.slowbatch.size=100
segment.fastbatch.size=500
segment.countdaily.interval=30
segment.counthourly.interval=24


#----------------------------------------------------
# Ad-hoc Query settings
#----------------------------------------------------
z1coprocessors.enable=false
z1coprocessors.clients=P2,P3,P4,P5

#----------------------------------------------------
# Upload settings
#----------------------------------------------------
segment.upload.batch.size=1000
user.upload.batch.size=1000
entity.upload.batch.size=1000

#----------------------------------------------------
# BX Marketplace settings
#----------------------------------------------------
bx.marketplace=false
bx.mp.repot=/home/<USER>/git/module
bx.mp.url=https://mp.zineone.com
bx.mp.apikey=mp@feb2b6b1-269b-47fa-b8fb-840e6d9cf3a5
bx.mp.ns=mktpl_com
#----------------------------------------------------
# Map individual client NS to specific MP NS.
# Each setting requires: client.ns, mp.ns, mp.apikey
# Can list 1 or more settings, separated by commas
#----------------------------------------------------
#bx.client.settings=client.ns:bae_com|mp.ns:dp_com|mp.apikey:mp@73cfe9e7-6cb5-4182-8dd5-28b1133127bc

#----------------------------------------------------
# Chat and AppBox settings
#----------------------------------------------------
chat.enable.agent=false
chat.subject.retrieve.max=100

#----------------------------------------------------
# History of actions in Profile tables
#----------------------------------------------------
max.actions.history=100

#----------------------------------------------------
# Global Coordinated Cache and Cube Scheduled Update settings
#----------------------------------------------------
global.coordinated.cache.enabled=true
global.coordinated.cache.scheduled.delay=1000

cube.scheduled.update.enabled=true
system.cube.scheduled.update.enabled=false
cube.scheduled.update.delay=1000

# Default exclusions:
cube.scheduled.update.exclusions=SegmentMetricHourlyCube,SegmentMetricDailyCube,SegmentMetricMonthlyCube,SegmentRecountMetricCube,MABCube,ControlGroupCube

#----------------------------------------------------
# Scheduled Local Cache Cleaner:
# Will be disabled if value equals 0 or not set.
# Recommended setting to 1hr (3600000)
#----------------------------------------------------
cache.clean.interval.ms=3600000

#----------------------------------------------------
# ZineOne InfoSec
#----------------------------------------------------
ssl.cert.file=/home/<USER>/ssl/zocert.p12
ssl.cert.password=zineone

#----------------------------------------------------
# keystore
#----------------------------------------------------
keystore.type=jks
keystore.file=war/WEB-INF/classes/keystore.jks
keystore.password=test1234
keystore.key.alias=localhost
keystore.key.password=test1234

#----------------------------------------------------
# AES
#----------------------------------------------------
enc.aes.transformation=AES/CBC/PKCS5Padding
enc.aes.secret.key.algorithm=PBKDF2WithHmacSHA1

#----------------------------------------------------
# RSA
#----------------------------------------------------
enc.rsa.transformation=RSA/ECB/PKCS1Padding
enc.rsa.key.algorithm=RSA
enc.rsa.signature.algorithm=SHA256withRSA

#----------------------------------------------------
# SDK versions
#----------------------------------------------------
#Minimum certified sdk version against the platform
sdk.certified.ver=4.191.4

#Minimum sdk version overrides certified version
#sdk.min.ver=4.191.4

# S3 Access
ml.pipeline.datastore.bucket=z1-tmp
ml.pipeline.datastore.profile=z1_dev

# ML data storage
ml.pipeline.datasink.enable=false
ml.pipeline.hbase.store.enable=false
ml.pipeline.datasink.s3.batch.count=1

# ML training and scoring data
ml.pipeline.data.base.folder=ext/data/ml

# ML training notification email list
ml.pipeline.training.email.notification.list=<EMAIL>

# Scoring Endpoint
# When we set this to true. It will use url from the pipeline on C3
ml.score.use.predict.url=true

# This will be used when the ml.score.use.predict.url is set to false
ml.score.endpoint=http://localhost:9001/score

# ----- This is only for testing through fastapi. Only the given pipeline would pass
#ml.score.test.pipeline=ws_epp_v2_test


#Session data
sessiondata.scheduler.interval.sec=130
sessiondata.datasink.s3.batch.count=1
datastore.bucket=z1-tmp
datastore.profile=z1_dev
ns.datastore.bucket=z1-tmp
ns.datastore.profile=z1_dev

infra.subcloud.key=default
server.url=https://localhost:8888

#Profile Cache
cache.max.nsprofiles=1

