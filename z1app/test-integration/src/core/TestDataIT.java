package core;

import org.apache.ignite.configuration.IgniteConfiguration;
import udichi.core.App;
import udichi.core.UContext;
import udichi.gateway.AppServiceFacade;
import z1.c3.CustomConfig;
import z1.c3.Goal;
import z1.c3.Journey;
import z1.c3.Segment;
import z1.c3.SignalPart;
import z1.commons.Const;
import z1.core.Context;
import z1.cube.hbasestore.HBaseCubeHandlerFactory;

import java.util.Calendar;
import java.util.List;

public class TestDataIT {

    public static final String NS = "z1_test_com";
    public static final String host = "http://localhost:8888";
    final static String appId = "udc.system.core.ZineOneUnitTest";

    static
    {

        String zkConnect = "localhost:2182";
        App.getInstance().setProperty("zk.connect", zkConnect);
        String mongoConnect = "localhost:27017";
        App.getInstance().setProperty("mongodb.connect",
                "mongodb://" + mongoConnect);
        String kafkaServers = "localhost:9092";
        App.getInstance().setProperty("kafka.bootstrap.servers", kafkaServers);
        App.init();
        Context context = Context.createInstanceForInternal(NS,
                Const.PROPERTY_FILE);
        App.setCubeHandlerFactorySupplier((ctx) -> {
            return new HBaseCubeHandlerFactory(ctx);
        });

        Context.init();

        System.out.println("App startup: " + appId);
        System.out.println("Udichi zk.connect: " + zkConnect);
        System.out.println(
                "mongodb.connect: " + App.getInstance().getProperty("mongodb.connect"));

        // Start ignite

        IgniteConfiguration cfg = new IgniteConfiguration();
        // cfg.setPeerClassLoadingEnabled(true);
        // Start Ignite node.
        // Ignition.start(cfg);

        // Create local Object Cache
        // Z1ObjectCache.init();

    }

    protected void print(Object o)
    {
        System.out.println(o);
    }

    static protected Context sCtx;

    public static Context getContext()
    {
        UContext uctx = UContext.getInstance().setNamespace(NS);
        return Context.getInstance(uctx);
    }

    protected void start()
    {
        this.startTime = Calendar.getInstance().getTimeInMillis();
        print("----------------");
    }

    protected void start(String str)
    {
        this.startTime = Calendar.getInstance().getTimeInMillis();
        print("----------------");
        print(str);
        print("----------------");
    }

    protected void time()
    {
        long endTime = Calendar.getInstance().getTimeInMillis();
        print(">>>>>> Time taken: " + (endTime - startTime) + " ms");
        this.startTime = endTime;
    }

    protected void time(String text)
    {
        long endTime = Calendar.getInstance().getTimeInMillis();
        if (text != null)
        {
            print(">>>>>> [" + text + "]  Time taken: " + (endTime - startTime)
                    + " ms");
        }
        else
        {
            print(">>>>>> Time taken: " + (endTime - startTime) + " ms");
        }
        this.startTime = endTime;
    }

    protected static void subscribeApp()
    {
        UContext ctx = UContext.getInstance().setNamespace(NS);
        AppServiceFacade asf = new AppServiceFacade(ctx);

        asf.subscribeApp(appId, "Unit test app");
    }

    protected static void unsubscribeApp()
    {
        UContext ctx = UContext.getInstance().setNamespace(NS);
        AppServiceFacade asf = new AppServiceFacade(ctx);
        asf.unsubscribeApp(appId);
    }

    public static void initBeforeClass() throws Exception
    {
        clearAllSegments();
        clearAllSignals();
        clearAllGoals();
        clearAllJourneys();
        clearAllCustomConfigs();
    }

    public static void clearAllSegments()
    {
        Context ctx = getContext();
        UContext uctx = ctx.getUContext();
        for (Segment seg : Segment.loadAll(uctx, true))
        {
            if (seg == null) continue;
            Segment.delete(uctx, seg.getId());
        }
    }

    public static void clearAllSignals()
    {
        Context ctx = getContext();
        UContext uctx = ctx.getUContext();
        for (SignalPart sig : SignalPart.loadAll(uctx))
        {
            if (sig == null) continue;
            SignalPart.delete(uctx, sig.getId());
        }
    }

    public static void clearAllGoals()
    {
        Context ctx = getContext();
        UContext uctx = ctx.getUContext();

        for (Goal item : Goal.loadAll(uctx))
        {
            if (item == null) continue;
            Goal.delete(uctx, item.getId());
        }
    }

    public static void clearAllJourneys()
    {
        Context ctx = getContext();
        UContext uctx = ctx.getUContext();

        for (Journey item : Journey.loadAll(uctx, Journey.Type.c1))
        {
            if (item == null) continue;
            Journey.delete(uctx, item.getId(), Journey.Type.c1);
        }

        for (Journey item : Journey.loadAll(uctx, Journey.Type.campaign))
        {
            if (item == null) continue;
            Journey.delete(uctx, item.getId(), Journey.Type.campaign);
        }

        for (Journey item : Journey.loadAll(uctx, Journey.Type.journey))
        {
            if (item == null) continue;
            Journey.delete(uctx, item.getId(), Journey.Type.journey);
        }
    }

    public static void clearAllCustomConfigs()
    {
        Context ctx = getContext();
        UContext uctx = ctx.getUContext();

        CustomConfig.delete(uctx, CustomConfig.Type.eventInfo);

        List<CustomConfig> ccList = CustomConfig.loadAll(uctx,
                CustomConfig.Type.contextAttribute);
        for (CustomConfig cc : ccList)
        {
            if (cc == null) continue;
            cc.delete();
        }
    }

    long startTime;
}
