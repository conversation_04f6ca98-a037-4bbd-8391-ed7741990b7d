package core;

import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runners.MethodSorters;
import udichi.core.UContext;
import udichi.core.cache.ObjectCache;
import udichi.core.util.JsonMarshaller;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.SystemConfig;
import z1.core.Context;
import z1.core.Profile;
import z1.core.Type.IndexType;
import z1.core.profile.ProfileService;
import z1.core.profile.ProfileUtil;
import z1.core.utils.ProfileIterator;

import java.io.IOException;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class ProfileCreateOnConnectIT extends TestDataIT
{
  // private static final String HOST = "dev.zineone.com:8888";
  // private static final String APIKEY =
  // "zb@2f0d28f6-afcb-4ffc-91e0-24a7757090e8";
  // private static final String NS = "unittest_com";

  // private static final String HOST = "localhost:8888";
  // private static final String APIKEY =
  // "localhost@85f4a6e4-5a63-449d-97a8-a8fe4b4456ca";
  // private static final String NS = "z1_com";

  private static Context _getRemoteContext()
  {
    UContext ctx = UContext.getInstance().setNamespace(NS);
    ctx.setUnitTestMode(true);
    return Context.getInstance(ctx);
  }

  @BeforeClass
  public static void setUpBeforeClass() throws Exception
  {

    Context ctx = _getRemoteContext();
    CustomConfig systemConfig = CustomConfig.load(ctx.getUContext(),
        "systemConfig", Type.systemConfig);
    if (systemConfig != null) systemConfig.delete();
    ProfileUtil pu = new ProfileUtil();
    pu.removeAllProfiles(ctx);
  }

  @AfterClass
  public static void tearDownAfterClass() throws Exception
  {
  }

  @Before
  public void setUp() throws IOException
  {
  }

  @After
  public void tearDown()
  {
    UContext ctx = UContext.getInstance().setNamespace(NS);
    ObjectCache.getInstace(ctx).invalidateAll();
  }

  @Test
  public void testAnonymousUserSendPush() throws Exception
  {
    String pid1 = null;
    Map<String, String> otherKeys = java.util.Collections.emptyMap();

    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;
    try
    {
      String deviceId = "YmnRTfgr542g44";
      pid1 = _connect(deviceId, deviceId, otherKeys);
      _createPushRegForDevice(pid1, deviceId);

      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(deviceId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      assertTrue(this._canSendPushToDevice(pid1, deviceId));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
    }

  }

  @Test
  public void testAnonymousToKnownUser() throws Exception
  {
    String pid1 = null;
    String pid2 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();

    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      String deviceId = "X886fgr542g44";
      pid1 = _connect(deviceId, deviceId, otherKeys);
      _createPushRegForDevice(pid1, deviceId);

      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(deviceId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // Now the user logs in
      pid2 = _connect("X11", deviceId, otherKeys);
      assertTrue(pid1.equals(pid2));

      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // The push reg ID must be found
      assertTrue(this._canSendPushToDevice(pid1, deviceId));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testAnonToKnownUserToAnonWithSelectLastUserOnDevice()
      throws Exception
  {
    CustomConfig systemConfig = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;

    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      // ----
      // Set the last user on device flag
      UContext ctx = getContext().getUContext();
      ctx.setUnitTestMode(true);
      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(ctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(ctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      String deviceId = "poiuytrewq0987654321";
      String cid = "asdffdsa";

      Profile.CreateProfileResult cpRes1 = Profile
          .identityResolver(getContext()).withCustomerId(deviceId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid1 = cpRes1.profile().getProperty(Profile.ID);
      assertTrue(cid1.equals(deviceId));
      pid1 = cpRes1.profile().getKeyValue();

      Profile.CreateProfileResult cpRes2 = Profile
          .identityResolver(getContext()).withCustomerId(cid)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid2 = cpRes2.profile().getProperty(Profile.ID);
      pid2 = cpRes2.profile().getKeyValue();
      assertTrue(cid2.equals(cid));
      assertTrue(pid2.equals(pid1));

      Profile.CreateProfileResult cpRes3 = Profile
          .identityResolver(getContext()).withCustomerId(deviceId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid3 = cpRes3.profile().getProperty(Profile.ID);
      assertTrue(cid3.equals(cid));
      pid3 = cpRes3.profile().getKeyValue();
      assertTrue(pid3.equals(pid1));
    }
    finally
    {
//      Context ctx = _getRemoteContext();
//      Profile.deleteProfile(ctx, pid1);
//      if (!pid1.equals(pid2)) Profile.deleteProfile(ctx, pid2);
//      if (!pid1.equals(pid3)) Profile.deleteProfile(ctx, pid3);
    }
  }

  @Ignore
  @Test
  public void testAnonToKnownUserToAnonWithoutSelectLastUserOnDevice()
      throws Exception
  {
    CustomConfig systemConfig = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;

    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      // ----
      // Set the last user on device flag
      UContext ctx = getContext().getUContext();

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(ctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(ctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "false");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      String deviceId = "qwertyuiop0987654321";
      String cid = "fdsaasdf";

      Profile.CreateProfileResult cpRes1 = Profile
          .identityResolver(getContext()).withCustomerId(deviceId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid1 = cpRes1.profile().getProperty(Profile.ID);
      assertTrue(cid1.equals(deviceId));
      pid1 = cpRes1.profile().getKeyValue();
      assertTrue(pid1!=null);

      Profile.CreateProfileResult cpRes2 = Profile
          .identityResolver(getContext()).withCustomerId(cid)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid2 = cpRes2.profile().getProperty(Profile.ID);
      pid2 = cpRes2.profile().getKeyValue();
      assertTrue(pid2!=null);
      assertTrue(cid2.equals(cid));
      assertTrue(pid2.equals(pid1));

      Profile.CreateProfileResult cpRes3 = Profile
          .identityResolver(getContext()).withCustomerId(deviceId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid3 = cpRes3.profile().getProperty(Profile.ID);
      assertTrue(cid3.equals(deviceId));
      assertFalse(cid3.equals(cid));
      pid3 = cpRes3.profile().getKeyValue();
      assertTrue(pid3!=null);
      assertTrue(pid3.equals(pid1));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      if (!pid1.equals(pid2)) Profile.deleteProfile(ctx, pid2);
      if (!pid1.equals(pid3)) Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testMultipleAnonymousWithSameDeviceId() throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"

    String pid1 = null;
    String pid2 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();

    // Test case will FAIL when otherKeys map is passed Empty.
    // Android SDK sends "apikey" in URL header of every call to ZO. Hence using
    // this.
    // Check ZMOB-6233 for further details.
    otherKeys.put("apikey", "localhost@1f5859aa-1863-41ea-a0f7-d9133103e432");

    try
    {
      String deviceId = "qwerty12345";
      pid1 = _connect(deviceId, deviceId, otherKeys);
      pid2 = _connect(deviceId, deviceId, otherKeys);

      assertTrue(pid1.equals(pid2));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testStartAsAnonThenSecondaryKeyUserWithDIDeqCID() throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    String pid1 = null;
    String pid2 = null;
    final String ENC_ID = "aiuywerd987234";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {
      String deviceId = "iooioioioioi238";
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // adding secondary key to otherkeys
      otherKeys.put("encryptedId", ENC_ID);
      pid2 = _connect(deviceId, deviceId, otherKeys);

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);

      assertTrue(pid1.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testStartAsAnonThenStartAgainAsSameAnonThenStartSecondaryKeyUserWithDIDeqCID()
      throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    // connect as an anonymous user
    // connect again as an anonymous user
    // connect with secondary key while Customer ID == Device ID

    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    final String ENC_ID = "supsupsup3333";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {
      String deviceId = "bbbbkkjjj1357";
      // connect as anon user
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // connect as anon user again
      pid2 = _connect(deviceId, deviceId, otherKeys);

      // adding secondary key to otherkeys
      // connect as anon user with secondary key
      otherKeys.put("encryptedId", ENC_ID);
      pid3 = _connect(deviceId, deviceId, otherKeys);

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);

      // adding secondary key to anonymous profile should not create a new
      // profile
      // it should consume the existing anonymous profile
      assertTrue(pid3.equals(pid1));
      assertTrue(pid1.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testNegativeCaseStartAsAnonThenSecondaryKeyUserWithDIDeqCID()
      throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    String pid1 = null;
    String pid2 = null;
    final String ENC_ID = "aiuywerda987234";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Map<String, String> otherKeys1 = new java.util.HashMap<>();
    Map<String, String> otherKeys2 = new java.util.HashMap<>();

    try
    {
      String deviceId = "jjjjj238";
      // empty encrypted ID when the system first sees this device
      otherKeys1.put("encryptedId", "");
      pid1 = _connect(deviceId, deviceId, otherKeys1);

      // adding secondary key to otherkeys
      otherKeys2.put("encryptedId", ENC_ID);
      pid2 = _connect(deviceId, deviceId, otherKeys2);

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);

      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid1.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testAnonThenTwoDifferentUsersWithSecondaryKey() throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    final String ENC_ID1 = "iuywqehawd";
    final String ENC_ID2 = "lkjfouerw9834";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {
      String deviceId = "897348234234njfw";
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // adding secondary key to otherkeys
      otherKeys.put("encryptedId", ENC_ID1);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid2));

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID1;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // A different user logs in
      otherKeys.clear();
      otherKeys.put("encryptedId", ENC_ID2);
      pid3 = _connect(deviceId, deviceId, otherKeys);
      assertFalse(pid1.equals(pid3));

      h = "encryptedId|" + ENC_ID2;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid3.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testTwoAnonThenTwoDifferentUsersWithSecondaryKey()
      throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    String pid = null;
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    final String ENC_ID1 = "iuywqehawd";
    final String ENC_ID2 = "lkjfouerw9834";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {
      // Anon user
      String deviceId = "897348234234njfw";
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // Another anon login
      pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid.equals(pid1));

      // adding secondary key to otherkeys
      otherKeys.put("encryptedId", ENC_ID1);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid2));

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID1;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // A different user logs in
      otherKeys.clear();
      otherKeys.put("encryptedId", ENC_ID2);
      pid3 = _connect(deviceId, deviceId, otherKeys);
      assertFalse(pid1.equals(pid3));

      h = "encryptedId|" + ENC_ID2;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid3.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testAnonThenUserWithSecondaryKeyAndAnotherAnonThenUserWithSecondaryKey()
      throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    String pid = null;
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    final String ENC_ID1 = "iuywqehawd";
    final String ENC_ID2 = "lkjfouerw9834";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {
      // Anon user
      String deviceId = "897348234234njfw";
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // adding secondary key to otherkeys
      otherKeys.put("encryptedId", ENC_ID1);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid2));

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID1;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // Another anon login
      otherKeys.clear();
      pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid.equals(pid1));

      // A different user logs in
      otherKeys.clear();
      otherKeys.put("encryptedId", ENC_ID2);
      pid3 = _connect(deviceId, deviceId, otherKeys);
      assertFalse(pid2.equals(pid3));

      h = "encryptedId|" + ENC_ID2;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid3.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testAnonThenUserWithSecondaryKeyAndAnotherAnonThenUserWith2aryKeyForNotRememberLastUserOnDevice()
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user - Customer ID == Device ID same as anon user

    CustomConfig systemConfig = null;
    String pid = null;
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    final String ENC_ID1 = "iuywqehawd11";
    final String ENC_ID2 = "lkjfouerw983411";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();
    UContext uctx = ctx.getUContext();

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(uctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(uctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "false");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Anon user
      String deviceId = "897348234234njfw11";
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // adding secondary key to otherkeys
      otherKeys.put("encryptedId", ENC_ID1);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid2));

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID1;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // Another anon login
      otherKeys.clear();
      pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid.equals(pid1));

      // A different user logs in
      otherKeys.clear();
      otherKeys.put("encryptedId", ENC_ID2);
      pid3 = _connect(deviceId, deviceId, otherKeys);
      assertFalse(pid2.equals(pid3));

      h = "encryptedId|" + ENC_ID2;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      // back end should NOT accept empty secondary key values
      // so pid1 should be the same as the profile created with a non-empty
      // secondary key-value pair
      assertTrue(pid3.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testStartAsAnonThenSecondaryKeyUserWithDifferentCIDOnSameDevice()
      throws Exception
  {
    // Settings --> Configurations --> System Settings --> "Remember last known
    // user on device" EQUALS "false"
    // Settings --> Configurations --> System Settings --> "To ignore use of
    // secondary keys" EQUALS "false"

    // Anonymous user - Customer ID == Device ID
    // Secondary Key user
    // - Customer ID !== Device ID
    // - user is now identified by both a secondary key and a new customer ID

    String pid1 = null;
    String pid2 = null;
    final String customerId1 = "asd9678232asd";
    final String ENC_ID = "aiuywerdi987234";
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi, pi2;

    Map<String, String> otherKeys = new java.util.HashMap<>();

    try
    {
      String deviceId = "qwerty1ewerwewe2345";
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // adding secondary key to otherkeys and using a new customerId
      otherKeys.put("encryptedId", ENC_ID);
      pid2 = _connect(customerId1, deviceId, otherKeys);

      // Find profile by Secondary key - encrypted id
      String h = "encryptedId|" + ENC_ID;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);

      // The new user should consume the device profile
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      pi2 = ps.findProfiles(customerId1, IndexType.CUSTOMERID);

      // The new user should consume the device profile
      assertTrue(pid1.equals(pi2.next().getKeyValue()));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testAnonToSecondaryKeythenToAnonToCIDWithSelectLastUserOnDevice()
      throws Exception
  {
    CustomConfig systemConfig = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    String pidx = null;

    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      // ----
      // Set the last user on device flag
      UContext ctx = getContext().getUContext();

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(ctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(ctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      String deviceId = "qwertyuiop0987654321";
      String cid = "fdsaasdf";
      String second = "poiuytr";

      Profile.CreateProfileResult cpRes1 = Profile
          .identityResolver(getContext()).withCustomerId(deviceId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid1 = cpRes1.profile().getProperty(Profile.ID);
      assertTrue(cid1.equals(deviceId));
      pid1 = cpRes1.profile().getKeyValue();

      otherKeys.put("key1", second);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid2.equals(pid1));

      // Find profile by Secondary key - encrypted id
      String h = "key1|" + second;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      assertTrue(pi.next().getKeyValue().equals(pid2));

      // connect anon
      otherKeys.clear();
      pid3 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid3.equals(pid1));

      // connect with CID
      Profile.CreateProfileResult cpResx = Profile
          .identityResolver(getContext()).withCustomerId(cid)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cidx = cpResx.profile().getProperty(Profile.ID);
      pidx = cpResx.profile().getKeyValue();
      assertTrue(pidx.equals(pid1));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      if (!pid1.equals(pid2)) Profile.deleteProfile(ctx, pid2);
      if (!pid1.equals(pid3)) Profile.deleteProfile(ctx, pid3);
      if (!pid1.equals(pidx)) Profile.deleteProfile(ctx, pidx);
      Profile.deleteProfile(ctx, pid1);
    }
  }

  @Ignore
  @Test
  public void testAnonToSecondaryKeythenToAnonToCIDWithDeSelectLastUserOnDevice()
      throws Exception
  {
    CustomConfig systemConfig = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    String pidx = null;

    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      // ----
      // Set the last user on device flag
      UContext ctx = getContext().getUContext();

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(ctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(ctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "false");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      String deviceId = "abcqwertyuiop0987654321";
      String cid = "fdsaasdf123";
      String second = "poiuytr123";

      Profile.CreateProfileResult cpRes1 = Profile
          .identityResolver(getContext()).withCustomerId(deviceId)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      String cid1 = cpRes1.profile().getProperty(Profile.ID);
      assertTrue(cid1.equals(deviceId));
      pid1 = cpRes1.profile().getKeyValue();

      otherKeys.put("key1", second);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid2.equals(pid1));

      // Find profile by Secondary key - encrypted id
      String h = "key1|" + second;
      pi = ps.findProfiles(h, IndexType.CUSTOMKEY);
      assertTrue(pi.next().getKeyValue().equals(pid2));

      // connect anon
      otherKeys.clear();
      pid3 = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid3.equals(pid1));

      // connect with CID
      Profile.CreateProfileResult cpResx = Profile
          .identityResolver(getContext()).withCustomerId(cid)
          .withDeviceId(deviceId).withSecondaryKeyValues(otherKeys)
          .findOrCreateProfile();
      pidx = cpResx.profile().getKeyValue();
      assertTrue(!pidx.equals(pid1));

    }
    finally
    {
      Context ctx = _getRemoteContext();
//      if (!pid1.equals(pid2)) Profile.deleteProfile(ctx, pid2);
//      if (!pid1.equals(pid3)) Profile.deleteProfile(ctx, pid3);
//      if (!pid1.equals(pidx)) Profile.deleteProfile(ctx, pidx);
//      Profile.deleteProfile(ctx, pid1);
    }
  }

  @Test
  public void testMultipleUsersOnSameDevice() throws Exception
  {
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;
    try
    {
      String deviceId = "Y6786fgr542g44";
      pid1 = _connect("MLOD11", deviceId, otherKeys);
      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // The push reg ID must not be found
      assertTrue(_getPushRegForDevice(pid1, deviceId) == null);

      // Now the new user logs in to the same device
      pid2 = _connect("Z11", deviceId, otherKeys);
      assertFalse(pid1.equals(pid2));
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid2.equals(pi.next().getKeyValue()));
      // This user registers
      _createPushRegForDevice(pid2, deviceId);
      // The push reg ID must be found
      assertTrue(this._canSendPushToDevice(pid2, deviceId));
      // assertTrue(_getPushRegForDevice(pid2, deviceId) != null);

      // Now the old user logs in to the same device
      pid3 = _connect("MLOD11", deviceId, otherKeys);
      assertFalse(pid2.equals(pid3));
      assertTrue(pid1.equals(pid3));
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid3.equals(pi.next().getKeyValue()));
      // The push reg ID must be found
      // assertTrue(_getPushRegForDevice(pid3, deviceId) != null);
      assertTrue(this._canSendPushToDevice(pid3, deviceId));
      // The push reg ID must not be found for the old user
      // as he is not the push target any more
      //assertTrue(_getPushRegForDevice(pid2, deviceId) == null);

      // Now the other user logs in to the same device
      pid2 = _connect("Z11", deviceId, otherKeys);
      assertFalse(pid1.equals(pid2));
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid2.equals(pi.next().getKeyValue()));
      // The push reg ID must be found
      // assertTrue(_getPushRegForDevice(pid2, deviceId) != null);
      assertTrue(this._canSendPushToDevice(pid2, deviceId));
      // The push reg ID must not be found for the old user
      // as he is not the push target any more
      assertTrue(_getPushRegForDevice(pid1, deviceId) == null);

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
    }
  }

  @Test
  public void testUserUsedMultipleDevices() throws Exception
  {
    String pid1 = null;
    String pid2 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileIterator pi;

    try
    {
      String deviceId1 = "A7786fgr542g44";
      String deviceId2 = "A667984434sd45";

      pid1 = _connect("K11", deviceId1, otherKeys);
      _createPushRegForDevice(pid1, deviceId1);

      // Now the new user logs in to the other device
      pid2 = _connect("K11", deviceId2, otherKeys);
      assertTrue(pid1.equals(pid2));
      // The push reg ID must not be found, device2 is not registered yet
      assertTrue(_getPushRegForDevice(pid2, deviceId2) == null);
      // The push reg ID must be found, device1 is regsitered
      assertTrue(_getPushRegForDevice(pid2, deviceId1) != null);
      assertTrue(this._canSendPushToDevice(pid2, deviceId1));

      // Find profile by device ID
      ProfileService ps = new ProfileService(_getRemoteContext());
      pi = ps.findProfiles(deviceId1, IndexType.DEVICE);
      assertTrue(pi.hasNext());
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      pi = ps.findProfiles(deviceId2, IndexType.DEVICE);
      assertTrue(pi.hasNext());
      assertTrue(pid1.equals(pi.next().getKeyValue()));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testUserWithCustomKeyLogsInToNewDeviceAnonymously()
      throws Exception
  {
    String pid1 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();

    try
    {
      final String deviceId1 = "MXT6ffrt5q12";
      final String deviceId2 = "KJASD876234887";
      final String ENC_ID = "aiuywer987234";

      otherKeys.put("encryptedId", ENC_ID);

      pid1 = _connect(deviceId1, deviceId1, otherKeys);
      _createPushRegForDevice(pid1, deviceId1);
      // Find profile by device ID
      pi = ps.findProfiles(deviceId1, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find the profile with custom key
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();

      assertTrue(p.getKeyValue().equals(pid1));

      // Now the user goes to a second device anonymously
      String pid2 = _connect(deviceId2, deviceId2,
          java.util.Collections.emptyMap());
      assertFalse(pid1.equals(pid2));
      // The push reg ID must be not found for device2
      assertTrue(_getPushRegForDevice(pid2, deviceId2) == null);

      // Now the user logs in and uses the custom keys
      pid2 = _connect(deviceId2, deviceId2, otherKeys);
      // We must get the same Profile
      assertTrue(pid1.equals(pid2));
      // Both devices must point to the new user
      pi = ps.findProfiles(deviceId1, IndexType.DEVICE);
      assertTrue(pid2.equals(pi.next().getKeyValue()));
      pi = ps.findProfiles(deviceId2, IndexType.DEVICE);
      p = pi.next();
      assertTrue(pid2.equals(p.getKeyValue()));
      // Last device on the profile must be the last one
      assertTrue(
          deviceId2.equals(p.getProperty(Profile.Fields.lastDeviceId.value)));
      // The push reg ID must be found for device1 for this profile
      // assertTrue(_getPushRegForDevice(pid2, deviceId1) != null);
      assertTrue(this._canSendPushToDevice(pid2, deviceId1));

    }
    finally
    {
      Profile.deleteProfile(ctx, pid1);
    }
  }

  @Test
  public void testUserWithCustomKey() throws Exception
  {
    String pid1 = null;
    String pid = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();

    try
    {
      final String customerId1 = "asd9678232asd";
      final String customerId2 = "87sdbwg8973e";
      final String customerId3 = "asjdhg87623kjhscvmne";
      final String deviceId = "MXT6ffrt5q12";
      final String ENC_ID = "asdh437643asd";

      otherKeys.put("encryptedId", ENC_ID);

      // User connects with custom key
      pid1 = _connect(deviceId, deviceId, otherKeys);
      _createPushRegForDevice(pid1, deviceId);
      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find the profile with custom key
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p.getKeyValue().equals(pid1));

      // Now a customer ID is assigned
      pid = _connect(customerId1, deviceId, otherKeys);
      assertTrue(pid1.equals(pid));

      // Now a new user with a different customer ID but having the same
      // secondary
      // key. We should pickup the old profile, but the ID will change to the
      // new.
      pid = _connect(customerId2, deviceId, otherKeys);
      assertTrue(pid1.equals(pid));
      p = Profile.instance(ctx, pid);
      String cid = p.getProperty(Profile.ID);
      assertTrue(customerId2.equals(cid));
      // Find by 2 customers ID will give us the same profile
      pi = ps.findProfiles(customerId2, IndexType.CUSTOMERID);
      assertTrue(pid.equals(pi.next().getKeyValue()));
      pi = ps.findProfiles(customerId1, IndexType.CUSTOMERID);
      assertTrue(pid.equals(pi.next().getKeyValue()));

    }
    finally
    {
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid);
    }
  }

  @Test
  public void testUserWithCustomKeyLogsInAfterDeviceUsedByOther()
      throws Exception
  {
    String pid1 = null;
    String pid2 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();

    try
    {
      final String deviceId = "MXT6ffrt5q12";
      final String ENC_ID = "asdh437643asd";

      otherKeys.put("encryptedId", ENC_ID);

      // User connects with custom key
      pid1 = _connect(deviceId, deviceId, otherKeys);
      _createPushRegForDevice(pid1, deviceId);
      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find the profile with custom key
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p.getKeyValue().equals(pid1));

      // Now a new user logs in to the same device
      pid2 = _connect("AA11", deviceId, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid2));
      // Device must point to the new user with the same profile
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid2.equals(pi.next().getKeyValue()));
      assertTrue(this._canSendPushToDevice(pid2, deviceId));

      // Now the old user logs in to the same device with device Id = customer
      // ID
      // but with the same custom key
      String pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid));
      // Device must point to the old user
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid.equals(pi.next().getKeyValue()));
      // The push reg ID must be found for this profile
      // assertTrue(_getPushRegForDevice(pid, deviceId) != null);
      assertTrue(this._canSendPushToDevice(pid, deviceId));

    }
    finally
    {
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testTwoUserLogInWithCustomKeyToSameDevice() throws Exception
  {
    String pid1 = null;
    String pid2 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();

    try
    {
      final String deviceId = "MXT6ffrt5q1211A";
      final String ENC_ID_1 = "asdh437643asdxx";
      final String ENC_ID_2 = "cddh437643acdyy";

      otherKeys.put("encryptedId", ENC_ID_1);

      // User connects with custom key
      pid1 = _connect(deviceId, deviceId, otherKeys);
      // Find the profile with custom key
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p.getKeyValue().equals(pid1));
      // Now a new user logs in to the same device with different encryptedId
      otherKeys.put("encryptedId", ENC_ID_2);
      pid2 = _connect(deviceId, deviceId, otherKeys);
      // The profileId of the two users shouldn't be same
      assertFalse(pid1.equals(pid2));
    }
    finally
    {
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testTwoAnonUserLogInWithCustomKeyToSameDeviceWithSelectLastUserOnDevice()
      throws Exception
  {
    CustomConfig systemConfig = null;
    String pid0 = null;
    String pid1 = null;
    String pid2 = null;
    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();
    UContext uctx = ctx.getUContext();

    try
    {
      // ----
      // Set the last user on device flag
      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(uctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(uctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Users log in

      final String deviceId = "MXT6ffrt5q1211A";
      final String ENC_ID_1 = "asdh437643asdxx";
      final String ENC_ID_2 = "cddh437643acdyy";

      // First the connect call is made as an anonymous user without any
      // customkeys
      otherKeys.clear();

      pid0 = _connect(deviceId, deviceId, otherKeys);

      // New User with unique id logs in
      otherKeys.put("encryptedId", ENC_ID_1);

      // User connects with custom key
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // This users profile should match the anonymous profile
      assertTrue(pid0.equals(pid1));

      // Find the profile with custom key
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p.getKeyValue().equals(pid1));
      // Now a new user logs in to the same device with different encryptedId
      otherKeys.put("encryptedId", ENC_ID_2);

      pid2 = _connect(deviceId, deviceId, otherKeys);
      // The profileId of the two users shouldn't be same
      assertFalse(pid1.equals(pid2));
    }
    finally
    {
      Profile.deleteProfile(ctx, pid0);
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
    }
  }

  @Test
  public void testExistingAndNewUsersLogInWithCustomKeyAndSwapDeviceWithSelectLastUserOnDevice()
      throws Exception
  {
    // Use case:
    // Remember Last Known User On Device: TRUE
    // Two Devices belonging to two users
    // Anon User on D1 (pid0)
    // CK1 logs in to D1 (pid0)
    // CK2 logs in to D1 (pid1)
    // Anon User on D2 (pid2)
    // CK1 on D2 (pid0)
    // CK3 on D2 (pid3)

    CustomConfig systemConfig = null;
    String pid0 = null;
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    String pid4 = null;
    String pid5 = null;

    Map<String, String> otherKeys = new java.util.HashMap<>();
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();
    UContext uctx = ctx.getUContext();

    try
    {
      // ----
      // Set the last user on device flag
      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(uctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(uctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Users log in

      final String deviceId = "MXT6ffrt5q1211A";
      final String deviceId2 = "MXT6ffrt5q1211B";
      final String ENC_ID_1 = "asdh437643asdxx";
      final String ENC_ID_2 = "cddh437643acdyy";
      final String ENC_ID_3 = "cddh437643asdxx";

      // First the connect call is made as an anonymous user without any
      // customkeys
      otherKeys.clear();

      pid0 = _connect(deviceId, deviceId, otherKeys);

      // New User with unique id logs in
      otherKeys.put("encryptedId", ENC_ID_1);

      // User connects with custom key - ENC_ID_1
      pid1 = _connect(deviceId, deviceId, otherKeys);

      // This users profile should match the anonymous profile
      assertTrue(pid0.equals(pid1));

      // Find the profile with custom key - ENC_ID_1
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p.getKeyValue().equals(pid1));

      // Now a new user logs in to the same device with different encryptedId -
      // ENC_ID_2
      otherKeys.put("encryptedId", ENC_ID_2);

      pid2 = _connect(deviceId, deviceId, otherKeys);
      // The profileId of the two users shouldn't be same
      assertFalse(pid1.equals(pid2));

      // Then an anonymous user logs in to another new device
      otherKeys.clear();

      pid3 = _connect(deviceId2, deviceId2, otherKeys);

      // User 1 then logs in to this new device - ENC_ID_1
      otherKeys.put("encryptedId", ENC_ID_1);

      pid4 = _connect(deviceId2, deviceId2, otherKeys);

      // Existing User in system
      // - confirm that it is the exiting user
      // - existing user and anonymous profile should NOT match
      assertTrue(pid4.equals(pid1));
      assertTrue(p.getKeyValue().equals(pid4));
      assertFalse(pid3.equals(pid4));

      // New User logs in to this new device AFTER existing user logs in -
      // ENC_ID_3

      otherKeys.put("encryptedId", ENC_ID_3);

      pid5 = _connect(deviceId2, deviceId2, otherKeys);

      // new user on new device AFTER existing user logged out
      // Find the profile with custom key - ENC_ID_3
      Profile p1 = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p1.getKeyValue().equals(pid5));

      // - device and new user profile should NOT match
      // - existing user and new user profiles should NOT match
      assertFalse(pid3.equals(pid5));
      assertFalse(pid5.equals(pid4));

    }
    finally
    {
      Profile.deleteProfile(ctx, pid0);
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
      Profile.deleteProfile(ctx, pid4);
      Profile.deleteProfile(ctx, pid5);
    }
  }

  @Test
  public void testPushRegAfterAnonymousConnects() throws Exception
  {
    String pid1 = null;
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      String deviceId = "9890bc96c84a1ac3";
      pid1 = _connect(deviceId, deviceId, java.util.Collections.emptyMap());
      _createPushRegForDevice(pid1, deviceId);

      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(deviceId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // User connects again
      String pid = _connect(deviceId, deviceId,
          java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid));
      // String pushReg = _getPushRegForDevice(pid, deviceId);
      // // The push reg ID must be found
      // assertTrue(pushReg != null);
      assertTrue(this._canSendPushToDevice(pid, deviceId));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
    }
  }

  @Test
  public void testCanSendPushToAnonymousUserAfterLogInUser() throws Exception
  {
    String pid1 = null;
    String pid2 = null;
    String pid3 = null;
    String pid4 = null;
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      String deviceId1 = "9890bc96c84a1ac3";
      String deviceId2 = "jashd876234nkqwe9123";
      String customerId = "1006523456";
      // Anonymous user connects
      pid1 = _connect(deviceId1, deviceId1, java.util.Collections.emptyMap());
      _createPushRegForDevice(pid1, deviceId1);
      assertTrue(this._isDeviceProfileForPush(pid1, deviceId1));

      // User logsIn with CustId
      pid2 = _connect(customerId, deviceId1, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid2));
      assertTrue(this._isDeviceProfileForPush(pid2, deviceId1));
      assertTrue(this._isDeviceProfileForPush(pid1, deviceId1));

      // Anonymous user connect from second device
      pid3 = _connect(deviceId2, deviceId2, java.util.Collections.emptyMap());
      _createPushRegForDevice(pid3, deviceId2);
      assertTrue(this._isDeviceProfileForPush(pid3, deviceId2));

      // Known User logs In
      pid4 = _connect(customerId, deviceId2, java.util.Collections.emptyMap());
      assertFalse(pid3.equals(pid4));
      assertTrue(this._isDeviceProfileForPush(pid4, deviceId2));
      // This device should get marked true for push ..so that we dont send push
      assertFalse(this._isDeviceProfileForPush(pid3, deviceId2));

    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      Profile.deleteProfile(ctx, pid3);
      Profile.deleteProfile(ctx, pid4);
    }
  }

  @Test
  public void testSelectLastUserOnDeviceForAnonymousUser()
  {
    CustomConfig systemConfig = null;
    UContext uctx = _getRemoteContext().getUContext();
    String pid1 = null;
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      ///////////
      // SEtting the flag so that the old user on the device should be picked
      // up when an anonymous user logs in.

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(uctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(uctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Connecting ........

      String customerId = "1006523456";
      String deviceId = "9890bc96c84a1ac3";

      // Connect as a regular user
      pid1 = _connect(customerId, deviceId, java.util.Collections.emptyMap());
      _createPushRegForDevice(pid1, deviceId);

      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(customerId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // User connects again, but anonymously.
      // NOTE: Here we should pick up the old profile on the device as we set
      // the flag
      String pid = _connect(deviceId, deviceId,
          java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid));
      // String pushReg = _getPushRegForDevice(pid, deviceId);
      // // The push reg ID must be found
      // assertTrue(pushReg != null);
      assertTrue(this._canSendPushToDevice(pid, deviceId));

      // The customer ID should remain the old customer ID
      Profile p = Profile.instance(_getRemoteContext(), pid);
      String cid = p.getProperty(Profile.ID);
      assertTrue(customerId.equals(cid));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);

      if (systemConfig != null) systemConfig.delete();
    }

  }

  @Ignore
  @Test
  public void testNegativeCaseSelectLastUserOnDeviceForAnonymousUser()
  {
    CustomConfig systemConfig = null;
    UContext uctx = _getRemoteContext().getUContext();
    String pid1 = null;
    String pid2 = null;
    String dProfile = null;
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      ///////////
      // SEtting the flag so that the old user on the device should be picked
      // up when an anonymous user logs in.

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(uctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(uctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "false");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Connecting ........

      String customerId = "1006523456";
      String customerId2 = "3476349349";
      // String oldDeviceId = "87eruytwe09723";
      String deviceId = "9890bc96c84a1ac3";

      // Connect as an anonymous user
      String dPid = _connect(deviceId, deviceId,
          java.util.Collections.emptyMap());

      // Connect as a regular user to the same device
      pid1 = _connect(customerId, deviceId, java.util.Collections.emptyMap());
      // Should get the same profile given to this customer - customer using
      // this new device
      // for the first time
      assertTrue(dPid.equals(pid1));

      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(customerId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // User connects again, but anonymously to the old device.
      dProfile = _connect(deviceId, deviceId, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(dProfile));

      // Now new customer logs in
      pid2 = _connect(customerId2, deviceId, java.util.Collections.emptyMap());
      assertFalse(dProfile.equals(pid2));

      // Again an anonymous user logs in, we should get the device profile back
      String pid = _connect(deviceId, deviceId,
          java.util.Collections.emptyMap());
      assertFalse(pid2.equals(pid));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);

      if (systemConfig != null) systemConfig.delete();
    }

  }

  @Test
  public void testSelectLastUserOnDeviceForAnonymousUserAfterSecondDevice()
  {
    CustomConfig systemConfig = null;
    UContext uctx = _getRemoteContext().getUContext();
    String pid1 = null;
    String pid2 = null;
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    try
    {
      ///////////
      // SEtting the flag so that the old user on the device should be picked
      // up when an anonymous user logs in.

      Map<String, Object> map = null;
      systemConfig = CustomConfig.load(uctx, "systemConfig", Type.systemConfig);
      if (systemConfig == null)
      {
        systemConfig = CustomConfig.create(uctx, Type.systemConfig);
        map = new java.util.HashMap<>();
      }
      else
      {
        String sc = systemConfig.getPayload();
        if (sc != null)
        {
          map = new JsonMarshaller().readAsObject(sc, Map.class);
        }
      }

      assertTrue(map != null);
      map.put("z1.useLastProfileOnDevice", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Connecting ........

      String customerId = "1006523765348423";
      String deviceId1 = "9890bc96c84aasd87234";
      String deviceId2 = "jashd876234nkqwe9123";

      // Connect as a regular user
      pid1 = _connect(customerId, deviceId1, java.util.Collections.emptyMap());

      // Find profile by device ID
      pi = ps.findProfiles(deviceId1, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(customerId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // Connect to the second device
      pid2 = _connect(customerId, deviceId2, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid2));
      // Find profile by device ID
      pi = ps.findProfiles(deviceId2, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));
      // Find profile by customer ID
      pi = ps.findProfiles(customerId, IndexType.CUSTOMERID);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // User connects again, but anonymously to the first device.
      // NOTE: Here we should pick up the old profile on the device as we set
      // the flag
      String pid = _connect(deviceId1, deviceId1,
          java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid));

      // The customer ID should remain the old customer ID
      Profile p = Profile.instance(_getRemoteContext(), pid);
      String cid = p.getProperty(Profile.ID);
      assertTrue(customerId.equals(cid));
    }
    finally
    {
      Context ctx = _getRemoteContext();
      Profile.deleteProfile(ctx, pid1);

      if (systemConfig != null) systemConfig.delete();
    }

  }

  // NOTE : Oct 25, 2017
  // This test case is needed for a gap in our SDK, which will eventually be
  // resolved. Today SDK is not sending the secondary keys along with the
  // customer
  // ID so that the back-end can coorelate them to the same profile.
  // We will introduce a flag to ignore the secondary keys.
  @Test
  public void testSelectLastUserOnDeviceAndIgnoreSecondaryKey()
  {
    CustomConfig systemConfig = null;
    UContext uctx = _getRemoteContext().getUContext();
    String pid = null;
    String pid1 = null;
    String pid2 = null;
    ProfileService ps = new ProfileService(_getRemoteContext());
    ProfileIterator pi;

    Context ctx = _getRemoteContext();

    try
    {
      ///////////
      // SEtting the flag so that the old user on the device should be picked
      // up when an anonymous user logs in.

      systemConfig = CustomConfig.create(uctx, Type.systemConfig);
      Map<String, Object> map = new java.util.HashMap<>();
      map.put("z1.useLastProfileOnDevice", "true");
      map.put("z1.ignoreSecondaryKey", "true");

      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      // Connecting ........

      String customerId = "jhgsad7653242hw";
      String customerId2 = "jhsgd87234kjsd";
      String deviceId = "9890bc96c84auytwqe8763244";
      final String ENC_ID = "asdjg765234";

      Map<String, String> otherKeys = new java.util.HashMap<>();
      otherKeys.put("encryptedId", ENC_ID);

      // Connect as anonymous user without sec key
      pid1 = _connect(deviceId, deviceId, java.util.Collections.emptyMap());
      // Find profile by device ID
      pi = ps.findProfiles(deviceId, IndexType.DEVICE);
      assertTrue(pid1.equals(pi.next().getKeyValue()));

      // Now connect as a valid user on the same device
      pid = _connect(customerId, deviceId, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid));

      // Connect as anonymous user
      pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid));
      // We'll still find by secondary key
      Profile p = Profile.identityResolver(ctx)
          .withSecondaryKeyValues(otherKeys).findAProfile();
      assertTrue(p != null);
      assertTrue(pid1.equals(p.getKeyValue()));

      // If the connect call comes without the secondary keys, what the SDK is
      // doing
      // now. We'll assume the anonymous profile (remember we ignored the
      // secondary key)
      pid = _connect(customerId, deviceId, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid));

      // Now the user connects with a customer ID and sends the other Keys.
      // It should still be the same user as cutomer ID is same
      pid = _connect(customerId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid));

      // Now another profile logs in with same secondary keys
      pid2 = _connect(customerId2, deviceId, otherKeys);
      assertFalse(pid1.equals(pid2));

      // Then a connect call comes with anonymous ID and the secondary key.
      // This should pick up the last know user on the device as that flag
      // is set now
      pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid2.equals(pid));
      p = Profile.instance(ctx, pid);
      String cid = p.getProperty(Profile.ID);
      assertTrue(customerId2.equals(cid));

      // Now the first user logs back in
      pid = _connect(customerId, deviceId, java.util.Collections.emptyMap());
      assertTrue(pid1.equals(pid));

      // And another anonymous connect came, this time it should pick up pid1
      pid = _connect(deviceId, deviceId, otherKeys);
      assertTrue(pid1.equals(pid));
      p = Profile.instance(ctx, pid);
      cid = p.getProperty(Profile.ID);
      assertTrue(customerId.equals(cid));

    }
    finally
    {
      Profile.deleteProfile(ctx, pid);
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      if (systemConfig != null) systemConfig.delete();
    }
  }

  @Test
  public void testCreateProfileWithCustomerIdIsTheOnlyKeyAllowed()
  {
    Context ctx = _getRemoteContext();
    CustomConfig systemConfig = null;
    UContext uctx = ctx.getUContext();
    String pid = null;
    String pid1 = null;
    String pid2 = null;
    ProfileService ps = new ProfileService(ctx);

    try
    {
      SystemConfig.putValue(uctx, "z1.customerIdOnlyKey", "false");

      String customerId1 = "jhgsad7653242hw";
      String customerId2 = "jhsgd87234kjsd";
      String deviceId = "9890bc96c84auytwqe8763244";

      final String EMAIL = "<EMAIL>";
      final String PHONE = "1234567890";

      Map<String, String> otherKeys = new java.util.HashMap<>();
      otherKeys.put("email", EMAIL);
      otherKeys.put("phone", PHONE);

      pid1 = _connect(customerId1, deviceId, otherKeys);
      pid2 = _connect(customerId2, deviceId, otherKeys);

      // 1. Default setting "z1.customerIdOnlyKey" false
      Boolean customerIdOnlyKey = SystemConfig.getBooleanValue(uctx,
          "z1.customerIdOnlyKey", false);
      assertFalse(customerIdOnlyKey); // Checking if flag is set correctly

      // As they have the same email, they should be the same profile
      assertTrue(pid1.equals(pid2));

      // Searching a profile by email
      Profile p = ps.findAProfile(EMAIL, IndexType.EMAIL);
      assertTrue(p != null);

      // Searching a profile by phone
      p = ps.findAProfile(PHONE, IndexType.PHONE);
      assertTrue(p != null);

      // delete them now
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);

      // 2. Setting "z1.customerIdOnlyKey" true
      // Now set the flag to ignore email as primary key
      systemConfig = CustomConfig.create(uctx, Type.systemConfig);
      Map<String, Object> map = new java.util.HashMap<>();
      map.put("z1.customerIdOnlyKey", "true");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      customerIdOnlyKey = SystemConfig.getBooleanValue(uctx,
          "z1.customerIdOnlyKey", false);
      assertTrue(customerIdOnlyKey); // Checking if flag is set correctly

      ObjectCache.getInstace(uctx).invalidateAll();

      // Run the same, they should fail
      pid1 = _connect(customerId1, deviceId, otherKeys);
      pid2 = _connect(customerId2, deviceId, otherKeys);
      assertFalse(pid1.equals(pid2));

      // No index entry for email and phone created when flag is true
      p = ps.findAProfile(customerId1, IndexType.CUSTOMERID);

      String em = p.getProperty(Profile.Fields.email.value);
      assertTrue(em == null);
      String ph = p.getProperty(Profile.Fields.phone.value);
      assertTrue(ph == null);

      // Return null profile if We search by email
      p = ps.findAProfile(EMAIL, IndexType.EMAIL);
      assertTrue(p == null);

      // Return null profile if We search by phone
      p = ps.findAProfile(EMAIL, IndexType.PHONE);
      assertTrue(p == null);

      // The api call will return null
      assertTrue(Profile.identityResolver(ctx).withPrimaryKeyValues(otherKeys)
          .findAProfile() == null);
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);

      // 3. Setting "z1.customerIdOnlyKey" false again
      map.put("z1.customerIdOnlyKey", "false");
      systemConfig.setPayload(new JsonMarshaller().serializeMap(map));
      systemConfig.save();

      assertTrue(customerIdOnlyKey); // Checking if flag is set correctly
      ObjectCache.getInstace(uctx).invalidateAll();

      pid1 = _connect(customerId1, deviceId, otherKeys);
      pid2 = _connect(customerId2, deviceId, otherKeys);

      // As they have the same email, they should be the same profile
      assertTrue(pid1.equals(pid2));

      // Searching a profile by email
      p = ps.findAProfile(EMAIL, IndexType.EMAIL);
      assertTrue(p != null);

      // Searching a profile by phone
      p = ps.findAProfile(PHONE, IndexType.PHONE);
      assertTrue(p != null);

    }
    finally
    {
      Profile.deleteProfile(ctx, pid);
      Profile.deleteProfile(ctx, pid1);
      Profile.deleteProfile(ctx, pid2);
      if (systemConfig != null) systemConfig.delete();
    }
  }

  /////////////////////////////////////////////////////////////
  private String _connect(String custId, String deviceId,
      Map<String, String> otherKeys)
  {
    UContext ctx = getContext().getUContext();
    Profile.CreateProfileResult cpRes = Profile.identityResolver(getContext())
        .withCustomerId(custId).withDeviceId(deviceId)
        .withSecondaryKeyValues(otherKeys).findOrCreateProfile();
    return cpRes.profile().getKeyValue();
  }

  private String _getPushRegForDevice(String pid, String deviceId)
  {
    Profile p = Profile.instance(getContext(), pid);
    String data = p.getProperty("pushRegId");
    if (data == null) return null;

    Map<String, Object> map = new JsonMarshaller().readAsMap(data);
    // reg will be null in case of following pattern: {"ios": {}}
    // It will be fixed https://zineone.atlassian.net/browse/ZMOB-20209
    Map<String, Object> reg = (Map<String, Object>) map.get("ios");
    if (reg == null) return null;
    return (String) reg.get(deviceId);
  }

  private void _createPushRegForDevice(String pid, String deviceId)
  {
    Profile p = Profile.instance(getContext(), pid);
    String data = String.format(
        "{\"ios\": {\"%s\": \"a41f2943c871f2adefb6c9f0b72c0f9bb3ce66baf812b64d17025312ede3673a\"}}",
        deviceId);
    p.addProperty("pushRegId", data);
    p.flush();
  }

  private boolean _canSendPushToDevice(String pid, String deviceId)
  {
    Profile p = Profile.instance(getContext(), pid);
    String data = p.getProperty("pushRegId");
    if (data == null) return false;

    Map<String, Object> map = new JsonMarshaller().readAsMap(data);
    Map<String, Object> reg = (Map<String, Object>) map.get("ios");
    data = (String) reg.get(deviceId);
    if (data == null) return false;

    // Look for is this is not a device profile. If this is a device profile
    // no push will be sent
    data = p.getProperty(Profile.Fields.deviceProile.value);
    if ("true".equalsIgnoreCase(data)) return false;
    return true;
  }

  private boolean _isDeviceProfileForPush(String pid, String deviceId)
  {
    Profile p = Profile.instance(getContext(), pid);
    // Look for is this is not a device profile. If this is a device profile
    // no push will be sent
    String data = p.getProperty(Profile.Fields.deviceProile.value);
    if (data == null || data.isEmpty()) return true;
    if ("true".equalsIgnoreCase(data)) return false;

    return true;
  }

}
