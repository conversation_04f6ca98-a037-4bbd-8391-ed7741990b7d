<?xml version = '1.0' encoding = 'windows-1252'?>
<project name="udichi" default="devmake" basedir=".">
  <property file = "${basedir}/../../build.properties"/>
	
  <property name="UDICHI_ROOT" value="${udichi.home}"/>
  <!--<property name="BUILD_TAG" value="" />-->
  	
  <property name="lib.dir" value="${UDICHI_ROOT}/lib"/>
  <property name="plugins.dir" value="${UDICHI_ROOT}/plugins"/>
  <property name="release.dir" value="${UDICHI_ROOT}/war"/>
  <property name="tmp.dir" value="/tmp"/>


	
	<target name="stable" depends="dev_clean,compile,devmake">
	</target>
	
	<target name="nightly" depends="dev_clean,debugcompile,devmake">
	</target>
	
	
	<!--
	////////////////////////////// 
	Clean 
	////////////////////////////// 
	-->
	<target name="dev_clean">
		<delete dir="target/classes"/>
		<mkdir dir="target/classes"/>
	</target>


	<!--
	////////////////////////////// 
	Compilation 
	////////////////////////////// 
	-->
	<target name="compile">
		<echo message="======================================" />
		<echo message="Compiling z1app Java sources." />
		<echo message="======================================" />
		<javac srcdir="src" destdir="target/classes" classpathref="project.class.path"/>
		<copy todir="target/classes">
			<fileset dir="src" excludes="**/*.java" />
		</copy>
	</target>
	
	<target name="debugcompile">
		<echo message="======================================" />
		<echo message="Compiling z1app Java sources with debug flags." />
		<echo message="======================================" />
		<javac srcdir="src" destdir="target/classes" classpathref="project.class.path" debug="true" debuglevel="lines,vars,source"/>
		<copy todir="target/classes">
			<fileset dir="src" excludes="**/*.java" />
		</copy>
	</target>

  <target name="make" depends="clean,minify_js,cpWar,createPluginJar">
  	<antcall target="createJar">
  	    <param name="target.dir" value="${release.dir}"/>
  	</antcall>
  	<antcall target="finish"/>
  </target>	
	
  <target name="devmake" depends="js_make,createPluginJar">
  	<antcall target="createJar">
  	    <param name="target.dir" value="${release.dir}"/>
  	</antcall>
  	<antcall target="finish"/>
  </target>	

  <!--=======================================================
     Init / Clean
  ========================================================-->
  <target name="init">
    <echo message="Initialize compilation. Creating output folders..."/>
    <tstamp/>
    <mkdir dir="${release.dir}"/>
  </target>
  
  <target name="clean">
    <echo message="Removing release directories..."/>
    <!--Delete output directories-->
    <delete dir="${release.dir}"/>
  </target>

  <!--=======================================================
     Build Dev JS
  ========================================================-->
  <!-- Copying dev JS to target -->
  <target name="js_make">
    <echo message="Building Un-Minified JS..."></echo>
    <ant antfile="js/build/build.xml" target="dev_make" inheritall="false" >
    	<property name="js.version" value="${z1.release.version}"/>
    </ant>
  </target>
	
  <!--=======================================================
     Build JS
  ========================================================-->
  <!-- generating JS minification -->
  <target name="minify_js" depends="init">
    <echo message="Building Minified JS..."></echo>
    <ant antfile="js/build/build.xml" target="release" inheritall="false" />
  </target>
  
  <!-- Copy JS files and resources -->
  <target name="cpWar">
    <echo message="Copying all JS from dev environment..."/>
    <copy todir="${release.dir}/lib">
      <fileset dir="js/release"/>
    </copy>

    <!-- Check if legacy profile is active -->
    <condition property="is.legacy.profile">
      <equals arg1="${maven.profile.active}" arg2="legacy"/>
    </condition>

    <!-- Copy resources based on profile -->
    <antcall target="copyResourcesDefault"/>
    <antcall target="copyResourcesLegacy"/>

  </target>

  <!-- Copy resources for default profile (exclude META-INF) -->
  <target name="copyResourcesDefault" unless="is.legacy.profile">
    <echo message="Copying all resources from dev environment, excluding META-INF (default profile)..."/>
    <copy todir="${release.dir}">
        <fileset dir="js/src/html" excludes="WEB-INF/classes/META-INF/**"/>
    </copy>
  </target>

  <!-- Copy resources for legacy profile (include META-INF) -->
  <target name="copyResourcesLegacy" if="is.legacy.profile">
    <echo message="Copying all resources from dev environment, including META-INF (legacy profile)..."/>
    <copy todir="${release.dir}">
        <fileset dir="js/src/html"/>
    </copy>
  </target>

  <!--=======================================================
   Create plugin Jar files 
  ========================================================-->
  <target name="createPluginJar">
    <!--
     We'll add following jars as plugins
    -->
    <!--echo message="Copying plugin jars..."/>
    <copy file="z1social.jar" tofile="${plugins.dir}/z1social.jar"/-->

  </target>

  <!--=======================================================
    Create Jar file for web app 
  ========================================================-->
  <target name="createJar">
    
  	<!-- <echo message="Creating JAR for z1app class files..."/>
    <delete file="${target.dir}/WEB-INF/lib/z1app.jar"/>
    <jar destfile="${target.dir}/WEB-INF/lib/z1app.jar">
      <fileset dir="target/classes" includes="**"/>
    </jar>    

    <echo message="Copying core jars..."/>
    <delete file="${target.dir}/WEB-INF/lib/z1core.jar"/>
    <copy file="z1core.jar" tofile="${target.dir}/WEB-INF/lib/z1core.jar"/>

    <echo message="Copying social jars..."/>
    <delete file="${target.dir}/WEB-INF/lib/z1social.jar"/>
  	<copy file="z1social.jar" tofile="${target.dir}/WEB-INF/lib/z1social.jar"/>
    
    <echo message="Copying z1app jar to z1test ..."/>
    <copy tofile="../../testsuites/z1test/z1app.jar" file="${target.dir}/WEB-INF/lib/z1app.jar"/>
  	-->
  </target>

<available file="../.git" type="dir" property="git.present"/>

<target name="git.revision" description="Store git revision in ${repository.version}" if="git.present">
    <exec executable="git" outputproperty="git.revision" failifexecutionfails="false" errorproperty="">
        <arg value="describe"/>
        <arg value="--tags"/>
        <arg value="--always"/>
        <arg value="HEAD"/>
    </exec>
    <condition property="repository.version" value="${git.revision}" else="unknown">
        <and>
            <isset property="git.revision"/>
            <length string="${git.revision}" trim="yes" length="0" when="greater"/>
        </and>
    </condition>
</target>

<target name="git.commit" description="Store git commit in ${repository.commit}" if="git.present">
    <exec executable="git" outputproperty="git.commit" failifexecutionfails="false" errorproperty="">
        <arg value="log"/>
        <arg value="--pretty=format:'%h'"/>
        <arg value="-1"/>
    </exec>
    <condition property="repository.commit" value="${git.commit}" else="unknown">
        <and>
            <isset property="git.commit"/>
            <length string="${git.commit}" trim="yes" length="0" when="greater"/>
        </and>
    </condition>
</target>


  <target name="finish" depends="git.revision,git.commit">
    <!--Create the time stamp for the new build-->
    <tstamp>
      <format property="NOW" pattern="EEE, d MMM yyyy HH:mm:ss zzz"/>
    </tstamp>

    <!-- Ensure tmp directory exists and copy version template -->
    <mkdir dir="${tmp.dir}"/>
    <copy file="version.html" tofile="${tmp.dir}/version.html" overwrite="true" />

  	<copy file="${tmp.dir}/version.html" tofile="${release.dir}/version.html" overwrite="true" />

    <replace file="${release.dir}/version.html" token="@Z1APP_BUILD@" value="${BUILD_TAG}"/>  
    <replace file="${release.dir}/version.html" token="@Z1APP_VERSION@" value="${repository.version} #${repository.commit}"/>       	
    <replace file="${release.dir}/version.html" token="@Z1APP_TSTAMP@" value="${NOW}"/>     
           
	<echo>Build version is ${repository.version} #${repository.commit}</echo>
  	<echo>Build date/time is ${NOW}</echo>
  	<echo>Build tag from Jenkins is ${BUILD_TAG}</echo>
  	<echo>z1 version is ${z1.release.version}</echo>
    <echo message="Finished building the WAR file"/>
  </target>

<target name="index.html" depends="git.revision" description="build index.html from template">
    <copy file="index.html.template" tofile="index.html" overwrite="yes">
        <filterchain>
            <replacetokens>
                <token key="repository.version" value="${repository.version}" />
            </replacetokens>
        </filterchain>
    </copy>
</target>


</project>
