<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="http://com/z1/Registration/def" xmlns:tns="http://com/z1/Registration/def" elementFormDefault="qualified">

    <complexType name="registrationType">
    	<sequence>
    		<element name="fname" type="string"></element>
    		<element name="lname" type="string"></element>
    		<element name="email" type="string"></element>
    		<element name="password" type="string"></element>
    		<element name="companyName" type="string"></element>
    		<element name="industry" type="string"></element>

    		<element name="competitor" type="tns:competitorType"
    			maxOccurs="unbounded" minOccurs="0">
    		</element>
    		<element name="companyUrl" type="tns:urlInfoType"
    			maxOccurs="unbounded" minOccurs="0">
    		</element>
    		<element name="competitorUrl" type="tns:urlInfoType"
    			maxOccurs="unbounded" minOccurs="0">
    		</element>

    		<element name="custom" type="tns:customType"
    			maxOccurs="unbounded" minOccurs="0">
    		</element>
    	</sequence>
    	<attribute name="recaptchaResponse" type="string"></attribute>
    </complexType>




    <complexType name="competitorType">
    	<sequence>
    		<element name="companyName" type="string"></element>
    		<element name="url" type="string"></element>
    	</sequence>
    </complexType>

    <complexType name="urlInfoType">
    	<sequence>
    		<element name="id" type="string"></element>
    		<element name="name" type="string"></element>
    		<element name="alias" type="string"></element>
    		<element name="url" type="string"></element>
    		<element name="imageUrl" type="string"></element>
    		<element name="description" type="string"></element>
    		<element name="accessToken" type="string"></element>
    		<element name="channelType" type="string"></element>
    		<element name="isCompetitor" type="string"></element>
    		<element name="likes" type="string"></element>
    		<element name="ptat" type="string"></element>

    		<element name="languages" type="string" maxOccurs="unbounded" minOccurs="1"></element>
    		<element name="industry" type="string"></element>
    	</sequence>
    </complexType>



    
    <complexType name="customType">
    	<sequence>
    		<element name="nv" maxOccurs="unbounded" minOccurs="0">
    			<complexType>
    				<attribute name="name" type="string"></attribute>
    				<attribute name="value" type="string"></attribute>
    			</complexType></element>
    	</sequence>
    	<attribute name="type" type="string"></attribute>
    </complexType>
</schema>