<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>z1app</groupId>
	<artifactId>z1app</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<properties>
		<tmp.dir>/tmp</tmp.dir>
		<jetty.version>9.4.30.v20200611</jetty.version>
		<z1.release.version>${platformVersion}-${git.commit.id.abbrev}</z1.release.version>
		<UDICHI_ROOT>${udichi.home}</UDICHI_ROOT>
		<Z1CODE_PATH>${z1code.home}</Z1CODE_PATH>
		<Z1APP_PATH>${z1app.home}</Z1APP_PATH>
		<mockito.version>3.7.7</mockito.version>
		<powermock.version>2.0.9</powermock.version>
		

		<!-- Developers who want to use http: please change the property below 
			to false -->
		<web.ssl>true</web.ssl>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<lib.dir>${UDICHI_ROOT}/lib</lib.dir>
		<plugins.dir>${UDICHI_ROOT}/plugins</plugins.dir>
		<release.dir>${UDICHI_ROOT}/war</release.dir>
	</properties>
	<build>
		<finalName>z1app</finalName>
		<sourceDirectory>src</sourceDirectory>
		<testSourceDirectory>${project.basedir}/test</testSourceDirectory>
		<resources>
			<resource>
				<directory>src/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>**/*.properties</include>
				</includes>
			</resource>
		</resources>
		<testResources>
			<testResource>
				<directory>${project.basedir}/test/resources</directory>
			</testResource>
			<testResource>
				<directory>${project.basedir}/test</directory>
				<excludes>
					<exclude>**/*.java</exclude>
				</excludes>
			</testResource>
		</testResources>
		<plugins>
			<!-- Commented out here because it is used inside the legacy profile -->
<!--			<plugin>-->
<!--				<artifactId>maven-resources-plugin</artifactId>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<id>copy-resources</id>-->
<!--						<phase>install</phase>-->
<!--						<goals>-->
<!--							<goal>copy-resources</goal>-->
<!--						</goals>-->
<!--						<configuration>-->
<!--							<outputDirectory>${UDICHI_ROOT}/war/WEB-INF</outputDirectory>-->
<!--							<resources>-->
<!--								<resource>-->
<!--									<directory>js/src/html/WEB-INF/</directory>-->
<!--									<filtering>true</filtering>-->
<!--									<includes>-->
<!--										<include>**/*.xml</include>-->
<!--										<include>**/version.properties</include>-->
<!--										<include>**/domainEvents.json</include>-->
<!--									</includes>-->
<!--								</resource>-->
<!--							</resources>-->
<!--						</configuration>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>1.8</version>

				<executions>
					<execution>
						<id>dev-ant</id>
						<phase>package</phase>
						<configuration>
							<target>
								<echo message="Building devmake..."></echo>
								<ant antfile="build.xml" target="devmake">
									<property name="maven.profile.active" value="${maven.profile.active}"/>
								</ant>
							</target>

						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
					<execution>
						<id>deploy-ant</id>
						<phase>deploy</phase>
						<configuration>
							<target>
								<echo message="Building make..."></echo>
								<ant antfile="build.xml" target="make">
									<property name="maven.profile.active" value="${maven.profile.active}"/>
								</ant>
							</target>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.7.0</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>install</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${release.dir}/WEB-INF/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>false</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
							<excludeTransitive>true</excludeTransitive>
							<includeScope>compile</includeScope>
							<excludeArtifactIds>z1nlp3p,com.sessionai</excludeArtifactIds>

						</configuration>
					</execution>
					<execution>
						<id>copy</id>
						<phase>install</phase>
						<goals>
							<goal>copy</goal>
						</goals>


						<configuration>
							<artifactItems>
								<artifactItem>
									<groupId>z1app</groupId>
									<artifactId>z1app</artifactId>
									<version>0.0.1-SNAPSHOT</version>
									<type>jar</type>
									<overWrite>true</overWrite>
									<outputDirectory>${release.dir}/WEB-INF/lib</outputDirectory>
									<destFileName>z1app.jar</destFileName>
								</artifactItem>
							</artifactItems>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.7</version>
				<executions>
					<execution>
						<id>copy-js-resources</id>
						<phase>install</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<overwrite>true</overwrite>
							<outputDirectory>${release.dir}/lib/${z1.release.version}</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/js/src</directory>
									<includes>
										<include>z1/c3/desktop/main.js</include>
										<include>z1/s/main.js</include>
										<include>z1/resetpassword/main.js</include>
										<include>z1/apps/desktop/main.js</include>
										<include>z1/mc/desktop/main.js</include>
										<include>z1/agent/desktop/main.js</include>
									</includes>
									<filtering>true</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-html-resources</id>
						<phase>compile</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<overwrite>true</overwrite>
							<outputDirectory>${project.build.outputDirectory}</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/src</directory>
									<includes>
										<include>com/z1/resource/c3/desktop.html</include>
										<include>com/z1/resource/c3/standalone.html</include>
										<include>com/z1/resource/c3/passwordreset.html</include>
										<include>com/z1/resource/apps/desktop.html</include>
										<include>com/z1/resource/mc/desktop.html</include>
										<include>com/z1/resource/agent/desktop.html</include>
									</includes>
									<filtering>true</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-uduchi-resources</id>
						<phase>install</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<overwrite>true</overwrite>
							<outputDirectory>${release.dir}/lib/${z1.release.version}/udc</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/js/src/html/lib/udc</directory>
									<includes>
										<include>**/*</include>
									</includes>
									<filtering>false</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
					<!-- copying entire udichi html again for workbench -->
					<execution>
						<id>copy-workbench-uduchi-resources</id>
						<phase>install</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<overwrite>true</overwrite>
							<outputDirectory>${release.dir}/lib/udc</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/js/src/html/lib/udc</directory>
									<includes>
										<include>**/*</include>
									</includes>
									<filtering>false</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
				<version>3.0.0</version>
				<executions>
					<execution>
						<id>get-the-git-infos</id>
						<goals>
							<goal>revision</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
					<prefix>git</prefix>
					<verbose>false</verbose>
					<injectAllReactorProjects>true</injectAllReactorProjects>
					<generateGitPropertiesFile>true</generateGitPropertiesFile>
					<generateGitPropertiesFilename>${basedir}/src/resources/git.properties</generateGitPropertiesFilename>
					<format>json</format>
					<gitDescribe>
						<skip>false</skip>
						<always>true</always>
						<dirty>HEAD</dirty>
						<tags>true</tags>
					</gitDescribe>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>properties-maven-plugin</artifactId>
				<version>1.0.0</version>
				<executions>
					<execution>
						<phase>initialize</phase>
						<goals>
							<goal>read-project-properties</goal>
						</goals>
						<configuration>
							<files>
								<file>src/resources/git.properties</file>
								<file>js/src/html/WEB-INF/classes/META-INF/version.properties</file>
							</files>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>build-helper-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>add-integration-test-sources</id>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>add-test-source</goal>
						</goals>
						<configuration>
							<sources>
								<source>${project.basedir}/test-integration/src</source>
							</sources>
						</configuration>
					</execution>
					<execution>
						<id>add-integration-test-resources</id>
						<phase>generate-test-resources</phase>
						<goals>
							<goal>add-test-resource</goal>
						</goals>
						<configuration>
							<resources>
								<resource>
									<directory>${project.basedir}/test-integration/resources</directory>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>


			<!-- Add Failsafe Plugin for Integration Tests -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-failsafe-plugin</artifactId>
				<configuration>
					<skipITs>true</skipITs>
					<environmentVariables>
						<UDICHI_HOME>${UDICHI_ROOT}</UDICHI_HOME>
					</environmentVariables>
					<includes>
						<include>**/*IT.java</include>
					</includes>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>integration-test</goal>
							<goal>verify</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!-- ZMOB-24277 Temporarily commented out to unblock a release-->
			<!-- plugin>
				<groupId>com.github.searls</groupId>
				<artifactId>jasmine-maven-plugin</artifactId>
				<version>2.2</version>
				<executions>
					<execution>
						<goals>
							<goal>test</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<jsSrcDir>${project.basedir}/js/src/z1</jsSrcDir>
					<jsTestSrcDir>${project.basedir}/js/src/z1/jsUnitTest/specs</jsTestSrcDir>
					<customRunnerTemplate>${project.basedir}/js/src/z1/jsUnitTest/jasmine/dojoSpecRunner.html</customRunnerTemplate>
					<additionalContexts>
						<context>
							<contextRoot>udc</contextRoot>
							<directory>${project.basedir}/js/src/html/lib/udc</directory>
						</context>
						<context>
							<contextRoot>z1</contextRoot>
							<directory>${project.basedir}/js/src/z1</directory>
						</context>
					</additionalContexts>
				</configuration>
			</plugin -->
		</plugins>
	</build>

	<dependencies>
		<!-- https://mvnrepository.com/artifact/commons-fileupload/commons-fileupload -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.3</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.braintreepayments.gateway/braintree-java -->
		<dependency>
			<groupId>com.braintreepayments.gateway</groupId>
			<artifactId>braintree-java</artifactId>
			<version>2.39.2</version>
		</dependency>

		<dependency>
			<groupId>com.z1</groupId>
			<artifactId>z1social</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.z1</groupId>
			<artifactId>udcmodel</artifactId>
			<version>1.1.12</version>
		</dependency>
			<dependency>
			<groupId>com.z1</groupId>
			<artifactId>udichi</artifactId>
			<version>1.1.12</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-util -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-util</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-server -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-server</artifactId>
			<version>${jetty.version}</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>javax.servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-webapp -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-webapp</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-servlet -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-servlet</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-http -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-http</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-io -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-io</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-security -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-security</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty/jetty-xml -->
		<dependency>
			<groupId>org.eclipse.jetty</groupId>
			<artifactId>jetty-xml</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty.websocket/websocket-servlet -->
		<dependency>
			<groupId>org.eclipse.jetty.websocket</groupId>
			<artifactId>websocket-servlet</artifactId>
			<version>${jetty.version}</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>javax.servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty.websocket/websocket-server -->
		<dependency>
			<groupId>org.eclipse.jetty.websocket</groupId>
			<artifactId>websocket-server</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty.websocket/websocket-client -->
		<dependency>
			<groupId>org.eclipse.jetty.websocket</groupId>
			<artifactId>websocket-client</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty.websocket/websocket-api -->
		<dependency>
			<groupId>org.eclipse.jetty.websocket</groupId>
			<artifactId>websocket-api</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.eclipse.jetty.websocket/websocket-common -->
		<dependency>
			<groupId>org.eclipse.jetty.websocket</groupId>
			<artifactId>websocket-common</artifactId>
			<version>${jetty.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/javax.servlet/servlet-api -->
		<dependency>
		    <groupId>javax.servlet</groupId> 
		    <artifactId>servlet-api</artifactId>
		    <version>2.5</version>
		</dependency>
    <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.3.2</version>
    </dependency>
    
    <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>${mockito.version}</version>
      <scope>test</scope>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.mockito/mockito-inline -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
    </dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-module-junit4</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>${powermock.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.powermock</groupId>
			<artifactId>powermock-api-mockito2</artifactId>
			<version>2.0.9</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<!--
    Legacy profile:
    Activates when -Dlegacy=true is passed on the command line.
    Uses the z1core repositories version of z1core (0.0.1-SNAPSHOT).

    To activate this profile, run:
    mvn clean install -Dlegacy=true -DskipTests
	-->
	<profiles>
		<!-- Legacy profile -->
		<profile>
			<id>legacy-build</id>
			<activation>
				<property>
					<name>legacy</name>
				</property>
			</activation>
			<properties>
				<maven.profile.active>legacy</maven.profile.active>
			</properties>
			<build>
				<resources>
					<resource>
						<directory>src/META-INF</directory>
						<includes>
							<include>**/*</include>
						</includes>
					</resource>
				</resources>
				<plugins>
					<plugin>
						<artifactId>maven-resources-plugin</artifactId>
						<executions>
							<execution>
								<id>copy-resources-legacy</id>
								<phase>install</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<outputDirectory>${UDICHI_ROOT}/war/WEB-INF</outputDirectory>
									<resources>
										<resource>
											<directory>js/src/html/WEB-INF/</directory>
											<filtering>true</filtering>
											<includes>
												<include>**/*.xml</include>
												<include>**/version.properties</include>
												<include>**/domainEvents.json</include>
											</includes>
										</resource>
									</resources>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
			<dependencies>
				<dependency>
					<groupId>z1core</groupId>
					<artifactId>z1core</artifactId>
					<version>0.0.1-SNAPSHOT</version>
				</dependency>
			</dependencies>
		</profile>

		<!-- Default profile (non-legacy) -->
		<profile>
			<id>default-build</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<maven.profile.active>default</maven.profile.active>
			</properties>
			<dependencies>
				<dependency>
					<groupId>com.sessionai</groupId>
					<artifactId>z1core</artifactId>
					<version>1.0.0</version>
				</dependency>
			</dependencies>
		</profile>
	</profiles>
	<repositories>
    <repository>
		<id>archiva.internal</id>
		<name>Internal Release Repository</name>
		<url>https://arb.zineone.com/repository/internal/</url>
    </repository>
  </repositories>

</project>