<?xml version = '1.0' encoding = 'windows-1252'?>
<project name="z1app" default="dev_make" basedir=".">
	<property file = "${basedir}/../../../../build.properties"/>

	<!--
    This script builds both the development build and release build. Development build will
    simply copy the files to the WAR folder. Release build will create a compact form of the
    JS sources for better loading. 
  -->
	<target name="load.properties"
    description = "Set properties for ther build">

		<property name="UDICHI_ROOT" value="${udichi.home}" />
		<property name="BUILD_VERSION" value="${build.version}" />

		<!-- Google Closure Compiler location-->
		<property name="closure.dir" value="${basedir}/compiler.jar" />
		<!-- Rhino location-->
		<property name="js.jar" value="${basedir}/js.jar" />
		<!-- Source JS dirs-->
		<property name="build.dir" value="build" />
		<property name="src.dir" value="../src/z1" />
		<echo message="z1.release.version ${js.version}"/>
		<property name="out.dir" value="${UDICHI_ROOT}/war/lib/${js.version}" />
		<property name="release.src.dir" value="../src" />
		<property name="release.dir" value="../release" />
		<property name="tmp.dir" value="../release/tmp"/>
		<property name="test.dir" value="../src/z1/tests" />
		<property name="html.src.dir" value="../src/html" />
		<property name="html.out.dir" value="${UDICHI_ROOT}/war" />
		<property name="war.lib" value="${UDICHI_ROOT}/war/lib" />

	</target>

	<target name="initdevmake" depends="load.properties"
    description="Create and clean build dir structure">
		<!--Create the time stamp for the new build-->
		<tstamp>
			<format property="TODAY" pattern="EEE, d MMM yyyy HH:mm:ss Z"/>
		</tstamp>

		<!-- Delete old build dirs-->
		<echo message="Removing the 'old' dir if exist."/>
		<delete dir="${UDICHI_ROOT}/war/lib/z1"/>
		<delete dir="${UDICHI_ROOT}/war/lib/udc"/>
		<!-- Delete previous build dirs-->
		<echo message="Removing the release dir if exist."/>
		<delete dir="${out.dir}/z1"/>
		<delete dir="${out.dir}/udc"/>
		<!-- Recreate the  build dirs-->
		<echo message="Creating Z1 release dir"/>
		<mkdir dir="${out.dir}/z1" />

	</target>


	<target name="init" depends="load.properties"
    description="Create and clean build dir structure">
		<!--Create the time stamp for the new build-->
		<tstamp>
			<format property="TODAY" pattern="EEE, d MMM yyyy HH:mm:ss Z"/>
		</tstamp>

		<!-- Delete previous build dirs-->
		<echo message="Removing the release dir if exist."/>
		<delete dir="${release.dir}/z1"/>
		<echo message="Creating Minification release dir"/>
		<mkdir dir="${release.dir}/z1" />

	</target>


	<!--
  ============================================================ 
  The Development time build. This will simply copy the files from
  Source folder to the WAR 
  ============================================================ 
  -->
	<target name="dev_make" depends="initdevmake,cpAll4Dev,copyHtml,cleanup">
		<echo message="End Dev Build"/>
	</target>

	<target name="cpAll4Dev" depends="load.properties">
		<echo message="Copying all for dev environment..."/>
		<copy todir="${out.dir}/z1">
			<fileset dir="${src.dir}">
				<include name="**/*.js"/>
				<include name="**/*.html"/>
				<include name="**/*.css"/>
				<include name="**/*.json"/>
				<include name="**/*.template"/>
				<include name="**/*.png"/>
			</fileset>
		</copy>

	</target>

	<!--
   Copying static HTML pages
  -->
	<target name="copyHtml" depends="load.properties">
		<!-- Check if legacy profile is active -->
		<condition property="is.legacy.profile">
			<equals arg1="${maven.profile.active}" arg2="legacy"/>
		</condition>

		<!-- Copy HTML resources based on profile -->
		<antcall target="copyHtmlDefault"/>
		<antcall target="copyHtmlLegacy"/>
	</target>

	<!-- Copy HTML for default profile (exclude WEB-INF to avoid overriding z1api files) -->
	<target name="copyHtmlDefault" unless="is.legacy.profile">
		<echo message="Copying static pages from ${html.src.dir} to ${html.out.dir} (default profile - excluding WEB-INF)..."/>
		<copy todir="${html.out.dir}" overwrite="true" force="true">
			<fileset dir="${html.src.dir}">
				<exclude name="**/udc/**" />
				<exclude name="**/*.xml" />
				<exclude name="WEB-INF/**" />
			</fileset>
		</copy>
	</target>

	<!-- Copy HTML for legacy profile (include WEB-INF for backward compatibility) -->
	<target name="copyHtmlLegacy" if="is.legacy.profile">
		<echo message="Copying static pages from ${html.src.dir} to ${html.out.dir} (legacy profile - including WEB-INF)..."/>
		<copy todir="${html.out.dir}" overwrite="true" force="true">
			<fileset dir="${html.src.dir}">
				<exclude name="**/udc/**" />
				<exclude name="**/*.xml" />
			</fileset>
		</copy>
	</target>

	<target name="cleanup" depends="copyHtml">
		<echo>Clean up extra files</echo>
		<echo>----------------------------------------------</echo>
		<delete>
			<fileset dir="${war.lib}"
					 includes="tstamp.txt"/>
		</delete>
	</target>


	<!--
===========================================================
Actions for production release.
===========================================================
-->

	<!-- Release Build -->
	<!--target name="release"
      description="Builds project files for production use"
      depends="load.properties,init,cpAll4Release,js.minify,copy,cp2war,copyHtml">
      <echo>Entire Project Build Finished!!!!</echo>
  </target-->

	<target name="release"
      description="Builds project files for production use"
      depends="load.properties,init,cpAll4Release,cp2war,copyHtml,cleanup">
		<echo>Entire Project Build Finished!!!!</echo>
	</target>

	<!--
    Copy sources to the release folder before compilation
  -->
	<target name="cpAll4Release">
		<echo message="Copying all to release folder"/>
		<copy todir="${release.dir}/z1">
			<fileset dir="${src.dir}">
				<include name="**/*.js"/>
				<include name="**/*.html"/>
				<include name="**/*.css"/>
				<include name="**/*.json"/>
				<include name="**/*.template"/>
				<include name="**/*.png"/>
				<exclude name="tests/**/*.js"/>
				<exclude name="tests/**/*.html"/>
			</fileset>
		</copy>
	</target>


	<!--JS Lint-->
	<target name="js.lint" depends="init">
		<echo>Starting js lint</echo>
		<echo>----------------------------------------------</echo>
		<pathconvert pathsep=" " property="jsfiles">
			<fileset dir="${release.dir}/z1"
        includes="**/*.js"/>
		</pathconvert>
		<exec dir="${release.dir}/z1" executable="java" failonerror="true">
			<arg line="-jar ${js.jar} ${jslint.js} ${jsfiles}"/>
		</exec>
		<echo>Finished</echo>
	</target>


	<!--
    ...................................... 
    Minify JS files 
    ......................................
   -->
	<target name="js.minify"
      description="Minifies JavaScript files">
		<echo>Starting minification</echo>
		<echo>----------------------------------------------</echo>
		<apply executable="java" parallel="false" dest="${release.dir}/z1">
			<fileset dir="${release.dir}/z1"
        includes="**/*.js"/>

			<arg line="-jar"/>
			<arg path="${closure.dir}"/>
			<arg line="--js"/>
			<srcfile/>
			<arg line="--js_output_file"/>
			<mapper type="glob" from="*.js" to="*-min.js"/>
			<targetfile/>
			<arg line="--compilation_level SIMPLE_OPTIMIZATIONS" />
			<!--arg line="ADVANCED_OPTIMIZATIONS"/>
      <arg line="SIMPLE_OPTIMIZATIONS"/-->
			<arg line="--jscomp_warning internetExplorerChecks"/>
			<!--arg line="internetExplorerChecks"/-->
		</apply>
		<echo> Finished</echo>
	</target>

	<!-- 
    Delete *-min.js and copy them over to *.js  
  -->
	<target name="copy">
		<!--target name="copy" depends="load.properties,init"-->
		<echo>Copying file over to target</echo>
		<echo>----------------------------------------------</echo>
		<copy todir="${release.dir}/z1">
			<fileset dir="${release.dir}/z1"
      includes="**/*-min.js"/>
		</copy>

		<move todir="${release.dir}/z1" includeemptydirs="false">
			<fileset dir="${release.dir}/z1"/>
			<!--mapper type="glob" from="**/*-min.*" to="**/*.*"/-->
			<mapper type="regexp" from="^(.*)\-(.*)\.(.*)$$" to="\1.\3"/>
		</move>
		<delete>
			<fileset dir="${release.dir}/z1"
      includes="**/*-min.js"/>
		</delete>
	</target>

	<target name="cp2war">
		<echo>Copying file over to war/lib</echo>
		<echo>----------------------------------------------</echo>
		<copy todir="${out.dir}/z1">
			<fileset dir="${release.dir}">
				<include name="**/*.js"/>
				<include name="**/*.html"/>
				<include name="**/*.json"/>
				<include name="**/*.css"/>
				<include name="**/*.template"/>
				<include name="**/*.png"/>
			</fileset>
		</copy>
	</target>
</project>


