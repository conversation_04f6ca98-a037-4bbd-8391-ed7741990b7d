:root{
    /* gray, neutral */
    --c-gray-0: #ffffff;
    --c-gray-10: #FAFAFA;
    --c-gray-20: #F0F0F0;
    --c-gray-30: #F1F2F3;
    --c-gray-40: #F5F5F5;
    --c-gray-50: #E0E0E0;
    --c-gray-60: #D9D9D9;
    --c-gray-70: #CCCCCC;
    --c-gray-80: #B8B8B8;
    --c-gray-90: #8C8C8C;
    --c-gray-100: #595959;
    --c-gray-110: #141414;

    --c-blue-10: #EAF5FE;
    --c-blue-50: #085DAD;

    --o-blue-c-p: 8, 93, 173;
    --o-blue-c: rgb(var(--o-blue-c-p));

    /* purple */
    --c-purple-10: #E4CCFF; /* thistle2 */
    --c-purple-40: #7765E3;

    --c-pill-bg-color: var(--c-blue-70);

    --btn-primary-c: rgb(var(--o-blue-c-p));
    --btn-primary0-c: rgb(var(--o-blue-c-p));
    --lv-ttl-c: rgba(var(--o-blue-c-p), 1);
    --fld-v-c: #555555;
    --fld-v-c: rgba(0, 0, 0, .91);

    --pnl-fltr-btn-c: white;
	--pnl-fltr-btn-bg-c: var(--o-blue-c);
	--pnl-fltr-sel-item-bg-c: var(--c-gray-30);
	--pnl-fltr-sel-item-x: var(--c-gray-100);
	--pnl-fltr-sel-item-c: var(--c-gray-110);
	--pnl-fltr-btn-bdr: 1px solid var(--c-gray-80);
	--pnl-fltr-bdr: 1px solid rgba(var(--o-blue-c-p), .16);

/* BEGIN COPY */
	--pg-rhs-bg-c-grey: #f5f5f5;
	--pg-rhs-bg-c-white: #ffffff;
	--pg-rhs-white-bx-bg-c: #ffffff;

/* ========c3Table2======== */

	--lv-cl-pd-v: 4px;
	--lv-cl-e-pd-v: 16px;
	--lv-cl-e-pd-h: 10px;
	--lv2-cl-pd-v: 16px;
	--lv-cl-pd-h: 6px;
	--lv2-cl-pd-h: 16px;
	--lv2-cl-pd-h-icn: 8px;
	--lv-cl-pd-h-x: calc(var(--lv-cl-pd-h) + 4px);
	--lv-cl-edge-pd-h: 24px;

	--lv2-bdr-c: var(--c-gray-50);
	--lv2-tr-bdr-c: var(--lv2-bdr-c);

	--lv2-tr-box-shdo: inset 0px -1px 0px var(--lv2-tr-bdr-c);

	--lv2-cl-ft-sz: 14px;
	--lv2-cl-lnh: 20px;
	--lv2-cl-c: #141414;

	--lvt-date-ts-w: 155px;
	--lv-c-date-s: 120px;
	--lv-c-date-ts: var(--lvt-date-ts-w);
	--lv-c-icn-20: 20px;
	--lv-c-icn-26: 26px;
	--lv-c-txt-e: 0px;
	--lv-c-txt-xs: 40px;
	--lv-c-txt-s: 60px;
	--lv-c-txt-s2: 70px;
	--lv-c-txt-s3: 90px;
	--lv-c-txt-s4: 105px;
	--lv-c-txt-m: 120px;
	--lv-c-txt-m2: 150px;
	--lv-c-txt-m-hx: 112px;
	--lv-c-txt-l: 190px;
	--lv-c-txt-l2: 220px;
	--lv-c-txt-xl: 260px;
	--lv-c-txt-xxl: 300px;
	--lv-c-txt-xxxl: 340px;
	--lv-c-txt-4xl: 420px;
	--lv-c-txt-fx1m: 160px;
	--lv-c-btns-s: 40px;
	--lv-c-btns-m: 60px;

	--lv-tag-ft-sz: 10px;

	--lv-rw-xpd-bdr-c: rgb(0 0 0 / 21%);

	--lv-ttl-c: rgba(var(--o-blue-c-p), 1);
	--grd-ttl-c: rgba(var(--o-blue-c-p), .91);

    --grd-bx-bg-c: rgba(249, 249, 249, .9);
    --grd-bx-bg-c: white;
/* ========c3Table2========== */
/* END COPY */

	.c3_purpleIt {
		--litepicker-day-color-hover: var(--c-purple-40);
		--c-pill-bg-color: var(--c-purple-40);
	}
}

body, html {
  font-family: arial, verdana, helvetica, sans-serif;
  font-size: 90%;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

p {
  margin: 0;
  padding: 0;
}

#borderContainer {
  width: 100%;
  height: 100%;
}

/**
 =======================================================
 Override default Dojo theme for Workbench
 =======================================================
*/

.claro .dijitTitlePaneContentOuter {
	background: #ffffff;
	border: 0px solid #b5bcc7;
	border-top: none;
}

.claro .dijitSplitContainer-dijitContentPane, .claro .dijitBorderContainer-dijitContentPane {
  padding: 0px;
  overflow: hidden;
}


/** Rounder top border container */
.claro .dijitBorderContainer-child {
  border: 1px solid #CCCCCC;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.claro .dojoxExpandoRight .dojoxExpandoTitle,.claro .dojoxExpandoLeft .dojoxExpandoTitle,.claro .dojoxExpandoClosed .dojoxExpandoTitle
  {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}

.claro .dojoxGrid {
  border: none;
}

.claro .dijitAccordionContainer .dijitAccordionContainer-child {
  padding: 0;
}

.claro .dijitDialog {
	border: thin solid #cecece;
	box-shadow: 2px 2px 90px 5px rgba(150, 150, 150, 0.5);
	border-radius: 0px 0px 0px 0px;
	background: #fff;
}
.claro .c3_dlg2 {
	padding-bottom: 0px !important;
	box-shadow: 0px 0px 4px rgb(0 0 0 / 10%), 0px 8px 40px rgb(0 0 0 / 20%);
	border-radius: 8px;
}

.c3_dlg2.c3_br20 {
  border-radius: 20px;
}


.claro .dijitDialogTitleBar {
  -moz-border-bottom-colors: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  background-color: #cccccc;
  background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 100%);
  background-repeat: repeat-x;
  border-color: -moz-use-text-color #FFFFFF #FFFFFF;
  border-image: none;
  border-right: 1px solid #FFFFFF;
  border-style: none solid solid;
  border-width: medium 1px 1px;
  padding: 5px 7px 4px;
}


.claro .dijitDialogTitle {
    color: #777777;
    font-size: 1.091em;
    font-weight: bold;
    padding: 0 1px;
}

/**
* general class
**/

.c3_contnr_type_inlineSize {
	container-type: inline-size;
}

.c3_bgc_fafafa {
	background-color: #FAFAFA !important;
}
.c3_marginL_16 {
	margin-left: 16px;
}
.c3_mWidth_140 {
  min-width: 140px;
}
.c3_mWidth_300 {
	min-width: 300px;
}
.c3_maxWidth_400 {
	max-width: 400px;
}
.c3_wMaxCont {
  width: max-content;
}

.c3_mHeight_140 {
  min-height: 140px;
}

/* box-shadow */
 .c3_box_shadow_1 {
	box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);
 }
 .c3_box_shadow_2 {
    box-shadow: var(--shdo-drpDwn);
 }

 /* center justify page */
[data-dojo-attach-point="content"]:has(> div.c3_pgCentered) {
	container-type: inline-size;
	container-name: pgContent;
}

/**
 * ====================================================
 * Layout - flex related classes
 * ====================================================
 */
.c3_fx {
	display: flex;
}
.c3_cols_nowrap {
	display: flex;
	flex-flow: column nowrap;
	justify-content: flex-start;
	align-items: stretch;
}
.c3_cols_reverse_nowrap {
	display: flex;
	flex-flow: column-reverse nowrap;
	justify-content: flex-start;
	align-items: stretch;
}
/* do not shrink child divs that are not flex: 1 */
.c3_cols_nowrap>* {
	flex-shrink: 0;
}
.c3_cols_nowrap>.c3_flex_1 {
	flex-shrink: initial;
}
.c3_cols_nowrap.shrk_ok>* {
	flex-shrink: initial;
}
/**/

.c3_cols_nowrap_center {
	display: flex;
	flex-flow: column nowrap;
	justify-content: center;
	align-items: stretch;
}

.c3_cols_nowrap_around {
	display: flex;
	flex-flow: column nowrap;
	justify-content: space-around;
	align-items: stretch;
}

/*
 For 2 or 3 rows layout:
 header, body, footer
 header, body
 body footer
 add c3_flex_1 to body
*/
.c3_cols_nowrap_edge {
	display: flex;
	flex-flow: column nowrap;
	justify-content: space-between;
	align-items: stretch;
}

.c3_rows_nowrap {
	display: flex;
	flex-flow: row nowrap;
	justify-content: space-between;
}

.c3_rows_nowrap_start {
	display: flex;
	flex-flow: row nowrap;
	justify-content: flex-start;
}

.c3_rows_nowrap_around {
	display: flex;
	flex-flow: row nowrap;
	justify-content: space-around;
}

.c3_rows_nowrap_center {
	display: flex;
	flex-flow: row nowrap;
	justify-content: space-between;
	align-items: center;
}

.c3_rows_nowrap_start_center {
	display: flex;
	flex-flow: row nowrap;
	justify-content: flex-start;
	align-items: center;
}
.c3_rows_reverse_nowrap_start_center {
	display: flex;
	flex-flow: row-reverse nowrap;
	justify-content: flex-start;
	align-items: center;
}
.c3_rows_nowrap_end_center {
	display: flex;
	flex-flow: row nowrap;
	justify-content: flex-end;
	align-items: center;
}

.c3_cols_wrap {
	display: flex;
	flex-flow: column wrap;
	justify-content: flex-start;
	align-items: stretch;
}

.c3_rows_wrap {
	display: flex;
	flex-flow: row wrap;
	justify-content: flex-start;
}

/* For 2 or 3 column that can wrap */
.c3_rows_wrap_edge {
	display: flex;
	flex-flow: row wrap;
	justify-content: space-between;
}

.c3_rows_wrap_end {
	display: flex;
	flex-flow: row wrap;
	justify-content: flex-end;
}

/* grid */
.c3_gd {
	display: grid;
}

/* equal size stretched boxes */
/* add 'c3_flex_eq' class with *_nowrap class */
.c3_cols_nowrap.c3_flex_eq>* {
	flex: 1;
}

.c3_rows_nowrap.c3_flex_eq>* {
	flex: 1;
}

.c3_rows_wrap_edge.c3_flex_eq>* {
	flex: 1;
}

.c3_flex_hidden>.c3_flex_1 {
	overflow: hidden;
}

.c3_hidden {
	overflow: hidden;
}
.c3_x_hidden {
	overflow-x: hidden;
}

.c3_xy_auto {
	overflow: auto;
}
.c3_x_auto {
	overflow-x: auto;
}
.c3_y_auto {
	overflow-y: auto;
}

.c3_flex_4 {
	flex: 4;
}
.c3_flex_3 {
	flex: 3;
}
.c3_flex_2 {
	flex: 2;
}
.c3_flex_1 {
	flex: 1;
}
.c3_flex_10a {
	flex: 1 0 auto;
}
.c3_flex_auto {
	flex: 0 0 auto;
}
/* inflexible, no-shrink */
.c3_flex_n {
	flex: none;
}

.c3_flex_noshrk {
	flex-shrink: 0;
}
.c3_flex_noshrk_childs>* {
	flex-shrink: 0;
}

.c3_flex_basis0 {
  flex-basis: 0;
}

/* one element centered inside container */
.c3_flex_center_1, .c3_cols_c {
	display: flex;
	flex-flow: column nowrap;
	justify-content: center;
	align-items: center;
}

.c3_rows_nowrap_center {
	display: flex;
	flex-flow: row nowrap;
	justify-content: center;
	align-items: center;
}

.c3_align_center {
	align-items: center;
}
.c3_align_s_s {
	align-self: start;
}
.c3_align_s_c {
	align-self: center;
}
.c3_align_s_e {
	align-self: end;
}
.c3_justify_center, .c3_j_c {
	justify-content: center;
}
.c3_j_e {
	justify-content: flex-end;
}
.c3_j_sb {
	justify-content: space-between;
}
.c3_j_start {
	justify-content: start;
}
.c3_j_s {
	justify-content: stretch;
}
.c3_j_evenly {
  justify-items: evenly;
}

.c3_ji_c {
	justify-items: center;
}
.c3_ji_s {
	justify-items: stretch;
}

.c3_js_e {
	justify-self: end;
}

.c3_align_start {
	align-items: start;
}
.c3_align_stretch {
	align-items: stretch;
}
.c3_align_end {
	align-items: flex-end;
}

/* use with .c3_cols_nowrap */
.c3_cols_spacing>*, .c3_cols_spacing_e>* {
	margin-bottom: 4px;
	margin-top: 4px;
}
/* _xe => no 0 margin at edge */
.c3_cols_spacing_x>*, .c3_cols_spacing_xe>* {
	margin-bottom: 10px;
	margin-top: 10px;
}
.c3_cols_spacing_xx>*, .c3_cols_spacing_xxe>* {
	margin-bottom: 20px;
	margin-top: 20px;
}

.c3_cols_spacing>*:first-child, .c3_cols_spacing_x>*:first-child,
	.c3_cols_spacing_xx>*:first-child {
	margin-top: initial;
}
.c3_cols_spacing>*:last-child, .c3_cols_spacing_x>*:last-child,
	.c3_cols_spacing_xx>*:last-child {
	margin-bottom: initial;
}
.c3_cols_spacing>*:only-child, .c3_cols_spacing_x>*:only-child,
	.c3_cols_spacing_xx>*:only-child {
	margin-top: initial;
	margin-bottom: initial;
}

/* use with .c3_rows_nowrap */
.c3_rows_spacing>*, .c3_rows_spacing_e>* {
	margin-right: 6px;
}
.c3_rows_nowrap.c3_rows_spacing>*:last-child, .c3_rows_nowrap_start.c3_rows_spacing>*:last-child,
 .c3_rows_nowrap_center.c3_rows_spacing>*:last-child {
	margin-right: 0px;
}
.c3_rows_spacing_x>*, .c3_rows_spacing_xe>* {
	/* x - extra space */
	margin-right: 15px;
}
.c3_rows_nowrap.c3_rows_spacing_x>*:last-child, .c3_rows_nowrap_start.c3_rows_spacing_x>*:last-child,
 .c3_rows_nowrap_center.c3_rows_spacing_x>*:last-child {
	margin-right: 0px;
}
.c3_rows_spacing_xx>*, .c3_rows_spacing_xxe>* {
	margin-right: 25px;
}
.c3_rows_nowrap.c3_rows_spacing_xx>*:last-child, .c3_rows_nowrap_start.c3_rows_spacing_xx>*:last-child,
 .c3_rows_nowrap_center.c3_rows_spacing_xx>*:last-child {
	margin-right: 0px;
}
.c3_rows_spacing>*:last-child, .c3_rows_spacing_x>*:last-child,
	.c3_rows_spacing_xx>*:last-child {
	margin-right: initial;
}
.c3_rows_spacing>*:only-child, .c3_rows_spacing_x>*:only-child,
	.c3_rows_spacing_xx>*:only-child {
	margin-right: initial;
}

.c3_ar_1x1 {
	aspect-ratio: 1/1;
}

.c3_order_-1 {order: -1;}

.c3_pa_top {
	position-area: top center;
}
.c3_pa_top_center {
	position-area: top span-left;
}
.c3_pa_center_top {
	position-area: top span-right;
}
.c3_pa_top_full {
	position-area: top;
}
.c3_pa_left {
	position-area: left center;
}
.c3_pa_left_center {
	position-area: left span-top;
}
.c3_pa_center_left {
	position-area: left span-bottom;
}
.c3_pa_left_full {
	position-area: left;
}
.c3_pa_right {
	position-area: right center;
}
.c3_pa_right_center {
	position-area: right span-top;
}
.c3_pa_center_right {
	position-area: right span-bottom;
}
.c3_pa_right_full {
	position-area: right;
}
.c3_pa_bottom {
	position-area: bottom center;
}
.c3_pa_bottom_center {
	position-area: bottom span-left;
}
.c3_pa_center_bottom {
	position-area: bottom span-right;
}
.c3_pa_bottom_full {
	position-area: bottom;
}
.c3_pa_top_left {
	position-area: top left'
}
.c3_pa_top_right {
	position-area: top right;
}
.c3_pa_bottom_left {
	position-area: bottom left;
}
.c3_pa_bottom_right {
	position-area: bottom right;
}

/* fallback */
.c3_posi_try_flipBlock {
	position-try-fallbacks: flip-block;
}
.c3_posi_try_flipInline {
	position-try-fallbacks: flip-inline;
}
.c3_posi_try_flipStart {
	position-try-fallbacks: flip-start;
}

/* svg - icons */
.c3i {
	width: 100%;
	height: 100%;
	/* default - same as font size */
	width: 1em;
	height: 1em;
    color: inherit;
    fill: currentColor;
}
.c3i_txt {
	/* same as font size */
	width: 1em;
	height: 1em;
}
.c3i_x1 {
	flex: 1;
	width: unset;
	height: unset;
}

.c3_gp05 {
	gap: .5px;
}
.c3_gp1 {
	gap: 1px;
}
.c3_gp2 {
	gap: 2px;
}
.c3_gp3 {
	gap: 3px;
}
.c3_gp4, .c3_gp_s {
	gap: 4px;
}
.c3_gp5 {
	gap: 5px;
}
.c3_gp6 {
	gap: 6px;
}
.c3_gp8, .c3_gp {
	gap: 8px;
}
.c3_gp10 {
	gap: 10px;
}
.c3_gp12 {
	gap: 12px;
}
.c3_gp14 {
	gap: 14px;
}
.c3_gp15 {
	gap: 15px;
}
.c3_gp16 {
	gap: 16px;
}
.c3_gp20, .c3_gp_x {
	gap: 20px;
}
.c3_gp22 {
	gap: 22px;
}
.c3_gp24, .c3_gp_xl {
	gap: 24px;
}
.c3_gp25 {
	gap: 25px;
}
.c3_gp28 {
	gap: 28px;
}
.c3_gp32 {
  gap: 32px;
}
.c3_gp33 {
  gap: 33px;
}
.c3_gp40, .c3_gp_xx {
	gap: 40px;
}
.c3_gp48 {
	gap: 48px;
}
.c3_gp50 {
	gap: 50px;
}
.c3_gp70 {
	gap: 70px;
}
.c3_gpR2 {
	row-gap: 2px;
}
.c3_gpR3 {
	row-gap: 3px;
}
.c3_gpR4, .c3_gpRs {
	row-gap: 4px;
}
.c3_gpR6, .c3_gpR {
	row-gap: 6px;
}
.c3_gpR8 {
	row-gap: 8px;
}
.c3_gpR10 {
	row-gap: 10px;
}
.c3_gpR15, .c3_gpRx {
	row-gap: 15px;
}
.c3_gpR16 {
	row-gap: 16px;
}
.c3_gpR20 {
	row-gap: 20px;
}
.c3_gpR24 {
	row-gap: 24px;
}
.c3_gpR25, .c3_gpRxx {
	row-gap: 25px;
}
.c3_gpR48 {
	row-gap: 48px;
}

.c3_gpC3 {
	column-gap: 3px;
}
.c3_gpC4, .c3_gpCs {
	column-gap: 4px;
}
.c3_gpC6 {
	column-gap: 6px;
}
.c3_gpC8, .c3_gpC {
	column-gap: 8px;
}
.c3_gpC10 {
	column-gap: 10px;
}
.c3_gpC15 {
	column-gap: 15px;
}
.c3_gpC16 {
	column-gap: 16px;
}
.c3_gpC20, .c3_gpCx {
	column-gap: 20px;
}
.c3_gpC24 {
	column-gap: 24px;
}
.c3_gpC25 {
	column-gap: 25px;
}
.c3_gpC32 {
	column-gap: 32px;
}
.c3_gpC40, .c3_gpCxx {
	column-gap: 40px;
}
.c3_gpC60 {
	column-gap: 60px;
}

.c3_posA {
	position: absolute;
}
.c3_posR {
	position: relative;
}

/* generic titles */
/* BEGIN COPY */
.c3_ttl {
	font-weight: 500;
}
.c3_ttl1 {
	font-size: 20px;
	font-weight: 500;
	line-height: 28px;
}
.c3_ttl2 {
	font-size: 16px;
	font-weight: 500;
}
.c3_ttl3 {
	font-size: 14px;
	font-weight: 500;
}
.c3_ttl4 {
	font-size: 12px;
	font-weight: 500;
}

/* bit more custom compared to above */
.c3_ttl2n {
	font-size: 16px;
	font-weight: 600;
	color: var(--c-gray-110);
}
.c3_ttlH3 {
	font-family: Roboto;
	font-weight: 500;
	font-size: 20px;
	line-height: 28px;
	color: var(--c-gray-110);
}

.c3_ttl_fw_n {
	font-weight: normal;
}
/*
 * Common padding, border radius font-sizes and buttons
 */
 .c3_pd_8h {
 	padding-left: 8px;
 	padding-right: 8px;
 }
 .c3_pd_10h {
	padding-left: 10px;
	padding-right: 10px;
 }
 .c3_pd-h_0l {
 	padding-left: 0;
 }
 .c3_ffm-inh {
 	font-family: inherit;
 }
 .c3_bdr0 {
 	border: 0;
 }
 .c3_pd_0 {
 	padding: 0px;
 }
 .c3_pd_1v {
 	padding-top: 1px;
 	padding-bottom: 1px;
 }
.c3_pd_4v {
	padding-top: 4px;
	padding-bottom: 4px;
}
.c3_pd_16v {
	padding-top: 16px;
	padding-bottom: 16px;
}
.c3_pd_24 {
	padding: 24px;
}

.c3_br4 {
	border-radius: 4px;
}
.c3_br6 {
	border-radius: 6px;
}
.c3_br8 {
	border-radius: 8px;
}
.c3_br20 {
  border-radius: 20px;
}

.c3_fs14 {
	font-size: 14px;
}

.c3_btn {
	cursor: pointer;
}


.c3_pill {
	background-color: var(--c-pill-bg-color);
	color: var(--c-gray-0);
	border-radius: 100px;
	padding: 14px;
	padding-top: 6px;
	padding-bottom: 6px;
}

.c3_clear {
	clear: both;
}

.c3_upper {
	text-transform: uppercase;
}

.c3_hide {
	display: none !important;
}

.c3_hide_imp {
	display: none !important;
}

.c3_break_word {
	overflow-wrap: break-word;
	word-break: break-word;
}

.pr { position: relative; }
.c3_posR {
	position: relative;
}
.c3_posA {
	position: absolute;
}
.c3_posF {
	position: fixed;
}
.c3_posAT0 {top:0;}
.c3_posAR0 {right:0;}
.c3_posAB0 {bottom:0;}
.c3_posAL0 {left:0;}
.c3_posW100 {width:100%;}
.c3_posH100 {height:100%;}
.c3_posAT48 {top:48px;}

.c3_saveText {
	position: absolute;
	color: #67b661;
	margin: 5px 10px 0px 15%;
	right: 3%;
}

.c3_wordBreak {
	word-break: break-all;
}
.c3_text_ellipsis {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}
.c3_txt_nowrp {
	white-space: nowrap;
}
.c3_txt_cap {
    text-transform: capitalize;
}
/* border colors */
.c3-bdr-c-blue10 {border-color: var(--c-blue-10);}
.c3-bdr-c-blue50 {border-color: var(--c-blue-50);}
.c3-bdr-c-blue60 {border-color: var(--c-blue-60);}
.c3-bdr-c-blue70 {border-color: var(--c-blue-70);}
.c3-bdr-c-gray50 {border-color: var(--c-gray-50);}
.c3-bdr-c-gray90 {border-color: var(--c-gray-90);}
.c3-bdr-c-gray110 {border-color: var(--c-gray-110);}
.c3-bdr-c-red50 {border-color: var(--c-red-50);}
.c3-bdr-c-red60 {border-color: var(--c-red-60);}
.c3-bdr-c-black {border-color: black;}
.c3-bdr-c-white {border-color: white;}

.c3-bdr-c-blue50_t {border-top: 2px solid var(--c-blue-50);}
.c3-bdr-c-blue50_b {border-bottom: 2px solid var(--c-blue-50);}
/* END COPY */

@layer layer2 {
	.btn-grp-fx, .btn-fx{
		display: -webkit-flex;
		display: flex;
		-webkit-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-justify-content: space-between;
		justify-content: space-between;
		-webkit-align-items: center;
		align-items: center;
	}

	.btn-lbl {
		text-transform: capitalize;
	}
	.btn-lbl-lo {
		text-transform: lowercase;
	}
	.btn-lbl-up {
		text-transform: uppercase;
	}

	.btn-icn {
	}
	.btn-icn-sq {
		display: flex;
		flex-flow: column nowrap;
		justify-content: center;
		align-items: center;

		border: 1px solid var(--c-gray-80);
		box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.05);
		border-radius: 4px;
	}
	/* no border/outline */
	.btn-icn-sq-no-bdr {
		display: flex;
		flex-flow: column nowrap;
		justify-content: center;
		align-items: center;

		border: 1px solid transparent;
		border-radius: 4px;
		box-shadow: unset;
	}
	.btn-icn-sq:hover {
		background-color: var(--c-gray-40);
	}
	.btn-icn-sq:active {
		box-shadow: inset 0px 2px 0px rgba(0, 0, 0, 0.05);
		background-color: var(--c-gray-50);
	}
	.btn-icn-sq:focus {
		background-color: var(--c-gray-0);
	}

	.c3_btn.c3_txt_lnk {
		box-shadow: unset;
	}

	.btn-n.c3_rows_nowrap>i{
		opacity: .87;
		font-size: 81%;
	}

	.btn-n {
		-moz-user-select: none;
		border: 1px solid rgba(0, 0, 0, 0);
		cursor: pointer;
		font-size: 14px;
		font-weight: normal;
		line-height: 1.42857;
		padding: 6px 12px;
		text-align: center;
		vertical-align: middle;
		white-space: nowrap;
	}

	.btn-default-outline {
		background: #d7dacc;
		color: #757575;
	}

	.btn-grp-fx {
		gap: 15px;
	}
	.btn-fx, .tab-fx {
		gap: 9.25px;
	}

	.btn-fx>i {
		opacity: .87;
		font-size: 81%;
	}

	.btn-flat {
		box-shadow: unset;
	}
}

.btn-default-outline2_new {
	background: none !important;
	border: 1px solid #bbbbbb !important;
}
.btn-default-outline2_new:hover {
	background: #d7dacc !important;
	color: #fff !important;
}
.btn-primary0 {
	background-color: var(--btn-primary0-c);
	border-color: #007BFF;
	color: #FFFFFF;
}
.btn-primary0:hover {
	color: #fff;
	background-color: #286090;
	background-color: #005ABA;
	border-color: #204d74;
}
.btn-secondary-outline {
	color: var(--c-gray-110);
	border: 1px solid var(--c-gray-80) !important;
	font-weight: 400 !important;
}
.btn-secondary-outline .caret {
	border-top-color:  var(--c-gray-80);
}
.btn-secondary-outline:hover, .btn-secondary-outline.active {
	background: var(--c-gray-40);
	color: var(--c-gray-110);
}
.btn-secondary-outline:hover .caret, .btn-secondary-outline.active .caret {
	border-top-color: white;
}
.btn-secondary-outline-disabled {
	color: var(--c-gray-80);
	border: 1px solid var(--c-gray-60) !important;
	font-weight: 400 !important;
	box-shadow: 0px 0px 7px 2px rgba(219, 219, 219, 0.58) !important;
	background-color: var(--c-gray-20);
	cursor: default !important;
}

.c3_txt_lnk1 {
	color: var(--lv-ttl-c);
	cursor: pointer;
}
.c3_txt_lnk1:focus,
.c3_txt_lnk1:hover {
	color: #1F5199;
	text-decoration: underline;
}
.c3_txt_lnk1:active {
	text-decoration: none;
	color: var(--c-active);
}

/*
 * ==================================================================
 *  Runtime page layouts
 * ==================================================================
 */
.udc_hcenter {
  position:absolute;
  right:50%;
}

.udc_vcenter {
  position:absolute;
  bottom:50%;
}

.boldFont {
  font-weight:bold;
}

/* A Text attached to an icon */
.udc_image_text {
  padding-left:6px;
  vertical-align: bottom;
  cursor: pointer;
  font-size: 11px;
  color: #777777;
  font-weight: bold;
}

/* A clickable image */
.udc_image_link {
  cursor: pointer;
}
.udc_image_link:hover {

}


.udc_link {
  font-style: normal;
  cursor: pointer;
}
.udc_link:hover {
  border-bottom: solid thin blue;
}

.udc_frame {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #EEEEEE;
}

.udc_frame_header {
  /*background: url("/res/bigbg.png") center;*/
  background-color: #D9DBE0;
  height: 40px;
  border-bottom: thin solid #E6FFF2;
  border-left: 12px solid #993333;
}

.udc_frame_header_caption {
    color: #333333;
    height: 30px;
    text-shadow: 2px 2px 2px #666666;
}

.udc_frame_body {
  position: absolute;
  width: 100%;
  top: 41px;
  bottom: 30px;
}

.udc_frame_footer {
  position: absolute;
  bottom: 0;
  height: 30px;
  width: 100%;
  border-top-style: solid;
  border-top-width: thin;
  border-top-color: #b9b9b9;
  background-color: #eaeaea;
}


/**
 * ===========================================
 * Runtime Forms
 * ===========================================
 */
.udc_form {
  width: 300px;
  padding: 10px;
}

/** Header of the login form */
.udc_form_header {
  height: 30px;
  font-size: larger;
}

.udc_form_row_item {
  padding: 5px 5px 5px 5px;
}

.udc_help_text {
  font-size: smaller;
  color: #8b8b8b;
}




/**
 * =================================
 * Runtime frames
 * =================================
 */
.udc_headless_frame {
  position: absolute;
  left: 10px;
  right: 10px;
  top: 10px;
  bottom: 10px;
  /*border: this solid #CCCCCC;
  border-radius: 6px;
  box-shadow: 2px 2px 10px 4px #BBBBBB;*/
  background-color: #FFFFFF;
  padding: 20px;
}


/**
 ====================================================
 Command Bar Styles
 ====================================================
 */

.udc_command_bar {
  height: 100%;
}

.udc_command_bar_item {
  float: left;
  cursor: pointer;
  padding-right: 6px;
  padding-left: 6px;
  color: #4775A3;
  height: 100%;
  font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
}

.udc_command_bar_selectable_item {
  float: left;
  cursor: pointer;
  padding-right: 6px;
  padding-left: 6px;
  color: #777777;
  height: 100%;
  font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
}


.udc_command_bar_item_separator {
  float: left;
  padding-top: 10px;
}

.udc_command_bar_selectable_item:hover {
  /*background-color: #CCCCCC;*/
  color: #000000;
  border-bottom: solid #E08566;
}

.udc_command_bar_item_selected {
    background-color: #FFFFFF;
    color: #222222;
    cursor: pointer;
    float: left;
    font-family: Verdana,Geneva,Arial,Helvetica,sans-serif;
    font-weight: bold;
    height: 100%;
    padding-left: 6px;
    padding-right: 6px;

}


/**
 * =====================================================
 * A Box around a div with a header
 * =====================================================
 */
.udc_box {
	position: relative;
  border: thin solid #DDDDDD;
  border-radius: 6px;
}

.udc_box .header {
  height: 20px;
  background-color: #CCCCCC;
  border-bottom: thin solid #999999;
  padding: 4px;
}

.udc_box .content {
	position: absolute;
  top: 30px;
  bottom: 0px;
  width: 98%;
  overflow: hidden;
}

.udc_left_right_align {
margin-left: 10px;
margin-right: 10px;
}
/**
 * =====================================================
 * Table layout classes by div
 * =====================================================
 */
.udc_table_layout {

}

.udc_table_layout .udc_table_row {
  padding-bottom: 6px;
}

.udc_table_layout .udc_table_row .udc_table_first_col {
  padding: 4px;
  float: left;
}

.udc_table_layout .udc_table_row .udc_table_col {
  padding: 4px;
  float: left;
}

.udc_table_layout .udc_table_row .udc_table_last_col {
  padding: 4px;
  clear: all;
}


/**
 * ====================================================
 * A simple table to show data
 * ====================================================
 */
.udc_data_table {
  border-collapse: collapse;
}

.udc_data_table_leftText {
	border-collapse: collapse;
	text-align: left;
}

.udc_data_table .udc_data_header {
    background-color: #DEDEDE;
    border-bottom: thin solid #CCCCCC;
    box-shadow: 2px 2px 2px #999999;
    height: 30px;
    padding: 4px;
    text-align: left;
}

.udc_data_table .udc_data_row {
  padding: 4px;
  border-bottom: thin solid #DDDDDD;
  height: 30px;
}

.udc_data_table .udc_data_row .udc_data_col {

}


/**
 * ====================================================
 * Chart display styles
 * ====================================================
 */
/* A table element that */
.udc_chart_tooltip {
  font-size: small;
}

.udc_dash_component_div {
 position: relative;
}


.udc_chart_area {
}


/**
 *   Chart Filter Styles
 */

.udc_filter_area {
  float: right;
  height: 100%;
  width: 200px;
}

.udc_data_item {
  position: relative;
  padding-bottom: 20px;
}

.udc_data_item .item_content {
  color: #3D3D3D;
  font-size: 11px;
  left: 4px;
  max-height: 100px;
  min-height: 20px;
  overflow-y: auto;
  position: relative;
  width:90%;
}

.udc_data_item .item_content .filter_item {
  cursor: pointer;
}
.udc_filter_area .udc_data_item .item_content .filter_item:hover {
  background-color: #D6EBFF;
}

.udc_data_item .item_content .filter_item_selected {
  cursor: pointer;
  background-color: #ADD6FF;
}


.udc_data_item .item_header {
  height: 20px;
  padding-bottom: 5px;
}

.udc_data_item .item_header .header_text {
  color: #006E00;
  float: left;
  font-weight: bold;
  left: 4px;
  top: 2px;
  position: relative;
  font-size: 12px;
}

.udc_data_item .item_header .header_del {
  float: right;
  right: 0px;
  top: 2px;
  position: relative;
}

.udc_chart_legend_area {
  border: thin solid #AAAAAA;
  border-radius: 4px 4px 4px 4px;
  padding: 4px;
}


/*
 * ====================================================
 * Runtime page styles
 * ====================================================
 */

.udc_dash_template_header {
  margin-bottom: 10px;
}

/* Top most header of a page that shows a single dashboard and contains a nav control */
.udc_dash_page_header {
  background-color: #474747;
  box-shadow: 0 1px 1px 1px #999999;
  height: 30px;
  margin-bottom: 10px;
}

.udc_dash_page_header_item {
  position: relative;
  top: 3px;
  padding-left: 10px;
}

/* Page body that hosts a dashboard */
.udc_dash_page_body {
  margin: 10px;
}

.udc_dash_header {
}

/*
 * ====================================================
 * Date & Time selector widget
 * ====================================================
 */

.chartTimeSelector {
	float: right;
}
.chartTimeSelector_box {
	border: 1px solid #efefef;
	background-color: #FFFFF8;
	color: #9c9fa1;
	margin-bottom: 6px;
}
.chartTimeSelector label {
	background: none;
}
.c3_timeSelector_control_box {
	position: absolute;
	border: 1px solid #ccc;
	box-shadow: 0px 3px 6px rgba(111, 111, 111, 0.2);
	min-width: 200px;
	overflow-x: hidden;
	overflow-y: auto;
	color: #333;
	background-color: #fff;
	padding: 0px 0px 10px 0px;
	z-index: 999;
}

.c3_timeSelector_display_box {
	display: flex;
	flex-flow: row;
	align-items: flex-end;
	justify-content: space-between;
	/* padding: 2px; */
	cursor: pointer;
	font-size: 14px;
	/* padding: 4px 10px; */
}

/*.c3_timeSelector_display_box:hover .c3_timeSelector_display_item_v {
	background-color: lightgray;
}*/
.c3_timeSelector_display_box:hover {
	/* background-color: #eeeeee;
	padding: 4px 10px; */
}
.c3_dash_body .c3_timeSelector_display_box {
	font-size: 11px;
}
.c3_timeSelector_display_item {
	margin-left: 6px;
	margin-right: 6px;
}
.c3_timeSelector_control_body {
	padding: 20px;
}
.c3_timeSelector_control_r {
	clear: both;
	margin-bottom: 4px;
}
.c3_timeSelector_control_ro {
	border-bottom: 1px solid rgba(63, 81, 181, 0.47);
	padding: 14px 0px;
}
.c3_timeSelector_control_ro:last-child {
	border-bottom: 0;
}
.c3_timeSelector_control_footer {
	padding: 2px 20px;
}
.c3_timeSelector_control_f_btns {
	float: right;
}
.c3_timeSelector_control_f_btns .btn {
	padding: 4px 12px;
}
.c3_timeSelector_display_item label {
	color: #999;
}
.c3_timeSelector_control_header {
	color: #838383;
	stroke: none;
	text-shadow: none;
	font-size: 16px;
	padding: 10px 20px;
	background: #f5f5f5;
}
/*
=====================================
Slider Styles
=====================================
*/

.c3_rngSldr {
	width: 100%;
	position: relative;
	height: 3px;
}

.c3_rngSldr .c3_rngSldrInner {
	position: absolute;
	left: 13px;
	right: 15px;
	height: inherit;
}

.c3_rngSldr .c3_rngSldrInner .c3_sldrInvL {
	position: absolute;
	left: 0;
	height: inherit;
	border-radius: 10px;
	background-color: #CCC;
}

.c3_rngSldr .c3_rngSldrInner .c3_sldrInvR {
	position: absolute;
	right: 0;
	height: inherit;
	border-radius: 10px;
	background-color: #CCC;
}

.c3_rngSldr .c3_rngSldrInner .c3_sldrRngBar {
	position: absolute;
	left: 0;
	height: inherit;
	border-radius: 10px;
	background-color: #0055a9;
}

.c3_rngSldr .c3_rngSldrInner .c3_sldrThmb {
	position: absolute;
	top: -4px;
	z-index: 2;
	height: 12px;
	width: 12px;
	text-align: left;
	margin-left: -11px;
	cursor: pointer;
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
	background-color: #FFF;
	border-radius: 50%;
	outline: none;
}

.c3_rngSldr input[type=range] {
	position: absolute;
	pointer-events: none;
	-webkit-appearance: none;
	z-index: 3;
	height: 14px;
	top: -2px;
	width: 100%;
	opacity: 0;
}
.c3_rngSldr.c3_mozRngSldr input[type=range] {
	/* fix for FF unable to apply focus style bug  */
	border: 1px solid white;
	/*required for proper track sizing in FF*/
	width: 300px;
}

.c3_rngSldr.c3_mozRngSldr input[type=range]::-moz-range-thumb{
	pointer-events: all;
	width: 28px;
	height: 28px;
	border-radius: 0px;
	border: 0 none;
	background: #0055a9;
	-webkit-appearance: none;
}
.c3_rngSldr.c3_mozRngSldr input[type=range]:focus::-moz-range-track {
	background: transparent;
	border: transparent;
}

.c3_rngSldr input[type=range]::-webkit-slider-thumb{
	pointer-events: all;
	width: 28px;
	height: 28px;
	border-radius: 0px;
	border: 0 none;
	background: #0055a9;
	-webkit-appearance: none;
}
.c3_rngSldr input[type=range]:focus::-webkit-slider-runnable-track {
	background: transparent;
	border: transparent;
}

.c3_rngSldr input[type=range]:focus {
	outline: none;
}

.c3_rngSldr input[type=range]::-ms-fill-lower {
	background: transparent;
	border: 0 none;
}

.c3_rngSldr input[type=range]::-ms-fill-upper {
	background: transparent;
	border: 0 none;
}

.c3_rngSldr input[type=range]::-ms-tooltip {
	display: none;
}

.c3_rngSldr .c3_rngSldrInner .c3_sldrSign {
	position: absolute;
	margin-left: -7px;
	top: 20px;
	z-index:3;
	color: #000;
	align-items: center;
	-webkit-justify-content: center;
	justify-content: center;
	text-align: center;
	font-size: 11px;
}

/*
=====================================
Slider Styles - END
=====================================
*/

.c3_timeRangeBox {
	border: 1px solid var(--c-gray-80);
	border-radius: 4px;
	font-size: 14px;
	padding: 5px 10px;
	line-height: 1.7;
	font-weight: 400;
	transition: all 0.15s ease 0s;
	box-sizing: border-box;
}
.c3_timeRangeBox:focus,
.c3_timeRangeBox:hover {
	background-color: #eeeeee;
}
.c3_tsIcon {
	font-size: 14px;
	font-weight: 400;
}
.c3_tsIcon .ti-angle-down {
	padding-top: 1px;
	font-weight: 800;
}
.c3_tsIcon .ti-calendar {
	color: #8C8C8C;
}
.c3_blkoutDate .ti-calendar {
	margin-left: 5px;
}

.c3_chartTemp_title {
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	font-family: Roboto, system-ui;
	font-size: 18px;
	font-weight: normal;
	color: #000000;
	align-items: center;
}

.google-visualization-tooltip {
  pointer-events: none;
  border: 0px!important;
  background-color: #FFFFFF;
  border-radius: 8px !important;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1) !important;
  font-family: Roboto;
}
.d3-visualization-tooltip {
	background-color: #FFF;
	border: 1px solid var(--c-gray-80);
	box-sizing: border-box;
	box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.2), 0px 2px 10px rgba(0, 0, 0, 0.1);
	border-radius: 8px;
	padding: 14px 16px;
	font-size: 12px;
}

/*
=====================================
Dashboard Edit boxes
=====================================
*/

.c3_chartTemplate_r2 .dijitTextBox {
    line-height: 20px;
    font-size: 14px;
    font-weight: 400;
    font-family: 'Roboto';
    font-style: normal;
    border-radius: 4px;
    color: #141414;
    height: 36px;
    width: 100px;
}

.c3_chartTemplate_r2 .dijitInputField, .c3_chartTemplate_r2 .dijitValidationContainer {
    padding-top: 8px !important;
    padding-left: 8px !important;
}
.c3_chartTemplate_r2 .dijitValidationContainer {
    border-radius: 0px 4px 4px 0px;
}

.c3_input_number:focus {
	border-color: var(--c-purple-40);
}

/*
=====================================
Dashboard Edit boxes - END
=====================================
*/

.c3_pnlFltrBtn {
	font-size: 16px;
	padding: 9px;
	color: var(--pnl-fltr-btn-bg-c);
	background-color: var(--pnl-fltr-btn-c);
	border: var(--pnl-fltr-btn-bdr);
}
.c3_pnlFltrBtn:first-child, .c3_ftrDataBtn:first-child {
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}
 .c3_pnlFltrBtn:last-child {
	border-left: 0px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}
.c3_pnlFltrSel {
	z-index: 2;
}
.c3_pnlFltr {
	position: relative;
}
.c3_pnlFltrB {
	background-color: #FFFFFF;
}
.c3_pnlFltrBH1 {
	font-size: 20px;
    color: #141414;
    font-family: Roboto;
    font-weight: 500;
}
.c3_pnlFltrCat {
	/*padding: 4px 24px 4px 4px;
	border-right: 1px solid silver;
	flex: 2; */
	min-width: 150px;
}
.c3_pnlFltrCat:last-child {
	border: 0px;
	flex: 1;
}
.c3_pnlFltrBH2 {
	color: var(--pnl-fltr-btn-bg-c, #007aff);
	text-transform: uppercase;
	min-height: 24px;
}
.c3_pnlFltrSBtn {
	background-color: var(--pnl-fltr-sel-item-bg-c);
	padding: 2px 8px;
	border-radius: 2px;
	font-size: 11px;
}
.c3_pnlFltrSBtnLbl {
    color: var(--pnl-fltr-sel-item-c);
}
.c3_pnlFltrSBtnX {
    color: var(--pnl-fltr-sel-item-x);
}
.c3_pnlFltrSBtnX>i {
    font-weight: 600;
    font-size: 8px;
}
.c3_pnlFltrCatH {
	font-weight: 500;
    font-size: 16px;
    color: #141414;
    font-family: Roboto;
    line-height: 24px;
}
.c3_pnlFltrCatI {
    width: 12.5px;
    height: 9.13px;
    color: #141414;
}
.c3_pnlFltrBOvh {
    width: 0;
    position: fixed;
    top: 0;
    right: 0px;
    bottom: 0px;
    left: unset;
    overflow-x: hidden;
}
.c3_pnlFltrBOv {
    min-width: 310px;
    width: 20%;
    max-width: 400px;
    position: fixed;
    z-index: 10000;
    top: 0;
    right: 0px;
    bottom: 0px;
    left: unset;
    overflow-x: hidden;
    box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.1), 0px 4px 20px rgba(0, 0, 0, 0.15);
    padding: 24px 0px 0px 0px;
}
.c3_pnlFltrBOv2 {
    width: 300px;
    max-height: 500px;
    position: absolute;
    top: 39px;
    bottom: unset;
    right: unset;
    left: unset;
}
.c3_pnlFltrBC {
	display: grid;
	/* grid-gap: 8px 6px; */
	grid-template-columns: repeat(auto-fit, minmax(180px, auto));
	/* grid-template-columns: repeat(auto-fill, minmax(281px, auto)); */
}
.c3_pnlFltrSec {
    padding-left: 24px;
    padding-right: 24px;
}
.c3_pnlFltrC {
	display: grid;
	grid-gap: 0px 40px;
	/*ltr*/
	/* grid-template-columns: repeat(auto-fill, minmax(60px, 120px)); */

	grid-auto-flow: column;
	grid-template-rows: repeat(auto-fill, minmax(20px, 30px));
	/*max-height: 120px;*/
	grid-template-rows: repeat(4, minmax(20px, 30px));
	grid-template-rows: repeat(4, minmax(24px, auto));
	align-items: start;
	grid-row-gap: 0px;
}
.c3_pnlFltrLbl {
    font-size: 14px;
    font-weight: 400;
    color: #141414;
    font-family: Roboto;
    line-height: 20px;
}
.c3_pnlFltrBF {
    border-top: 1px solid var(--c-gray-50);
}
.c3_pnlFltrClx {
    font-size: 10px;
}
.c3_pnFltrDefault {
  position: fixed;
  top: 0;
  right: 0px;
  left: unset;
  overflow-x: hidden;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.1), 0px 4px 20px rgba(0, 0, 0, 0.15);
}

/* lite date picker begin */
.day-item.is-locked[data-multiselect-max="1"][tabindex="0"] {
	color: var(--litepicker-day-color) !important;
}
.litepicker[data-plugins="multiselect"] .container__days .day-item[tabindex="0"]:not(.is-selected):hover {
	color: var(--litepicker-day-color);
	-webkit-box-shadow: inset 0 0 0 1px var(--litepicker-day-color-hover);
	box-shadow: inset 0 0 0 1px var(--litepicker-day-color-hover);
}
.c3_litePicker_dt_cont {
	border: 1px solid var(--c-gray-80);
	border-radius: 4px;
	font-size: 14px;
	padding: 5px 10px;
	height: 100%;
	font-weight: 400;
	transition: all 0.15s ease 0s;
	box-sizing: border-box;
}
.c3_purpleIt .c3_litePicker_dt_cont {
	border-radius: 20px;
	border: 1px solid var(--c-purple-40);
}
.c3_dt_picker_cont_wrp {
	border: 1px solid var(--c-gray-80);
	border-radius: 8px;
	user-select: none;
	max-height: calc(75vh - 30px);
}
.c3_dt_picker_cont {
	position: relative;
	padding: 5px 10px;
	height: 100%;
	min-width: 180px;
	font-weight: 400;
	transition: all 0.15s ease 0s;
	box-sizing: border-box;
}
.c3_timePickerCont .c3_dt_picker_cont {
	position: relative;
	padding: 5px 10px;
	height: 100%;
	min-width: 180px;
	font-weight: 400;
	transition: all 0.15s ease 0s;
	box-sizing: border-box;
}
.c3_purpleIt .c3_timePickerCont .c3_fld_deco {
	border: 1px solid var(--c-purple-40);
}
.c3_purpleIt .c3_dt_picker_cont {
	padding: 0 20px 0 0;
}
.c3_dt_picker_cont:hover {
	cursor: pointer;
}	
.c3_dt_picker_cont .c3_dt_picker_box {
	line-height: 1.7;
}
.c3_purpleIt .c3_dt_picker_cont .c3_dt_picker_box {
	flex-flow: row-reverse;
	align-items: center;
	padding: 1px;
	justify-content: flex-end;
}
.c3_purpleIt .c3_dt_icon_cont {
	border-radius: 50px;
	padding: 5px 10px 5px 5px;
	background-color: var(--c-purple-10);
}
.c3_blkoutDate.c3_dt_icon_cont {
	width: 20px;
	height: 24px;
}
.c3_predef_cont {
	padding: 16px 0;
	border-top-left-radius: 8px !important;
}
.c3_predef_cont_2 {
	border-bottom-left-radius: 8px !important;
}
.c3_predef_mon_cont {
	border-top-right-radius: 8px !important;
	border-bottom-left-radius: unset !important;
	border-top-left-radius: unset !important;
}
.c3_predef_mon_cont_2 {
	border-bottom-right-radius: 8px !important;
}
.c3_predef_mon_cont_3 {
	border-top-right-radius: 8px !important;
	border-top-left-radius: 8px !important;
}
.c3_predef_unset_radius {
	border-bottom-left-radius: unset !important;
	border-bottom-right-radius: unset !important;
}
.c3_te_version_cont {
	width: auto !important;
	border-top-left-radius: unset !important;
	border-top-right-radius: unset !important;
	border-bottom-left-radius: 8px !important;
	border-bottom-right-radius: 8px !important;
}

.c3_nobrd_rds_tp_left{
	border-top-left-radius: unset !important;
}
.c3_brd_tp_left{
	border-top-left-radius: 4px;
}
.c3_brd_tp_right{
	border-top-right-radius: 4px;
}
.c3_predef_dt_cont {
	box-sizing: content-box;
	padding: 8px 0;
	overflow-y: auto;
	height: 200px;
	font-size: 14px;
	font-family: Roboto, system-ui;
}
.c3_predef_dt_cont:first-child {
	padding: 0 0;
}
.c3_predef_dt_cont:last-child {
	padding: 0 0;
}
.c3_predef_dt_btn {
	padding: 8px 8px;
	margin: 0 8px;
}
.c3_btn.c3_predef_dt_btn:hover {
	color: #2196F3;
	box-shadow: inset 0 0 0 1px #2196F3;
	border-radius: 3px 3px;
}
.c3_purpleIt .c3_btn.c3_predef_dt_btn:hover {
	color: var(--c-purple-40);
	box-shadow: inset 0 0 0 1px var(--c-purple-40);
	border-radius: 20px 20px;
}
.c3_predef_dt_btn_active {
	background-color: #132D54;
	color: #ffffff;
	border-radius: 3px 3px;
}
.c3_pred_dt_btn_active:hover {
	color: #ffffff;
}

/* overrided Litepicker class */
.is-selected::after {
	background-color: var(--c-pill-bg-color) !important;
	border-top-right-radius: 100px !important;
	border-bottom-right-radius: 100px !important;
	border-bottom-left-radius: 100px !important;
	border-top-left-radius: 100px !important;
}
.is-in-range {
	background-color: #F2F7FE !important;
}
.is-start-date {
	border-bottom-left-radius: 100px !important;
	border-top-left-radius: 100px !important;
	background-color: var(--c-pill-bg-color) !important;
}
.is-end-date {
	border-top-right-radius: 100px !important;
	border-bottom-right-radius: 100px !important;
	background-color: var(--c-pill-bg-color) !important;
}
.litepicker {
	font-family: Roboto, system-ui !important;
	font-size: 1em !important;
	background-color: var(--c-gray-0);
}
.c3_purpleIt.litepicker .container__days .day-item {
	border-radius: 20px;
}
.litepicker .container__footer {
	background-color: var(--c-gray-0) !important;
	box-shadow: none !important;
}
.litepicker .container__footer .button-apply {
	background-color: var(--c-pill-bg-color) !important;
}
.c3_purpleIt.litepicker .container__footer .button-apply {
	border-radius: 20px;
}
.litepicker .container__footer .button-apply:hover {
	cursor: pointer;
}
.c3_purpleIt.litepicker .container__footer .button-cancel {
	border-radius: 20px;
}
.litepicker .container__footer .button-cancel:hover {
	cursor: pointer;
}
.month-item-header {
	font-weight: 700 !important;
}
/* lite date picker end */
/* ========c3Table2======== */
/* BEGIN COPY */
:focus {
	outline: none;
}
/* table sort icon */
.c3_data_table th>.c3_txt_lnk::after {
	content: "\00a0";
	margin-left: 4px;
	min-width: 10px;
	font-size: 90%;
	display: inline-block;
	/*transform: scale(.7);*/
	opacity: .6;
}
th.c3_sort_descending-true>.c3_txt_lnk::after {
	/*content: "\2193";*/
	content: url(res/angleDown.svg);
}
th.c3_sort_descending-false>.c3_txt_lnk::after {
	/*content: "\2191";*/
	content: url(res/angleUp.svg);
}
.c3_lvCHSrtI {
	opacity: 0;
}
.c3_lvCHSrtOn .c3_lvCHSrtI {
	opacity: initial;
}
.c3_txt_c>.c3_lvCHIn.c3_rows_nowrap_start_center {
	justify-content: center;
}
.c3_lvRw .c3_move_handle {
	background: url('res/drag_move_icon.png') no-repeat 5px 8px;
}
.c3_nav_mnuSubIC_selected_lvl1 {
	color: #023361;
}
.c3_nav_mnuSubIC_selected_lvl2 {
	color: #023361;
	background-color: var(--c-gray-30);
	border-radius: 4px;
}
.c3_lv2 .c3_waitBx::before {
	top: 16px;
	left: 16px;
	width: calc(100% - 32px);
	height: calc(100% - 32px);
}
.c3_lvBdy {
	/*align-items: flex-start;*/
	position: relative;
}
.c3_lvT {
	display: flex;
	flex-flow: column nowrap;
	justify-content: flex-start;
	align-items: stretch;
	flex: 1;
	border: 1px solid #ebebeb;
	position: relative;
}
.c3_lv2 .c3_lvT {
	border-color: var(--lv2-bdr-c);
}
.c3_lvNW[data-lv-t-wide="1"] .c3_lvT {
	align-self: flex-start;
}
.c3_lvNoSideBdr .c3_lvT {
	border-left-color: transparent;
	border-right-color: transparent;
}
.c3_lvNoBottomBdr .c3_lvT {
	border-bottom-color: transparent;
}
.c3_lvNoTopBdr .c3_lvT {
	border-top-color: transparent;
}
.c3_lvRw {
	display: flex;
	flex-flow: row nowrap;
	justify-content: flex-start;
	align-items: stretch;
	padding-top: 5px;
	padding-bottom: 5px;
}
.c3_lv2 .c3_lvRw {
	padding-top: 0px;
	padding-bottom: 0px;
}
.c3_lvR {
	color: #4F5C72;
	border-bottom: 1px solid rgba(216, 221, 230, 0.62);
}
.c3_lvR:last-child {
	border-bottom: 1px solid transparent;
}
.c3_lv2 .c3_lvR {
	border-bottom: 0px;
	box-shadow: var(--lv2-tr-box-shdo);
}
.c3_lv2 .c3_lvR:last-child {
	border-bottom: 0px;
	box-shadow: unset;
}
.c3_lvR:nth-child(odd) {
	background-color: var(--tr-bg-o-c);
}
.c3_lv2R:nth-child(odd) {
	background-color: var(--tr-bg-e-c);
}
.c3_lvR:nth-child(even) {
	background-color: var(--tr-bg-e-c);
}
.c3_lv2 .c3_lvRSel {
	background-color: var(--c-gray-40);
	box-shadow: inset 0px -1px 0px var(--c-gray-50);
}
.c3_lvHvr .c3_lvR:hover {
	background-color: var(--c-gray-40);
}
.c3_lvRH {
	/*background-color: #D4E9F0;*/
	font-size: 12px;
	font-weight: 600;
	font-family: Roboto;
	color: rgb(38, 35, 46);
	border-bottom: 2px solid #7FB7D0;
	position: sticky;
	top: 0;
	z-index: 10;
	background-color: white;
}
.c3_lv2 .c3_lvRH {
	border-bottom: 1px solid rgba(216, 221, 230, 0.62);
	font-size: 14px;
}
.c3_lvCH {
	position: relative;
}
.c3_lvCHSrtI {
	color: #595959;
	width: 8.75px;
}
.c3_lvCHIn {
}
.c3_lvCl {
	overflow: hidden;
	padding: var(--lv-cl-pd-v) var(--lv-cl-pd-h);
}
.c3_lv2 .c3_lvCl {
	padding: var(--lv2-cl-pd-v) var(--lv2-cl-pd-h);
}
.c3_lv2 .c3_lvCl_icn {
	padding-left: var(--lv2-cl-pd-h-icn);
	padding-right: var(--lv2-cl-pd-h-icn);
}
.c3_lvCl_pd_hx {
	padding-left: var(--lv-cl-pd-h-x);
	padding-right: var(--lv-cl-pd-h-x);
}
.c3_lv_pd_h2 {
	padding-left: 2px;
	padding-right: 2px;
}
.c3_lv_pd_h0 {
	padding-left: 0px;
	padding-right: 0px;
}
.c3_lv_pd_v0 {
	padding-top: 0px;
	padding-bottom: 0px;
}
.c3_lvNoSideBdr .c3_lvCl:first-child {
	padding-left: var(--lv-cl-edge-pd-h);
}
.c3_lvNoSideBdr .c3_lvCl:last-child {
	padding-right: var(--lv-cl-edge-pd-h);
}

.c3_lv2 .c3_lvCl {
	font-size: var(--lv2-cl-ft-sz);
	line-height: var(--lv2-cl-lnh);
	color: var(--lv2-cl-c);
}
.c3_lv2 .c3_lvCl_s {
    font-size: var(--lv2-cl-ft-sz);
    line-height: var(--lv2-cl-lnh-s);
    color: var(--lv2-cl-c);
}

.c3_lv_date_s {
	min-width: var(--lv-c-date-s);
	max-width: var(--lv-c-date-s);
}
.c3_lv_date_ts {
	min-width: var(--lvt-date-ts-w);
	max-width: var(--lvt-date-ts-w);
}
.c3_lv_icn_20, .c3_lv_icn_20_1x {
	min-width: var(--lv-c-icn-20);
	max-width: var(--lv-c-icn-20);
}
.c3_lv_icn_20_2x {
	min-width: calc(2*(var(--lv-c-icn-20)) + 16px);
	max-width: calc(2*(var(--lv-c-icn-20)) + 16px);
}
.c3_lv_icn_20_3x {
	min-width: calc(3*(var(--lv-c-icn-20)) + 2*16px);
	max-width: calc(3*(var(--lv-c-icn-20)) + 2*16px);
}
.c3_lv_icn_26, .c3_lv_icn_26_1x {
	min-width: var(--lv-c-icn-26);
	max-width: var(--lv-c-icn-26);
}
.c3_lv_icn_26_2x {
	min-width: calc(2*(var(--lv-c-icn-26)) + 16px);
	max-width: calc(2*(var(--lv-c-icn-26)) + 16px);
}
.c3_lv_icn_26_3x {
	min-width: calc(3*(var(--lv-c-icn-26)) + 2*16px);
	max-width: calc(3*(var(--lv-c-icn-26)) + 2*16px);
}
.c3_lv_text_e {
  min-width: var(--lv-c-txt-e);
  max-width: var(--lv-c-txt-e);
}
.c3_lv_text_xs {
	min-width: var(--lv-c-txt-xs);
	max-width: var(--lv-c-txt-xs);
}
.c3_lv_text_s {
	min-width: var(--lv-c-txt-s);
	max-width: var(--lv-c-txt-s);
}
.c3_lv_text_s2 {
	min-width: var(--lv-c-txt-s2);
	max-width: var(--lv-c-txt-s2);
}
.c3_lv_text_s3 {
	min-width: var(--lv-c-txt-s3);
	max-width: var(--lv-c-txt-s3);
}
.c3_lv_text_s4 {
	min-width: var(--lv-c-txt-s4);
	max-width: var(--lv-c-txt-s4);
}
.c3_lv_text_m {
	min-width: var(--lv-c-txt-m);
	max-width: var(--lv-c-txt-m);
}
.c3_lv_text_m2 {
	min-width: var(--lv-c-txt-m2);
	max-width: var(--lv-c-txt-m2);
}
.c3_lv_text_m.c3_lvCl_pd_hx {
	min-width: var(--lv-c-txt-m-hx);
	max-width: var(--lv-c-txt-m-hx);
}
.c3_lv_text_l {
	min-width: var(--lv-c-txt-l);
	max-width: var(--lv-c-txt-l);
}
.c3_lv_text_l2 {
	min-width: var(--lv-c-txt-l2);
	max-width: var(--lv-c-txt-l2);
}
.c3_lv_text_xl {
	min-width: var(--lv-c-txt-xl);
	max-width: var(--lv-c-txt-xl);
}
.c3_lv_text_xxl {
	min-width: var(--lv-c-txt-xxl);
	max-width: var(--lv-c-txt-xxl);
}
.c3_lv_text_xxxl {
	min-width: var(--lv-c-txt-xxxl);
	max-width: var(--lv-c-txt-xxxl);
}
.c3_lv_text_4xl {
	min-width: var(--lv-c-txt-4xl);
	max-width: var(--lv-c-txt-4xl);
}
.c3_lv_text_fx1m {
	min-width: var(--lv-c-txt-fx1m);
}
.c3_lv_btns_s {
	min-width: var(--lv-c-btns-s);
	max-width: var(--lv-c-btns-s);
}
.c3_lv_btns_m {
	min-width: var(--lv-c-btns-m);
	max-width: var(--lv-c-btns-m);
}
.c3_lvPrevImg {
	max-width: 260px;
}
.c3_lvTag {
	background-color: orange;
	color: white;
	border-radius: 2px;
	padding: 2px 6px;
	font-size: var(--lv-tag-ft-sz);
	margin: 2px;
	letter-spacing: .4px;
	text-transform: uppercase;
}
.c3_lvTagGrd {
	border-top-right-radius: 0px;
	border-bottom-right-radius: 0px;
}
.c3_lvTag:first-child {
	margin-left: 0px;
}
.c3_lvTag:last-child {
	margin-right: 0px;
}
.c3_lvTag:only-child {
	margin: 0px;
}
.c3_lvTagcampaign {
	background-color: #ffa000;
}
.c3_lvTagcampaignonce {
	background-color: #c2185b;
}
.c3_lvTagTRIGGERED {
	background-color: #ffa000;
}
.c3_lvTagSCHEDULED {
	background-color: #c2185b;
}
.c3_lvTagACTIVITY {
	background-color: #8bc34a;
}
.c3_lvTagWEATHER {
	background-color: #c2185b;
}
.c3_lvTagPATTERN {
	background-color: #8bc34a;
}
.c3_lvTagLOCATION {
	background-color: #ffa000;
}
.c3_lvTagDYNAMIC {
	background-color: #ffa000;
}
.c3_lvTagUPLOADED {
	background-color: #c2185b;
}
.c3_lvTagML {
	background-color: #5E2538;
}
.c3_lvTagOTHER {
	background-color: cornflowerblue;
}

.c3_lvBtn_i {
	font-size: 15px;
	opacity: .5;
}
.c3_lvBtn_i:hover {
	opacity: 1;
}
.c3_grdBtn_i {
	opacity: .5;
}
.c3_grdBtn_i:hover {
	opacity: 1;
}

.c3_lvTtl {
}

.c3_lv_x {
	max-width: initial;
}
.c3_lv_x1 {
	flex: 1;
}
.c3_lv_x2 {
	flex: 2;
}
.c3_lv_x3 {
	flex: 3;
}

.c3_lvClG {
	display: flex;
	flex-flow: row nowrap;
	/*align-items: center;*/
}
.c3_lvT_ml .c3_lvClG1 {
}
.c3_lvT_segment .c3_lvClG1 {
}
.c3_lvT_experience .c3_lvClG1 {
}
.c3_lvT_segmentUl .c3_lvClG1 {
}

/* max-width */
@media screen and (max-width: 962px) {
	.c3_z1Tbl .c3_z1TblTopBar {
		flex-flow: row nowrap;
	}
}
@media screen and (max-width: 1047px) {
	.c3_z1Tbl .c3_z1TblTopBar {
		flex-flow: row nowrap;
	}
}
@media screen and (max-width: 1134px) {
	.c3_z1Tbl .c3_z1TblTopBar {
		flex-flow: row nowrap;
	}
}
@media screen and (max-width: 1192px) {
	.c3_z1TblTopBar {
		flex-flow: row nowrap;
	}
}
@media screen and (max-width: 1205px) {
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
}
@media screen and (max-width: 1290px) {
	.c3_z1Tbl .c3_lv_text_xxxl {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lvClG1,
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD,
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
}
@media screen and (max-width: 1362px) {
	.c3_lvT_chart .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
}
@media screen and (max-width: 1350px) {
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
}
@media screen and (max-width: 1380px) {
	.c3_z1Tbl .c3_lv_text_xxxl {
		min-width: var(--lv-c-txt-m2);
		max-width: var(--lv-c-txt-m2);
	}
}
@media screen and (max-width: 1517px) {
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
}
@media screen and (max-width: 1537px) {
	.c3_z1Tbl .c3_lv_text_xxxl {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
	/* .c3_z1Tbl .c3_lv_text_m {
		min-width: var(--lv-c-txt-s2);
		max-width: var(--lv-c-txt-s2);
	} */
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
	.c3_z1Tbl .c3_lvClG1 {
		flex-flow: column nowrap;
	}
	.c3_z1Tbl .c3_lvClGD {
		font-size: 11px;
	}
}
@media screen and (max-width: 1920px) {
	.c3_z1TblTopBar {
		flex-flow: row wrap;
	}
}

/* min width screen */
@media screen and (min-width: 962px) {
	.c3_z1Tbl .c3_lv_textExp_xxxl {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_textExp_l {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_date_ts {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_textExp_m {
		min-width: var(--lv-c-txt-s2);
		max-width: var(--lv-c-txt-s2);
	}
	.c3_z1Tbl .c3_lv_textExp_s3 {
		min-width: var(--lv-c-txt-s2);
		max-width: var(--lv-c-txt-s2);
	}
	.c3_z1Tbl .c3_lv_textExp2_s3 {
		min-width: var(--lv-c-txt-s2);
		max-width: var(--lv-c-txt-s2);
	}
	.c3_z1Tbl .c3_lv_textExp_s {
		min-width: var(--lv-c-txt-xs);
		max-width: var(--lv-c-txt-xs);
	}
	.c3_z1Tbl .c3_lv_name_xxl {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
	.c3_z1Tbl .c3_lv_name_text_m {
		min-width: var(--lv-c-txt-s2);
		max-width: var(--lv-c-txt-s2);
	}
	.c3_z1Tbl .c3_lv_name_xxxl {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
	.c3_z1Tbl .c3_lv_name_l {
		min-width: var(--lv-c-txt-s2);
		max-width: var(--lv-c-txt-s2);
	}
	.c3_z1Tbl .c3_lv_name_m {
		min-width: var(--lv-c-txt-s3);
		max-width: var(--lv-c-txt-s3);
	}
}
@media screen and (min-width: 1540px) {
	.c3_z1Tbl .c3_lv_textExp_xxxl {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
	.c3_z1Tbl .c3_lv_textExp2_s3 {
		min-width: var(--lv-c-txt-s3);
		max-width: var(--lv-c-txt-s3);
	}
	.c3_z1Tbl .c3_lv_name_xxl {
		min-width: var(--lv-c-txt-xxl);
		max-width: var(--lv-c-txt-xxl);
	}
	.c3_z1Tbl .c3_lv_name_xxxl {
		min-width: var(--lv-c-txt-xxl);
		max-width: var(--lv-c-txt-xxl);
	}
}
@media screen and (min-width: 1600px) {
	.c3_z1Tbl .c3_lv_textExp_l {
		min-width: var(--lv-c-txt-m2);
		max-width: var(--lv-c-txt-m2);
	}
	.c3_z1Tbl .c3_lv_textExp_m {
		min-width: var(--lv-c-txt-s3);
		max-width: var(--lv-c-txt-s3);
	}
	.c3_z1Tbl .c3_lv_textExp_s3 {
		min-width: var(--lv-c-txt-s3);
		max-width: var(--lv-c-txt-s3);
	}
	.c3_z1Tbl .c3_lv_textExp_s {
		min-width: var(--lv-c-txt-s);
		max-width: var(--lv-c-txt-s);
	}
	.c3_z1Tbl .c3_lv_name_text_m {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_sumLift_s3 {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_name_xxxl {
		min-width: var(--lv-c-txt-xxxl);
		max-width: var(--lv-c-txt-xxxl);
	}
	.c3_z1Tbl .c3_lv_name_l {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
}
@media screen and (min-width: 1720px) {
	.c3_z1Tbl .c3_lv_textExp_xxxl {
		min-width: var(--lv-c-txt-xl);
		max-width: var(--lv-c-txt-xl);
	}
	.c3_z1Tbl .c3_lv_date_ts {
		min-width: var(--lv-c-txt-m2);
		max-width: var(--lv-c-txt-m2);
	}
	.c3_z1Tbl .c3_lv_name_xxl {
		min-width: var(--lv-c-txt-xxxl);
		max-width: var(--lv-c-txt-xxxl);
	}
	.c3_z1Tbl .c3_lv_sumLift_s3 {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
	.c3_z1Tbl .c3_lv_name_l {
		min-width: var(--lv-c-txt-m2);
		max-width: var(--lv-c-txt-m2);
	}
	.c3_z1Tbl .c3_lv_name_m {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
}
@media screen and (min-width: 1820px) {
	.c3_z1Tbl .c3_lv_textExp_xxxl {
		min-width: var(--lv-c-txt-xxl);
		max-width: var(--lv-c-txt-xxl);
	}
	.c3_z1Tbl .c3_lv_textExp_m {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_textExp_s3 {
		min-width: var(--lv-c-txt-m);
		max-width: var(--lv-c-txt-m);
	}
	.c3_z1Tbl .c3_lv_name_l {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
}
@media screen and (min-width: 1900px) {
	.c3_z1Tbl .c3_lv_textExp_xxxl {
		min-width: var(--lv-c-txt-xxxl);
		max-width: var(--lv-c-txt-xxxl);
	}
	.c3_z1Tbl .c3_lv_textExp_l {
		min-width: var(--lv-c-txt-l);
		max-width: var(--lv-c-txt-l);
	}
	.c3_z1Tbl .c3_lv_date_ts {
		min-width: var(--lvt-date-ts-w);
		max-width: var(--lvt-date-ts-w);
	}
}

.c3_lv_timeRange {
	/* font-size: 90%; */
	/* color: rgb(0 0 0 / 42%); */
	color: var(--c-gray-100);
	/*color: #4F5C72;*/
	letter-spacing: -.2px;
	padding: 0px;
}
.c3_lv_timeRange .c3_timerControl_out_v {
	/* background-color: rgb(100 149 237 / 11%); */
	padding: 0px 2px;
	letter-spacing: .79px;
	color: rgb(0 0 0 / 72%);
	/* color: #4F5C72; */
	color: var(--c-gray-110);
}
.c3_lvSchdSclWb {
	border-bottom: 1px solid #00000017;
}
.c3_lvR.dojoDndItem {
	padding: 5px 0px !important;
}
.c3_lvR.dojoDndItemOver {
	border-color: transparent;
	border-bottom-width: 1px;
	border-bottom-color: rgb(0 0 0 / 61%);
	/*padding: 5px 2px;*/
	cursor: default;
}
div.c3_lvR.c3_tr_drop_target {
	border-style: solid;
	border-bottom-style: dashed;
}
.c3_lvR.dojoDndItemBefore,
.c3_lvR.dojoDndItemAfter {
	border-color: rgb(25, 69, 114) !important;
}
.c3_lvDnDCue {
	display: none;
	position: absolute;
	left: 30px;
	background-color: dimGray;
	color: white;
	padding: 1px 2px;
	border-radius: 3px;
}
.c3_lvR.dojoDndItemBefore .c3_DnDCueBf,
.c3_lvR.dojoDndItemAfter .c3_DnDCueAf {
	display: flex;
}
.c3_lvR.c3_lvRwXpdH {
	background-color: #ECEFF1;
	box-shadow: inset 0px 1px 1px 0px var(--lv-rw-xpd-bdr-c),
		inset -1px 0px 1px 0px var(--lv-rw-xpd-bdr-c),
		inset 1px 0px 1px var(--lv-rw-xpd-bdr-c);
	border-bottom: 0px;
}
.c3_lvRwXpdB {
	padding-top: 10px;
	border-top: 0;
	box-shadow: inset 0px -1px 1px 0px var(--lv-rw-xpd-bdr-c),
		inset -1px 0px 1px 0px var(--lv-rw-xpd-bdr-c),
		inset 1px 0px 1px var(--lv-rw-xpd-bdr-c);
	margin-top: -1px;
}
.c3_lvCH.c3_lv_grab {
	border-left: 3px solid transparent;
}
.c3_lvExSiAxns {
	display: none;
	top: 2px;
	right: 2px;
	color: gray;
}
.c3_lvExSiCell:hover .c3_lvExSiAxns {
	display: flex;
}
.c3_lvExSiAxns:hover {
	color: black;
}
.c3_lvEvtMpCC {
	min-height: 36px;
}
.c3_lvEvtMpArw {
	color: #8c8c8c;
}
.c3_lvR .c3_sel_shdo {
	box-shadow: inset 0px -2px 1px blue;
}
.c3_lvR .c3_lvRSel {
	background-color: var(--c-gray-40);
	box-shadow: inset 0px -1px 0px var(--c-gray-50);
}
.c3_lvThumbImg {
	max-width: 100px;
}
.c3_pgBGGrey {
	background-color: var(--pg-rhs-bg-c-grey);
}
.c3_bxWhite {
	background-color: var(--pg-rhs-bg-c-white);
	/*box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.05), 0px 0px 1px rgba(0, 0, 0, 0.25);*/
	border-radius: 8px;
}
.c3_pgBGGrey .c3_bxWhite_s {
	background-color: var(--pg-rhs-bg-c-white);
	/*box-shadow: 0px 1px 0px rgb(0 0 0 / 5%);*/
	border-radius: 4px;
}
.c3_bxOutline {
	border: 1px solid var(--c-gray-50);
	box-sizing: border-box;
	/*box-shadow: 0px 2px 1px rgb(0 0 0 / 5%), 0px 0px 1px rgb(0 0 0 / 25%);*/
	border-radius: 8px;
}
/* ========c3Table2======== */
/* ===========Search============ */
.c3_txt_lnk {
	cursor: pointer;
}
.c3_txt_lnk:focus,
.c3_txt_lnk:hover {
	cursor: pointer;
	text-decoration: underline;
}

/* c3 tooltip popup text */
.c3_tooltip {
	position: relative;
	display: inline-block;
	overflow: unset;
}
.c3_tooltip[data-tooltip-txt]::after {
	visibility: hidden;
	background-color: var(--c-gray-100);
	color: var(--c-gray-0);
	border-radius: 6px;
	padding: 5px 10px;
	position: absolute;
	z-index: 999;
	transform: scale(.8);
	content: attr(data-tooltip-txt);
	width: fit-content;
	height: fit-content;
}
.c3_tooltip:hover[data-tooltip-txt]::after {
	visibility: visible;
}
.c3_tooltip.c3_l20_b25[data-tooltip-txt]::after {
	bottom: 25%;
	left: 20%;
}
.c3_tooltip.c3_l20_b65[data-tooltip-txt]::after {
	bottom: 65%;
	left: 20%;
}
.c3_tooltip.c3_l20_b75[data-tooltip-txt]::after {
	bottom: 75%;
	left: 20%;
}
.c3_tooltip.c3_l30_b10[data-tooltip-txt]::after {
	bottom: 10%;
	left: 30%;
}
.c3_tooltip.c3_l10_t40[data-tooltip-txt]::after {
	top: 40%;
	left: 10%;
}
.c3_tooltip.c3_l20_t40[data-tooltip-txt]::after {
	top: 40%;
	left: 20%;
}
.c3_tooltip.c3_l20_t50[data-tooltip-txt]::after {
	top: 50%;
	left: 20%;
}
.c3_tooltip.c3_l20_t70[data-tooltip-txt]::after {
	top: 70%;
	left: 20%;
}
.c3_lvRw:last-child .c3_posChg_b75::after {
	top: unset;
	bottom: 75%;
}
.c3_tooltip.c3_r0[data-tooltip-txt]::after {
    right: 0%;
}
.c3_tooltip.c3_r0_t100[data-tooltip-txt]::after {
	top: 100%;
	right: 0%;
}

.c3_tblSrchWidth {
	min-width: 320px;
}

.c3_vertical_dashed_divider {
    content: '';
    border-left: 1px dashed var(--border-color, #B8B8B8);
}

.c3_html_fld {
	height: 27px;
	/* border-radius: 2px; */
	border-radius: var(--fld-bdr-r);
	border-color: rgb(222, 222, 222);
	border-width: 1px;
	/* border-collapse: collapse; */
	padding-left: 8px;
	/* border-top: 1px solid rgb(222, 222, 222); */
	/* border: 1px solid; */
	border-style: solid;
	/* debug */
	/*background-color: aquamarine !important;*/
}
.c3_srchTypAh {
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	border-color: transparent;
	box-shadow: none;
	opacity: 1;
	background: rgb(255, 255, 255) none repeat scroll 0% 0%;
	color: var(--c-gray-70);
}
.c3_srchDrpDwn {
	z-index: 1000;
	background-color: #ffffff;
	box-shadow: var(--shdo-drpDwn);
}
.c3_srchOpn:hover, .c3_srchOpn.active {
	background-color: #EAF5FE;
}
.c3_srchInpG {
	border-color: transparent;
}
.c3_srchInp {
	background-color: transparent;
	z-index: 1;
}
.c3_srchRhsIn {
	background-color: white;
	z-index: 1;
}
/* ===========Search============ */
/* END COPY */
/* override reboot css */
.c3_z1TblC *, .c3_z1TblC *::before, .c3_z1TblC *::after {
    box-sizing: unset;
}
.c3_z1Tbl .c3_lvCl_e {
  padding: var(--lv-cl-e-pd-v) var(--lv-cl-e-pd-h);
}
