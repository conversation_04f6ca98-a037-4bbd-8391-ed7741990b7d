Q. What is z1app?

This is UI to view the Session AI profiles and various installed solutions that runs 
on Udichi platform. This app runs within udichi web app and therefore installed within
the same war file. 


----------------------------------------------
Q How to setup to build z1app?

As the target of this app to install within the Udichi web app, the build needs to 
know the location where udichi application is insatalled. You need to change two 
build files to add that info.

a) Change the "build.xml" under "z1app/"
b) Change the "build.xml" under "z1app/js/build folder"

to change the following line

<property name="UDICHI_ROOT" value="../../udcrepos/udichi" />
   
to a relative path where udichi app is installed.

----------------------------------------------
Q. How to build z1app

For you test purpose, just run "devmake" from the build.xml under z1app. To run 
the command from eclipse, right click on that file and select "Run As..." to 
select "devmake". This will take care of creating jars, copying them over etc.


----------------------------------------------
Q. How to launch the Session AI app?

Start udichi platform. Open a browser and point to the url "localhost:8888/apps"
(assuming that you configured udichi to bind to port 8888).

